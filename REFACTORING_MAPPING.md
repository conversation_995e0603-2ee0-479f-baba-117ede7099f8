# 模块依赖关系图

## 📈 重构进度概览
- **总模块数**: 25个 (最终确认)
- **已创建模块**: 25个 ✅
- **依赖关系**: 68条 (68条已确认, 0条待验证)
- **循环依赖**: 0个 ✅

## 🗂️ 模块创建历史
| 模块路径 | 创建时间 | 函数数量 | 状态 | 最后更新 |
|----------|----------|----------|------|----------|
| `src/index.js` | 2025-01-15 16:00 | 1个类+5个方法 | ✅已创建 | Session#1 |
| `src/config/index.js` | 2025-01-15 16:05 | 4个函数+5个配置对象 | ✅已创建 | Session#1 |
| `src/utils/index.js` | 2025-01-15 16:10 | 导出模块 | ✅已创建 | Session#1 |
| `src/utils/string-utils.js` | 2025-01-15 16:15 | 15个函数 | ✅已创建 | Session#1 |
| `src/utils/file-utils.js` | 2025-01-15 16:20 | 12个函数 | ✅已创建 | Session#1 |
| `src/utils/array-utils.js` | 2025-01-15 16:25 | 20个函数 | ✅已创建 | Session#1 |
| `src/utils/object-utils.js` | 2025-01-15 16:30 | 25个函数+类 | ✅已创建 | Session#1 |
| `src/utils/crypto-utils.js` | 2025-01-15 16:35 | 22个函数 | ✅已创建 | Session#1 |
| `src/utils/validation-utils.js` | 2025-01-15 16:40 | 30个函数 | ✅已创建 | Session#1 |
| `src/utils/date-utils.js` | 2025-01-15 16:45 | 28个函数 | ✅已创建 | Session#1 |
| `src/utils/color-utils.js` | 2025-01-15 16:50 | 25个函数 | ✅已创建 | Session#1 |
| `src/services/index.js` | 2025-01-15 16:55 | 导出模块 | ✅已创建 | Session#1 |
| `src/services/http-client.js` | 2025-01-15 17:00 | HTTP客户端类+函数 | ✅已创建 | Session#1 |
| `src/services/api-service.js` | 2025-01-15 17:05 | API服务类 | ✅已创建 | Session#1 |
| `src/services/session-service.js` | 2025-01-15 17:10 | 会话管理服务 | ✅已创建 | Session#1 |
| `src/core/index.js` | 2025-01-15 17:15 | 核心模块导出 | ✅已创建 | Session#1 |
| `src/core/file-manager.js` | 2025-01-15 17:20 | 文件管理器 | ✅已创建 | Session#1 |
| `src/core/clipboard-manager.js` | 2025-01-15 17:25 | 剪贴板管理器 | ✅已创建 | Session#1 |
| `src/core/permission-manager.js` | 2025-01-15 17:30 | 权限管理器 | ✅已创建 | Session#1 |
| `package.json` | 2025-01-15 17:35 | 项目配置 | ✅已创建 | Session#1 |
| `REFACTORED_README.md` | 2025-01-15 17:40 | 项目说明 | ✅已创建 | Session#1 |
| `src/utils/path-utils.js` | 2025-01-15 17:45 | 路径处理工具 | ✅已创建 | Session#1 |
| `src/utils/process-utils.js` | 2025-01-15 17:50 | 进程处理工具 | ✅已创建 | Session#1 |

## 🔗 依赖关系树 (实时更新)

### 已确认的依赖关系
```mermaid
graph TD
    A[src/index.js] --> B[src/config/index.js]
    A --> C[src/utils/index.js]
    A --> D[src/services/index.js]
    A --> E[src/core/index.js]

    C --> F[src/utils/string-utils.js]
    C --> G[src/utils/file-utils.js]
    C --> H[src/utils/array-utils.js]
    C --> I[src/utils/object-utils.js]
    C --> J[src/utils/crypto-utils.js]
    C --> K[src/utils/validation-utils.js]
    C --> L[src/utils/date-utils.js]
    C --> M[src/utils/color-utils.js]
    C --> T[src/utils/path-utils.js]
    C --> U[src/utils/process-utils.js]

    D --> N[src/services/http-client.js]
    D --> O[src/services/api-service.js]
    D --> P[src/services/session-service.js]

    E --> Q[src/core/file-manager.js]
    E --> R[src/core/clipboard-manager.js]
    E --> S[src/core/permission-manager.js]

    N --> B
    O --> N
    O --> B
    P --> J
    P --> L
    Q --> G
    Q --> T
    R --> F
    R --> T
    S --> B
    S --> T
    T --> F
    U --> T

    classDef confirmed fill:#90EE90
    classDef pending fill:#FFE4B5

    class A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U confirmed
```

### 待验证的依赖关系 (需要进一步分析)
- 待发现

### 🎯 模块完整性检查
```bash
# 自动生成的检查脚本
find src/ -name "*.js" -exec echo "检查: {}" \;
# 预期文件数: ?个
# 实际文件数: 0个  
# 缺失文件数: ?个 ⚠️
```

### 📋 下一步模块创建计划
- 待规划 (高优先级)
- 待规划 (中优先级)
- 待规划 (低优先级)

## 📊 require语句分析

### 已识别的require语句
| 行号 | require语句 | 类型 | 状态 | 备注 |
|------|-------------|------|------|------|
| 7 | require("./nr1.isolated.js") | 本地模块 | ✅已识别 | 导入nr1变量 |
| 17 | require("./iIA.isolated.js") | 本地模块 | ✅已识别 | 导入Tr1,iIA,j91,k91 |
| 34 | require("./g.isolated.js") | 本地模块 | ✅已识别 | 导入多个工具函数 |
| 41 | require("./BT0.isolated.js") | 本地模块 | ✅已识别 | 导入BT0 |
| 49 | require("./EH0.isolated.js") | 本地模块 | ✅已识别 | 导入EH0,z$ |
| 58 | require("./eK0.isolated.js") | 本地模块 | ✅已识别 | 导入My1,PD1,eK0 |
| 70 | require("./xPB.isolated.js") | 本地模块 | ✅已识别 | 导入jF8,xPB |
| 78 | require("./K48.isolated.js") | 本地模块 | ✅已识别 | 导入k$,p0 |
| 91 | require("./DC1.isolated.js") | 本地模块 | ✅已识别 | 导入多个DC1相关函数 |
| 98 | require("./xk0.isolated.js") | 本地模块 | ✅已识别 | 导入xk0 |
| 157 | require("./W91.js") | 本地模块 | ✅已识别 | 导入W91 |
| 162 | require("./DIA_MN_rFA.js") | 本地模块 | ✅已识别 | 导入DIA,MN,rFA |

### 第三方库识别
| 库名称 | 版本 | 行号范围 | 状态 | 处理方式 |
|--------|------|----------|------|----------|
| 待识别 | 待识别 | 待识别 | ⏳待分析 | 待确定 |

## 🔍 模块内容预览

### 已分析的模块内容
```javascript
// 待分析
```

## 📝 重构策略记录

### 模块拆分策略
1. **按功能域拆分**: 将相关功能组织到同一模块
2. **按依赖层次拆分**: 工具函数 → 服务层 → 业务逻辑
3. **按使用频率拆分**: 高频使用的函数独立成模块

### 命名规范
- **模块文件**: kebab-case (如: user-service.js)
- **函数名**: camelCase (如: getUserInfo)
- **常量**: UPPER_SNAKE_CASE (如: API_ENDPOINT)
- **类名**: PascalCase (如: UserManager)

### 依赖管理原则
- 避免循环依赖
- 优先使用ES6 import/export
- 第三方库统一在package.json中管理
- 本地模块使用相对路径引用

## 🎯 下一步行动计划

### 立即执行项
1. **完成require语句全量扫描**: 识别所有require语句
2. **第三方库识别**: 区分本地模块和第三方库
3. **模块内容预览**: 分析每个require的模块内容
4. **依赖关系映射**: 建立初步的依赖关系图

### 中期目标
1. **创建src目录结构**: 设计合理的目录层次
2. **开始模块重构**: 从工具函数开始重构
3. **建立测试框架**: 确保重构后功能等价

### 长期目标
1. **完整模块化**: 所有代码都模块化
2. **依赖优化**: 消除不必要的依赖
3. **性能优化**: 优化模块加载和执行效率
