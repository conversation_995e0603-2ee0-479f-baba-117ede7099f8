/**
 * API服务模块
 * @original: 原始文件中的API调用相关函数
 */

import { httpClient } from './http-client.js';
import { API_CONFIG } from '../config/index.js';

/**
 * API服务类
 * @original: 原始文件中的API相关逻辑
 */
export class ApiService {
  constructor(options = {}) {
    this.baseURL = options.baseURL || API_CONFIG.BASE_API_URL;
    this.accessToken = options.accessToken;
    this.organizationId = options.organizationId;
    
    // 设置HTTP客户端
    if (this.accessToken) {
      httpClient.setAuthToken(this.accessToken);
    }
  }
  
  /**
   * 设置访问令牌
   * @param {string} token - 访问令牌
   */
  setAccessToken(token) {
    this.accessToken = token;
    httpClient.setAuthToken(token);
  }
  
  /**
   * 设置组织ID
   * @param {string} organizationId - 组织ID
   */
  setOrganizationId(organizationId) {
    this.organizationId = organizationId;
  }
  
  /**
   * 获取认证头部
   * @returns {Object} 认证头部
   * @original: 原始文件L56107-56112中的XT0函数
   */
  getAuthHeaders() {
    if (!this.accessToken) {
      throw new Error('No access token found. Please authenticate first.');
    }
    
    return {
      'Authorization': `Bearer ${this.accessToken}`,
      'Content-Type': 'application/json'
    };
  }
  
  /**
   * OAuth授权URL生成
   * @param {Object} params - 授权参数
   * @returns {string} 授权URL
   * @original: 原始文件中的OAuth配置
   */
  generateAuthUrl(params = {}) {
    const {
      clientId = API_CONFIG.CLIENT_ID,
      redirectUri,
      scope = 'openid profile email',
      state,
      responseType = 'code'
    } = params;
    
    const authUrl = new URL(API_CONFIG.CONSOLE_AUTHORIZE_URL);
    authUrl.searchParams.set('client_id', clientId);
    authUrl.searchParams.set('response_type', responseType);
    authUrl.searchParams.set('scope', scope);
    
    if (redirectUri) {
      authUrl.searchParams.set('redirect_uri', redirectUri);
    }
    
    if (state) {
      authUrl.searchParams.set('state', state);
    }
    
    return authUrl.toString();
  }
  
  /**
   * 交换授权码获取访问令牌
   * @param {string} code - 授权码
   * @param {string} redirectUri - 重定向URI
   * @returns {Promise<Object>} 令牌信息
   */
  async exchangeCodeForToken(code, redirectUri) {
    const tokenData = {
      grant_type: 'authorization_code',
      code,
      redirect_uri: redirectUri,
      client_id: API_CONFIG.CLIENT_ID
    };
    
    try {
      const response = await httpClient.post(API_CONFIG.TOKEN_URL, tokenData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
      
      return response;
    } catch (error) {
      throw new Error(`Token exchange failed: ${error.message}`);
    }
  }
  
  /**
   * 刷新访问令牌
   * @param {string} refreshToken - 刷新令牌
   * @returns {Promise<Object>} 新的令牌信息
   */
  async refreshAccessToken(refreshToken) {
    const tokenData = {
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
      client_id: API_CONFIG.CLIENT_ID
    };
    
    try {
      const response = await httpClient.post(API_CONFIG.TOKEN_URL, tokenData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
      
      // 更新访问令牌
      if (response.access_token) {
        this.setAccessToken(response.access_token);
      }
      
      return response;
    } catch (error) {
      throw new Error(`Token refresh failed: ${error.message}`);
    }
  }
  
  /**
   * 创建API密钥
   * @param {Object} params - 创建参数
   * @returns {Promise<Object>} API密钥信息
   */
  async createApiKey(params = {}) {
    const url = API_CONFIG.API_KEY_URL;
    const headers = this.getAuthHeaders();
    
    try {
      const response = await httpClient.post(url, params, { headers });
      return response;
    } catch (error) {
      throw new Error(`API key creation failed: ${error.message}`);
    }
  }
  
  /**
   * 获取用户角色
   * @returns {Promise<Array>} 用户角色列表
   */
  async getUserRoles() {
    const url = API_CONFIG.ROLES_URL;
    const headers = this.getAuthHeaders();
    
    try {
      const response = await httpClient.get(url, { headers });
      return response;
    } catch (error) {
      throw new Error(`Failed to get user roles: ${error.message}`);
    }
  }
  
  /**
   * 获取代码会话列表
   * @returns {Promise<Array>} 会话列表
   * @original: 原始文件L56113-56131中的zcB函数
   */
  async getCodeSessions() {
    if (!this.organizationId) {
      throw new Error('Organization ID is required');
    }
    
    const url = `${this.baseURL}/api/oauth/organizations/${this.organizationId}/code/sessions`;
    const headers = this.getAuthHeaders();
    
    try {
      const response = await httpClient.get(url, { headers });
      return response.sessions || [];
    } catch (error) {
      throw new Error(`Failed to fetch code sessions: ${error.message}`);
    }
  }
  
  /**
   * 恢复代码会话
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Object>} 会话数据
   * @original: 原始文件L56191-56216中的VT0函数
   */
  async resumeCodeSession(sessionId) {
    if (!this.organizationId) {
      throw new Error('Organization ID is required');
    }
    
    const url = `${this.baseURL}/api/oauth/organizations/${this.organizationId}/code/sessions/${sessionId}/resume`;
    const headers = this.getAuthHeaders();
    
    try {
      const response = await httpClient.post(url, {}, { headers });
      return response;
    } catch (error) {
      throw new Error(`Failed to resume code session: ${error.message}`);
    }
  }
  
  /**
   * 创建远程会话
   * @param {string} description - 会话描述
   * @returns {Promise<Object>} 会话信息
   * @original: 原始文件L56326-56391中的sM8函数
   */
  async createRemoteSession(description) {
    if (!this.organizationId) {
      throw new Error('Organization ID is required');
    }
    
    const url = `${this.baseURL}/api/oauth/organizations/${this.organizationId}/code/sessions`;
    const headers = this.getAuthHeaders();
    
    const sessionData = {
      description,
      metadata: {
        platform: process.platform,
        nodeVersion: process.version,
        timestamp: new Date().toISOString()
      }
    };
    
    try {
      const response = await httpClient.post(url, sessionData, { headers });
      return response;
    } catch (error) {
      throw new Error(`Failed to create remote session: ${error.message}`);
    }
  }
  
  /**
   * 发送聊天消息
   * @param {Array} messages - 消息数组
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 响应数据
   */
  async sendChatMessage(messages, options = {}) {
    const url = `${this.baseURL}/v1/messages`;
    const headers = this.getAuthHeaders();
    
    const requestData = {
      model: options.model || 'claude-3-sonnet-20240229',
      max_tokens: options.maxTokens || 4096,
      messages,
      ...options
    };
    
    try {
      const response = await httpClient.post(url, requestData, { headers });
      return response;
    } catch (error) {
      throw new Error(`Chat message failed: ${error.message}`);
    }
  }
  
  /**
   * 流式发送聊天消息
   * @param {Array} messages - 消息数组
   * @param {Object} options - 选项
   * @returns {Promise<ReadableStream>} 流式响应
   */
  async sendChatMessageStream(messages, options = {}) {
    const url = `${this.baseURL}/v1/messages`;
    const headers = {
      ...this.getAuthHeaders(),
      'Accept': 'text/event-stream'
    };
    
    const requestData = {
      model: options.model || 'claude-3-sonnet-20240229',
      max_tokens: options.maxTokens || 4096,
      messages,
      stream: true,
      ...options
    };
    
    try {
      const response = await httpClient.post(url, requestData, { 
        headers,
        responseType: 'stream'
      });
      return response;
    } catch (error) {
      throw new Error(`Stream chat message failed: ${error.message}`);
    }
  }
  
  /**
   * 上传文件
   * @param {File|Buffer} file - 文件对象
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 上传结果
   */
  async uploadFile(file, options = {}) {
    const url = `${this.baseURL}/v1/files`;
    const headers = this.getAuthHeaders();
    
    try {
      const response = await httpClient.uploadFile(url, file, {
        config: { headers },
        ...options
      });
      return response;
    } catch (error) {
      throw new Error(`File upload failed: ${error.message}`);
    }
  }
  
  /**
   * 获取文件信息
   * @param {string} fileId - 文件ID
   * @returns {Promise<Object>} 文件信息
   */
  async getFileInfo(fileId) {
    const url = `${this.baseURL}/v1/files/${fileId}`;
    const headers = this.getAuthHeaders();
    
    try {
      const response = await httpClient.get(url, { headers });
      return response;
    } catch (error) {
      throw new Error(`Failed to get file info: ${error.message}`);
    }
  }
  
  /**
   * 删除文件
   * @param {string} fileId - 文件ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteFile(fileId) {
    const url = `${this.baseURL}/v1/files/${fileId}`;
    const headers = this.getAuthHeaders();
    
    try {
      const response = await httpClient.delete(url, { headers });
      return response;
    } catch (error) {
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }
  
  /**
   * 获取模型列表
   * @returns {Promise<Array>} 模型列表
   */
  async getModels() {
    const url = `${this.baseURL}/v1/models`;
    const headers = this.getAuthHeaders();
    
    try {
      const response = await httpClient.get(url, { headers });
      return response.data || [];
    } catch (error) {
      throw new Error(`Failed to get models: ${error.message}`);
    }
  }
  
  /**
   * 获取使用统计
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 使用统计
   */
  async getUsageStats(params = {}) {
    const url = `${this.baseURL}/v1/usage`;
    const headers = this.getAuthHeaders();
    
    try {
      const response = await httpClient.get(url, { 
        headers,
        params
      });
      return response;
    } catch (error) {
      throw new Error(`Failed to get usage stats: ${error.message}`);
    }
  }
}

// 创建默认API服务实例
export const apiService = new ApiService();

/**
 * 创建带有特定配置的API服务
 * @param {Object} options - 配置选项
 * @returns {ApiService} API服务实例
 */
export function createApiService(options) {
  return new ApiService(options);
}
