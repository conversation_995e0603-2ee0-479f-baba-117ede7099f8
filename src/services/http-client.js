/**
 * HTTP客户端服务
 * @original: 原始文件中的HTTP请求相关函数
 */

import axios from 'axios';
import { API_CONFIG } from '../config/index.js';

/**
 * HTTP客户端类
 * @original: 原始文件中的axios配置和HTTP请求逻辑
 */
export class HttpClient {
  constructor(options = {}) {
    this.baseURL = options.baseURL || API_CONFIG.BASE_API_URL;
    this.timeout = options.timeout || 30000;
    this.retryAttempts = options.retryAttempts || 3;
    this.retryDelay = options.retryDelay || 1000;
    
    // 创建axios实例
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Claude-Code/1.0.72'
      }
    });
    
    // 设置请求拦截器
    this.setupRequestInterceptors();
    
    // 设置响应拦截器
    this.setupResponseInterceptors();
  }
  
  /**
   * 设置请求拦截器
   * @private
   */
  setupRequestInterceptors() {
    this.client.interceptors.request.use(
      (config) => {
        // 添加时间戳
        config.metadata = { startTime: Date.now() };
        
        // 记录请求日志
        console.debug(`HTTP Request: ${config.method?.toUpperCase()} ${config.url}`);
        
        return config;
      },
      (error) => {
        console.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );
  }
  
  /**
   * 设置响应拦截器
   * @private
   */
  setupResponseInterceptors() {
    this.client.interceptors.response.use(
      (response) => {
        // 计算请求耗时
        const duration = Date.now() - response.config.metadata.startTime;
        console.debug(`HTTP Response: ${response.status} (${duration}ms)`);
        
        return response;
      },
      async (error) => {
        const config = error.config;
        
        // 如果没有配置或已经重试过，直接抛出错误
        if (!config || config.__retryCount >= this.retryAttempts) {
          return Promise.reject(error);
        }
        
        // 初始化重试计数
        config.__retryCount = config.__retryCount || 0;
        config.__retryCount++;
        
        // 检查是否应该重试
        if (this.shouldRetry(error)) {
          console.warn(`Retrying request (${config.__retryCount}/${this.retryAttempts}): ${config.url}`);
          
          // 等待重试延迟
          await this.delay(this.retryDelay * config.__retryCount);
          
          // 重新发送请求
          return this.client(config);
        }
        
        return Promise.reject(error);
      }
    );
  }
  
  /**
   * 判断是否应该重试
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否应该重试
   * @private
   */
  shouldRetry(error) {
    // 网络错误或超时错误应该重试
    if (error.code === 'ECONNABORTED' || error.code === 'ENOTFOUND' || error.code === 'ECONNRESET') {
      return true;
    }
    
    // 5xx服务器错误应该重试
    if (error.response && error.response.status >= 500) {
      return true;
    }
    
    // 429 Too Many Requests应该重试
    if (error.response && error.response.status === 429) {
      return true;
    }
    
    return false;
  }
  
  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   * @private
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * GET请求
   * @param {string} url - 请求URL
   * @param {Object} config - 请求配置
   * @returns {Promise} 响应数据
   */
  async get(url, config = {}) {
    try {
      const response = await this.client.get(url, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * POST请求
   * @param {string} url - 请求URL
   * @param {*} data - 请求数据
   * @param {Object} config - 请求配置
   * @returns {Promise} 响应数据
   */
  async post(url, data, config = {}) {
    try {
      const response = await this.client.post(url, data, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * PUT请求
   * @param {string} url - 请求URL
   * @param {*} data - 请求数据
   * @param {Object} config - 请求配置
   * @returns {Promise} 响应数据
   */
  async put(url, data, config = {}) {
    try {
      const response = await this.client.put(url, data, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * PATCH请求
   * @param {string} url - 请求URL
   * @param {*} data - 请求数据
   * @param {Object} config - 请求配置
   * @returns {Promise} 响应数据
   */
  async patch(url, data, config = {}) {
    try {
      const response = await this.client.patch(url, data, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * DELETE请求
   * @param {string} url - 请求URL
   * @param {Object} config - 请求配置
   * @returns {Promise} 响应数据
   */
  async delete(url, config = {}) {
    try {
      const response = await this.client.delete(url, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * 设置认证头
   * @param {string} token - 认证令牌
   */
  setAuthToken(token) {
    if (token) {
      this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete this.client.defaults.headers.common['Authorization'];
    }
  }
  
  /**
   * 设置默认头部
   * @param {Object} headers - 头部对象
   */
  setDefaultHeaders(headers) {
    Object.assign(this.client.defaults.headers.common, headers);
  }
  
  /**
   * 处理错误
   * @param {Error} error - 原始错误
   * @returns {Error} 处理后的错误
   * @private
   */
  handleError(error) {
    if (error.response) {
      // 服务器响应了错误状态码
      const { status, data, statusText } = error.response;
      const message = data?.message || data?.error || statusText || 'Request failed';
      
      const customError = new Error(message);
      customError.status = status;
      customError.data = data;
      customError.originalError = error;
      
      return customError;
    } else if (error.request) {
      // 请求已发出但没有收到响应
      const customError = new Error('Network error: No response received');
      customError.code = 'NETWORK_ERROR';
      customError.originalError = error;
      
      return customError;
    } else {
      // 请求配置出错
      const customError = new Error(`Request configuration error: ${error.message}`);
      customError.code = 'CONFIG_ERROR';
      customError.originalError = error;
      
      return customError;
    }
  }
  
  /**
   * 上传文件
   * @param {string} url - 上传URL
   * @param {File|Buffer} file - 文件对象
   * @param {Object} options - 选项
   * @returns {Promise} 响应数据
   */
  async uploadFile(url, file, options = {}) {
    const formData = new FormData();
    formData.append('file', file);
    
    // 添加其他字段
    if (options.fields) {
      Object.entries(options.fields).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }
    
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...options.config
    };
    
    // 上传进度回调
    if (options.onUploadProgress) {
      config.onUploadProgress = options.onUploadProgress;
    }
    
    return this.post(url, formData, config);
  }
  
  /**
   * 下载文件
   * @param {string} url - 下载URL
   * @param {Object} options - 选项
   * @returns {Promise} 响应数据
   */
  async downloadFile(url, options = {}) {
    const config = {
      responseType: 'stream',
      ...options.config
    };
    
    // 下载进度回调
    if (options.onDownloadProgress) {
      config.onDownloadProgress = options.onDownloadProgress;
    }
    
    try {
      const response = await this.client.get(url, config);
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * 取消请求
   * @returns {Object} 取消令牌
   */
  createCancelToken() {
    return axios.CancelToken.source();
  }
  
  /**
   * 检查请求是否被取消
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否被取消
   */
  isCancel(error) {
    return axios.isCancel(error);
  }
}

// 创建默认HTTP客户端实例
export const httpClient = new HttpClient();

/**
 * 创建带有特定配置的HTTP客户端
 * @param {Object} options - 配置选项
 * @returns {HttpClient} HTTP客户端实例
 */
export function createHttpClient(options) {
  return new HttpClient(options);
}

/**
 * 简单的GET请求函数
 * @param {string} url - 请求URL
 * @param {Object} config - 请求配置
 * @returns {Promise} 响应数据
 * @original: 原始文件L53391-53405中的VdB函数
 */
export async function get(url, config = {}) {
  return httpClient.get(url, config);
}

/**
 * 简单的POST请求函数
 * @param {string} url - 请求URL
 * @param {*} data - 请求数据
 * @param {Object} config - 请求配置
 * @returns {Promise} 响应数据
 */
export async function post(url, data, config = {}) {
  return httpClient.post(url, data, config);
}

/**
 * 简单的PUT请求函数
 * @param {string} url - 请求URL
 * @param {*} data - 请求数据
 * @param {Object} config - 请求配置
 * @returns {Promise} 响应数据
 */
export async function put(url, data, config = {}) {
  return httpClient.put(url, data, config);
}

/**
 * 简单的DELETE请求函数
 * @param {string} url - 请求URL
 * @param {Object} config - 请求配置
 * @returns {Promise} 响应数据
 */
export async function del(url, config = {}) {
  return httpClient.delete(url, config);
}
