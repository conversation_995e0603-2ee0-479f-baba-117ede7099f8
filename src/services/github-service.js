/**
 * GitHub集成服务模块
 * @original: 原始文件中的GitHub Actions和OAuth相关函数
 */

import { executeCommand, executeCommandAsync } from '../utils/process-utils.js';
import { fileExists, readFileContent, writeFileContent } from '../utils/file-utils.js';
import { normalizePath, joinPath } from '../utils/path-utils.js';
import { openBrowser } from '../utils/browser-utils.js';

/**
 * GitHub Actions工作流类型
 * @original: 原始文件L40145中的selectedWorkflows配置
 */
export const GITHUB_WORKFLOWS = {
  CLAUDE: 'claude',
  CLAUDE_REVIEW: 'claude-review'
};

/**
 * 认证类型定义
 * @original: 原始文件L40147中的authType配置
 */
export const AUTH_TYPES = {
  API_KEY: 'api_key',
  OAUTH_TOKEN: 'oauth_token'
};

/**
 * GitHub服务类
 * @original: 原始文件L40149-40563中的E$8函数和相关逻辑
 */
export class GitHubService {
  constructor() {
    this.defaultState = {
      step: 'check-gh',
      selectedRepoName: '',
      currentRepo: '',
      useCurrentRepo: true,
      apiKeyOrOAuthToken: '',
      useExistingKey: true,
      currentWorkflowInstallStep: 0,
      warnings: [],
      secretExists: false,
      secretName: 'ANTHROPIC_API_KEY',
      useExistingSecret: true,
      workflowExists: false,
      selectedWorkflows: [GITHUB_WORKFLOWS.CLAUDE, GITHUB_WORKFLOWS.CLAUDE_REVIEW],
      selectedApiKeyOption: 'new',
      authType: AUTH_TYPES.API_KEY
    };
  }
  
  /**
   * 检查GitHub CLI状态
   * @returns {Promise<Object>} 检查结果
   * @original: 原始文件L40159-40199中的Z函数
   */
  async checkGitHubCLI() {
    const warnings = [];
    
    // 检查GitHub CLI是否安装
    try {
      executeCommand('gh --version', { stdio: 'ignore' });
    } catch {
      warnings.push({
        title: 'GitHub CLI not found',
        message: 'GitHub CLI (gh) does not appear to be installed or accessible.',
        instructions: [
          'Install GitHub CLI from https://cli.github.com/',
          'macOS: brew install gh',
          'Windows: winget install --id GitHub.cli',
          'Linux: See installation instructions at https://github.com/cli/cli#installation'
        ]
      });
    }
    
    // 检查GitHub CLI认证状态
    try {
      executeCommand('gh auth status', { stdio: 'ignore' });
    } catch {
      warnings.push({
        title: 'GitHub CLI not authenticated',
        message: 'GitHub CLI does not appear to be authenticated.',
        instructions: [
          'Run: gh auth login',
          'Follow the prompts to authenticate with GitHub',
          'Or set up authentication using environment variables or other methods'
        ]
      });
    }
    
    // 检查当前Git仓库
    let currentRepo = '';
    try {
      executeCommand('git rev-parse --is-inside-work-tree', { stdio: 'ignore' });
      const remoteUrl = executeCommand('git remote get-url origin', { encoding: 'utf8' }).trim();
      const match = remoteUrl.match(/github\.com[:/]([^/]+\/[^/]+)(\.git)?$/);
      if (match) {
        currentRepo = match[1]?.replace(/\.git$/, '') || '';
      }
    } catch {
      // 不在Git仓库中或没有origin远程
    }
    
    return {
      warnings,
      currentRepo,
      hasWarnings: warnings.length > 0
    };
  }
  
  /**
   * 检查仓库访问权限
   * @param {string} repoName - 仓库名称 (owner/repo格式)
   * @returns {Promise<Object>} 权限检查结果
   * @original: 原始文件L40248-40265中的I函数
   */
  async checkRepositoryAccess(repoName) {
    try {
      const result = await executeCommandAsync('gh', [
        'api', `repos/${repoName}`, '--jq', '.permissions.admin'
      ]);
      
      if (result.code === 0) {
        return {
          hasAccess: result.stdout.trim() === 'true'
        };
      }
      
      if (result.stderr.includes('404') || result.stderr.includes('Not Found')) {
        return {
          hasAccess: false,
          error: 'repository_not_found'
        };
      }
      
      return {
        hasAccess: false
      };
    } catch {
      return {
        hasAccess: false
      };
    }
  }
  
  /**
   * 检查工作流文件是否存在
   * @param {string} repoName - 仓库名称
   * @returns {Promise<boolean>} 工作流是否存在
   * @original: 原始文件L40267-40269中的Y函数
   */
  async checkWorkflowExists(repoName) {
    try {
      const result = await executeCommandAsync('gh', [
        'api', `repos/${repoName}/contents/.github/workflows/claude.yml`, '--jq', '.sha'
      ]);
      return result.code === 0;
    } catch {
      return false;
    }
  }
  
  /**
   * 检查GitHub Actions密钥是否存在
   * @param {string} repoName - 仓库名称
   * @param {string} secretName - 密钥名称
   * @returns {Promise<boolean>} 密钥是否存在
   * @original: 原始文件L40270-40296中的W函数
   */
  async checkSecretExists(repoName, secretName = 'ANTHROPIC_API_KEY') {
    try {
      const result = await executeCommandAsync('gh', [
        'secret', 'list', '--app', 'actions', '--repo', repoName
      ]);
      
      if (result.code === 0) {
        const secretPattern = new RegExp(`^${secretName}\\s+`);
        return result.stdout.split('\n').some(line => secretPattern.test(line));
      }
      
      return false;
    } catch {
      return false;
    }
  }
  
  /**
   * 验证仓库名称格式
   * @param {string} repoName - 仓库名称
   * @returns {Array} 验证警告数组
   * @original: 原始文件L40303-40347中的仓库验证逻辑
   */
  validateRepositoryName(repoName) {
    const warnings = [];
    let normalizedName = repoName;
    
    // 处理GitHub URL格式
    if (repoName.includes('github.com')) {
      const match = repoName.match(/github\.com[:/]([^/]+\/[^/]+)(\.git)?$/);
      if (!match) {
        warnings.push({
          title: 'Invalid GitHub URL format',
          message: 'The repository URL format appears to be invalid.',
          instructions: [
            'Use format: owner/repo or https://github.com/owner/repo',
            'Example: anthropics/claude-cli'
          ]
        });
      } else {
        normalizedName = match[1]?.replace(/\.git$/, '') || '';
      }
    }
    
    // 检查owner/repo格式
    if (!normalizedName.includes('/')) {
      warnings.push({
        title: 'Repository format warning',
        message: 'Repository should be in format "owner/repo"',
        instructions: [
          'Use format: owner/repo',
          'Example: anthropics/claude-cli'
        ]
      });
    }
    
    return {
      warnings,
      normalizedName
    };
  }
  
  /**
   * 设置GitHub Actions工作流
   * @param {string} repoName - 仓库名称
   * @param {string} apiKey - API密钥
   * @param {string} secretName - 密钥名称
   * @param {Function} progressCallback - 进度回调
   * @param {boolean} skipWorkflow - 是否跳过工作流创建
   * @param {Array} selectedWorkflows - 选择的工作流
   * @param {string} authType - 认证类型
   * @param {Object} options - 其他选项
   * @returns {Promise<void>}
   * @original: 原始文件L40204-40244中的G函数调用的QgB函数
   */
  async setupGitHubActions(repoName, apiKey, secretName, progressCallback, skipWorkflow = false, selectedWorkflows = [], authType = AUTH_TYPES.API_KEY, options = {}) {
    const {
      useCurrentRepo = true,
      workflowExists = false,
      secretExists = false
    } = options;
    
    try {
      // 步骤1: 设置密钥
      if (progressCallback) progressCallback();
      
      if (apiKey && !secretExists) {
        await this.setRepositorySecret(repoName, secretName, apiKey);
      }
      
      // 步骤2: 创建工作流文件
      if (progressCallback) progressCallback();
      
      if (!skipWorkflow && !workflowExists) {
        await this.createWorkflowFiles(repoName, selectedWorkflows, authType);
      }
      
      // 步骤3: 完成设置
      if (progressCallback) progressCallback();
      
    } catch (error) {
      if (error.message.includes('workflow file already exists')) {
        throw new Error('A Claude workflow file already exists in this repository.');
      }
      throw error;
    }
  }
  
  /**
   * 设置仓库密钥
   * @param {string} repoName - 仓库名称
   * @param {string} secretName - 密钥名称
   * @param {string} secretValue - 密钥值
   * @returns {Promise<void>}
   * @private
   */
  async setRepositorySecret(repoName, secretName, secretValue) {
    await executeCommandAsync('gh', [
      'secret', 'set', secretName,
      '--app', 'actions',
      '--repo', repoName,
      '--body', secretValue
    ]);
  }
  
  /**
   * 创建工作流文件
   * @param {string} repoName - 仓库名称
   * @param {Array} workflows - 工作流列表
   * @param {string} authType - 认证类型
   * @returns {Promise<void>}
   * @private
   */
  async createWorkflowFiles(repoName, workflows, authType) {
    for (const workflow of workflows) {
      const workflowContent = this.generateWorkflowContent(workflow, authType);
      const workflowPath = `.github/workflows/${workflow}.yml`;
      
      // 检查本地是否存在工作流文件
      if (fileExists(workflowPath)) {
        throw new Error(`workflow file already exists: ${workflowPath}`);
      }
      
      // 创建工作流文件
      await this.createWorkflowFile(repoName, workflowPath, workflowContent);
    }
  }
  
  /**
   * 生成工作流内容
   * @param {string} workflowType - 工作流类型
   * @param {string} authType - 认证类型
   * @returns {string} 工作流YAML内容
   * @private
   */
  generateWorkflowContent(workflowType, authType) {
    const secretName = authType === AUTH_TYPES.OAUTH_TOKEN ? 'CLAUDE_CODE_OAUTH_TOKEN' : 'ANTHROPIC_API_KEY';
    
    const baseWorkflow = `name: Claude Code ${workflowType}

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  claude-code:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Run Claude Code
      uses: anthropics/claude-code-action@v1
      with:
        api-key: \${{ secrets.${secretName} }}
        workflow-type: ${workflowType}
`;
    
    return baseWorkflow;
  }
  
  /**
   * 创建工作流文件到仓库
   * @param {string} repoName - 仓库名称
   * @param {string} filePath - 文件路径
   * @param {string} content - 文件内容
   * @returns {Promise<void>}
   * @private
   */
  async createWorkflowFile(repoName, filePath, content) {
    // 使用GitHub CLI创建文件
    const encodedContent = Buffer.from(content).toString('base64');
    
    await executeCommandAsync('gh', [
      'api',
      `repos/${repoName}/contents/${filePath}`,
      '--method', 'PUT',
      '--field', `message=Add ${filePath} workflow`,
      '--field', `content=${encodedContent}`
    ]);
  }
  
  /**
   * 打开GitHub App安装页面
   * @returns {Promise<void>}
   * @original: 原始文件L40245-40247中的F函数
   */
  async openGitHubAppInstallation() {
    await openBrowser('https://github.com/apps/claude');
  }
  
  /**
   * 获取现有API密钥
   * @returns {string|null} 现有的API密钥
   * @original: 原始文件L40150中的RY函数调用
   */
  getExistingApiKey() {
    // 从多个来源读取现有API密钥
    const sources = [
      process.env.ANTHROPIC_API_KEY,
      process.env.CLAUDE_API_KEY,
      process.env.GITHUB_TOKEN,
      process.env.GH_TOKEN
    ];

    // 返回第一个非空的API密钥
    for (const key of sources) {
      if (key && key.trim()) {
        return key.trim();
      }
    }

    // 尝试从配置文件读取
    try {
      const fs = require('fs');
      const path = require('path');
      const os = require('os');

      const configPath = path.join(os.homedir(), '.claude', 'config.json');
      if (fs.existsSync(configPath)) {
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        return config.apiKey || config.anthropicApiKey || null;
      }
    } catch (error) {
      // 忽略配置文件读取错误
    }

    return null;
  }
  
  /**
   * 检查是否支持OAuth
   * @returns {boolean} 是否支持OAuth
   * @original: 原始文件L40154中的xz函数调用
   */
  supportsOAuth() {
    // 检查OAuth支持
    try {
      // 检查是否在支持OAuth的环境中
      const hasWindow = typeof window !== 'undefined';
      const hasProcess = typeof process !== 'undefined';

      // 浏览器环境支持OAuth
      if (hasWindow && window.location) {
        return true;
      }

      // Node.js环境需要检查是否有必要的模块
      if (hasProcess) {
        try {
          require('http');
          require('url');
          return true;
        } catch (error) {
          return false;
        }
      }

      return false;
    } catch (error) {
      console.warn('OAuth support check failed:', error);
      return false;
    }
  }
  
  /**
   * 生成仓库访问警告
   * @param {string} repoName - 仓库名称
   * @param {Object} accessResult - 访问检查结果
   * @returns {Array} 警告数组
   * @private
   * @original: 原始文件L40320-40329中的权限警告逻辑
   */
  generateAccessWarnings(repoName, accessResult) {
    const warnings = [];
    
    if (accessResult.error === 'repository_not_found') {
      warnings.push({
        title: 'Repository not found',
        message: `Repository ${repoName} was not found or you don't have access.`,
        instructions: [
          `Check that the repository name is correct: ${repoName}`,
          'Ensure you have access to this repository',
          'For private repositories, make sure your GitHub token has the "repo" scope',
          'You can add the repo scope with: gh auth refresh -h github.com -s repo,workflow'
        ]
      });
    } else if (!accessResult.hasAccess) {
      warnings.push({
        title: 'Admin permissions required',
        message: `You might need admin permissions on ${repoName} to set up GitHub Actions.`,
        instructions: [
          'Repository admins can install GitHub Apps and set secrets',
          'Ask a repository admin to run this command if setup fails',
          'Alternatively, you can use the manual setup instructions'
        ]
      });
    }
    
    return warnings;
  }
  
  /**
   * 验证API密钥格式
   * @param {string} apiKey - API密钥
   * @returns {boolean} 是否有效
   */
  validateApiKey(apiKey) {
    if (!apiKey || typeof apiKey !== 'string') {
      return false;
    }
    
    // 基本格式检查
    return apiKey.length > 10 && apiKey.startsWith('sk-');
  }
  
  /**
   * 清理仓库名称
   * @param {string} repoName - 原始仓库名称
   * @returns {string} 清理后的仓库名称
   */
  cleanRepositoryName(repoName) {
    return repoName.trim().replace(/\.git$/, '');
  }
  
  /**
   * 获取仓库信息
   * @param {string} repoName - 仓库名称
   * @returns {Promise<Object>} 仓库信息
   */
  async getRepositoryInfo(repoName) {
    try {
      const result = await executeCommandAsync('gh', [
        'api', `repos/${repoName}`
      ]);
      
      if (result.code === 0) {
        return JSON.parse(result.stdout);
      }
      
      throw new Error(`Failed to get repository info: ${result.stderr}`);
    } catch (error) {
      throw new Error(`Failed to get repository info: ${error.message}`);
    }
  }
  
  /**
   * 列出仓库的GitHub Actions密钥
   * @param {string} repoName - 仓库名称
   * @returns {Promise<Array>} 密钥列表
   */
  async listRepositorySecrets(repoName) {
    try {
      const result = await executeCommandAsync('gh', [
        'secret', 'list', '--app', 'actions', '--repo', repoName
      ]);
      
      if (result.code === 0) {
        return result.stdout.split('\n')
          .filter(line => line.trim())
          .map(line => {
            const parts = line.split(/\s+/);
            return {
              name: parts[0],
              updatedAt: parts.slice(1).join(' ')
            };
          });
      }
      
      return [];
    } catch {
      return [];
    }
  }
  
  /**
   * 删除仓库密钥
   * @param {string} repoName - 仓库名称
   * @param {string} secretName - 密钥名称
   * @returns {Promise<void>}
   */
  async deleteRepositorySecret(repoName, secretName) {
    await executeCommandAsync('gh', [
      'secret', 'delete', secretName,
      '--app', 'actions',
      '--repo', repoName
    ]);
  }
  
  /**
   * 检查GitHub CLI版本
   * @returns {Promise<string>} GitHub CLI版本
   */
  async getGitHubCLIVersion() {
    try {
      const result = executeCommand('gh --version', { encoding: 'utf8' });
      const match = result.match(/gh version (\d+\.\d+\.\d+)/);
      return match ? match[1] : 'unknown';
    } catch {
      throw new Error('GitHub CLI not found');
    }
  }
  
  /**
   * 获取当前认证用户信息
   * @returns {Promise<Object>} 用户信息
   */
  async getCurrentUser() {
    try {
      const result = await executeCommandAsync('gh', ['api', 'user']);
      if (result.code === 0) {
        return JSON.parse(result.stdout);
      }
      throw new Error('Not authenticated');
    } catch (error) {
      throw new Error(`Failed to get current user: ${error.message}`);
    }
  }
}

// 创建默认GitHub服务实例
export const gitHubService = new GitHubService();

/**
 * 检查GitHub CLI状态的简单函数
 * @returns {Promise<Object>} 检查结果
 */
export async function checkGitHubCLI() {
  return gitHubService.checkGitHubCLI();
}

/**
 * 设置GitHub Actions的简单函数
 * @param {string} repoName - 仓库名称
 * @param {string} apiKey - API密钥
 * @param {Object} options - 选项
 * @returns {Promise<void>}
 */
export async function setupGitHubActions(repoName, apiKey, options = {}) {
  const {
    secretName = 'ANTHROPIC_API_KEY',
    workflows = [GITHUB_WORKFLOWS.CLAUDE],
    authType = AUTH_TYPES.API_KEY,
    progressCallback = null
  } = options;
  
  return gitHubService.setupGitHubActions(
    repoName,
    apiKey,
    secretName,
    progressCallback,
    false,
    workflows,
    authType,
    options
  );
}
