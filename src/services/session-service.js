/**
 * 会话服务模块
 * @original: 原始文件中的会话管理相关函数
 */

import { generateSessionId } from '../utils/crypto-utils.js';
import { getCurrentTimestamp } from '../utils/date-utils.js';

/**
 * 会话状态管理
 * @original: 原始文件L2002-2176中的会话状态管理逻辑
 */
class SessionState {
  constructor() {
    this.sessionId = null;
    this.startTime = getCurrentTimestamp();
    this.lastInteractionTime = getCurrentTimestamp();
    this.cwd = process.cwd();
    
    // 成本和使用统计
    this.totalCostUSD = 0;
    this.totalAPIDuration = 0;
    this.totalAPIDurationWithoutRetries = 0;
    this.totalLinesAdded = 0;
    this.totalLinesRemoved = 0;
    this.hasUnknownModelCost = false;
    
    // 模型使用统计
    this.modelUsage = {};
    this.initialMainLoopModel = null;
    this.mainLoopModelOverride = null;
    this.maxRateLimitFallbackActive = false;
    
    // 遥测相关
    this.meter = null;
    this.sessionCounter = null;
    this.locCounter = null;
    this.prCounter = null;
    this.commitCounter = null;
    this.costCounter = null;
    this.tokenCounter = null;
    this.codeEditToolDecisionCounter = null;
    this.activeTimeCounter = null;
    this.loggerProvider = null;
    this.eventLogger = null;
    
    // 会话配置
    this.isNonInteractiveSession = false;
    this.isInteractive = true;
    this.clientType = 'cli';
    this.flagSettingsPath = null;
    
    // 代理颜色管理
    this.agentColorMap = new Map();
    this.agentColorIndex = 0;
    
    // 后台Shell管理
    this.backgroundShells = new Map();
    this.backgroundShellSubscribers = new Set();
    this.backgroundShellCounter = 0;
  }
  
  /**
   * 重置会话状态
   */
  reset() {
    this.sessionId = null;
    this.startTime = getCurrentTimestamp();
    this.lastInteractionTime = getCurrentTimestamp();
    this.totalCostUSD = 0;
    this.totalAPIDuration = 0;
    this.totalAPIDurationWithoutRetries = 0;
    this.totalLinesAdded = 0;
    this.totalLinesRemoved = 0;
    this.hasUnknownModelCost = false;
    this.modelUsage = {};
    this.agentColorIndex = 0;
    this.backgroundShellCounter = 0;
  }
}

// 全局会话状态实例
const sessionState = new SessionState();

/**
 * 会话服务类
 */
export class SessionService {
  constructor() {
    this.state = sessionState;
  }
  
  /**
   * 生成新的会话ID
   * @returns {string} 会话ID
   * @original: 原始文件L2002-2004中的Mj0函数
   */
  generateSessionId() {
    const sessionId = generateSessionId();
    this.state.sessionId = sessionId;
    return sessionId;
  }
  
  /**
   * 设置会话ID
   * @param {string} sessionId - 会话ID
   * @original: 原始文件L2005-2007中的Rj0函数
   */
  setSessionId(sessionId) {
    this.state.sessionId = sessionId;
  }
  
  /**
   * 获取当前会话ID
   * @returns {string} 会话ID
   */
  getSessionId() {
    return this.state.sessionId;
  }
  
  /**
   * 设置当前工作目录
   * @param {string} cwd - 工作目录路径
   * @original: 原始文件L2008-2010中的Tj0函数
   */
  setCwd(cwd) {
    this.state.cwd = cwd;
  }
  
  /**
   * 获取当前工作目录
   * @returns {string} 工作目录路径
   */
  getCwd() {
    return this.state.cwd;
  }
  
  /**
   * 记录API使用情况
   * @param {number} costUSD - 成本（美元）
   * @param {number} duration - 持续时间
   * @param {number} durationWithoutRetries - 不含重试的持续时间
   * @param {number} inputTokens - 输入令牌数
   * @param {number} outputTokens - 输出令牌数
   * @param {string} modelName - 模型名称
   * @original: 原始文件L2011-2021中的Pj0函数
   */
  async recordApiUsage(costUSD, duration, durationWithoutRetries, inputTokens, outputTokens, modelName) {
    this.state.totalCostUSD += costUSD;
    this.state.totalAPIDuration += duration;
    this.state.totalAPIDurationWithoutRetries += durationWithoutRetries;
    
    let modelUsage = this.state.modelUsage[modelName] ?? {
      inputTokens: 0,
      outputTokens: 0,
      cacheReadInputTokens: 0,
      cacheCreationInputTokens: 0,
      webSearchRequests: 0
    };
    
    modelUsage.inputTokens += inputTokens;
    modelUsage.outputTokens += outputTokens;
    this.state.modelUsage[modelName] = modelUsage;
  }
  
  /**
   * 获取总成本
   * @returns {number} 总成本（美元）
   * @original: 原始文件L2022-2024中的aq函数
   */
  getTotalCost() {
    return this.state.totalCostUSD;
  }
  
  /**
   * 获取总API持续时间
   * @returns {number} 总持续时间
   * @original: 原始文件L2025-2027中的fj函数
   */
  getTotalApiDuration() {
    return this.state.totalAPIDuration;
  }
  
  /**
   * 获取会话运行时间
   * @returns {number} 运行时间（毫秒）
   * @original: 原始文件L2028-2030中的uu1函数
   */
  getSessionUptime() {
    return getCurrentTimestamp() - this.state.startTime;
  }
  
  /**
   * 更新最后交互时间
   * @original: 原始文件L2031-2033中的nA1函数
   */
  updateLastInteractionTime() {
    this.state.lastInteractionTime = getCurrentTimestamp();
  }
  
  /**
   * 获取最后交互时间
   * @returns {number} 最后交互时间戳
   * @original: 原始文件L2064-2066中的VW1函数
   */
  getLastInteractionTime() {
    return this.state.lastInteractionTime;
  }
  
  /**
   * 记录代码行数变更
   * @param {number} linesAdded - 添加的行数
   * @param {number} linesRemoved - 删除的行数
   * @original: 原始文件L2034-2036中的mu1函数
   */
  recordLinesChanged(linesAdded, linesRemoved) {
    this.state.totalLinesAdded += linesAdded;
    this.state.totalLinesRemoved += linesRemoved;
  }
  
  /**
   * 获取总添加行数
   * @returns {number} 总添加行数
   * @original: 原始文件L2037-2039中的JW1函数
   */
  getTotalLinesAdded() {
    return this.state.totalLinesAdded;
  }
  
  /**
   * 获取总删除行数
   * @returns {number} 总删除行数
   * @original: 原始文件L2040-2042中的XW1函数
   */
  getTotalLinesRemoved() {
    return this.state.totalLinesRemoved;
  }
  
  /**
   * 获取总输入令牌数
   * @returns {number} 总输入令牌数
   * @original: 原始文件L2043-2045中的Sj0函数
   */
  getTotalInputTokens() {
    return this.sumModelUsage('inputTokens');
  }
  
  /**
   * 获取总输出令牌数
   * @returns {number} 总输出令牌数
   * @original: 原始文件L2046-2048中的jj0函数
   */
  getTotalOutputTokens() {
    return this.sumModelUsage('outputTokens');
  }
  
  /**
   * 获取缓存读取令牌数
   * @returns {number} 缓存读取令牌数
   * @original: 原始文件L2049-2051中的yj0函数
   */
  getCacheReadInputTokens() {
    return this.sumModelUsage('cacheReadInputTokens');
  }
  
  /**
   * 获取缓存创建令牌数
   * @returns {number} 缓存创建令牌数
   * @original: 原始文件L2052-2054中的kj0函数
   */
  getCacheCreationInputTokens() {
    return this.sumModelUsage('cacheCreationInputTokens');
  }
  
  /**
   * 获取网络搜索请求数
   * @returns {number} 网络搜索请求数
   * @original: 原始文件L2055-2057中的_j0函数
   */
  getWebSearchRequests() {
    return this.sumModelUsage('webSearchRequests');
  }
  
  /**
   * 汇总模型使用情况
   * @param {string} field - 字段名
   * @returns {number} 汇总值
   * @private
   */
  sumModelUsage(field) {
    return Object.values(this.state.modelUsage).reduce((sum, usage) => sum + (usage[field] || 0), 0);
  }
  
  /**
   * 标记未知模型成本
   * @original: 原始文件L2058-2060中的du1函数
   */
  markUnknownModelCost() {
    this.state.hasUnknownModelCost = true;
  }
  
  /**
   * 检查是否有未知模型成本
   * @returns {boolean} 是否有未知模型成本
   * @original: 原始文件L2061-2063中的xj0函数
   */
  hasUnknownModelCost() {
    return this.state.hasUnknownModelCost;
  }
  
  /**
   * 获取模型使用情况
   * @returns {Object} 模型使用情况
   * @original: 原始文件L2067-2069中的vj0函数
   */
  getModelUsage() {
    return this.state.modelUsage;
  }
  
  /**
   * 设置初始主循环模型
   * @param {string} model - 模型名称
   * @original: 原始文件L2079-2081中的fj0函数
   */
  setInitialMainLoopModel(model) {
    this.state.initialMainLoopModel = model;
  }
  
  /**
   * 获取初始主循环模型
   * @returns {string} 模型名称
   * @original: 原始文件L2070-2072中的CW1函数
   */
  getInitialMainLoopModel() {
    return this.state.initialMainLoopModel;
  }
  
  /**
   * 设置主循环模型覆盖
   * @param {string} model - 模型名称
   * @original: 原始文件L2073-2075中的sA1函数
   */
  setMainLoopModelOverride(model) {
    this.state.mainLoopModelOverride = model;
  }
  
  /**
   * 设置最大速率限制回退状态
   * @param {boolean} active - 是否激活
   * @original: 原始文件L2076-2078中的bj0函数
   */
  setMaxRateLimitFallbackActive(active) {
    this.state.maxRateLimitFallbackActive = active;
  }
  
  /**
   * 设置会话类型
   * @param {boolean} isNonInteractive - 是否为非交互式会话
   * @original: 原始文件L2140-2142中的nj0函数
   */
  setNonInteractiveSession(isNonInteractive) {
    this.state.isNonInteractiveSession = isNonInteractive;
  }
  
  /**
   * 设置交互模式
   * @param {boolean} isInteractive - 是否为交互模式
   * @original: 原始文件L2143-2145中的sj0函数
   */
  setInteractive(isInteractive) {
    this.state.isInteractive = isInteractive;
  }
  
  /**
   * 设置客户端类型
   * @param {string} clientType - 客户端类型
   * @original: 原始文件L2146-2148中的oj0函数
   */
  setClientType(clientType) {
    this.state.clientType = clientType;
  }
  
  /**
   * 获取代理颜色映射
   * @returns {Map} 代理颜色映射
   * @original: 原始文件L2149-2151中的iu1函数
   */
  getAgentColorMap() {
    return this.state.agentColorMap;
  }
  
  /**
   * 获取代理颜色索引
   * @returns {number} 代理颜色索引
   * @original: 原始文件L2152-2154中的tj0函数
   */
  getAgentColorIndex() {
    return this.state.agentColorIndex;
  }
  
  /**
   * 递增代理颜色索引
   * @original: 原始文件L2155-2157中的ej0函数
   */
  incrementAgentColorIndex() {
    this.state.agentColorIndex++;
  }
  
  /**
   * 设置标志设置路径
   * @param {string} path - 路径
   * @original: 原始文件L2158-2160中的Ay0函数
   */
  setFlagSettingsPath(path) {
    this.state.flagSettingsPath = path;
  }
  
  /**
   * 获取后台Shell映射
   * @returns {Map} 后台Shell映射
   * @original: 原始文件L2161-2163中的sq函数
   */
  getBackgroundShells() {
    return this.state.backgroundShells;
  }
  
  /**
   * 添加后台Shell
   * @param {string} id - Shell ID
   * @param {Object} shell - Shell对象
   * @original: 原始文件L2164-2166中的By0函数
   */
  addBackgroundShell(id, shell) {
    this.state.backgroundShells.set(id, shell);
  }
  
  /**
   * 删除后台Shell
   * @param {string} id - Shell ID
   * @returns {boolean} 是否删除成功
   * @original: 原始文件L2167-2169中的Qy0函数
   */
  removeBackgroundShell(id) {
    return this.state.backgroundShells.delete(id);
  }
  
  /**
   * 获取后台Shell订阅者
   * @returns {Set} 订阅者集合
   * @original: 原始文件L2170-2172中的rc函数
   */
  getBackgroundShellSubscribers() {
    return this.state.backgroundShellSubscribers;
  }
  
  /**
   * 生成新的后台Shell计数器
   * @returns {number} 新的计数器值
   * @original: 原始文件L2173-2175中的Dy0函数
   */
  generateBackgroundShellCounter() {
    return ++this.state.backgroundShellCounter;
  }
  
  /**
   * 获取会话摘要
   * @returns {Object} 会话摘要
   */
  getSessionSummary() {
    return {
      sessionId: this.state.sessionId,
      startTime: this.state.startTime,
      uptime: this.getSessionUptime(),
      lastInteractionTime: this.state.lastInteractionTime,
      totalCost: this.state.totalCostUSD,
      totalApiDuration: this.state.totalAPIDuration,
      totalLinesAdded: this.state.totalLinesAdded,
      totalLinesRemoved: this.state.totalLinesRemoved,
      modelUsage: this.state.modelUsage,
      isInteractive: this.state.isInteractive,
      clientType: this.state.clientType
    };
  }
  
  /**
   * 导出会话状态
   * @returns {Object} 会话状态
   */
  exportState() {
    return {
      sessionId: this.state.sessionId,
      startTime: this.state.startTime,
      lastInteractionTime: this.state.lastInteractionTime,
      cwd: this.state.cwd,
      totalCostUSD: this.state.totalCostUSD,
      totalAPIDuration: this.state.totalAPIDuration,
      totalLinesAdded: this.state.totalLinesAdded,
      totalLinesRemoved: this.state.totalLinesRemoved,
      modelUsage: this.state.modelUsage,
      isInteractive: this.state.isInteractive,
      clientType: this.state.clientType
    };
  }
  
  /**
   * 导入会话状态
   * @param {Object} stateData - 状态数据
   */
  importState(stateData) {
    Object.assign(this.state, stateData);
  }
}

// 创建默认会话服务实例
export const sessionService = new SessionService();

/**
 * 获取会话服务实例
 * @returns {SessionService} 会话服务实例
 */
export function getSessionService() {
  return sessionService;
}
