/**
 * MCP (Model Context Protocol) 服务管理模块
 * @original: 原始文件中的MCP服务器连接和管理相关函数
 */

import { executeCommand, executeCommandAsync } from '../utils/process-utils.js';
import { fileExists, readFileContent, writeFileContent } from '../utils/file-utils.js';
import { normalizePath, joinPath } from '../utils/path-utils.js';
import { generateRandomString } from '../utils/string-utils.js';

/**
 * MCP客户端状态定义
 * @original: 原始文件L40785-41456中的MCP状态管理
 */
export const MCP_CLIENT_STATES = {
  PENDING: 'pending',
  CONNECTED: 'connected',
  NEEDS_AUTH: 'needs-auth',
  FAILED: 'failed'
};

/**
 * MCP传输类型定义
 */
export const MCP_TRANSPORT_TYPES = {
  STDIO: 'stdio',
  SSE: 'sse',
  SSE_IDE: 'sse-ide',
  HTTP: 'http'
};

/**
 * MCP配置作用域
 */
export const MCP_CONFIG_SCOPES = {
  USER: 'user',
  PROJECT: 'project',
  LOCAL: 'local',
  DYNAMIC: 'dynamic'
};

/**
 * MCP服务管理类
 * @original: 原始文件L40785-41456中的MCP管理逻辑
 */
export class MCPService {
  constructor() {
    this.clients = new Map();
    this.tools = [];
    this.commands = [];
    this.resources = new Map();
    this.reconnectAttempts = new Map();
    this.maxReconnectAttempts = 3;
    this.reconnectDelay = 5000; // 5秒
  }
  
  /**
   * 初始化MCP连接
   * @param {Object} mcpConfig - MCP配置
   * @param {boolean} isStrictConfig - 是否为严格配置
   * @returns {Promise<void>}
   * @original: 原始文件L40957-40961中的YgB函数
   */
  async initializeConnections(mcpConfig = {}, isStrictConfig = false) {
    const config = isStrictConfig ? mcpConfig : { ...this.getDefaultConfig(), ...mcpConfig };
    
    // 清理现有连接
    await this.cleanup();
    
    // 初始化客户端状态
    for (const [name, serverConfig] of Object.entries(config)) {
      this.clients.set(name, {
        name,
        type: MCP_CLIENT_STATES.PENDING,
        config: serverConfig,
        client: null,
        isAuthenticated: false,
        scope: serverConfig.scope || MCP_CONFIG_SCOPES.DYNAMIC
      });
    }
    
    // 启动连接
    await this.connectAllServers();
  }
  
  /**
   * 连接所有MCP服务器
   * @returns {Promise<void>}
   * @private
   */
  async connectAllServers() {
    const connectionPromises = Array.from(this.clients.keys()).map(name => 
      this.connectServer(name).catch(error => {
        console.warn(`Failed to connect to MCP server ${name}:`, error.message);
        this.updateClientState(name, MCP_CLIENT_STATES.FAILED);
      })
    );
    
    await Promise.allSettled(connectionPromises);
  }
  
  /**
   * 连接单个MCP服务器
   * @param {string} serverName - 服务器名称
   * @returns {Promise<Object>} 连接结果
   * @original: 原始文件L40962-40968中的reconnectMcpServer函数
   */
  async connectServer(serverName) {
    const client = this.clients.get(serverName);
    if (!client) {
      throw new Error(`MCP server ${serverName} not found`);
    }
    
    try {
      this.updateClientState(serverName, MCP_CLIENT_STATES.PENDING);
      
      const connection = await this.createConnection(serverName, client.config);
      
      // 更新客户端状态
      this.clients.set(serverName, {
        ...client,
        type: connection.client.type,
        client: connection.client.type === MCP_CLIENT_STATES.CONNECTED ? connection.client : null,
        isAuthenticated: connection.isAuthenticated || false
      });
      
      // 注册工具和资源
      if (connection.tools) {
        this.registerTools(serverName, connection.tools);
      }
      
      if (connection.commands) {
        this.registerCommands(serverName, connection.commands);
      }
      
      if (connection.resources) {
        this.registerResources(serverName, connection.resources);
      }
      
      // 设置连接事件处理
      if (connection.client.type === MCP_CLIENT_STATES.CONNECTED) {
        this.setupConnectionHandlers(serverName, connection.client);
      }
      
      return connection;
      
    } catch (error) {
      this.updateClientState(serverName, MCP_CLIENT_STATES.FAILED);
      throw error;
    }
  }
  
  /**
   * 创建MCP连接
   * @param {string} serverName - 服务器名称
   * @param {Object} config - 服务器配置
   * @returns {Promise<Object>} 连接对象
   * @private
   * @original: 原始文件L40958中的Tq0函数调用
   */
  async createConnection(serverName, config) {
    switch (config.type) {
      case MCP_TRANSPORT_TYPES.STDIO:
        return this.createStdioConnection(serverName, config);
        
      case MCP_TRANSPORT_TYPES.SSE:
      case MCP_TRANSPORT_TYPES.SSE_IDE:
        return this.createSSEConnection(serverName, config);
        
      case MCP_TRANSPORT_TYPES.HTTP:
        return this.createHTTPConnection(serverName, config);
        
      default:
        throw new Error(`Unsupported transport type: ${config.type}`);
    }
  }
  
  /**
   * 创建STDIO连接
   * @param {string} serverName - 服务器名称
   * @param {Object} config - 配置
   * @returns {Promise<Object>} 连接对象
   * @private
   */
  async createStdioConnection(serverName, config) {
    const { spawn } = require('child_process');
    
    const process = spawn(config.command, config.args || [], {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: { ...process.env, ...config.env }
    });
    
    // 等待进程启动
    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, 10000);
      
      process.on('spawn', () => {
        clearTimeout(timeout);
        resolve();
      });
      
      process.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
    
    // 创建MCP客户端
    const client = {
      type: MCP_CLIENT_STATES.CONNECTED,
      process,
      send: (message) => {
        process.stdin.write(JSON.stringify(message) + '\n');
      },
      onmessage: null,
      onclose: null,
      onerror: null
    };
    
    // 设置消息处理
    process.stdout.on('data', (data) => {
      const lines = data.toString().split('\n').filter(line => line.trim());
      for (const line of lines) {
        try {
          const message = JSON.parse(line);
          if (client.onmessage) {
            client.onmessage(message);
          }
        } catch (error) {
          console.warn(`Failed to parse MCP message from ${serverName}:`, error);
        }
      }
    });
    
    process.on('close', (code) => {
      if (client.onclose) {
        client.onclose(code);
      }
    });
    
    process.on('error', (error) => {
      if (client.onerror) {
        client.onerror(error);
      }
    });
    
    // 执行握手
    const capabilities = await this.performHandshake(client, serverName);
    
    return {
      client,
      tools: capabilities.tools || [],
      commands: capabilities.commands || [],
      resources: capabilities.resources || [],
      isAuthenticated: false
    };
  }
  
  /**
   * 创建SSE连接
   * @param {string} serverName - 服务器名称
   * @param {Object} config - 配置
   * @returns {Promise<Object>} 连接对象
   * @private
   */
  async createSSEConnection(serverName, config) {
    const EventSource = require('eventsource');
    
    // 检查认证状态
    const authTokens = await this.getAuthTokens(serverName, config);
    const isAuthenticated = Boolean(authTokens);
    
    if (!isAuthenticated && config.requiresAuth) {
      return {
        client: {
          type: MCP_CLIENT_STATES.NEEDS_AUTH
        },
        isAuthenticated: false
      };
    }
    
    const headers = {};
    if (authTokens) {
      headers['Authorization'] = `Bearer ${authTokens.access_token}`;
    }
    
    const eventSource = new EventSource(config.url, { headers });
    
    const client = {
      type: MCP_CLIENT_STATES.CONNECTED,
      eventSource,
      send: async (message) => {
        // SSE通常是单向的，需要通过HTTP发送消息
        const response = await fetch(`${config.url}/send`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers
          },
          body: JSON.stringify(message)
        });
        
        if (!response.ok) {
          throw new Error(`Failed to send message: ${response.statusText}`);
        }
      },
      onmessage: null,
      onclose: null,
      onerror: null
    };
    
    eventSource.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        if (client.onmessage) {
          client.onmessage(message);
        }
      } catch (error) {
        console.warn(`Failed to parse SSE message from ${serverName}:`, error);
      }
    };
    
    eventSource.onerror = (error) => {
      if (client.onerror) {
        client.onerror(error);
      }
    };
    
    eventSource.onopen = () => {
      console.log(`SSE connection opened to ${serverName}`);
    };
    
    // 等待连接建立
    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('SSE connection timeout'));
      }, 10000);
      
      eventSource.onopen = () => {
        clearTimeout(timeout);
        resolve();
      };
      
      eventSource.onerror = (error) => {
        clearTimeout(timeout);
        reject(error);
      };
    });
    
    // 执行握手
    const capabilities = await this.performHandshake(client, serverName);
    
    return {
      client,
      tools: capabilities.tools || [],
      commands: capabilities.commands || [],
      resources: capabilities.resources || [],
      isAuthenticated
    };
  }
  
  /**
   * 创建HTTP连接
   * @param {string} serverName - 服务器名称
   * @param {Object} config - 配置
   * @returns {Promise<Object>} 连接对象
   * @private
   */
  async createHTTPConnection(serverName, config) {
    // 检查认证状态
    const authTokens = await this.getAuthTokens(serverName, config);
    const isAuthenticated = Boolean(authTokens);
    
    if (!isAuthenticated && config.requiresAuth) {
      return {
        client: {
          type: MCP_CLIENT_STATES.NEEDS_AUTH
        },
        isAuthenticated: false
      };
    }
    
    const headers = {
      'Content-Type': 'application/json'
    };
    
    if (authTokens) {
      headers['Authorization'] = `Bearer ${authTokens.access_token}`;
    }
    
    const client = {
      type: MCP_CLIENT_STATES.CONNECTED,
      baseUrl: config.url,
      headers,
      send: async (message) => {
        const response = await fetch(`${config.url}/mcp`, {
          method: 'POST',
          headers,
          body: JSON.stringify(message)
        });
        
        if (!response.ok) {
          throw new Error(`HTTP request failed: ${response.statusText}`);
        }
        
        return response.json();
      },
      onmessage: null,
      onclose: null,
      onerror: null
    };
    
    // 执行握手
    const capabilities = await this.performHandshake(client, serverName);
    
    return {
      client,
      tools: capabilities.tools || [],
      commands: capabilities.commands || [],
      resources: capabilities.resources || [],
      isAuthenticated
    };
  }
  
  /**
   * 执行MCP握手
   * @param {Object} client - 客户端对象
   * @param {string} serverName - 服务器名称
   * @returns {Promise<Object>} 服务器能力
   * @private
   */
  async performHandshake(client, serverName) {
    const initMessage = {
      jsonrpc: '2.0',
      id: generateRandomString(16),
      method: 'initialize',
      params: {
        protocolVersion: '2024-11-05',
        capabilities: {
          roots: {
            listChanged: true
          },
          sampling: {}
        },
        clientInfo: {
          name: 'claude-code',
          version: '1.0.0'
        }
      }
    };
    
    const response = await this.sendMessage(client, initMessage);
    
    if (response.error) {
      throw new Error(`Handshake failed: ${response.error.message}`);
    }
    
    // 发送initialized通知
    await this.sendMessage(client, {
      jsonrpc: '2.0',
      method: 'notifications/initialized'
    });
    
    return response.result?.capabilities || {};
  }
  
  /**
   * 发送消息到MCP服务器
   * @param {Object} client - 客户端对象
   * @param {Object} message - 消息
   * @returns {Promise<Object>} 响应
   * @private
   */
  async sendMessage(client, message) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Message timeout'));
      }, 30000);
      
      const messageId = message.id;
      
      if (messageId) {
        const originalOnMessage = client.onmessage;
        client.onmessage = (response) => {
          if (response.id === messageId) {
            clearTimeout(timeout);
            client.onmessage = originalOnMessage;
            resolve(response);
          } else if (originalOnMessage) {
            originalOnMessage(response);
          }
        };
      }
      
      client.send(message).catch(reject);
      
      if (!messageId) {
        // 通知消息，不需要响应
        clearTimeout(timeout);
        resolve({});
      }
    });
  }
  
  /**
   * 设置连接事件处理器
   * @param {string} serverName - 服务器名称
   * @param {Object} client - 客户端对象
   * @private
   * @original: 原始文件L40929-40956中的连接事件处理
   */
  setupConnectionHandlers(serverName, client) {
    client.onclose = () => {
      console.log(`MCP connection closed: ${serverName}`);
      this.handleConnectionClose(serverName);
    };
    
    client.onerror = (error) => {
      console.error(`MCP connection error for ${serverName}:`, error);
      this.handleConnectionError(serverName, error);
    };
  }
  
  /**
   * 处理连接关闭
   * @param {string} serverName - 服务器名称
   * @private
   */
  async handleConnectionClose(serverName) {
    const client = this.clients.get(serverName);
    if (!client) return;
    
    // 清理资源
    this.unregisterServerResources(serverName);
    
    // 尝试重连
    if (client.config.type === MCP_TRANSPORT_TYPES.SSE || 
        client.config.type === MCP_TRANSPORT_TYPES.SSE_IDE) {
      await this.attemptReconnect(serverName);
    } else {
      this.updateClientState(serverName, MCP_CLIENT_STATES.FAILED);
    }
  }
  
  /**
   * 处理连接错误
   * @param {string} serverName - 服务器名称
   * @param {Error} error - 错误对象
   * @private
   */
  handleConnectionError(serverName, error) {
    console.error(`MCP server ${serverName} error:`, error.message);
    this.updateClientState(serverName, MCP_CLIENT_STATES.FAILED);
  }
  
  /**
   * 尝试重连
   * @param {string} serverName - 服务器名称
   * @returns {Promise<void>}
   * @private
   */
  async attemptReconnect(serverName) {
    const attempts = this.reconnectAttempts.get(serverName) || 0;
    
    if (attempts >= this.maxReconnectAttempts) {
      console.error(`Max reconnect attempts reached for ${serverName}`);
      this.updateClientState(serverName, MCP_CLIENT_STATES.FAILED);
      return;
    }
    
    this.reconnectAttempts.set(serverName, attempts + 1);
    
    console.log(`Attempting to reconnect to ${serverName} (attempt ${attempts + 1})`);
    
    setTimeout(async () => {
      try {
        await this.connectServer(serverName);
        this.reconnectAttempts.delete(serverName);
        console.log(`Successfully reconnected to ${serverName}`);
      } catch (error) {
        console.error(`Reconnection failed for ${serverName}:`, error.message);
        await this.attemptReconnect(serverName);
      }
    }, this.reconnectDelay);
  }
  
  /**
   * 更新客户端状态
   * @param {string} serverName - 服务器名称
   * @param {string} state - 新状态
   * @private
   */
  updateClientState(serverName, state) {
    const client = this.clients.get(serverName);
    if (client) {
      this.clients.set(serverName, {
        ...client,
        type: state
      });
    }
  }
  
  /**
   * 注册工具
   * @param {string} serverName - 服务器名称
   * @param {Array} tools - 工具列表
   * @private
   */
  registerTools(serverName, tools) {
    // 移除该服务器的旧工具
    this.tools = this.tools.filter(tool => !tool.name.startsWith(`${serverName}:`));
    
    // 添加新工具
    const prefixedTools = tools.map(tool => ({
      ...tool,
      name: `${serverName}:${tool.name}`,
      serverName,
      isMcp: true
    }));
    
    this.tools.push(...prefixedTools);
  }
  
  /**
   * 注册命令
   * @param {string} serverName - 服务器名称
   * @param {Array} commands - 命令列表
   * @private
   */
  registerCommands(serverName, commands) {
    // 移除该服务器的旧命令
    this.commands = this.commands.filter(cmd => !cmd.name.startsWith(`${serverName}:`));
    
    // 添加新命令
    const prefixedCommands = commands.map(cmd => ({
      ...cmd,
      name: `${serverName}:${cmd.name}`,
      serverName,
      isMcp: true
    }));
    
    this.commands.push(...prefixedCommands);
  }
  
  /**
   * 注册资源
   * @param {string} serverName - 服务器名称
   * @param {Array} resources - 资源列表
   * @private
   */
  registerResources(serverName, resources) {
    this.resources.set(serverName, resources);
  }
  
  /**
   * 注销服务器资源
   * @param {string} serverName - 服务器名称
   * @private
   */
  unregisterServerResources(serverName) {
    // 移除工具
    this.tools = this.tools.filter(tool => tool.serverName !== serverName);
    
    // 移除命令
    this.commands = this.commands.filter(cmd => cmd.serverName !== serverName);
    
    // 移除资源
    this.resources.delete(serverName);
  }
  
  /**
   * 获取认证令牌
   * @param {string} serverName - 服务器名称
   * @param {Object} config - 配置
   * @returns {Promise<Object|null>} 认证令牌
   * @private
   * @original: 原始文件L41475中的lm类tokens方法
   */
  async getAuthTokens(serverName, config) {
    // @todo: 实现从安全存储中获取认证令牌的逻辑
    return null;
  }
  
  /**
   * 获取默认MCP配置
   * @returns {Object} 默认配置
   * @private
   * @original: 原始文件L40883中的Qz函数
   */
  getDefaultConfig() {
    // @todo: 实现从配置文件读取默认MCP配置的逻辑
    return {};
  }
  
  /**
   * 获取所有客户端状态
   * @returns {Array} 客户端状态列表
   */
  getClientStates() {
    return Array.from(this.clients.values());
  }
  
  /**
   * 获取服务器工具
   * @param {string} serverName - 服务器名称
   * @returns {Array} 工具列表
   */
  getServerTools(serverName) {
    return this.tools.filter(tool => tool.serverName === serverName);
  }
  
  /**
   * 获取服务器命令
   * @param {string} serverName - 服务器名称
   * @returns {Array} 命令列表
   */
  getServerCommands(serverName) {
    return this.commands.filter(cmd => cmd.serverName === serverName);
  }
  
  /**
   * 获取服务器资源
   * @param {string} serverName - 服务器名称
   * @returns {Array} 资源列表
   */
  getServerResources(serverName) {
    return this.resources.get(serverName) || [];
  }
  
  /**
   * 清理所有连接
   * @returns {Promise<void>}
   */
  async cleanup() {
    for (const [serverName, client] of this.clients) {
      if (client.client && client.type === MCP_CLIENT_STATES.CONNECTED) {
        try {
          if (client.client.process) {
            client.client.process.kill();
          } else if (client.client.eventSource) {
            client.client.eventSource.close();
          }
        } catch (error) {
          console.warn(`Error closing MCP connection ${serverName}:`, error);
        }
      }
    }
    
    this.clients.clear();
    this.tools = [];
    this.commands = [];
    this.resources.clear();
    this.reconnectAttempts.clear();
  }
  
  /**
   * 检查服务器是否已连接
   * @param {string} serverName - 服务器名称
   * @returns {boolean} 是否已连接
   */
  isServerConnected(serverName) {
    const client = this.clients.get(serverName);
    return client?.type === MCP_CLIENT_STATES.CONNECTED;
  }
  
  /**
   * 获取连接统计信息
   * @returns {Object} 统计信息
   */
  getConnectionStats() {
    const states = Array.from(this.clients.values());
    
    return {
      total: states.length,
      connected: states.filter(c => c.type === MCP_CLIENT_STATES.CONNECTED).length,
      pending: states.filter(c => c.type === MCP_CLIENT_STATES.PENDING).length,
      needsAuth: states.filter(c => c.type === MCP_CLIENT_STATES.NEEDS_AUTH).length,
      failed: states.filter(c => c.type === MCP_CLIENT_STATES.FAILED).length,
      totalTools: this.tools.length,
      totalCommands: this.commands.length,
      totalResources: Array.from(this.resources.values()).reduce((sum, resources) => sum + resources.length, 0)
    };
  }
}

// 创建默认MCP服务实例
export const mcpService = new MCPService();

/**
 * 初始化MCP连接的简单函数
 * @param {Object} config - MCP配置
 * @param {boolean} isStrict - 是否为严格配置
 * @returns {Promise<void>}
 */
export async function initializeMCP(config = {}, isStrict = false) {
  return mcpService.initializeConnections(config, isStrict);
}

/**
 * 重连MCP服务器的简单函数
 * @param {string} serverName - 服务器名称
 * @returns {Promise<Object>} 连接结果
 */
export async function reconnectMCPServer(serverName) {
  return mcpService.connectServer(serverName);
}
