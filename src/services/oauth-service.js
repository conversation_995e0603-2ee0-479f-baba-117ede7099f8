/**
 * OAuth认证服务模块
 * @original: 原始文件中的OAuth认证相关函数
 */

import { openBrowser } from '../utils/browser-utils.js';
import { generateRandomString } from '../utils/string-utils.js';
import { httpClient } from './http-client.js';

/**
 * OAuth状态定义
 * @original: 原始文件L39970-40020中的OAuth状态管理
 */
export const OAUTH_STATES = {
  STARTING: 'starting',
  WAITING_FOR_LOGIN: 'waiting_for_login',
  PROCESSING: 'processing',
  SUCCESS: 'success',
  ERROR: 'error',
  ABOUT_TO_RETRY: 'about_to_retry'
};

/**
 * OAuth错误类型
 */
export const OAUTH_ERROR_TYPES = {
  NETWORK_ERROR: 'network_error',
  INVALID_RESPONSE: 'invalid_response',
  USER_CANCELLED: 'user_cancelled',
  TIMEOUT: 'timeout',
  UNKNOWN: 'unknown'
};

/**
 * OAuth认证服务类
 * @original: 原始文件L39970-40131中的OAuth认证逻辑
 */
export class OAuthService {
  constructor() {
    this.timeouts = new Set();
    this.pollInterval = 1000; // 1秒轮询间隔
    this.maxPollAttempts = 300; // 最多轮询5分钟
    this.authEndpoint = 'https://api.anthropic.com/oauth';
    this.clientId = 'claude-code';
  }
  
  /**
   * 开始OAuth认证流程
   * @param {Object} options - 认证选项
   * @returns {Promise<string>} 访问令牌
   * @original: 原始文件L39970-40020中的OAuth启动逻辑
   */
  async startOAuthFlow(options = {}) {
    const {
      onStateChange = () => {},
      onUrlGenerated = () => {},
      openBrowserAutomatically = true
    } = options;
    
    try {
      // 步骤1: 开始认证
      onStateChange({
        state: OAUTH_STATES.STARTING
      });
      
      // 生成认证参数
      const authParams = this.generateAuthParams();
      const authUrl = this.buildAuthUrl(authParams);
      
      // 步骤2: 等待用户登录
      onStateChange({
        state: OAUTH_STATES.WAITING_FOR_LOGIN,
        url: authUrl
      });
      
      onUrlGenerated(authUrl);
      
      // 自动打开浏览器
      if (openBrowserAutomatically) {
        setTimeout(() => {
          this.openAuthUrl(authUrl);
        }, 100);
      }
      
      // 步骤3: 轮询获取令牌
      onStateChange({
        state: OAUTH_STATES.PROCESSING
      });
      
      const accessToken = await this.pollForToken(authParams);
      
      // 步骤4: 认证成功
      onStateChange({
        state: OAUTH_STATES.SUCCESS,
        token: accessToken
      });
      
      return accessToken;
      
    } catch (error) {
      const errorMessage = this.parseError(error);
      onStateChange({
        state: OAUTH_STATES.ERROR,
        message: errorMessage,
        toRetry: {
          state: OAUTH_STATES.STARTING
        }
      });
      
      throw error;
    }
  }
  
  /**
   * 生成认证参数
   * @returns {Object} 认证参数
   * @private
   */
  generateAuthParams() {
    return {
      clientId: this.clientId,
      state: generateRandomString(32),
      codeVerifier: generateRandomString(128),
      nonce: generateRandomString(32),
      timestamp: Date.now()
    };
  }
  
  /**
   * 构建认证URL
   * @param {Object} authParams - 认证参数
   * @returns {string} 认证URL
   * @private
   */
  buildAuthUrl(authParams) {
    const { clientId, state, codeVerifier, nonce } = authParams;
    
    // 生成code_challenge
    const codeChallenge = this.generateCodeChallenge(codeVerifier);
    
    const params = new URLSearchParams({
      client_id: clientId,
      response_type: 'code',
      scope: 'claude-code',
      state: state,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256',
      nonce: nonce,
      redirect_uri: 'urn:ietf:wg:oauth:2.0:oob'
    });
    
    return `${this.authEndpoint}/authorize?${params.toString()}`;
  }
  
  /**
   * 生成PKCE code challenge
   * @param {string} codeVerifier - code verifier
   * @returns {string} code challenge
   * @private
   */
  generateCodeChallenge(codeVerifier) {
    const crypto = require('crypto');
    return crypto
      .createHash('sha256')
      .update(codeVerifier)
      .digest('base64url');
  }
  
  /**
   * 打开认证URL
   * @param {string} authUrl - 认证URL
   * @private
   * @original: 原始文件L40000-40008中的浏览器打开逻辑
   */
  async openAuthUrl(authUrl) {
    try {
      await openBrowser(authUrl);
    } catch (error) {
      console.warn('Failed to open browser automatically:', error.message);
    }
  }
  
  /**
   * 轮询获取访问令牌
   * @param {Object} authParams - 认证参数
   * @returns {Promise<string>} 访问令牌
   * @private
   * @original: 原始文件L40003-40008中的轮询逻辑
   */
  async pollForToken(authParams) {
    const { state, codeVerifier } = authParams;
    let attempts = 0;
    
    return new Promise((resolve, reject) => {
      const poll = async () => {
        if (attempts >= this.maxPollAttempts) {
          reject(new Error('OAuth timeout: No response received within the expected time'));
          return;
        }
        
        try {
          const tokenResponse = await this.checkTokenStatus(state, codeVerifier);
          
          if (tokenResponse.access_token) {
            resolve(tokenResponse.access_token);
            return;
          }
          
          if (tokenResponse.error) {
            if (tokenResponse.error === 'authorization_pending') {
              // 继续轮询
              attempts++;
              const timeout = setTimeout(poll, this.pollInterval);
              this.timeouts.add(timeout);
            } else {
              reject(new Error(`OAuth error: ${tokenResponse.error_description || tokenResponse.error}`));
            }
            return;
          }
          
          // 继续轮询
          attempts++;
          const timeout = setTimeout(poll, this.pollInterval);
          this.timeouts.add(timeout);
          
        } catch (error) {
          reject(error);
        }
      };
      
      // 开始轮询
      const timeout = setTimeout(poll, this.pollInterval);
      this.timeouts.add(timeout);
    });
  }
  
  /**
   * 检查令牌状态
   * @param {string} state - 状态参数
   * @param {string} codeVerifier - code verifier
   * @returns {Promise<Object>} 令牌响应
   * @private
   */
  async checkTokenStatus(state, codeVerifier) {
    try {
      const response = await httpClient.post(`${this.authEndpoint}/token`, {
        grant_type: 'authorization_code',
        client_id: this.clientId,
        state: state,
        code_verifier: codeVerifier
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
      
      return response.data;
    } catch (error) {
      if (error.response?.status === 400) {
        // 可能是还在等待用户授权
        return {
          error: 'authorization_pending'
        };
      }
      
      throw error;
    }
  }
  
  /**
   * 解析错误信息
   * @param {Error} error - 错误对象
   * @returns {string} 错误消息
   * @private
   * @original: 原始文件L40010-40020中的错误处理逻辑
   */
  parseError(error) {
    if (error.message.includes('timeout')) {
      return 'Authentication timed out. Please try again.';
    }
    
    if (error.message.includes('network')) {
      return 'Network error occurred during authentication. Please check your connection and try again.';
    }
    
    if (error.message.includes('cancelled')) {
      return 'Authentication was cancelled by user.';
    }
    
    return error.message || 'An unexpected error occurred during authentication.';
  }
  
  /**
   * 清理资源
   * @original: 原始文件L40061-40065中的清理逻辑
   */
  cleanup() {
    this.timeouts.forEach(timeout => clearTimeout(timeout));
    this.timeouts.clear();
  }
  
  /**
   * 验证访问令牌
   * @param {string} accessToken - 访问令牌
   * @returns {Promise<boolean>} 是否有效
   */
  async validateAccessToken(accessToken) {
    try {
      const response = await httpClient.get(`${this.authEndpoint}/validate`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });
      
      return response.status === 200;
    } catch {
      return false;
    }
  }
  
  /**
   * 刷新访问令牌
   * @param {string} refreshToken - 刷新令牌
   * @returns {Promise<Object>} 新的令牌信息
   */
  async refreshAccessToken(refreshToken) {
    try {
      const response = await httpClient.post(`${this.authEndpoint}/token`, {
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
        client_id: this.clientId
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
      
      return response.data;
    } catch (error) {
      throw new Error(`Failed to refresh token: ${error.message}`);
    }
  }
  
  /**
   * 撤销访问令牌
   * @param {string} accessToken - 访问令牌
   * @returns {Promise<void>}
   */
  async revokeAccessToken(accessToken) {
    try {
      await httpClient.post(`${this.authEndpoint}/revoke`, {
        token: accessToken,
        client_id: this.clientId
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
    } catch (error) {
      throw new Error(`Failed to revoke token: ${error.message}`);
    }
  }
  
  /**
   * 获取用户信息
   * @param {string} accessToken - 访问令牌
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo(accessToken) {
    try {
      const response = await httpClient.get(`${this.authEndpoint}/userinfo`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });
      
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get user info: ${error.message}`);
    }
  }
  
  /**
   * 检查OAuth是否可用
   * @returns {Promise<boolean>} 是否可用
   */
  async isOAuthAvailable() {
    try {
      const response = await httpClient.get(`${this.authEndpoint}/health`, {
        timeout: 5000
      });
      
      return response.status === 200;
    } catch {
      return false;
    }
  }
  
  /**
   * 生成设备代码（用于设备流程）
   * @returns {Promise<Object>} 设备代码信息
   */
  async generateDeviceCode() {
    try {
      const response = await httpClient.post(`${this.authEndpoint}/device/code`, {
        client_id: this.clientId,
        scope: 'claude-code'
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
      
      return response.data;
    } catch (error) {
      throw new Error(`Failed to generate device code: ${error.message}`);
    }
  }
  
  /**
   * 使用设备代码轮询获取令牌
   * @param {string} deviceCode - 设备代码
   * @param {number} interval - 轮询间隔（秒）
   * @returns {Promise<string>} 访问令牌
   */
  async pollDeviceToken(deviceCode, interval = 5) {
    const maxAttempts = 120; // 10分钟
    let attempts = 0;
    
    return new Promise((resolve, reject) => {
      const poll = async () => {
        if (attempts >= maxAttempts) {
          reject(new Error('Device authentication timeout'));
          return;
        }
        
        try {
          const response = await httpClient.post(`${this.authEndpoint}/device/token`, {
            grant_type: 'urn:ietf:params:oauth:grant-type:device_code',
            device_code: deviceCode,
            client_id: this.clientId
          }, {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
            }
          });
          
          if (response.data.access_token) {
            resolve(response.data.access_token);
            return;
          }
          
        } catch (error) {
          if (error.response?.data?.error === 'authorization_pending') {
            // 继续轮询
            attempts++;
            const timeout = setTimeout(poll, interval * 1000);
            this.timeouts.add(timeout);
            return;
          }
          
          reject(error);
          return;
        }
        
        // 继续轮询
        attempts++;
        const timeout = setTimeout(poll, interval * 1000);
        this.timeouts.add(timeout);
      };
      
      // 开始轮询
      const timeout = setTimeout(poll, interval * 1000);
      this.timeouts.add(timeout);
    });
  }
}

// 创建默认OAuth服务实例
export const oauthService = new OAuthService();

/**
 * 开始OAuth认证的简单函数
 * @param {Object} options - 认证选项
 * @returns {Promise<string>} 访问令牌
 */
export async function startOAuthFlow(options = {}) {
  return oauthService.startOAuthFlow(options);
}

/**
 * 验证访问令牌的简单函数
 * @param {string} accessToken - 访问令牌
 * @returns {Promise<boolean>} 是否有效
 */
export async function validateAccessToken(accessToken) {
  return oauthService.validateAccessToken(accessToken);
}

/**
 * 检查OAuth是否可用的简单函数
 * @returns {Promise<boolean>} 是否可用
 */
export async function isOAuthAvailable() {
  return oauthService.isOAuthAvailable();
}
