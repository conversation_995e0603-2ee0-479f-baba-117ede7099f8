/**
 * 数组处理工具函数
 * @original: 原始文件中的数组处理相关函数
 */

/**
 * 获取数组的最后一个元素
 * @param {Array} array - 输入数组
 * @returns {*} 最后一个元素，如果数组为空则返回undefined
 * @original: 原始文件L1754-1757中的lrB函数
 */
export function last(array) {
  const length = array == null ? 0 : array.length;
  return length ? array[length - 1] : undefined;
}

/**
 * 扁平化数组（一层）
 * @param {Array} array - 要扁平化的数组
 * @returns {Array} 扁平化后的数组
 * @original: 原始文件L1550-1553中的InB函数
 */
export function flatten(array) {
  const length = array == null ? 0 : array.length;
  return length ? baseFlatten(array, 1) : [];
}

/**
 * 基础扁平化函数（内部使用）
 * @param {Array} array - 要扁平化的数组
 * @param {number} depth - 扁平化深度
 * @param {Function} predicate - 判断函数
 * @param {boolean} isStrict - 是否严格模式
 * @param {Array} result - 结果数组
 * @returns {Array} 扁平化后的数组
 * @original: 原始文件L1537-1548中的kP0函数
 */
function baseFlatten(array, depth, predicate, isStrict, result) {
  let index = -1;
  const length = array.length;
  
  predicate || (predicate = isFlattenable);
  result || (result = []);
  
  while (++index < length) {
    const value = array[index];
    if (depth > 0 && predicate(value)) {
      if (depth > 1) {
        baseFlatten(value, depth - 1, predicate, isStrict, result);
      } else {
        arrayPush(result, value);
      }
    } else if (!isStrict) {
      result[result.length] = value;
    }
  }
  return result;
}

/**
 * 检查值是否可以扁平化
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否可以扁平化
 * @original: 原始文件L1533-1535中的FnB函数
 */
function isFlattenable(value) {
  return Array.isArray(value) || isArguments(value) || !!(Symbol.isConcatSpreadable && value && value[Symbol.isConcatSpreadable]);
}

/**
 * 检查值是否为arguments对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为arguments对象
 */
function isArguments(value) {
  return Object.prototype.toString.call(value) === '[object Arguments]';
}

/**
 * 将数组元素推入另一个数组
 * @param {Array} array - 目标数组
 * @param {Array} values - 要推入的值数组
 * @returns {Array} 目标数组
 */
function arrayPush(array, values) {
  let index = -1;
  const length = values.length;
  const offset = array.length;
  
  while (++index < length) {
    array[offset + index] = values[index];
  }
  return array;
}

/**
 * 数组切片函数
 * @param {Array} array - 输入数组
 * @param {number} start - 开始位置
 * @param {number} end - 结束位置
 * @returns {Array} 切片后的数组
 * @original: 原始文件L1559-1568中的znB函数
 */
export function slice(array, start, end) {
  let index = -1;
  let length = array.length;
  
  if (start < 0) {
    start = -start > length ? 0 : (length + start);
  }
  end = end > length ? length : end;
  if (end < 0) {
    end += length;
  }
  length = start > end ? 0 : ((end - start) >>> 0);
  start >>>= 0;
  
  const result = Array(length);
  while (++index < length) {
    result[index] = array[index + start];
  }
  return result;
}

/**
 * 数组去重
 * @param {Array} array - 输入数组
 * @param {Function} iteratee - 迭代函数
 * @returns {Array} 去重后的数组
 * @original: 原始文件L1982-1984中的PoB函数
 */
export function uniq(array, iteratee) {
  return (array && array.length) ? baseUniq(array, getIteratee(iteratee, 2)) : [];
}

/**
 * 基础去重函数
 * @param {Array} array - 输入数组
 * @param {Function} iteratee - 迭代函数
 * @param {Function} comparator - 比较函数
 * @returns {Array} 去重后的数组
 * @original: 原始文件L1954-1980中的ToB函数
 */
function baseUniq(array, iteratee, comparator) {
  let index = -1;
  let includes = arrayIncludes;
  let length = array.length;
  let isCommon = true;
  let result = [];
  let seen = result;
  
  if (comparator) {
    isCommon = false;
    includes = arrayIncludesWith;
  } else if (length >= 200) {
    const set = iteratee ? null : createSet(array);
    if (set) {
      return setToArray(set);
    }
    isCommon = false;
    includes = cacheHas;
    seen = new SetCache();
  } else {
    seen = iteratee ? [] : result;
  }
  
  outer:
  while (++index < length) {
    let value = array[index];
    const computed = iteratee ? iteratee(value) : value;
    
    value = (comparator || value !== 0) ? value : 0;
    if (isCommon && computed === computed) {
      let seenIndex = seen.length;
      while (seenIndex--) {
        if (seen[seenIndex] === computed) {
          continue outer;
        }
      }
      if (iteratee) {
        seen.push(computed);
      }
      result.push(value);
    } else if (!includes(seen, computed, comparator)) {
      if (seen !== result) {
        seen.push(computed);
      }
      result.push(value);
    }
  }
  return result;
}

/**
 * 检查数组是否包含指定值
 * @param {Array} array - 数组
 * @param {*} value - 要查找的值
 * @param {number} fromIndex - 开始查找的索引
 * @returns {boolean} 是否包含
 * @original: 原始文件L1385-1388中的tlB函数
 */
export function includes(array, value, fromIndex) {
  const length = array == null ? 0 : array.length;
  return !!length && baseIndexOf(array, value, fromIndex || 0) > -1;
}

/**
 * 基础索引查找函数
 * @param {Array} array - 数组
 * @param {*} value - 要查找的值
 * @param {number} fromIndex - 开始索引
 * @returns {number} 找到的索引，未找到返回-1
 * @original: 原始文件L1381-1383中的olB函数
 */
function baseIndexOf(array, value, fromIndex) {
  return value === value ? strictIndexOf(array, value, fromIndex) : baseFindIndex(array, baseIsNaN, fromIndex);
}

/**
 * 严格索引查找
 * @param {Array} array - 数组
 * @param {*} value - 要查找的值
 * @param {number} fromIndex - 开始索引
 * @returns {number} 找到的索引
 * @original: 原始文件L1374-1379中的rlB函数
 */
function strictIndexOf(array, value, fromIndex) {
  let index = fromIndex - 1;
  const length = array.length;
  
  while (++index < length) {
    if (array[index] === value) {
      return index;
    }
  }
  return -1;
}

/**
 * 基础查找索引函数
 * @param {Array} array - 数组
 * @param {Function} predicate - 判断函数
 * @param {number} fromIndex - 开始索引
 * @param {boolean} fromRight - 是否从右开始
 * @returns {number} 找到的索引
 */
function baseFindIndex(array, predicate, fromIndex, fromRight) {
  const length = array.length;
  let index = fromIndex + (fromRight ? 1 : -1);
  
  while (fromRight ? index-- : ++index < length) {
    if (predicate(array[index], index, array)) {
      return index;
    }
  }
  return -1;
}

/**
 * 检查值是否为NaN
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为NaN
 * @original: 原始文件L1370-1372中的slB函数
 */
function baseIsNaN(value) {
  return value !== value;
}

/**
 * 随机获取数组中的一个元素
 * @param {Array} array - 输入数组
 * @returns {*} 随机元素
 * @original: 原始文件L1898-1901中的HoB函数
 */
export function sample(array) {
  const length = array.length;
  return length ? array[random(0, length - 1)] : undefined;
}

/**
 * 生成指定范围内的随机整数
 * @param {number} lower - 下限
 * @param {number} upper - 上限
 * @returns {number} 随机整数
 * @original: 原始文件L1889-1891中的CoB函数
 */
function random(lower, upper) {
  return lower + Math.floor(Math.random() * (upper - lower + 1));
}

/**
 * 数组洗牌（Fisher-Yates算法）
 * @param {Array} array - 要洗牌的数组
 * @param {number} size - 洗牌后的大小
 * @returns {Array} 洗牌后的数组
 * @original: 原始文件L1912-1923中的UoB函数
 */
export function shuffle(array, size) {
  let index = -1;
  const length = array.length;
  const lastIndex = length - 1;
  
  size = size === undefined ? length : size;
  while (++index < size) {
    const rand = random(index, lastIndex);
    const value = array[rand];
    
    array[rand] = array[index];
    array[index] = value;
  }
  array.length = size;
  return array;
}

/**
 * 对数组进行采样
 * @param {Array} array - 输入数组
 * @param {number} n - 采样数量
 * @returns {Array} 采样结果
 * @original: 原始文件L1925-1927中的woB函数
 */
export function sampleSize(array, n) {
  return shuffle(copyArray(array), clamp(n, 0, array.length));
}

/**
 * 复制数组
 * @param {Array} source - 源数组
 * @param {Array} array - 目标数组
 * @returns {Array} 复制后的数组
 */
function copyArray(source, array) {
  let index = -1;
  const length = source.length;
  
  array || (array = Array(length));
  while (++index < length) {
    array[index] = source[index];
  }
  return array;
}

/**
 * 限制数值在指定范围内
 * @param {number} number - 要限制的数值
 * @param {number} lower - 下限
 * @param {number} upper - 上限
 * @returns {number} 限制后的数值
 */
function clamp(number, lower, upper) {
  if (number === number) {
    if (upper !== undefined) {
      number = number <= upper ? number : upper;
    }
    if (lower !== undefined) {
      number = number >= lower ? number : lower;
    }
  }
  return number;
}

/**
 * 数组求和
 * @param {Array} array - 输入数组
 * @param {Function} iteratee - 迭代函数
 * @returns {number} 求和结果
 * @original: 原始文件L1944-1946中的LoB函数
 */
export function sumBy(array, iteratee) {
  return (array && array.length) ? baseSum(array, getIteratee(iteratee, 2)) : 0;
}

/**
 * 基础求和函数
 * @param {Array} array - 输入数组
 * @param {Function} iteratee - 迭代函数
 * @returns {number} 求和结果
 * @original: 原始文件L1784-1793中的orB函数
 */
function baseSum(array, iteratee) {
  let result;
  let index = -1;
  const length = array.length;
  
  while (++index < length) {
    const current = iteratee(array[index]);
    if (current !== undefined) {
      result = result === undefined ? current : (result + current);
    }
  }
  return result;
}

/**
 * 获取迭代函数
 * @param {Function|string} value - 迭代器
 * @param {number} arity - 参数数量
 * @returns {Function} 迭代函数
 */
function getIteratee(value, arity) {
  // 实现完整的迭代器获取逻辑
  if (typeof value === 'function') {
    return value;
  }

  if (typeof value === 'string') {
    // 字符串路径，如 'name' 或 'user.name'
    if (value.includes('.')) {
      // 支持嵌套属性路径
      const path = value.split('.');
      return (item) => {
        let result = item;
        for (const key of path) {
          if (result == null) return undefined;
          result = result[key];
        }
        return result;
      };
    } else {
      // 简单属性访问
      return (item) => item?.[value];
    }
  }

  if (Array.isArray(value)) {
    // 数组形式的路径，如 ['user', 'name']
    return (item) => {
      let result = item;
      for (const key of value) {
        if (result == null) return undefined;
        result = result[key];
      }
      return result;
    };
  }

  if (value && typeof value === 'object') {
    // 对象匹配，如 { status: 'active' }
    return (item) => {
      for (const [key, val] of Object.entries(value)) {
        if (item?.[key] !== val) {
          return false;
        }
      }
      return true;
    };
  }

  // 默认情况：返回恒等函数
  return (item) => item;
}

/**
 * 数组包含检查（内部使用）
 */
function arrayIncludes(array, value) {
  const length = array == null ? 0 : array.length;
  return !!length && baseIndexOf(array, value, 0) > -1;
}

/**
 * 数组包含检查（带比较函数）
 */
function arrayIncludesWith(array, value, comparator) {
  let index = -1;
  const length = array == null ? 0 : array.length;
  
  while (++index < length) {
    if (comparator(value, array[index])) {
      return true;
    }
  }
  return false;
}

/**
 * 创建Set（如果支持）
 */
function createSet(array) {
  return (Set && (1 / setToArray(new Set([,-0]))[1]) == Infinity) ? null : function(array) {
    return new Set(array);
  };
}

/**
 * Set转数组
 */
function setToArray(set) {
  let index = -1;
  const result = Array(set.size);
  
  set.forEach(function(value) {
    result[++index] = value;
  });
  return result;
}

/**
 * 缓存检查函数
 */
function cacheHas(cache, key) {
  return cache.has(key);
}

/**
 * SetCache类（简化版）
 */
class SetCache {
  constructor(values) {
    let index = -1;
    const length = values == null ? 0 : values.length;
    
    this.__data__ = new Map();
    while (++index < length) {
      this.add(values[index]);
    }
  }
  
  add(value) {
    this.__data__.set(value, '__lodash_hash_undefined__');
    return this;
  }
  
  has(value) {
    return this.__data__.has(value);
  }
}

/**
 * 过滤数组元素
 * @param {Array} array - 输入数组
 * @param {Function} predicate - 判断函数
 * @returns {Array} 过滤后的数组
 * @original: 原始文件L1759-1764中的prB函数
 */
export function filter(array, predicate) {
  const result = [];
  arrayEach(array, function(value, index, array) {
    if (predicate(value, index, array)) {
      result.push(value);
    }
  });
  return result;
}

/**
 * 数组遍历函数
 * @param {Array} array - 输入数组
 * @param {Function} iteratee - 迭代函数
 * @returns {Array} 输入数组
 */
function arrayEach(array, iteratee) {
  let index = -1;
  const length = array == null ? 0 : array.length;
  
  while (++index < length) {
    if (iteratee(array[index], index, array) === false) {
      break;
    }
  }
  return array;
}

/**
 * 根据键值对创建对象
 * @param {Array} keys - 键数组
 * @param {Array} values - 值数组
 * @returns {Object} 创建的对象
 * @original: 原始文件L1998-2000中的joB函数
 */
export function zipObject(keys, values) {
  return baseZipObject(keys || [], values || [], assignValue);
}

/**
 * 基础zipObject函数
 * @param {Array} props - 属性数组
 * @param {Array} values - 值数组
 * @param {Function} assignFunc - 赋值函数
 * @returns {Object} 结果对象
 * @original: 原始文件L1986-1996中的SoB函数
 */
function baseZipObject(props, values, assignFunc) {
  let index = -1;
  const length = props.length;
  const valsLength = values.length;
  const result = {};
  
  while (++index < length) {
    const value = index < valsLength ? values[index] : undefined;
    assignFunc(result, props[index], value);
  }
  return result;
}

/**
 * 赋值函数
 * @param {Object} object - 目标对象
 * @param {string} key - 键
 * @param {*} value - 值
 */
function assignValue(object, key, value) {
  if ((value !== undefined && !eq(object[key], value)) || (value === undefined && !(key in object))) {
    object[key] = value;
  }
}

/**
 * 相等性检查
 * @param {*} value - 值1
 * @param {*} other - 值2
 * @returns {boolean} 是否相等
 */
function eq(value, other) {
  return value === other || (value !== value && other !== other);
}
