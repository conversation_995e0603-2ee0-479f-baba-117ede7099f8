/**
 * 颜色处理工具函数
 * @original: 原始文件中的颜色处理相关函数
 */

/**
 * ANSI颜色代码映射
 * @original: 原始文件L2413-2472中的颜色配置
 */
export const ANSI_COLORS = {
  // 重置和修饰符
  reset: [0, 0],
  bold: [1, 22],
  dim: [2, 22],
  italic: [3, 23],
  underline: [4, 24],
  overline: [53, 55],
  inverse: [7, 27],
  hidden: [8, 28],
  strikethrough: [9, 29],
  
  // 前景色
  black: [30, 39],
  red: [31, 39],
  green: [32, 39],
  yellow: [33, 39],
  blue: [34, 39],
  magenta: [35, 39],
  cyan: [36, 39],
  white: [37, 39],
  
  // 明亮前景色
  blackBright: [90, 39],
  redBright: [91, 39],
  greenBright: [92, 39],
  yellowBright: [93, 39],
  blueBright: [94, 39],
  magentaBright: [95, 39],
  cyanBright: [96, 39],
  whiteBright: [97, 39],
  
  // 背景色
  bgBlack: [40, 49],
  bgRed: [41, 49],
  bgGreen: [42, 49],
  bgYellow: [43, 49],
  bgBlue: [44, 49],
  bgMagenta: [45, 49],
  bgCyan: [46, 49],
  bgWhite: [47, 49],
  
  // 明亮背景色
  bgBlackBright: [100, 49],
  bgRedBright: [101, 49],
  bgGreenBright: [102, 49],
  bgYellowBright: [103, 49],
  bgBlueBright: [104, 49],
  bgMagentaBright: [105, 49],
  bgCyanBright: [106, 49],
  bgWhiteBright: [107, 49]
};

/**
 * 创建ANSI颜色函数
 * @param {number} offset - 偏移量，默认为0
 * @returns {Function} 颜色函数
 * @original: 原始文件L2413-2415中的To0函数
 */
export function createAnsiColorFunction(offset = 0) {
  return function(code) {
    return `\x1B[${code + offset}m`;
  };
}

/**
 * 创建256色ANSI函数
 * @param {number} offset - 偏移量，默认为0
 * @returns {Function} 256色函数
 * @original: 原始文件L2414-2415中的Po0函数
 */
export function create256ColorFunction(offset = 0) {
  return function(colorCode) {
    return `\x1B[${38 + offset};5;${colorCode}m`;
  };
}

/**
 * 创建RGB颜色ANSI函数
 * @param {number} offset - 偏移量，默认为0
 * @returns {Function} RGB颜色函数
 * @original: 原始文件L2415中的So0函数
 */
export function createRgbColorFunction(offset = 0) {
  return function(r, g, b) {
    return `\x1B[${38 + offset};2;${r};${g};${b}m`;
  };
}

/**
 * 应用ANSI颜色到文本
 * @param {string} text - 要着色的文本
 * @param {string} colorName - 颜色名称
 * @returns {string} 着色后的文本
 */
export function colorize(text, colorName) {
  const color = ANSI_COLORS[colorName];
  if (!color) {
    throw new Error(`Unknown color: ${colorName}`);
  }
  
  return `\x1B[${color[0]}m${text}\x1B[${color[1]}m`;
}

/**
 * 移除文本中的ANSI颜色代码
 * @param {string} text - 包含ANSI代码的文本
 * @returns {string} 清理后的文本
 */
export function stripAnsiColors(text) {
  // ANSI转义序列的正则表达式
  const ansiRegex = /\x1B\[[0-9;]*[mGKHF]/g;
  return text.replace(ansiRegex, '');
}

/**
 * 检查终端是否支持颜色
 * @returns {boolean} 是否支持颜色
 */
export function supportsColor() {
  // 检查环境变量
  if (process.env.FORCE_COLOR) {
    return true;
  }
  
  if (process.env.NO_COLOR || process.env.NODE_DISABLE_COLORS) {
    return false;
  }
  
  // 检查终端类型
  if (process.stdout && !process.stdout.isTTY) {
    return false;
  }
  
  // 检查TERM环境变量
  const term = process.env.TERM;
  if (term === 'dumb') {
    return false;
  }
  
  return true;
}

/**
 * 获取颜色级别
 * @returns {number} 颜色级别 (0: 无颜色, 1: 基本颜色, 2: 256色, 3: 真彩色)
 */
export function getColorLevel() {
  if (!supportsColor()) {
    return 0;
  }
  
  if (process.env.COLORTERM === 'truecolor') {
    return 3;
  }
  
  const term = process.env.TERM || '';
  if (term.includes('256') || term.includes('256color')) {
    return 2;
  }
  
  return 1;
}

/**
 * 十六进制颜色转RGB
 * @param {string} hex - 十六进制颜色值 (如 "#FF0000" 或 "FF0000")
 * @returns {Object} RGB对象 {r, g, b}
 */
export function hexToRgb(hex) {
  // 移除#号
  hex = hex.replace('#', '');
  
  // 验证格式
  if (!/^[0-9A-Fa-f]{6}$/.test(hex)) {
    throw new Error(`Invalid hex color: ${hex}`);
  }
  
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  
  return { r, g, b };
}

/**
 * RGB转十六进制颜色
 * @param {number} r - 红色分量 (0-255)
 * @param {number} g - 绿色分量 (0-255)
 * @param {number} b - 蓝色分量 (0-255)
 * @returns {string} 十六进制颜色值
 */
export function rgbToHex(r, g, b) {
  // 验证范围
  if (r < 0 || r > 255 || g < 0 || g > 255 || b < 0 || b > 255) {
    throw new Error('RGB values must be between 0 and 255');
  }
  
  const toHex = (n) => {
    const hex = Math.round(n).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };
  
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`.toUpperCase();
}

/**
 * RGB转HSL
 * @param {number} r - 红色分量 (0-255)
 * @param {number} g - 绿色分量 (0-255)
 * @param {number} b - 蓝色分量 (0-255)
 * @returns {Object} HSL对象 {h, s, l}
 */
export function rgbToHsl(r, g, b) {
  r /= 255;
  g /= 255;
  b /= 255;
  
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h, s, l = (max + min) / 2;
  
  if (max === min) {
    h = s = 0; // 无色
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }
  
  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    l: Math.round(l * 100)
  };
}

/**
 * HSL转RGB
 * @param {number} h - 色相 (0-360)
 * @param {number} s - 饱和度 (0-100)
 * @param {number} l - 亮度 (0-100)
 * @returns {Object} RGB对象 {r, g, b}
 */
export function hslToRgb(h, s, l) {
  h /= 360;
  s /= 100;
  l /= 100;
  
  const hue2rgb = (p, q, t) => {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1/6) return p + (q - p) * 6 * t;
    if (t < 1/2) return q;
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
    return p;
  };
  
  let r, g, b;
  
  if (s === 0) {
    r = g = b = l; // 无色
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }
  
  return {
    r: Math.round(r * 255),
    g: Math.round(g * 255),
    b: Math.round(b * 255)
  };
}

/**
 * 计算颜色亮度
 * @param {number} r - 红色分量 (0-255)
 * @param {number} g - 绿色分量 (0-255)
 * @param {number} b - 蓝色分量 (0-255)
 * @returns {number} 亮度值 (0-255)
 */
export function getLuminance(r, g, b) {
  // 使用相对亮度公式
  return Math.round(0.299 * r + 0.587 * g + 0.114 * b);
}

/**
 * 判断颜色是否为深色
 * @param {number} r - 红色分量 (0-255)
 * @param {number} g - 绿色分量 (0-255)
 * @param {number} b - 蓝色分量 (0-255)
 * @param {number} threshold - 阈值，默认为128
 * @returns {boolean} 是否为深色
 */
export function isDarkColor(r, g, b, threshold = 128) {
  return getLuminance(r, g, b) < threshold;
}

/**
 * 获取对比色（黑色或白色）
 * @param {number} r - 红色分量 (0-255)
 * @param {number} g - 绿色分量 (0-255)
 * @param {number} b - 蓝色分量 (0-255)
 * @returns {string} 对比色 ("black" 或 "white")
 */
export function getContrastColor(r, g, b) {
  return isDarkColor(r, g, b) ? 'white' : 'black';
}

/**
 * 混合两种颜色
 * @param {Object} color1 - 颜色1 {r, g, b}
 * @param {Object} color2 - 颜色2 {r, g, b}
 * @param {number} ratio - 混合比例 (0-1)，0为完全是color1，1为完全是color2
 * @returns {Object} 混合后的颜色 {r, g, b}
 */
export function blendColors(color1, color2, ratio = 0.5) {
  if (ratio < 0 || ratio > 1) {
    throw new Error('Ratio must be between 0 and 1');
  }
  
  return {
    r: Math.round(color1.r * (1 - ratio) + color2.r * ratio),
    g: Math.round(color1.g * (1 - ratio) + color2.g * ratio),
    b: Math.round(color1.b * (1 - ratio) + color2.b * ratio)
  };
}

/**
 * 调整颜色亮度
 * @param {Object} color - 颜色 {r, g, b}
 * @param {number} factor - 调整因子 (-1到1)，负数变暗，正数变亮
 * @returns {Object} 调整后的颜色 {r, g, b}
 */
export function adjustBrightness(color, factor) {
  if (factor < -1 || factor > 1) {
    throw new Error('Factor must be between -1 and 1');
  }
  
  const adjust = (value) => {
    if (factor < 0) {
      return Math.round(value * (1 + factor));
    } else {
      return Math.round(value + (255 - value) * factor);
    }
  };
  
  return {
    r: Math.max(0, Math.min(255, adjust(color.r))),
    g: Math.max(0, Math.min(255, adjust(color.g))),
    b: Math.max(0, Math.min(255, adjust(color.b)))
  };
}

/**
 * 生成颜色调色板
 * @param {Object} baseColor - 基础颜色 {r, g, b}
 * @param {number} count - 颜色数量
 * @param {string} type - 类型：'monochromatic', 'analogous', 'complementary'
 * @returns {Array} 颜色数组
 */
export function generatePalette(baseColor, count = 5, type = 'monochromatic') {
  const hsl = rgbToHsl(baseColor.r, baseColor.g, baseColor.b);
  const colors = [];
  
  switch (type) {
    case 'monochromatic':
      for (let i = 0; i < count; i++) {
        const lightness = Math.max(10, Math.min(90, hsl.l + (i - Math.floor(count / 2)) * 20));
        colors.push(hslToRgb(hsl.h, hsl.s, lightness));
      }
      break;
      
    case 'analogous':
      for (let i = 0; i < count; i++) {
        const hue = (hsl.h + (i - Math.floor(count / 2)) * 30) % 360;
        colors.push(hslToRgb(hue < 0 ? hue + 360 : hue, hsl.s, hsl.l));
      }
      break;
      
    case 'complementary':
      colors.push(baseColor);
      if (count > 1) {
        const complementaryHue = (hsl.h + 180) % 360;
        colors.push(hslToRgb(complementaryHue, hsl.s, hsl.l));
      }
      // 填充其他颜色
      while (colors.length < count) {
        const randomHue = Math.floor(Math.random() * 360);
        colors.push(hslToRgb(randomHue, hsl.s, hsl.l));
      }
      break;
      
    default:
      throw new Error(`Unknown palette type: ${type}`);
  }
  
  return colors.slice(0, count);
}

/**
 * 将RGB颜色转换为ANSI 256色代码
 * @param {number} r - 红色分量 (0-255)
 * @param {number} g - 绿色分量 (0-255)
 * @param {number} b - 蓝色分量 (0-255)
 * @returns {number} ANSI 256色代码
 */
export function rgbToAnsi256(r, g, b) {
  // 灰度检查
  if (r === g && g === b) {
    if (r < 8) return 16;
    if (r > 248) return 231;
    return Math.round(((r - 8) / 247) * 24) + 232;
  }
  
  // 转换为6x6x6颜色立方体
  const toColorCube = (value) => Math.round(value / 255 * 5);
  
  const cr = toColorCube(r);
  const cg = toColorCube(g);
  const cb = toColorCube(b);
  
  return 16 + (36 * cr) + (6 * cg) + cb;
}

/**
 * 创建渐变色
 * @param {Object} startColor - 起始颜色 {r, g, b}
 * @param {Object} endColor - 结束颜色 {r, g, b}
 * @param {number} steps - 步数
 * @returns {Array} 渐变色数组
 */
export function createGradient(startColor, endColor, steps) {
  if (steps < 2) {
    throw new Error('Steps must be at least 2');
  }
  
  const colors = [];
  
  for (let i = 0; i < steps; i++) {
    const ratio = i / (steps - 1);
    colors.push(blendColors(startColor, endColor, ratio));
  }
  
  return colors;
}

/**
 * 颜色距离计算（欧几里得距离）
 * @param {Object} color1 - 颜色1 {r, g, b}
 * @param {Object} color2 - 颜色2 {r, g, b}
 * @returns {number} 颜色距离
 */
export function colorDistance(color1, color2) {
  const dr = color1.r - color2.r;
  const dg = color1.g - color2.g;
  const db = color1.b - color2.b;
  
  return Math.sqrt(dr * dr + dg * dg + db * db);
}

/**
 * 查找最接近的颜色
 * @param {Object} targetColor - 目标颜色 {r, g, b}
 * @param {Array} colorPalette - 颜色调色板
 * @returns {Object} 最接近的颜色
 */
export function findClosestColor(targetColor, colorPalette) {
  if (!colorPalette || colorPalette.length === 0) {
    throw new Error('Color palette cannot be empty');
  }
  
  let closestColor = colorPalette[0];
  let minDistance = colorDistance(targetColor, closestColor);
  
  for (let i = 1; i < colorPalette.length; i++) {
    const distance = colorDistance(targetColor, colorPalette[i]);
    if (distance < minDistance) {
      minDistance = distance;
      closestColor = colorPalette[i];
    }
  }
  
  return closestColor;
}
