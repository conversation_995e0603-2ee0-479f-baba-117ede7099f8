/**
 * 验证工具函数
 * @original: 原始文件中的验证相关函数
 */

/**
 * 检查值是否为空
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为空
 */
export function isEmpty(value) {
  if (value == null) {
    return true;
  }
  
  if (Array.isArray(value) || typeof value === 'string' || isArguments(value)) {
    return !value.length;
  }
  
  const tag = Object.prototype.toString.call(value);
  if (tag === '[object Map]' || tag === '[object Set]') {
    return !value.size;
  }
  
  if (isBuffer(value)) {
    return !value.length;
  }
  
  const proto = Object.getPrototypeOf(value);
  if (proto === null) {
    return !Object.keys(value).length;
  }
  
  for (const key in value) {
    if (Object.prototype.hasOwnProperty.call(value, key)) {
      return false;
    }
  }
  
  return true;
}

/**
 * 检查值是否为arguments对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为arguments对象
 */
function isArguments(value) {
  return Object.prototype.toString.call(value) === '[object Arguments]';
}

/**
 * 检查值是否为Buffer
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为Buffer
 */
function isBuffer(value) {
  return value != null && !Array.isArray(value) && 
         typeof value.constructor === 'function' && 
         typeof value.constructor.isBuffer === 'function' && 
         value.constructor.isBuffer(value);
}

/**
 * 检查值是否为数字
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为数字
 */
export function isNumber(value) {
  return typeof value === 'number' || 
         (isObjectLike(value) && Object.prototype.toString.call(value) === '[object Number]');
}

/**
 * 检查值是否为有限数字
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为有限数字
 */
export function isFinite(value) {
  return typeof value === 'number' && Number.isFinite(value);
}

/**
 * 检查值是否为整数
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为整数
 */
export function isInteger(value) {
  return typeof value === 'number' && Number.isInteger(value);
}

/**
 * 检查值是否为安全整数
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为安全整数
 */
export function isSafeInteger(value) {
  return Number.isSafeInteger(value);
}

/**
 * 检查值是否为NaN
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为NaN
 */
export function isNaN(value) {
  return isNumber(value) && value !== +value;
}

/**
 * 检查值是否为字符串
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为字符串
 */
export function isString(value) {
  return typeof value === 'string' || 
         (isObjectLike(value) && Object.prototype.toString.call(value) === '[object String]');
}

/**
 * 检查值是否为布尔值
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为布尔值
 */
export function isBoolean(value) {
  return value === true || value === false || 
         (isObjectLike(value) && Object.prototype.toString.call(value) === '[object Boolean]');
}

/**
 * 检查值是否为数组
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为数组
 */
export function isArray(value) {
  return Array.isArray(value);
}

/**
 * 检查值是否为对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为对象
 */
export function isObject(value) {
  const type = typeof value;
  return value != null && (type === 'object' || type === 'function');
}

/**
 * 检查值是否为类对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为类对象
 */
function isObjectLike(value) {
  return value != null && typeof value === 'object';
}

/**
 * 检查值是否为函数
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为函数
 */
export function isFunction(value) {
  if (!isObject(value)) {
    return false;
  }
  const tag = Object.prototype.toString.call(value);
  return tag === '[object Function]' || tag === '[object GeneratorFunction]' || 
         tag === '[object AsyncFunction]' || tag === '[object Proxy]';
}

/**
 * 检查值是否为日期对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为日期对象
 */
export function isDate(value) {
  return isObjectLike(value) && Object.prototype.toString.call(value) === '[object Date]';
}

/**
 * 检查值是否为正则表达式
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为正则表达式
 */
export function isRegExp(value) {
  return isObjectLike(value) && Object.prototype.toString.call(value) === '[object RegExp]';
}

/**
 * 检查值是否为Error对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为Error对象
 */
export function isError(value) {
  if (!isObjectLike(value)) {
    return false;
  }
  const tag = Object.prototype.toString.call(value);
  return tag === '[object Error]' || tag === '[object DOMException]' || 
         (typeof value.message === 'string' && typeof value.name === 'string' && !isPlainObject(value));
}

/**
 * 检查值是否为纯对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为纯对象
 */
function isPlainObject(value) {
  if (!isObjectLike(value) || Object.prototype.toString.call(value) !== '[object Object]') {
    return false;
  }
  
  if (Object.getPrototypeOf(value) === null) {
    return true;
  }
  
  let proto = value;
  while (Object.getPrototypeOf(proto) !== null) {
    proto = Object.getPrototypeOf(proto);
  }
  
  return Object.getPrototypeOf(value) === proto;
}

/**
 * 检查值是否为undefined
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为undefined
 */
export function isUndefined(value) {
  return value === undefined;
}

/**
 * 检查值是否为null
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为null
 */
export function isNull(value) {
  return value === null;
}

/**
 * 检查值是否为null或undefined
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为null或undefined
 */
export function isNil(value) {
  return value == null;
}

/**
 * 检查字符串是否为有效的邮箱地址
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否为有效邮箱
 */
export function isValidEmail(email) {
  if (!isString(email)) {
    return false;
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 检查字符串是否为有效的URL
 * @param {string} url - URL字符串
 * @returns {boolean} 是否为有效URL
 */
export function isValidUrl(url) {
  if (!isString(url)) {
    return false;
  }
  
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * 检查字符串是否为有效的UUID
 * @param {string} uuid - UUID字符串
 * @returns {boolean} 是否为有效UUID
 */
export function isValidUUID(uuid) {
  if (!isString(uuid)) {
    return false;
  }
  
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * 检查字符串是否为有效的JSON
 * @param {string} jsonString - JSON字符串
 * @returns {boolean} 是否为有效JSON
 */
export function isValidJSON(jsonString) {
  if (!isString(jsonString)) {
    return false;
  }
  
  try {
    JSON.parse(jsonString);
    return true;
  } catch {
    return false;
  }
}

/**
 * 检查数字是否在指定范围内
 * @param {number} value - 要检查的数字
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @param {boolean} inclusive - 是否包含边界值，默认为true
 * @returns {boolean} 是否在范围内
 */
export function isInRange(value, min, max, inclusive = true) {
  if (!isNumber(value)) {
    return false;
  }
  
  if (inclusive) {
    return value >= min && value <= max;
  } else {
    return value > min && value < max;
  }
}

/**
 * 检查字符串长度是否在指定范围内
 * @param {string} str - 要检查的字符串
 * @param {number} minLength - 最小长度
 * @param {number} maxLength - 最大长度
 * @returns {boolean} 长度是否在范围内
 */
export function isValidLength(str, minLength, maxLength) {
  if (!isString(str)) {
    return false;
  }
  
  const length = str.length;
  return length >= minLength && length <= maxLength;
}

/**
 * 检查字符串是否只包含字母
 * @param {string} str - 要检查的字符串
 * @returns {boolean} 是否只包含字母
 */
export function isAlpha(str) {
  if (!isString(str)) {
    return false;
  }
  
  return /^[a-zA-Z]+$/.test(str);
}

/**
 * 检查字符串是否只包含数字
 * @param {string} str - 要检查的字符串
 * @returns {boolean} 是否只包含数字
 */
export function isNumeric(str) {
  if (!isString(str)) {
    return false;
  }
  
  return /^[0-9]+$/.test(str);
}

/**
 * 检查字符串是否只包含字母和数字
 * @param {string} str - 要检查的字符串
 * @returns {boolean} 是否只包含字母和数字
 */
export function isAlphanumeric(str) {
  if (!isString(str)) {
    return false;
  }
  
  return /^[a-zA-Z0-9]+$/.test(str);
}

/**
 * 检查字符串是否为有效的十六进制
 * @param {string} str - 要检查的字符串
 * @returns {boolean} 是否为有效十六进制
 */
export function isHexadecimal(str) {
  if (!isString(str)) {
    return false;
  }
  
  return /^[0-9a-fA-F]+$/.test(str);
}

/**
 * 检查字符串是否为有效的Base64
 * @param {string} str - 要检查的字符串
 * @returns {boolean} 是否为有效Base64
 */
export function isBase64(str) {
  if (!isString(str)) {
    return false;
  }
  
  try {
    return Buffer.from(str, 'base64').toString('base64') === str;
  } catch {
    return false;
  }
}

/**
 * 检查IP地址是否有效
 * @param {string} ip - IP地址
 * @returns {boolean} 是否为有效IP地址
 */
export function isValidIP(ip) {
  if (!isString(ip)) {
    return false;
  }
  
  // IPv4检查
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  if (ipv4Regex.test(ip)) {
    return true;
  }
  
  // IPv6检查（简化版）
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  return ipv6Regex.test(ip);
}

/**
 * 检查端口号是否有效
 * @param {number|string} port - 端口号
 * @returns {boolean} 是否为有效端口号
 */
export function isValidPort(port) {
  const portNum = parseInt(port, 10);
  return isInteger(portNum) && portNum >= 1 && portNum <= 65535;
}

/**
 * 检查密码强度
 * @param {string} password - 密码
 * @param {Object} options - 选项
 * @param {number} options.minLength - 最小长度，默认8
 * @param {boolean} options.requireUppercase - 是否需要大写字母，默认true
 * @param {boolean} options.requireLowercase - 是否需要小写字母，默认true
 * @param {boolean} options.requireNumbers - 是否需要数字，默认true
 * @param {boolean} options.requireSpecialChars - 是否需要特殊字符，默认true
 * @returns {Object} 包含isValid和errors的对象
 */
export function validatePasswordStrength(password, options = {}) {
  const {
    minLength = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumbers = true,
    requireSpecialChars = true
  } = options;
  
  const errors = [];
  
  if (!isString(password)) {
    errors.push('Password must be a string');
    return { isValid: false, errors };
  }
  
  if (password.length < minLength) {
    errors.push(`Password must be at least ${minLength} characters long`);
  }
  
  if (requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (requireNumbers && !/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证对象是否符合指定的模式
 * @param {Object} obj - 要验证的对象
 * @param {Object} schema - 验证模式
 * @returns {Object} 包含isValid和errors的对象
 */
export function validateSchema(obj, schema) {
  const errors = [];
  
  if (!isObject(obj)) {
    errors.push('Value must be an object');
    return { isValid: false, errors };
  }
  
  for (const [key, validator] of Object.entries(schema)) {
    const value = obj[key];
    
    if (isFunction(validator)) {
      const result = validator(value);
      if (result !== true) {
        errors.push(`${key}: ${result}`);
      }
    } else if (isObject(validator)) {
      if (validator.required && isNil(value)) {
        errors.push(`${key} is required`);
        continue;
      }
      
      if (!isNil(value) && validator.type) {
        const typeCheck = getTypeChecker(validator.type);
        if (!typeCheck(value)) {
          errors.push(`${key} must be of type ${validator.type}`);
        }
      }
      
      if (validator.validate && isFunction(validator.validate)) {
        const result = validator.validate(value);
        if (result !== true) {
          errors.push(`${key}: ${result}`);
        }
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 获取类型检查函数
 * @param {string} type - 类型名称
 * @returns {Function} 类型检查函数
 */
function getTypeChecker(type) {
  const typeCheckers = {
    'string': isString,
    'number': isNumber,
    'boolean': isBoolean,
    'array': isArray,
    'object': isObject,
    'function': isFunction,
    'date': isDate,
    'regexp': isRegExp,
    'error': isError
  };
  
  return typeCheckers[type] || (() => false);
}
