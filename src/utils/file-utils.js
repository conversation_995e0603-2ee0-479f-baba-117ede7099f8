/**
 * 文件处理工具函数
 * @original: 原始文件中的文件处理相关函数
 */

import { join } from 'path';
import { tmpdir } from 'os';
import { randomUUID } from 'crypto';

/**
 * 生成临时文件路径
 * @param {string} prefix - 文件名前缀，默认为'claude-prompt'
 * @param {string} extension - 文件扩展名，默认为'.md'
 * @returns {string} 临时文件的完整路径
 * @original: 原始文件L1299-1302中的rmB函数
 */
export function generateTempFilePath(prefix = "claude-prompt", extension = ".md") {
  const uuid = randomUUID();
  return join(tmpdir(), `${prefix}-${uuid}${extension}`);
}

/**
 * 检查文件是否为PDF格式
 * @param {string} filename - 文件名
 * @returns {boolean} 是否为PDF文件
 * @original: 原始文件L2931-2934中的GK1函数
 */
export function isPdfFile(filename) {
  const extension = filename.startsWith(".") ? filename.slice(1) : filename;
  const pdfExtensions = new Set(["pdf"]);
  return pdfExtensions.has(extension.toLowerCase());
}

/**
 * 检查文件大小是否超过限制
 * @param {number} fileSize - 文件大小（字节）
 * @param {number} maxSize - 最大允许大小，默认为33554432字节（32MB）
 * @returns {boolean} 是否超过大小限制
 * @original: 原始文件中的文件大小检查逻辑
 */
export function isFileSizeExceeded(fileSize, maxSize = 33554432) {
  return fileSize > maxSize;
}

/**
 * 获取文件编码
 * @param {string} filePath - 文件路径
 * @returns {string} 文件编码（'utf8'或'binary'）
 * @todo: 实现文件编码检测逻辑
 */
export function getFileEncoding(filePath) {
  // @todo: 实现更复杂的编码检测逻辑
  // 目前简单返回utf8，实际应该检测文件内容
  return 'utf8';
}

/**
 * 安全地读取文件内容
 * @param {string} filePath - 文件路径
 * @returns {Promise<{content: string, encoding: string}>} 文件内容和编码信息
 * @original: 原始文件中的文件读取逻辑
 */
export async function safeReadFile(filePath) {
  const fs = await import('fs');
  
  try {
    const stats = fs.statSync(filePath);
    const encoding = getFileEncoding(filePath);
    
    let content = fs.readFileSync(filePath, { encoding });
    
    // 统一换行符为LF
    content = content.replaceAll('\r\n', '\n');
    
    return {
      content,
      encoding,
      size: stats.size,
      mtime: stats.mtimeMs
    };
  } catch (error) {
    throw new Error(`Failed to read file ${filePath}: ${error.message}`);
  }
}

/**
 * 检查路径是否安全（防止路径遍历攻击）
 * @param {string} filePath - 要检查的文件路径
 * @param {string} basePath - 基础路径
 * @returns {boolean} 路径是否安全
 */
export function isPathSafe(filePath, basePath) {
  const path = require('path');
  const resolvedPath = path.resolve(basePath, filePath);
  const resolvedBase = path.resolve(basePath);
  
  return resolvedPath.startsWith(resolvedBase);
}

/**
 * 创建目录（如果不存在）
 * @param {string} dirPath - 目录路径
 * @returns {Promise<void>}
 */
export async function ensureDirectory(dirPath) {
  const fs = await import('fs');
  
  try {
    await fs.promises.access(dirPath);
  } catch (error) {
    if (error.code === 'ENOENT') {
      await fs.promises.mkdir(dirPath, { recursive: true });
    } else {
      throw error;
    }
  }
}

/**
 * 获取文件的MIME类型
 * @param {string} filename - 文件名
 * @returns {string} MIME类型
 */
export function getMimeType(filename) {
  const extension = filename.split('.').pop()?.toLowerCase();
  
  const mimeTypes = {
    'js': 'application/javascript',
    'json': 'application/json',
    'md': 'text/markdown',
    'txt': 'text/plain',
    'html': 'text/html',
    'css': 'text/css',
    'pdf': 'application/pdf',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'svg': 'image/svg+xml'
  };
  
  return mimeTypes[extension] || 'application/octet-stream';
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 检查文件是否为文本文件
 * @param {string} filename - 文件名
 * @returns {boolean} 是否为文本文件
 */
export function isTextFile(filename) {
  const textExtensions = new Set([
    'txt', 'md', 'js', 'ts', 'jsx', 'tsx', 'json', 'xml', 'html', 'css', 'scss', 'sass',
    'py', 'java', 'c', 'cpp', 'h', 'hpp', 'cs', 'php', 'rb', 'go', 'rs', 'swift',
    'yml', 'yaml', 'toml', 'ini', 'cfg', 'conf', 'log', 'sql', 'sh', 'bash', 'zsh'
  ]);
  
  const extension = filename.split('.').pop()?.toLowerCase();
  return textExtensions.has(extension);
}

/**
 * 清理文件名（移除不安全字符）
 * @param {string} filename - 原始文件名
 * @returns {string} 清理后的文件名
 */
export function sanitizeFilename(filename) {
  // 移除或替换不安全的字符
  return filename
    .replace(/[<>:"/\\|?*]/g, '_')  // 替换不安全字符为下划线
    .replace(/\s+/g, '_')           // 替换空格为下划线
    .replace(/_{2,}/g, '_')         // 合并多个下划线
    .replace(/^_+|_+$/g, '');       // 移除开头和结尾的下划线
}
