/**
 * 加密和哈希工具函数
 * @original: 原始文件中的加密相关函数
 */

import { randomUUID, createHash, createHmac } from 'crypto';

/**
 * 生成UUID
 * @returns {string} UUID字符串
 * @original: 原始文件L1299-1302中使用的randomUUID
 */
export function generateUUID() {
  return randomUUID();
}

/**
 * 生成会话ID
 * @returns {string} 会话ID
 * @original: 原始文件L2002-2004中的Mj0函数
 */
export function generateSessionId() {
  return randomUUID();
}

/**
 * 创建MD5哈希
 * @param {string} data - 要哈希的数据
 * @returns {string} MD5哈希值
 */
export function createMD5Hash(data) {
  return createHash('md5').update(data).digest('hex');
}

/**
 * 创建SHA256哈希
 * @param {string} data - 要哈希的数据
 * @returns {string} SHA256哈希值
 */
export function createSHA256Hash(data) {
  return createHash('sha256').update(data).digest('hex');
}

/**
 * 创建SHA1哈希
 * @param {string} data - 要哈希的数据
 * @returns {string} SHA1哈希值
 */
export function createSHA1Hash(data) {
  return createHash('sha1').update(data).digest('hex');
}

/**
 * 创建HMAC签名
 * @param {string} data - 要签名的数据
 * @param {string} secret - 密钥
 * @param {string} algorithm - 算法，默认为'sha256'
 * @returns {string} HMAC签名
 */
export function createHMACSignature(data, secret, algorithm = 'sha256') {
  return createHmac(algorithm, secret).update(data).digest('hex');
}

/**
 * 验证HMAC签名
 * @param {string} data - 原始数据
 * @param {string} signature - 要验证的签名
 * @param {string} secret - 密钥
 * @param {string} algorithm - 算法，默认为'sha256'
 * @returns {boolean} 签名是否有效
 */
export function verifyHMACSignature(data, signature, secret, algorithm = 'sha256') {
  const expectedSignature = createHMACSignature(data, secret, algorithm);
  return timingSafeEqual(Buffer.from(signature, 'hex'), Buffer.from(expectedSignature, 'hex'));
}

/**
 * 时间安全的字符串比较
 * @param {Buffer} a - 缓冲区A
 * @param {Buffer} b - 缓冲区B
 * @returns {boolean} 是否相等
 */
function timingSafeEqual(a, b) {
  if (a.length !== b.length) {
    return false;
  }
  
  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a[i] ^ b[i];
  }
  
  return result === 0;
}

/**
 * 生成随机字符串
 * @param {number} length - 字符串长度
 * @param {string} charset - 字符集，默认为字母数字
 * @returns {string} 随机字符串
 */
export function generateRandomString(length = 32, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
  let result = '';
  const crypto = require('crypto');
  
  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, charset.length);
    result += charset[randomIndex];
  }
  
  return result;
}

/**
 * 生成随机十六进制字符串
 * @param {number} byteLength - 字节长度
 * @returns {string} 十六进制字符串
 */
export function generateRandomHex(byteLength = 16) {
  const crypto = require('crypto');
  return crypto.randomBytes(byteLength).toString('hex');
}

/**
 * 生成随机Base64字符串
 * @param {number} byteLength - 字节长度
 * @returns {string} Base64字符串
 */
export function generateRandomBase64(byteLength = 16) {
  const crypto = require('crypto');
  return crypto.randomBytes(byteLength).toString('base64');
}

/**
 * 简单的字符串混淆（不是真正的加密）
 * @param {string} text - 要混淆的文本
 * @param {number} shift - 位移量
 * @returns {string} 混淆后的文本
 */
export function obfuscateString(text, shift = 13) {
  return text.split('').map(char => {
    const code = char.charCodeAt(0);
    if (code >= 65 && code <= 90) {
      // 大写字母
      return String.fromCharCode(((code - 65 + shift) % 26) + 65);
    } else if (code >= 97 && code <= 122) {
      // 小写字母
      return String.fromCharCode(((code - 97 + shift) % 26) + 97);
    }
    return char;
  }).join('');
}

/**
 * 简单的字符串反混淆
 * @param {string} text - 要反混淆的文本
 * @param {number} shift - 位移量
 * @returns {string} 反混淆后的文本
 */
export function deobfuscateString(text, shift = 13) {
  return obfuscateString(text, 26 - shift);
}

/**
 * Base64编码
 * @param {string} text - 要编码的文本
 * @returns {string} Base64编码后的字符串
 */
export function base64Encode(text) {
  return Buffer.from(text, 'utf8').toString('base64');
}

/**
 * Base64解码
 * @param {string} encodedText - Base64编码的字符串
 * @returns {string} 解码后的文本
 */
export function base64Decode(encodedText) {
  return Buffer.from(encodedText, 'base64').toString('utf8');
}

/**
 * URL安全的Base64编码
 * @param {string} text - 要编码的文本
 * @returns {string} URL安全的Base64字符串
 */
export function base64UrlEncode(text) {
  return Buffer.from(text, 'utf8')
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * URL安全的Base64解码
 * @param {string} encodedText - URL安全的Base64字符串
 * @returns {string} 解码后的文本
 */
export function base64UrlDecode(encodedText) {
  // 添加填充
  let padded = encodedText;
  while (padded.length % 4) {
    padded += '=';
  }
  
  // 替换URL安全字符
  const base64 = padded.replace(/-/g, '+').replace(/_/g, '/');
  
  return Buffer.from(base64, 'base64').toString('utf8');
}

/**
 * 计算文件哈希
 * @param {string} filePath - 文件路径
 * @param {string} algorithm - 哈希算法，默认为'sha256'
 * @returns {Promise<string>} 文件哈希值
 */
export async function calculateFileHash(filePath, algorithm = 'sha256') {
  const fs = await import('fs');
  const hash = createHash(algorithm);
  
  return new Promise((resolve, reject) => {
    const stream = fs.createReadStream(filePath);
    
    stream.on('data', (data) => {
      hash.update(data);
    });
    
    stream.on('end', () => {
      resolve(hash.digest('hex'));
    });
    
    stream.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * 生成密码哈希（使用简单的盐值）
 * @param {string} password - 密码
 * @param {string} salt - 盐值，如果不提供则自动生成
 * @returns {Object} 包含哈希和盐值的对象
 */
export function hashPassword(password, salt) {
  if (!salt) {
    salt = generateRandomHex(16);
  }
  
  const hash = createHash('sha256')
    .update(password + salt)
    .digest('hex');
  
  return {
    hash,
    salt
  };
}

/**
 * 验证密码
 * @param {string} password - 要验证的密码
 * @param {string} hash - 存储的哈希值
 * @param {string} salt - 盐值
 * @returns {boolean} 密码是否正确
 */
export function verifyPassword(password, hash, salt) {
  const { hash: computedHash } = hashPassword(password, salt);
  return computedHash === hash;
}

/**
 * 生成JWT风格的token（简化版，不是真正的JWT）
 * @param {Object} payload - 载荷数据
 * @param {string} secret - 密钥
 * @param {number} expiresIn - 过期时间（秒）
 * @returns {string} Token字符串
 */
export function generateSimpleToken(payload, secret, expiresIn = 3600) {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };
  
  const now = Math.floor(Date.now() / 1000);
  const tokenPayload = {
    ...payload,
    iat: now,
    exp: now + expiresIn
  };
  
  const encodedHeader = base64UrlEncode(JSON.stringify(header));
  const encodedPayload = base64UrlEncode(JSON.stringify(tokenPayload));
  const signature = createHMACSignature(`${encodedHeader}.${encodedPayload}`, secret, 'sha256');
  const encodedSignature = base64UrlEncode(signature);
  
  return `${encodedHeader}.${encodedPayload}.${encodedSignature}`;
}

/**
 * 验证简单token
 * @param {string} token - 要验证的token
 * @param {string} secret - 密钥
 * @returns {Object|null} 解码后的载荷，如果无效则返回null
 */
export function verifySimpleToken(token, secret) {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }
    
    const [encodedHeader, encodedPayload, encodedSignature] = parts;
    
    // 验证签名
    const expectedSignature = createHMACSignature(`${encodedHeader}.${encodedPayload}`, secret, 'sha256');
    const expectedEncodedSignature = base64UrlEncode(expectedSignature);
    
    if (encodedSignature !== expectedEncodedSignature) {
      return null;
    }
    
    // 解码载荷
    const payload = JSON.parse(base64UrlDecode(encodedPayload));
    
    // 检查过期时间
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < now) {
      return null;
    }
    
    return payload;
  } catch (error) {
    return null;
  }
}

/**
 * 生成API密钥
 * @param {string} prefix - 前缀，默认为'ak'
 * @param {number} length - 密钥长度，默认为32
 * @returns {string} API密钥
 */
export function generateApiKey(prefix = 'ak', length = 32) {
  const randomPart = generateRandomString(length, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789');
  return `${prefix}_${randomPart}`;
}

/**
 * 验证API密钥格式
 * @param {string} apiKey - API密钥
 * @param {string} expectedPrefix - 期望的前缀
 * @returns {boolean} 格式是否正确
 */
export function validateApiKeyFormat(apiKey, expectedPrefix = 'ak') {
  const pattern = new RegExp(`^${expectedPrefix}_[A-Za-z0-9]+$`);
  return pattern.test(apiKey);
}
