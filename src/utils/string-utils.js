/**
 * 字符串处理工具函数
 * @original: 原始文件中的字符串处理相关函数
 */

/**
 * 去除字符串两端的空白字符
 * @param {string} str - 要处理的字符串
 * @returns {string} 处理后的字符串
 * @original: 原始文件L1322-1325中的DlB函数
 */
export function trimString(str) {
  if (!str) return str;
  
  // 找到字符串末尾第一个非空白字符的位置
  let endIndex = str.length;
  const whitespaceRegex = /\s/;
  
  while (endIndex-- && whitespaceRegex.test(str.charAt(endIndex)));
  
  // 去除开头和结尾的空白字符
  return str.slice(0, endIndex + 1).replace(/^\s+/, "");
}

/**
 * 将字符串转换为数字
 * @param {any} value - 要转换的值
 * @returns {number} 转换后的数字，如果无法转换则返回NaN
 * @original: 原始文件L1331-1342中的WlB函数
 */
export function toNumber(value) {
  if (typeof value === "number") return value;
  
  // 检查是否为Symbol类型（无法转换为数字）
  if (typeof value === "symbol") return NaN;
  
  // 如果是对象，尝试调用valueOf方法
  if (isObject(value)) {
    const primitive = typeof value.valueOf === "function" ? value.valueOf() : value;
    value = isObject(primitive) ? primitive + "" : primitive;
  }
  
  if (typeof value !== "string") return value === 0 ? value : +value;
  
  value = trimString(value);
  
  // 检查各种数字格式
  const binaryRegex = /^0b[01]+$/i;
  const octalRegex = /^0o[0-7]+$/i;
  const hexRegex = /^[-+]0x[0-9a-f]+$/i;
  
  if (binaryRegex.test(value) || octalRegex.test(value)) {
    return parseInt(value, value.charAt(1) === 'b' ? 2 : 8);
  }
  
  if (hexRegex.test(value)) {
    return parseInt(value, 16);
  }
  
  return +value;
}

/**
 * 将值转换为整数
 * @param {any} value - 要转换的值
 * @returns {number} 转换后的整数
 * @original: 原始文件L1355-1360中的VlB函数
 */
export function toInteger(value) {
  const number = toFinite(value);
  const remainder = number % 1;
  return number === number ? (remainder ? number - remainder : number) : 0;
}

/**
 * 将值转换为有限数字
 * @param {any} value - 要转换的值
 * @returns {number} 转换后的有限数字
 * @original: 原始文件L1346-1353中的XlB函数
 */
export function toFinite(value) {
  if (!value) return value === 0 ? value : 0;
  
  value = toNumber(value);
  
  if (value === Infinity || value === -Infinity) {
    const sign = value < 0 ? -1 : 1;
    return sign * Number.MAX_VALUE;
  }
  
  return value === value ? value : 0;
}

/**
 * 检查值是否为对象
 * @param {any} value - 要检查的值
 * @returns {boolean} 是否为对象
 */
function isObject(value) {
  const type = typeof value;
  return value != null && (type === "object" || type === "function");
}

/**
 * 将字符串分割为字符数组（支持Unicode）
 * @param {string} str - 要分割的字符串
 * @returns {string[]} 字符数组
 * @original: 原始文件L1615-1617中的mnB函数
 */
export function stringToArray(str) {
  return hasUnicodeWord(str) ? unicodeToArray(str) : str.split("");
}

/**
 * 检查字符串是否包含Unicode字符
 * @param {string} str - 要检查的字符串
 * @returns {boolean} 是否包含Unicode字符
 * @original: 原始文件L1583-1585中的OnB函数
 */
export function hasUnicodeWord(str) {
  const unicodeRegex = /[\u200d\ud800-\udfff\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff\ufe0e\ufe0f]/;
  return unicodeRegex.test(str);
}

/**
 * 将Unicode字符串转换为字符数组
 * @param {string} str - Unicode字符串
 * @returns {string[]} 字符数组
 * @original: 原始文件L1611-1613中的unB函数
 */
export function unicodeToArray(str) {
  const unicodeRegex = /\ud83c[\udffb-\udfff](?=\ud83c[\udffb-\udfff])|(?:[^\ud800-\udfff][\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]?|[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\ud800-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?(?:\u200d(?:[^\ud800-\udfff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?)*/g;
  return str.match(unicodeRegex) || [];
}

/**
 * 创建字符串大小写转换函数
 * @param {string} methodName - 转换方法名（如'toUpperCase'）
 * @returns {Function} 转换函数
 * @original: 原始文件L1619-1627中的dnB函数
 */
export function createCaseConverter(methodName) {
  return function(str) {
    str = toString(str);
    const strArray = hasUnicodeWord(str) ? stringToArray(str) : undefined;
    const firstChar = strArray ? strArray[0] : str.charAt(0);
    const restChars = strArray ? strArray.slice(1).join("") : str.slice(1);
    return firstChar[methodName]() + restChars;
  };
}

/**
 * 将值转换为字符串
 * @param {any} value - 要转换的值
 * @returns {string} 转换后的字符串
 */
export function toString(value) {
  return value == null ? "" : value + "";
}

/**
 * 首字母大写
 * @param {string} str - 要处理的字符串
 * @returns {string} 首字母大写的字符串
 * @original: 原始文件L1631-1633中的lnB函数
 */
export const capitalize = createCaseConverter("toUpperCase");

/**
 * 将字符串转换为驼峰命名格式
 * @param {string} str - 要转换的字符串
 * @returns {string} 驼峰命名格式的字符串
 */
export function toCamelCase(str) {
  return capitalize(toString(str).toLowerCase());
}

/**
 * 限制数值在指定范围内
 * @param {number} number - 要限制的数值
 * @param {number} lower - 下限
 * @param {number} upper - 上限
 * @returns {number} 限制后的数值
 * @original: 原始文件L1635-1641中的pnB函数
 */
export function clamp(number, lower, upper) {
  if (number === number) {
    if (upper !== undefined) {
      number = number <= upper ? number : upper;
    }
    if (lower !== undefined) {
      number = number >= lower ? number : lower;
    }
  }
  return number;
}

/**
 * URL编码函数
 * @param {string} str - 要编码的字符串
 * @returns {string} 编码后的字符串
 * @original: 原始文件L2208-2221中的Rn0函数
 */
export function encodeURIComponentCustom(str) {
  const replacements = {
    "!": "%21",
    "'": "%27",
    "(": "%28",
    ")": "%29",
    "~": "%7E",
    "%20": "+",
    "%00": "\x00"
  };
  
  return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function(match) {
    return replacements[match];
  });
}
