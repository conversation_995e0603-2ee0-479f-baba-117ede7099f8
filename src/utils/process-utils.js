/**
 * 进程处理工具函数
 * @original: 原始文件中的进程处理相关函数
 */

import { spawn, exec, execSync } from 'child_process';
import { getPlatform } from './path-utils.js';

/**
 * 执行命令并返回结果
 * @param {string} command - 要执行的命令
 * @param {Object} options - 选项
 * @returns {Promise<Object>} 执行结果
 */
export async function executeCommand(command, options = {}) {
  return new Promise((resolve, reject) => {
    exec(command, {
      encoding: 'utf8',
      maxBuffer: 1024 * 1024 * 10, // 10MB
      timeout: 30000,
      ...options
    }, (error, stdout, stderr) => {
      if (error) {
        reject({
          error,
          stdout,
          stderr,
          code: error.code
        });
      } else {
        resolve({
          stdout,
          stderr,
          code: 0
        });
      }
    });
  });
}

/**
 * 同步执行命令
 * @param {string} command - 要执行的命令
 * @param {Object} options - 选项
 * @returns {string} 命令输出
 */
export function executeCommandSync(command, options = {}) {
  try {
    return execSync(command, {
      encoding: 'utf8',
      maxBuffer: 1024 * 1024 * 10,
      ...options
    });
  } catch (error) {
    throw new Error(`Command failed: ${error.message}`);
  }
}

/**
 * 生成进程（spawn）
 * @param {string} command - 命令
 * @param {Array} args - 参数数组
 * @param {Object} options - 选项
 * @returns {Promise<Object>} 进程结果
 */
export async function spawnProcess(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'pipe',
      ...options
    });
    
    let stdout = '';
    let stderr = '';
    
    if (child.stdout) {
      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });
    }
    
    if (child.stderr) {
      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });
    }
    
    child.on('close', (code) => {
      resolve({
        code,
        stdout,
        stderr,
        pid: child.pid
      });
    });
    
    child.on('error', (error) => {
      reject({
        error,
        stdout,
        stderr,
        pid: child.pid
      });
    });
  });
}

/**
 * 检查命令是否存在
 * @param {string} command - 命令名称
 * @returns {boolean} 命令是否存在
 */
export function commandExists(command) {
  const platform = getPlatform();
  const checkCommand = platform === 'windows' ? 'where' : 'which';
  
  try {
    execSync(`${checkCommand} ${command}`, { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

/**
 * 获取命令的完整路径
 * @param {string} command - 命令名称
 * @returns {string|null} 命令的完整路径，如果不存在则返回null
 */
export function getCommandPath(command) {
  const platform = getPlatform();
  const checkCommand = platform === 'windows' ? 'where' : 'which';
  
  try {
    const result = execSync(`${checkCommand} ${command}`, { encoding: 'utf8' });
    return result.trim().split('\n')[0];
  } catch {
    return null;
  }
}

/**
 * 杀死进程
 * @param {number} pid - 进程ID
 * @param {string} signal - 信号，默认为'SIGTERM'
 * @returns {boolean} 是否成功
 */
export function killProcess(pid, signal = 'SIGTERM') {
  try {
    process.kill(pid, signal);
    return true;
  } catch (error) {
    console.error(`Failed to kill process ${pid}:`, error);
    return false;
  }
}

/**
 * 检查进程是否存在
 * @param {number} pid - 进程ID
 * @returns {boolean} 进程是否存在
 */
export function processExists(pid) {
  try {
    process.kill(pid, 0);
    return true;
  } catch {
    return false;
  }
}

/**
 * 获取当前进程信息
 * @returns {Object} 进程信息
 */
export function getCurrentProcessInfo() {
  return {
    pid: process.pid,
    ppid: process.ppid,
    platform: process.platform,
    arch: process.arch,
    version: process.version,
    versions: process.versions,
    cwd: process.cwd(),
    execPath: process.execPath,
    argv: process.argv,
    env: process.env,
    uptime: process.uptime(),
    memoryUsage: process.memoryUsage(),
    cpuUsage: process.cpuUsage()
  };
}

/**
 * 监听进程信号
 * @param {string} signal - 信号名称
 * @param {Function} handler - 处理函数
 * @returns {Function} 取消监听的函数
 */
export function onProcessSignal(signal, handler) {
  process.on(signal, handler);
  
  return () => {
    process.removeListener(signal, handler);
  };
}

/**
 * 优雅地退出进程
 * @param {number} code - 退出码，默认为0
 * @param {number} timeout - 超时时间（毫秒），默认为5000
 */
export function gracefulExit(code = 0, timeout = 5000) {
  console.log('Initiating graceful shutdown...');
  
  // 设置超时强制退出
  const forceExitTimer = setTimeout(() => {
    console.error('Graceful shutdown timeout, forcing exit');
    process.exit(code);
  }, timeout);
  
  // 清理资源的Promise数组
  const cleanupPromises = [];
  
  // 发出关闭事件
  process.emit('beforeExit', code);
  
  // 等待所有清理完成
  Promise.all(cleanupPromises)
    .then(() => {
      clearTimeout(forceExitTimer);
      console.log('Graceful shutdown completed');
      process.exit(code);
    })
    .catch((error) => {
      console.error('Error during graceful shutdown:', error);
      clearTimeout(forceExitTimer);
      process.exit(1);
    });
}

/**
 * 设置进程标题
 * @param {string} title - 进程标题
 */
export function setProcessTitle(title) {
  try {
    process.title = title;
  } catch (error) {
    console.warn(`Failed to set process title: ${error.message}`);
  }
}

/**
 * 获取环境变量
 * @param {string} name - 环境变量名
 * @param {*} defaultValue - 默认值
 * @returns {*} 环境变量值
 */
export function getEnvVar(name, defaultValue) {
  return process.env[name] ?? defaultValue;
}

/**
 * 设置环境变量
 * @param {string} name - 环境变量名
 * @param {string} value - 环境变量值
 */
export function setEnvVar(name, value) {
  process.env[name] = value;
}

/**
 * 检查是否在调试模式
 * @returns {boolean} 是否在调试模式
 */
export function isDebugMode() {
  return process.env.NODE_ENV === 'development' || 
         process.env.DEBUG === 'true' ||
         process.execArgv.some(arg => arg.includes('--inspect'));
}

/**
 * 检查是否在生产模式
 * @returns {boolean} 是否在生产模式
 */
export function isProductionMode() {
  return process.env.NODE_ENV === 'production';
}

/**
 * 获取Node.js版本信息
 * @returns {Object} 版本信息
 */
export function getNodeVersion() {
  const version = process.version;
  const match = version.match(/^v(\d+)\.(\d+)\.(\d+)/);
  
  if (match) {
    return {
      full: version,
      major: parseInt(match[1], 10),
      minor: parseInt(match[2], 10),
      patch: parseInt(match[3], 10)
    };
  }
  
  return {
    full: version,
    major: 0,
    minor: 0,
    patch: 0
  };
}

/**
 * 检查Node.js版本是否满足要求
 * @param {string} requiredVersion - 要求的版本（如 '>=14.0.0'）
 * @returns {boolean} 版本是否满足要求
 */
export function checkNodeVersion(requiredVersion) {
  const current = getNodeVersion();
  
  // 简单的版本比较实现
  const match = requiredVersion.match(/^(>=|>|<=|<|=)?(\d+)\.(\d+)\.(\d+)$/);
  if (!match) {
    throw new Error(`Invalid version format: ${requiredVersion}`);
  }
  
  const operator = match[1] || '=';
  const major = parseInt(match[2], 10);
  const minor = parseInt(match[3], 10);
  const patch = parseInt(match[4], 10);
  
  const currentVersion = current.major * 10000 + current.minor * 100 + current.patch;
  const requiredVersionNum = major * 10000 + minor * 100 + patch;
  
  switch (operator) {
    case '>=': return currentVersion >= requiredVersionNum;
    case '>': return currentVersion > requiredVersionNum;
    case '<=': return currentVersion <= requiredVersionNum;
    case '<': return currentVersion < requiredVersionNum;
    case '=': return currentVersion === requiredVersionNum;
    default: return false;
  }
}

/**
 * 获取系统资源使用情况
 * @returns {Object} 资源使用情况
 */
export function getResourceUsage() {
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  return {
    memory: {
      rss: memUsage.rss,
      heapTotal: memUsage.heapTotal,
      heapUsed: memUsage.heapUsed,
      external: memUsage.external,
      arrayBuffers: memUsage.arrayBuffers
    },
    cpu: {
      user: cpuUsage.user,
      system: cpuUsage.system
    },
    uptime: process.uptime(),
    loadAverage: require('os').loadavg(),
    platform: process.platform,
    arch: process.arch
  };
}

/**
 * 格式化内存大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
export function formatMemorySize(bytes) {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * 监控资源使用情况
 * @param {Function} callback - 回调函数
 * @param {number} interval - 监控间隔（毫秒），默认为1000
 * @returns {Function} 停止监控的函数
 */
export function monitorResourceUsage(callback, interval = 1000) {
  const timer = setInterval(() => {
    const usage = getResourceUsage();
    callback(usage);
  }, interval);
  
  return () => {
    clearInterval(timer);
  };
}
