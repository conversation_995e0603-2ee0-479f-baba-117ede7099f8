/**
 * 路径处理工具函数
 * @original: 原始文件中的路径处理相关函数
 */

import path from 'path';
import { homedir } from 'os';

/**
 * 获取平台信息
 * @returns {string} 平台名称
 * @original: 原始文件中的平台检测逻辑
 */
export function getPlatform() {
  const platform = process.platform;
  
  if (platform === 'darwin') return 'macos';
  if (platform === 'win32') return 'windows';
  if (platform === 'linux') {
    // 检查是否为WSL
    try {
      const fs = require('fs');
      const release = fs.readFileSync('/proc/version', 'utf8').toLowerCase();
      if (release.includes('microsoft') || release.includes('wsl')) {
        return 'wsl';
      }
    } catch {
      // 忽略错误，继续作为普通Linux处理
    }
    return 'linux';
  }
  
  return 'unknown';
}

/**
 * 规范化路径分隔符
 * @param {string} filePath - 文件路径
 * @returns {string} 规范化后的路径
 */
export function normalizePath(filePath) {
  return path.normalize(filePath).replace(/\\/g, '/');
}

/**
 * 解析波浪号路径
 * @param {string} filePath - 文件路径
 * @returns {string} 解析后的路径
 */
export function expandTildePath(filePath) {
  if (filePath === '~') {
    return homedir();
  }
  
  if (filePath.startsWith('~/')) {
    return path.join(homedir(), filePath.substring(2));
  }
  
  return filePath;
}

/**
 * 获取相对路径
 * @param {string} from - 起始路径
 * @param {string} to - 目标路径
 * @returns {string} 相对路径
 */
export function getRelativePath(from, to) {
  return path.relative(from, to);
}

/**
 * 获取绝对路径
 * @param {string} filePath - 文件路径
 * @param {string} basePath - 基础路径，默认为当前工作目录
 * @returns {string} 绝对路径
 */
export function getAbsolutePath(filePath, basePath = process.cwd()) {
  if (path.isAbsolute(filePath)) {
    return filePath;
  }
  
  const expandedPath = expandTildePath(filePath);
  return path.resolve(basePath, expandedPath);
}

/**
 * 获取文件扩展名
 * @param {string} filePath - 文件路径
 * @returns {string} 文件扩展名（包含点号）
 */
export function getFileExtension(filePath) {
  return path.extname(filePath);
}

/**
 * 获取文件名（不含扩展名）
 * @param {string} filePath - 文件路径
 * @returns {string} 文件名
 */
export function getFileName(filePath) {
  return path.basename(filePath, path.extname(filePath));
}

/**
 * 获取文件名（含扩展名）
 * @param {string} filePath - 文件路径
 * @returns {string} 完整文件名
 */
export function getBaseName(filePath) {
  return path.basename(filePath);
}

/**
 * 获取目录名
 * @param {string} filePath - 文件路径
 * @returns {string} 目录路径
 */
export function getDirName(filePath) {
  return path.dirname(filePath);
}

/**
 * 连接路径
 * @param {...string} paths - 路径片段
 * @returns {string} 连接后的路径
 */
export function joinPaths(...paths) {
  return path.join(...paths);
}

/**
 * 检查路径是否为绝对路径
 * @param {string} filePath - 文件路径
 * @returns {boolean} 是否为绝对路径
 */
export function isAbsolutePath(filePath) {
  return path.isAbsolute(filePath);
}

/**
 * 检查路径是否为相对路径
 * @param {string} filePath - 文件路径
 * @returns {boolean} 是否为相对路径
 */
export function isRelativePath(filePath) {
  return !path.isAbsolute(filePath);
}

/**
 * 解析路径为组件
 * @param {string} filePath - 文件路径
 * @returns {Object} 路径组件
 */
export function parsePath(filePath) {
  const parsed = path.parse(filePath);
  
  return {
    root: parsed.root,
    dir: parsed.dir,
    base: parsed.base,
    ext: parsed.ext,
    name: parsed.name,
    isAbsolute: path.isAbsolute(filePath),
    normalized: path.normalize(filePath)
  };
}

/**
 * 构建路径
 * @param {Object} pathObj - 路径对象
 * @returns {string} 构建的路径
 */
export function formatPath(pathObj) {
  return path.format(pathObj);
}

/**
 * 获取公共路径前缀
 * @param {Array} paths - 路径数组
 * @returns {string} 公共前缀路径
 */
export function getCommonPath(paths) {
  if (!paths || paths.length === 0) {
    return '';
  }
  
  if (paths.length === 1) {
    return path.dirname(paths[0]);
  }
  
  // 规范化所有路径
  const normalizedPaths = paths.map(p => path.resolve(p));
  
  // 找到最短路径
  const shortestPath = normalizedPaths.reduce((shortest, current) => 
    current.length < shortest.length ? current : shortest
  );
  
  // 逐字符比较找到公共前缀
  let commonPrefix = '';
  for (let i = 0; i < shortestPath.length; i++) {
    const char = shortestPath[i];
    if (normalizedPaths.every(p => p[i] === char)) {
      commonPrefix += char;
    } else {
      break;
    }
  }
  
  // 确保返回完整的目录路径
  return path.dirname(commonPrefix + 'x');
}

/**
 * 检查路径是否在指定目录内
 * @param {string} filePath - 文件路径
 * @param {string} dirPath - 目录路径
 * @returns {boolean} 是否在目录内
 */
export function isPathInDirectory(filePath, dirPath) {
  const resolvedFile = path.resolve(filePath);
  const resolvedDir = path.resolve(dirPath);
  
  return resolvedFile.startsWith(resolvedDir + path.sep) || resolvedFile === resolvedDir;
}

/**
 * 获取路径深度
 * @param {string} filePath - 文件路径
 * @returns {number} 路径深度
 */
export function getPathDepth(filePath) {
  const normalized = path.normalize(filePath);
  const parts = normalized.split(path.sep).filter(part => part && part !== '.');
  
  return parts.length;
}

/**
 * 清理路径（移除多余的分隔符和点）
 * @param {string} filePath - 文件路径
 * @returns {string} 清理后的路径
 */
export function cleanPath(filePath) {
  return path.normalize(filePath);
}

/**
 * 转换路径分隔符
 * @param {string} filePath - 文件路径
 * @param {string} separator - 目标分隔符，默认为当前平台的分隔符
 * @returns {string} 转换后的路径
 */
export function convertPathSeparator(filePath, separator = path.sep) {
  return filePath.replace(/[/\\]/g, separator);
}

/**
 * 获取路径的所有父目录
 * @param {string} filePath - 文件路径
 * @returns {Array} 父目录数组
 */
export function getParentDirectories(filePath) {
  const resolved = path.resolve(filePath);
  const parents = [];
  let current = path.dirname(resolved);
  
  while (current !== path.dirname(current)) {
    parents.push(current);
    current = path.dirname(current);
  }
  
  return parents;
}

/**
 * 检查路径是否有效
 * @param {string} filePath - 文件路径
 * @returns {boolean} 路径是否有效
 */
export function isValidPath(filePath) {
  if (!filePath || typeof filePath !== 'string') {
    return false;
  }
  
  // 检查非法字符
  const illegalChars = /[<>:"|?*]/;
  if (illegalChars.test(filePath)) {
    return false;
  }
  
  // 检查路径长度
  if (filePath.length > 260 && process.platform === 'win32') {
    return false;
  }
  
  try {
    path.parse(filePath);
    return true;
  } catch {
    return false;
  }
}

/**
 * 安全化路径（移除危险字符）
 * @param {string} filePath - 文件路径
 * @returns {string} 安全化后的路径
 */
export function sanitizePath(filePath) {
  if (!filePath) return '';
  
  // 移除或替换危险字符
  return filePath
    .replace(/[<>:"|?*]/g, '_')  // 替换非法字符
    .replace(/\.\./g, '_')       // 替换路径遍历
    .replace(/^\.+/, '')         // 移除开头的点
    .replace(/\.+$/, '');        // 移除结尾的点
}

/**
 * 生成唯一文件名
 * @param {string} basePath - 基础路径
 * @param {string} fileName - 文件名
 * @param {string} extension - 扩展名
 * @returns {string} 唯一文件名
 */
export function generateUniqueFileName(basePath, fileName, extension) {
  const fs = require('fs');
  let counter = 0;
  let uniqueName = fileName + extension;
  
  while (fs.existsSync(path.join(basePath, uniqueName))) {
    counter++;
    uniqueName = `${fileName}_${counter}${extension}`;
  }
  
  return uniqueName;
}

/**
 * 获取临时目录路径
 * @returns {string} 临时目录路径
 */
export function getTempDir() {
  return require('os').tmpdir();
}

/**
 * 创建临时文件路径
 * @param {string} prefix - 前缀
 * @param {string} extension - 扩展名
 * @returns {string} 临时文件路径
 */
export function createTempFilePath(prefix = 'temp', extension = '.tmp') {
  const { randomUUID } = require('crypto');
  const fileName = `${prefix}_${randomUUID()}${extension}`;
  return path.join(getTempDir(), fileName);
}

/**
 * 匹配路径模式（简单的glob匹配）
 * @param {string} pattern - 模式字符串
 * @param {string} filePath - 文件路径
 * @returns {boolean} 是否匹配
 */
export function matchPath(pattern, filePath) {
  // 简化的glob匹配实现
  const regexPattern = pattern
    .replace(/\./g, '\\.')
    .replace(/\*/g, '.*')
    .replace(/\?/g, '.');
  
  const regex = new RegExp(`^${regexPattern}$`);
  return regex.test(filePath);
}

/**
 * 过滤路径数组
 * @param {Array} paths - 路径数组
 * @param {string|Array} patterns - 模式字符串或数组
 * @param {boolean} exclude - 是否为排除模式，默认为false
 * @returns {Array} 过滤后的路径数组
 */
export function filterPaths(paths, patterns, exclude = false) {
  const patternArray = Array.isArray(patterns) ? patterns : [patterns];
  
  return paths.filter(filePath => {
    const matches = patternArray.some(pattern => matchPath(pattern, filePath));
    return exclude ? !matches : matches;
  });
}

/**
 * 获取路径的统计信息
 * @param {string} filePath - 文件路径
 * @returns {Object} 路径统计信息
 */
export function getPathStats(filePath) {
  const parsed = parsePath(filePath);
  const resolved = path.resolve(filePath);
  
  return {
    original: filePath,
    resolved,
    normalized: path.normalize(filePath),
    relative: path.relative(process.cwd(), resolved),
    depth: getPathDepth(filePath),
    isAbsolute: path.isAbsolute(filePath),
    platform: getPlatform(),
    separator: path.sep,
    ...parsed
  };
}

/**
 * 比较两个路径是否相同
 * @param {string} path1 - 路径1
 * @param {string} path2 - 路径2
 * @param {boolean} caseSensitive - 是否区分大小写，默认根据平台决定
 * @returns {boolean} 路径是否相同
 */
export function pathsEqual(path1, path2, caseSensitive = process.platform !== 'win32') {
  const resolved1 = path.resolve(path1);
  const resolved2 = path.resolve(path2);
  
  if (caseSensitive) {
    return resolved1 === resolved2;
  } else {
    return resolved1.toLowerCase() === resolved2.toLowerCase();
  }
}

/**
 * 获取路径的最近公共祖先
 * @param {Array} paths - 路径数组
 * @returns {string} 最近公共祖先路径
 */
export function getCommonAncestor(paths) {
  if (!paths || paths.length === 0) {
    return '';
  }
  
  if (paths.length === 1) {
    return path.dirname(paths[0]);
  }
  
  const resolvedPaths = paths.map(p => path.resolve(p));
  const pathParts = resolvedPaths.map(p => p.split(path.sep));
  
  const minLength = Math.min(...pathParts.map(parts => parts.length));
  const commonParts = [];
  
  for (let i = 0; i < minLength; i++) {
    const part = pathParts[0][i];
    if (pathParts.every(parts => parts[i] === part)) {
      commonParts.push(part);
    } else {
      break;
    }
  }
  
  return commonParts.join(path.sep) || path.sep;
}

/**
 * 检查路径是否为子路径
 * @param {string} childPath - 子路径
 * @param {string} parentPath - 父路径
 * @returns {boolean} 是否为子路径
 */
export function isSubPath(childPath, parentPath) {
  const resolvedChild = path.resolve(childPath);
  const resolvedParent = path.resolve(parentPath);
  
  return resolvedChild.startsWith(resolvedParent + path.sep) || resolvedChild === resolvedParent;
}

/**
 * 获取路径的所有可能变体
 * @param {string} filePath - 文件路径
 * @returns {Array} 路径变体数组
 */
export function getPathVariants(filePath) {
  const variants = new Set();
  
  // 原始路径
  variants.add(filePath);
  
  // 规范化路径
  variants.add(path.normalize(filePath));
  
  // 绝对路径
  variants.add(path.resolve(filePath));
  
  // 展开波浪号
  variants.add(expandTildePath(filePath));
  
  // 不同分隔符的版本
  variants.add(filePath.replace(/\\/g, '/'));
  variants.add(filePath.replace(/\//g, '\\'));
  
  return Array.from(variants);
}

/**
 * 创建安全的文件路径
 * @param {string} basePath - 基础路径
 * @param {string} relativePath - 相对路径
 * @returns {string} 安全的文件路径
 */
export function createSafePath(basePath, relativePath) {
  const sanitizedRelative = sanitizePath(relativePath);
  const joined = path.join(basePath, sanitizedRelative);
  const resolved = path.resolve(joined);
  const resolvedBase = path.resolve(basePath);
  
  // 确保结果路径在基础路径内
  if (!resolved.startsWith(resolvedBase)) {
    throw new Error('Path traversal detected');
  }
  
  return resolved;
}

/**
 * 获取路径的哈希值
 * @param {string} filePath - 文件路径
 * @returns {string} 路径哈希值
 */
export function getPathHash(filePath) {
  const crypto = require('crypto');
  const resolved = path.resolve(filePath);
  return crypto.createHash('md5').update(resolved).digest('hex');
}

/**
 * 比较路径数组
 * @param {Array} paths1 - 路径数组1
 * @param {Array} paths2 - 路径数组2
 * @returns {Object} 比较结果
 */
export function comparePaths(paths1, paths2) {
  const set1 = new Set(paths1.map(p => path.resolve(p)));
  const set2 = new Set(paths2.map(p => path.resolve(p)));
  
  const common = [...set1].filter(p => set2.has(p));
  const onlyIn1 = [...set1].filter(p => !set2.has(p));
  const onlyIn2 = [...set2].filter(p => !set1.has(p));
  
  return {
    common,
    onlyInFirst: onlyIn1,
    onlyInSecond: onlyIn2,
    identical: onlyIn1.length === 0 && onlyIn2.length === 0
  };
}

/**
 * 获取路径的层次结构
 * @param {Array} paths - 路径数组
 * @returns {Object} 层次结构对象
 */
export function buildPathHierarchy(paths) {
  const hierarchy = {};
  
  for (const filePath of paths) {
    const parts = path.resolve(filePath).split(path.sep).filter(Boolean);
    let current = hierarchy;
    
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      
      if (!current[part]) {
        current[part] = {
          type: i === parts.length - 1 ? 'file' : 'directory',
          children: {},
          fullPath: path.sep + parts.slice(0, i + 1).join(path.sep)
        };
      }
      
      current = current[part].children;
    }
  }
  
  return hierarchy;
}

/**
 * 扁平化路径层次结构
 * @param {Object} hierarchy - 层次结构对象
 * @param {string} type - 类型过滤：'file', 'directory', 'all'
 * @returns {Array} 扁平化的路径数组
 */
export function flattenPathHierarchy(hierarchy, type = 'all') {
  const result = [];
  
  function traverse(node, currentPath = '') {
    for (const [name, info] of Object.entries(node)) {
      const fullPath = info.fullPath || path.join(currentPath, name);
      
      if (type === 'all' || info.type === type) {
        result.push(fullPath);
      }
      
      if (info.children && Object.keys(info.children).length > 0) {
        traverse(info.children, fullPath);
      }
    }
  }
  
  traverse(hierarchy);
  return result;
}
