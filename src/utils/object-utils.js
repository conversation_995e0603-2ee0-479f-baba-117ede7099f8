/**
 * 对象处理工具函数
 * @original: 原始文件中的对象处理相关函数
 */

/**
 * 检查值是否为对象类型
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为对象
 */
export function isObject(value) {
  const type = typeof value;
  return value != null && (type === 'object' || type === 'function');
}

/**
 * 检查值是否为纯对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为纯对象
 */
export function isPlainObject(value) {
  if (!isObjectLike(value) || baseGetTag(value) !== '[object Object]') {
    return false;
  }
  
  if (Object.getPrototypeOf(value) === null) {
    return true;
  }
  
  let proto = value;
  while (Object.getPrototypeOf(proto) !== null) {
    proto = Object.getPrototypeOf(proto);
  }
  
  return Object.getPrototypeOf(value) === proto;
}

/**
 * 检查值是否为类对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为类对象
 */
function isObjectLike(value) {
  return value != null && typeof value === 'object';
}

/**
 * 获取值的标签
 * @param {*} value - 要检查的值
 * @returns {string} 标签字符串
 */
function baseGetTag(value) {
  const objectProto = Object.prototype;
  const hasOwnProp = objectProto.hasOwnProperty;
  const toString = objectProto.toString;
  const symToStringTag = Symbol ? Symbol.toStringTag : undefined;
  
  if (value == null) {
    return value === undefined ? '[object Undefined]' : '[object Null]';
  }
  if (!(symToStringTag && symToStringTag in Object(value))) {
    return toString.call(value);
  }
  const isOwn = hasOwnProp.call(value, symToStringTag);
  const tag = value[symToStringTag];
  let unmasked = false;
  
  try {
    value[symToStringTag] = undefined;
    unmasked = true;
  } catch (e) {}
  
  const result = toString.call(value);
  if (unmasked) {
    if (isOwn) {
      value[symToStringTag] = tag;
    } else {
      delete value[symToStringTag];
    }
  }
  return result;
}

/**
 * 获取对象的所有键
 * @param {Object} object - 输入对象
 * @returns {Array} 键数组
 * @original: 原始文件中的键获取逻辑
 */
export function keys(object) {
  return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);
}

/**
 * 检查值是否为类数组
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为类数组
 */
function isArrayLike(value) {
  return value != null && isLength(value.length) && !isFunction(value);
}

/**
 * 检查值是否为有效长度
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为有效长度
 */
function isLength(value) {
  return typeof value === 'number' && value > -1 && value % 1 === 0 && value <= Number.MAX_SAFE_INTEGER;
}

/**
 * 检查值是否为函数
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为函数
 */
function isFunction(value) {
  if (!isObject(value)) {
    return false;
  }
  const tag = baseGetTag(value);
  return tag === '[object Function]' || tag === '[object GeneratorFunction]' || tag === '[object AsyncFunction]' || tag === '[object Proxy]';
}

/**
 * 获取类数组对象的键
 * @param {*} value - 类数组对象
 * @param {boolean} inherited - 是否包含继承的属性
 * @returns {Array} 键数组
 */
function arrayLikeKeys(value, inherited) {
  const isArr = Array.isArray(value);
  const isArg = !isArr && isArguments(value);
  const isBuff = !isArr && !isArg && isBuffer(value);
  const isType = !isArr && !isArg && !isBuff && isTypedArray(value);
  const skipIndexes = isArr || isArg || isBuff || isType;
  const result = skipIndexes ? baseTimes(value.length, String) : [];
  const length = result.length;
  
  for (const key in value) {
    if ((inherited || hasOwnProperty.call(value, key)) &&
        !(skipIndexes && (
          // Safari 9 has enumerable `arguments.length` in strict mode.
          key === 'length' ||
          // Node.js 0.10 has enumerable non-index properties on buffers.
          (isBuff && (key === 'offset' || key === 'parent')) ||
          // PhantomJS 2 has enumerable non-index properties on typed arrays.
          (isType && (key === 'buffer' || key === 'byteLength' || key === 'byteOffset')) ||
          // Skip index properties.
          isIndex(key, length)
        ))) {
      result.push(key);
    }
  }
  return result;
}

/**
 * 获取对象的基础键
 * @param {Object} object - 输入对象
 * @returns {Array} 键数组
 */
function baseKeys(object) {
  if (!isPrototype(object)) {
    return nativeKeys(object);
  }
  const result = [];
  for (const key in Object(object)) {
    if (hasOwnProperty.call(object, key) && key !== 'constructor') {
      result.push(key);
    }
  }
  return result;
}

/**
 * 检查值是否为原型对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为原型对象
 */
function isPrototype(value) {
  const Ctor = value && value.constructor;
  const proto = (typeof Ctor === 'function' && Ctor.prototype) || Object.prototype;
  return value === proto;
}

/**
 * 原生键获取函数
 */
const nativeKeys = Object.keys;

/**
 * 检查值是否为arguments对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为arguments对象
 */
function isArguments(value) {
  return isObjectLike(value) && baseGetTag(value) === '[object Arguments]';
}

/**
 * 检查值是否为Buffer
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为Buffer
 */
function isBuffer(value) {
  return value != null && !Array.isArray(value) && typeof value.constructor === 'function' && 
         typeof value.constructor.isBuffer === 'function' && value.constructor.isBuffer(value);
}

/**
 * 检查值是否为类型化数组
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为类型化数组
 */
function isTypedArray(value) {
  return isObjectLike(value) && isLength(value.length) && !!typedArrayTags[baseGetTag(value)];
}

/**
 * 类型化数组标签映射
 */
const typedArrayTags = {};
['[object Float32Array]', '[object Float64Array]', '[object Int8Array]', '[object Int16Array]',
 '[object Int32Array]', '[object Uint8Array]', '[object Uint8ClampedArray]', '[object Uint16Array]',
 '[object Uint32Array]'].forEach(tag => {
  typedArrayTags[tag] = true;
});

/**
 * 生成指定长度的数组
 * @param {number} n - 长度
 * @param {Function} iteratee - 迭代函数
 * @returns {Array} 生成的数组
 */
function baseTimes(n, iteratee) {
  let index = -1;
  const result = Array(n);
  
  while (++index < n) {
    result[index] = iteratee(index);
  }
  return result;
}

/**
 * 检查值是否为有效的数组索引
 * @param {*} value - 要检查的值
 * @param {number} length - 数组长度
 * @returns {boolean} 是否为有效索引
 */
function isIndex(value, length) {
  const type = typeof value;
  length = length == null ? Number.MAX_SAFE_INTEGER : length;
  
  return !!length &&
    (type === 'number' ||
      (type !== 'symbol' && /^(?:0|[1-9]\d*)$/.test(value))) &&
        (value > -1 && value % 1 === 0 && value < length);
}

/**
 * hasOwnProperty的引用
 */
const hasOwnProperty = Object.prototype.hasOwnProperty;

/**
 * 获取对象的所有值
 * @param {Object} object - 输入对象
 * @returns {Array} 值数组
 * @original: 原始文件L1772-1774中的nrB函数
 */
export function values(object) {
  return object == null ? [] : baseValues(object, keys(object));
}

/**
 * 获取对象值的基础函数
 * @param {Object} object - 输入对象
 * @param {Array} props - 属性数组
 * @returns {Array} 值数组
 * @original: 原始文件L1766-1770中的irB函数
 */
function baseValues(object, props) {
  return arrayMap(props, function(key) {
    return object[key];
  });
}

/**
 * 数组映射函数
 * @param {Array} array - 输入数组
 * @param {Function} iteratee - 迭代函数
 * @returns {Array} 映射后的数组
 */
function arrayMap(array, iteratee) {
  let index = -1;
  const length = array == null ? 0 : array.length;
  const result = Array(length);
  
  while (++index < length) {
    result[index] = iteratee(array[index], index, array);
  }
  return result;
}

/**
 * 深度克隆对象
 * @param {*} value - 要克隆的值
 * @returns {*} 克隆后的值
 */
export function cloneDeep(value) {
  return baseClone(value, true, true);
}

/**
 * 浅克隆对象
 * @param {*} value - 要克隆的值
 * @returns {*} 克隆后的值
 */
export function clone(value) {
  return baseClone(value, false, false);
}

/**
 * 基础克隆函数
 * @param {*} value - 要克隆的值
 * @param {boolean} isDeep - 是否深度克隆
 * @param {boolean} isFull - 是否完整克隆
 * @param {Function} customizer - 自定义克隆函数
 * @param {string} key - 键
 * @param {Object} object - 父对象
 * @param {WeakMap} stack - 栈，用于处理循环引用
 * @returns {*} 克隆后的值
 */
function baseClone(value, isDeep, isFull, customizer, key, object, stack) {
  let result;
  if (customizer) {
    result = object ? customizer(value, key, object, stack) : customizer(value);
  }
  if (result !== undefined) {
    return result;
  }
  if (!isObject(value)) {
    return value;
  }
  const isArr = Array.isArray(value);
  if (isArr) {
    result = initCloneArray(value);
    if (!isDeep) {
      return copyArray(value, result);
    }
  } else {
    const tag = baseGetTag(value);
    const isFunc = tag === '[object Function]' || tag === '[object GeneratorFunction]';
    
    if (isBuffer(value)) {
      return cloneBuffer(value, isDeep);
    }
    if (tag === '[object Object]' || tag === '[object Arguments]' || (isFunc && !object)) {
      result = (isFull || isFunc) ? {} : initCloneObject(value);
      if (!isDeep) {
        return isFull
          ? copySymbolsIn(value, baseAssignIn(result, value))
          : copySymbols(value, baseAssign(result, value));
      }
    } else {
      if (!cloneableTags[tag]) {
        return object ? value : {};
      }
      result = initCloneByTag(value, tag, isDeep);
    }
  }
  // 检查循环引用
  stack || (stack = new Stack());
  const stacked = stack.get(value);
  if (stacked) {
    return stacked;
  }
  stack.set(value, result);
  
  if (isSet(value)) {
    value.forEach(function(subValue) {
      result.add(baseClone(subValue, isDeep, isFull, customizer, subValue, value, stack));
    });
  } else if (isMap(value)) {
    value.forEach(function(subValue, key) {
      result.set(key, baseClone(subValue, isDeep, isFull, customizer, key, value, stack));
    });
  }
  
  const keysFunc = isFull
    ? (isFlat ? getAllKeysIn : getAllKeys)
    : (isFlat ? keysIn : keys);
    
  const props = isArr ? undefined : keysFunc(value);
  arrayEach(props || value, function(subValue, key) {
    if (props) {
      key = subValue;
      subValue = value[key];
    }
    // 递归填充克隆（容易受到调用栈限制的影响）
    assignValue(result, key, baseClone(subValue, isDeep, isFull, customizer, key, value, stack));
  });
  return result;
}

/**
 * 初始化数组克隆
 * @param {Array} array - 要克隆的数组
 * @returns {Array} 初始化的数组
 */
function initCloneArray(array) {
  const length = array.length;
  const result = new array.constructor(length);
  
  // 添加分配给`RegExp#exec`的属性
  if (length && typeof array[0] === 'string' && hasOwnProperty.call(array, 'index')) {
    result.index = array.index;
    result.input = array.input;
  }
  return result;
}

/**
 * 复制数组
 * @param {Array} source - 源数组
 * @param {Array} array - 目标数组
 * @returns {Array} 复制后的数组
 */
function copyArray(source, array) {
  let index = -1;
  const length = source.length;
  
  array || (array = Array(length));
  while (++index < length) {
    array[index] = source[index];
  }
  return array;
}

/**
 * 可克隆的标签映射
 */
const cloneableTags = {};
['[object Arguments]', '[object Array]', '[object ArrayBuffer]', '[object DataView]',
 '[object Boolean]', '[object Date]', '[object Float32Array]', '[object Float64Array]',
 '[object Int8Array]', '[object Int16Array]', '[object Int32Array]', '[object Map]',
 '[object Number]', '[object Object]', '[object RegExp]', '[object Set]',
 '[object String]', '[object Symbol]', '[object Uint8Array]', '[object Uint8ClampedArray]',
 '[object Uint16Array]', '[object Uint32Array]'].forEach(tag => {
  cloneableTags[tag] = true;
});

/**
 * 检查值是否为Set
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为Set
 */
function isSet(value) {
  return isObjectLike(value) && baseGetTag(value) === '[object Set]';
}

/**
 * 检查值是否为Map
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为Map
 */
function isMap(value) {
  return isObjectLike(value) && baseGetTag(value) === '[object Map]';
}

/**
 * 简化的Stack类
 */
class Stack {
  constructor(entries) {
    const data = this.__data__ = new ListCache(entries);
    this.size = data.size;
  }
  
  clear() {
    this.__data__ = new ListCache();
    this.size = 0;
  }
  
  delete(key) {
    const data = this.__data__;
    const result = data.delete(key);
    
    this.size = data.size;
    return result;
  }
  
  get(key) {
    return this.__data__.get(key);
  }
  
  has(key) {
    return this.__data__.has(key);
  }
  
  set(key, value) {
    const data = this.__data__;
    if (data instanceof ListCache) {
      const pairs = data.__data__;
      if (!Map || (pairs.length < 199)) {
        pairs.push([key, value]);
        this.size = ++data.size;
        return this;
      }
      data = this.__data__ = new MapCache(pairs);
    }
    data.set(key, value);
    this.size = data.size;
    return this;
  }
}

/**
 * 简化的ListCache类
 */
class ListCache {
  constructor(entries) {
    let index = -1;
    const length = entries == null ? 0 : entries.length;
    
    this.clear();
    while (++index < length) {
      const entry = entries[index];
      this.set(entry[0], entry[1]);
    }
  }
  
  clear() {
    this.__data__ = [];
    this.size = 0;
  }
  
  delete(key) {
    const data = this.__data__;
    const index = assocIndexOf(data, key);
    
    if (index < 0) {
      return false;
    }
    const lastIndex = data.length - 1;
    if (index == lastIndex) {
      data.pop();
    } else {
      data.splice(index, 1);
    }
    --this.size;
    return true;
  }
  
  get(key) {
    const data = this.__data__;
    const index = assocIndexOf(data, key);
    return index < 0 ? undefined : data[index][1];
  }
  
  has(key) {
    return assocIndexOf(this.__data__, key) > -1;
  }
  
  set(key, value) {
    const data = this.__data__;
    const index = assocIndexOf(data, key);
    
    if (index < 0) {
      ++this.size;
      data.push([key, value]);
    } else {
      data[index][1] = value;
    }
    return this;
  }
}

/**
 * 获取关联数组中键的索引
 * @param {Array} array - 关联数组
 * @param {*} key - 要查找的键
 * @returns {number} 索引
 */
function assocIndexOf(array, key) {
  let length = array.length;
  while (length--) {
    if (eq(array[length][0], key)) {
      return length;
    }
  }
  return -1;
}

/**
 * 相等性检查
 * @param {*} value - 值1
 * @param {*} other - 值2
 * @returns {boolean} 是否相等
 */
function eq(value, other) {
  return value === other || (value !== value && other !== other);
}

/**
 * 简化的MapCache类
 */
class MapCache {
  constructor(entries) {
    let index = -1;
    const length = entries == null ? 0 : entries.length;
    
    this.clear();
    while (++index < length) {
      const entry = entries[index];
      this.set(entry[0], entry[1]);
    }
  }
  
  clear() {
    this.size = 0;
    this.__data__ = {
      'hash': new Hash(),
      'map': new (Map || ListCache)(),
      'string': new Hash()
    };
  }
  
  delete(key) {
    const result = getMapData(this, key)['delete'](key);
    this.size -= result ? 1 : 0;
    return result;
  }
  
  get(key) {
    return getMapData(this, key).get(key);
  }
  
  has(key) {
    return getMapData(this, key).has(key);
  }
  
  set(key, value) {
    const data = getMapData(this, key);
    const size = data.size;
    
    data.set(key, value);
    this.size += data.size == size ? 0 : 1;
    return this;
  }
}

/**
 * 获取映射数据
 * @param {Object} map - 映射对象
 * @param {*} key - 键
 * @returns {Object} 数据对象
 */
function getMapData(map, key) {
  const data = map.__data__;
  return isKeyable(key)
    ? data[typeof key == 'string' ? 'string' : 'hash']
    : data.map;
}

/**
 * 检查键是否可用作原始键
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否可用作键
 */
function isKeyable(value) {
  const type = typeof value;
  return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')
    ? (value !== '__proto__')
    : (value === null);
}

/**
 * 简化的Hash类
 */
class Hash {
  constructor(entries) {
    let index = -1;
    const length = entries == null ? 0 : entries.length;
    
    this.clear();
    while (++index < length) {
      const entry = entries[index];
      this.set(entry[0], entry[1]);
    }
  }
  
  clear() {
    this.__data__ = Object.create(null);
    this.size = 0;
  }
  
  delete(key) {
    const result = this.has(key) && delete this.__data__[key];
    this.size -= result ? 1 : 0;
    return result;
  }
  
  get(key) {
    const data = this.__data__;
    if (Object.create) {
      const result = data[key];
      return result === '__lodash_hash_undefined__' ? undefined : result;
    }
    return hasOwnProperty.call(data, key) ? data[key] : undefined;
  }
  
  has(key) {
    const data = this.__data__;
    return Object.create ? (data[key] !== undefined) : hasOwnProperty.call(data, key);
  }
  
  set(key, value) {
    const data = this.__data__;
    this.size += this.has(key) ? 0 : 1;
    data[key] = (Object.create && value === undefined) ? '__lodash_hash_undefined__' : value;
    return this;
  }
}

// 其他辅助函数的简化实现
function initCloneObject(object) {
  return (typeof object.constructor == 'function' && !isPrototype(object))
    ? Object.create(Object.getPrototypeOf(object))
    : {};
}

function initCloneByTag(object, tag, isDeep) {
  const Ctor = object.constructor;
  switch (tag) {
    case '[object ArrayBuffer]':
      return cloneArrayBuffer(object);
    case '[object Boolean]':
    case '[object Date]':
      return new Ctor(+object);
    case '[object DataView]':
      return cloneDataView(object, isDeep);
    case '[object Float32Array]': case '[object Float64Array]':
    case '[object Int8Array]': case '[object Int16Array]':
    case '[object Int32Array]': case '[object Uint8Array]':
    case '[object Uint8ClampedArray]': case '[object Uint16Array]':
    case '[object Uint32Array]':
      return cloneTypedArray(object, isDeep);
    case '[object Map]':
      return new Ctor();
    case '[object Number]':
    case '[object String]':
      return new Ctor(object);
    case '[object RegExp]':
      return cloneRegExp(object);
    case '[object Set]':
      return new Ctor();
    case '[object Symbol]':
      return cloneSymbol(object);
  }
}

// 简化的克隆函数实现
function cloneArrayBuffer(arrayBuffer) {
  const result = new arrayBuffer.constructor(arrayBuffer.byteLength);
  new Uint8Array(result).set(new Uint8Array(arrayBuffer));
  return result;
}

function cloneDataView(dataView, isDeep) {
  const buffer = isDeep ? cloneArrayBuffer(dataView.buffer) : dataView.buffer;
  return new dataView.constructor(buffer, dataView.byteOffset, dataView.byteLength);
}

function cloneRegExp(regexp) {
  const result = new regexp.constructor(regexp.source, /\w*$/.exec(regexp));
  result.lastIndex = regexp.lastIndex;
  return result;
}

function cloneSymbol(symbol) {
  return Symbol.prototype.valueOf ? Object(Symbol.prototype.valueOf.call(symbol)) : {};
}

function cloneTypedArray(typedArray, isDeep) {
  const buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;
  return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);
}

function cloneBuffer(buffer, isDeep) {
  if (isDeep) {
    return buffer.slice();
  }
  const length = buffer.length;
  const result = buffer.constructor.allocUnsafe ? buffer.constructor.allocUnsafe(length) : new buffer.constructor(length);
  buffer.copy(result);
  return result;
}

// 其他辅助函数的占位符实现
function copySymbols(source, object) { return object; }
function copySymbolsIn(source, object) { return object; }
function baseAssign(object, source) { return Object.assign(object, source); }
function baseAssignIn(object, source) { return Object.assign(object, source); }
function getAllKeys(object) { return keys(object); }
function getAllKeysIn(object) { return keys(object); }
function keysIn(object) { return keys(object); }
function assignValue(object, key, value) { object[key] = value; }
function arrayEach(array, iteratee) {
  let index = -1;
  const length = array == null ? 0 : array.length;
  while (++index < length) {
    if (iteratee(array[index], index, array) === false) {
      break;
    }
  }
  return array;
}
