/**
 * 日期处理工具函数
 * @original: 原始文件中的日期处理相关函数
 */

/**
 * 获取当前时间戳（毫秒）
 * @returns {number} 当前时间戳
 * @original: 原始文件L2028-2030中的uu1函数
 */
export function getCurrentTimestamp() {
  return Date.now();
}

/**
 * 获取当前时间戳（秒）
 * @returns {number} 当前时间戳（秒）
 */
export function getCurrentTimestampSeconds() {
  return Math.floor(Date.now() / 1000);
}

/**
 * 格式化日期
 * @param {Date|number|string} date - 日期对象、时间戳或日期字符串
 * @param {string} format - 格式字符串，默认为'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    throw new Error('Invalid date');
  }
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  const milliseconds = String(d.getMilliseconds()).padStart(3, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
    .replace('SSS', milliseconds);
}

/**
 * 解析日期字符串
 * @param {string} dateString - 日期字符串
 * @returns {Date} 解析后的日期对象
 */
export function parseDate(dateString) {
  const date = new Date(dateString);
  
  if (isNaN(date.getTime())) {
    throw new Error(`Invalid date string: ${dateString}`);
  }
  
  return date;
}

/**
 * 添加时间
 * @param {Date|number|string} date - 基础日期
 * @param {number} amount - 数量
 * @param {string} unit - 单位：'years', 'months', 'days', 'hours', 'minutes', 'seconds', 'milliseconds'
 * @returns {Date} 新的日期对象
 */
export function addTime(date, amount, unit) {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    throw new Error('Invalid date');
  }
  
  switch (unit) {
    case 'years':
      d.setFullYear(d.getFullYear() + amount);
      break;
    case 'months':
      d.setMonth(d.getMonth() + amount);
      break;
    case 'days':
      d.setDate(d.getDate() + amount);
      break;
    case 'hours':
      d.setHours(d.getHours() + amount);
      break;
    case 'minutes':
      d.setMinutes(d.getMinutes() + amount);
      break;
    case 'seconds':
      d.setSeconds(d.getSeconds() + amount);
      break;
    case 'milliseconds':
      d.setMilliseconds(d.getMilliseconds() + amount);
      break;
    default:
      throw new Error(`Invalid unit: ${unit}`);
  }
  
  return d;
}

/**
 * 减去时间
 * @param {Date|number|string} date - 基础日期
 * @param {number} amount - 数量
 * @param {string} unit - 单位
 * @returns {Date} 新的日期对象
 */
export function subtractTime(date, amount, unit) {
  return addTime(date, -amount, unit);
}

/**
 * 计算两个日期之间的差值
 * @param {Date|number|string} date1 - 日期1
 * @param {Date|number|string} date2 - 日期2
 * @param {string} unit - 返回单位：'years', 'months', 'days', 'hours', 'minutes', 'seconds', 'milliseconds'
 * @returns {number} 差值
 */
export function dateDiff(date1, date2, unit = 'milliseconds') {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  
  if (isNaN(d1.getTime()) || isNaN(d2.getTime())) {
    throw new Error('Invalid date');
  }
  
  const diffMs = d1.getTime() - d2.getTime();
  
  switch (unit) {
    case 'years':
      return diffMs / (1000 * 60 * 60 * 24 * 365.25);
    case 'months':
      return diffMs / (1000 * 60 * 60 * 24 * 30.44); // 平均月长度
    case 'days':
      return diffMs / (1000 * 60 * 60 * 24);
    case 'hours':
      return diffMs / (1000 * 60 * 60);
    case 'minutes':
      return diffMs / (1000 * 60);
    case 'seconds':
      return diffMs / 1000;
    case 'milliseconds':
      return diffMs;
    default:
      throw new Error(`Invalid unit: ${unit}`);
  }
}

/**
 * 检查是否为闰年
 * @param {number} year - 年份
 * @returns {boolean} 是否为闰年
 */
export function isLeapYear(year) {
  return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
}

/**
 * 获取月份的天数
 * @param {number} year - 年份
 * @param {number} month - 月份（1-12）
 * @returns {number} 天数
 */
export function getDaysInMonth(year, month) {
  if (month < 1 || month > 12) {
    throw new Error('Month must be between 1 and 12');
  }
  
  const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  
  if (month === 2 && isLeapYear(year)) {
    return 29;
  }
  
  return daysInMonth[month - 1];
}

/**
 * 获取日期的开始时间（当天00:00:00）
 * @param {Date|number|string} date - 日期
 * @returns {Date} 当天开始时间
 */
export function startOfDay(date) {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    throw new Error('Invalid date');
  }
  
  d.setHours(0, 0, 0, 0);
  return d;
}

/**
 * 获取日期的结束时间（当天23:59:59.999）
 * @param {Date|number|string} date - 日期
 * @returns {Date} 当天结束时间
 */
export function endOfDay(date) {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    throw new Error('Invalid date');
  }
  
  d.setHours(23, 59, 59, 999);
  return d;
}

/**
 * 获取周的开始时间（周一00:00:00）
 * @param {Date|number|string} date - 日期
 * @returns {Date} 本周开始时间
 */
export function startOfWeek(date) {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    throw new Error('Invalid date');
  }
  
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
  
  d.setDate(diff);
  d.setHours(0, 0, 0, 0);
  return d;
}

/**
 * 获取月的开始时间（1号00:00:00）
 * @param {Date|number|string} date - 日期
 * @returns {Date} 本月开始时间
 */
export function startOfMonth(date) {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    throw new Error('Invalid date');
  }
  
  d.setDate(1);
  d.setHours(0, 0, 0, 0);
  return d;
}

/**
 * 获取年的开始时间（1月1日00:00:00）
 * @param {Date|number|string} date - 日期
 * @returns {Date} 本年开始时间
 */
export function startOfYear(date) {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    throw new Error('Invalid date');
  }
  
  d.setMonth(0, 1);
  d.setHours(0, 0, 0, 0);
  return d;
}

/**
 * 检查日期是否在今天
 * @param {Date|number|string} date - 日期
 * @returns {boolean} 是否为今天
 */
export function isToday(date) {
  const d = new Date(date);
  const today = new Date();
  
  return d.getFullYear() === today.getFullYear() &&
         d.getMonth() === today.getMonth() &&
         d.getDate() === today.getDate();
}

/**
 * 检查日期是否在昨天
 * @param {Date|number|string} date - 日期
 * @returns {boolean} 是否为昨天
 */
export function isYesterday(date) {
  const d = new Date(date);
  const yesterday = subtractTime(new Date(), 1, 'days');
  
  return d.getFullYear() === yesterday.getFullYear() &&
         d.getMonth() === yesterday.getMonth() &&
         d.getDate() === yesterday.getDate();
}

/**
 * 检查日期是否在明天
 * @param {Date|number|string} date - 日期
 * @returns {boolean} 是否为明天
 */
export function isTomorrow(date) {
  const d = new Date(date);
  const tomorrow = addTime(new Date(), 1, 'days');
  
  return d.getFullYear() === tomorrow.getFullYear() &&
         d.getMonth() === tomorrow.getMonth() &&
         d.getDate() === tomorrow.getDate();
}

/**
 * 检查日期是否在本周
 * @param {Date|number|string} date - 日期
 * @returns {boolean} 是否在本周
 */
export function isThisWeek(date) {
  const d = new Date(date);
  const now = new Date();
  
  const startWeek = startOfWeek(now);
  const endWeek = addTime(startWeek, 7, 'days');
  
  return d >= startWeek && d < endWeek;
}

/**
 * 检查日期是否在本月
 * @param {Date|number|string} date - 日期
 * @returns {boolean} 是否在本月
 */
export function isThisMonth(date) {
  const d = new Date(date);
  const now = new Date();
  
  return d.getFullYear() === now.getFullYear() &&
         d.getMonth() === now.getMonth();
}

/**
 * 检查日期是否在本年
 * @param {Date|number|string} date - 日期
 * @returns {boolean} 是否在本年
 */
export function isThisYear(date) {
  const d = new Date(date);
  const now = new Date();
  
  return d.getFullYear() === now.getFullYear();
}

/**
 * 获取相对时间描述
 * @param {Date|number|string} date - 日期
 * @param {Date|number|string} baseDate - 基准日期，默认为当前时间
 * @returns {string} 相对时间描述
 */
export function getRelativeTime(date, baseDate = new Date()) {
  const d = new Date(date);
  const base = new Date(baseDate);
  
  if (isNaN(d.getTime()) || isNaN(base.getTime())) {
    throw new Error('Invalid date');
  }
  
  const diffMs = base.getTime() - d.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffSeconds < 60) {
    return diffSeconds <= 0 ? 'just now' : `${diffSeconds} seconds ago`;
  } else if (diffMinutes < 60) {
    return `${diffMinutes} minutes ago`;
  } else if (diffHours < 24) {
    return `${diffHours} hours ago`;
  } else if (diffDays < 30) {
    return `${diffDays} days ago`;
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30);
    return `${months} months ago`;
  } else {
    const years = Math.floor(diffDays / 365);
    return `${years} years ago`;
  }
}

/**
 * 将时间戳转换为ISO字符串
 * @param {number} timestamp - 时间戳（毫秒）
 * @returns {string} ISO字符串
 */
export function timestampToISO(timestamp) {
  return new Date(timestamp).toISOString();
}

/**
 * 将ISO字符串转换为时间戳
 * @param {string} isoString - ISO字符串
 * @returns {number} 时间戳（毫秒）
 */
export function isoToTimestamp(isoString) {
  const date = new Date(isoString);
  
  if (isNaN(date.getTime())) {
    throw new Error(`Invalid ISO string: ${isoString}`);
  }
  
  return date.getTime();
}

/**
 * 获取时区偏移量（分钟）
 * @param {Date} date - 日期对象，默认为当前时间
 * @returns {number} 时区偏移量（分钟）
 */
export function getTimezoneOffset(date = new Date()) {
  return date.getTimezoneOffset();
}

/**
 * 将日期转换为UTC
 * @param {Date|number|string} date - 日期
 * @returns {Date} UTC日期
 */
export function toUTC(date) {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    throw new Error('Invalid date');
  }
  
  return new Date(d.getTime() + (d.getTimezoneOffset() * 60000));
}

/**
 * 将UTC日期转换为本地时间
 * @param {Date|number|string} utcDate - UTC日期
 * @returns {Date} 本地时间
 */
export function fromUTC(utcDate) {
  const d = new Date(utcDate);
  
  if (isNaN(d.getTime())) {
    throw new Error('Invalid date');
  }
  
  return new Date(d.getTime() - (d.getTimezoneOffset() * 60000));
}

/**
 * 格式化持续时间
 * @param {number} milliseconds - 毫秒数
 * @param {Object} options - 选项
 * @param {boolean} options.includeMilliseconds - 是否包含毫秒，默认false
 * @param {boolean} options.compact - 是否使用紧凑格式，默认false
 * @returns {string} 格式化后的持续时间
 */
export function formatDuration(milliseconds, options = {}) {
  const { includeMilliseconds = false, compact = false } = options;
  
  if (typeof milliseconds !== 'number' || milliseconds < 0) {
    throw new Error('Duration must be a non-negative number');
  }
  
  const ms = milliseconds % 1000;
  const seconds = Math.floor(milliseconds / 1000) % 60;
  const minutes = Math.floor(milliseconds / (1000 * 60)) % 60;
  const hours = Math.floor(milliseconds / (1000 * 60 * 60)) % 24;
  const days = Math.floor(milliseconds / (1000 * 60 * 60 * 24));
  
  const parts = [];
  
  if (days > 0) {
    parts.push(compact ? `${days}d` : `${days} day${days !== 1 ? 's' : ''}`);
  }
  
  if (hours > 0) {
    parts.push(compact ? `${hours}h` : `${hours} hour${hours !== 1 ? 's' : ''}`);
  }
  
  if (minutes > 0) {
    parts.push(compact ? `${minutes}m` : `${minutes} minute${minutes !== 1 ? 's' : ''}`);
  }
  
  if (seconds > 0 || (parts.length === 0 && !includeMilliseconds)) {
    parts.push(compact ? `${seconds}s` : `${seconds} second${seconds !== 1 ? 's' : ''}`);
  }
  
  if (includeMilliseconds && (ms > 0 || parts.length === 0)) {
    parts.push(compact ? `${ms}ms` : `${ms} millisecond${ms !== 1 ? 's' : ''}`);
  }
  
  return parts.join(compact ? ' ' : ', ') || '0 seconds';
}

/**
 * 解析持续时间字符串
 * @param {string} durationString - 持续时间字符串，如 "1h 30m", "2d 3h 45m 30s"
 * @returns {number} 毫秒数
 */
export function parseDuration(durationString) {
  if (typeof durationString !== 'string') {
    throw new Error('Duration must be a string');
  }
  
  const regex = /(\d+)\s*([dhms])/g;
  let totalMs = 0;
  let match;
  
  while ((match = regex.exec(durationString)) !== null) {
    const value = parseInt(match[1], 10);
    const unit = match[2];
    
    switch (unit) {
      case 'd':
        totalMs += value * 24 * 60 * 60 * 1000;
        break;
      case 'h':
        totalMs += value * 60 * 60 * 1000;
        break;
      case 'm':
        totalMs += value * 60 * 1000;
        break;
      case 's':
        totalMs += value * 1000;
        break;
    }
  }
  
  return totalMs;
}
