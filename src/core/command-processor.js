/**
 * 命令处理器模块
 * @original: 原始文件中的命令解析和处理相关函数
 */

import { generateRandomString } from '../utils/string-utils.js';
import { formatDuration } from '../utils/date-utils.js';
import { executeCommand, executeCommandAsync } from '../utils/process-utils.js';

/**
 * 命令类型定义
 * @original: 原始文件L46189-46538中的命令类型处理
 */
export const COMMAND_TYPES = {
  LOCAL_JSX: 'local-jsx',
  SLASH: 'slash',
  BUILT_IN: 'built_in',
  CUSTOM: 'custom'
};

/**
 * 命令执行状态定义
 */
export const COMMAND_EXECUTION_STATES = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

/**
 * 命令处理器类
 * @original: 原始文件L46189-46538中的命令处理逻辑
 */
export class CommandProcessor {
  constructor() {
    this.commands = new Map();
    this.executionHistory = [];
    this.activeCommands = new Set();
    this.commandQueue = [];
    this.maxConcurrentCommands = 5;
  }
  
  /**
   * 注册命令
   * @param {Object} command - 命令对象
   */
  registerCommand(command) {
    if (!command.name) {
      throw new Error('Command must have a name');
    }
    
    this.commands.set(command.name, {
      ...command,
      registeredAt: Date.now(),
      type: command.type || COMMAND_TYPES.CUSTOM
    });
  }
  
  /**
   * 注销命令
   * @param {string} commandName - 命令名称
   */
  unregisterCommand(commandName) {
    this.commands.delete(commandName);
  }
  
  /**
   * 获取命令
   * @param {string} commandName - 命令名称
   * @returns {Object|null} 命令对象
   */
  getCommand(commandName) {
    return this.commands.get(commandName) || null;
  }
  
  /**
   * 获取所有命令
   * @returns {Array} 命令列表
   */
  getAllCommands() {
    return Array.from(this.commands.values());
  }
  
  /**
   * 解析命令输入
   * @param {string} input - 用户输入
   * @returns {Object} 解析结果
   * @original: 原始文件L46189-46538中的uh1函数
   */
  parseCommand(input) {
    if (!input || typeof input !== 'string') {
      return { type: 'text', content: input };
    }
    
    const trimmedInput = input.trim();
    
    // 检查是否为斜杠命令
    if (trimmedInput.startsWith('/')) {
      const parts = trimmedInput.slice(1).split(/\s+/);
      const commandName = parts[0];
      const args = parts.slice(1).join(' ');
      
      const command = this.getCommand(commandName);
      if (command) {
        return {
          type: COMMAND_TYPES.SLASH,
          command,
          commandName,
          args,
          originalInput: input
        };
      }
      
      // 未知的斜杠命令
      return {
        type: 'unknown_command',
        commandName,
        args,
        originalInput: input
      };
    }
    
    // 检查是否为本地JSX命令
    if (this.isLocalJSXCommand(trimmedInput)) {
      return {
        type: COMMAND_TYPES.LOCAL_JSX,
        content: trimmedInput,
        originalInput: input
      };
    }
    
    // 普通文本
    return {
      type: 'text',
      content: input
    };
  }
  
  /**
   * 检查是否为本地JSX命令
   * @param {string} input - 输入内容
   * @returns {boolean} 是否为JSX命令
   * @private
   */
  isLocalJSXCommand(input) {
    // 简单的JSX检测逻辑
    return input.includes('<') && input.includes('>') && 
           (input.includes('React') || input.includes('jsx') || input.includes('tsx'));
  }
  
  /**
   * 执行命令
   * @param {Object} parsedCommand - 解析后的命令
   * @param {Object} context - 执行上下文
   * @returns {AsyncGenerator} 执行结果生成器
   * @original: 原始文件L46420-46511中的yq8函数
   */
  async* executeCommand(parsedCommand, context) {
    const executionId = generateRandomString(16);
    const startTime = Date.now();
    
    try {
      this.activeCommands.add(executionId);
      
      // 记录命令开始执行
      this.recordExecution(parsedCommand, executionId, 'started', 0);
      
      switch (parsedCommand.type) {
        case COMMAND_TYPES.LOCAL_JSX:
          yield* this.executeLocalJSXCommand(parsedCommand, context, executionId);
          break;
          
        case COMMAND_TYPES.SLASH:
          yield* this.executeSlashCommand(parsedCommand, context, executionId);
          break;
          
        case 'text':
          yield* this.executeTextCommand(parsedCommand, context, executionId);
          break;
          
        case 'unknown_command':
          yield this.createErrorResult(executionId, `Unknown command: /${parsedCommand.commandName}`);
          break;
          
        default:
          yield this.createErrorResult(executionId, `Unsupported command type: ${parsedCommand.type}`);
      }
      
      // 记录命令完成
      const duration = Date.now() - startTime;
      this.recordExecution(parsedCommand, executionId, 'completed', duration);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordExecution(parsedCommand, executionId, 'failed', duration, error);
      yield this.createErrorResult(executionId, `Command execution failed: ${error.message}`);
    } finally {
      this.activeCommands.delete(executionId);
    }
  }
  
  /**
   * 执行本地JSX命令
   * @param {Object} parsedCommand - 解析后的命令
   * @param {Object} context - 执行上下文
   * @param {string} executionId - 执行ID
   * @returns {AsyncGenerator} 执行结果生成器
   * @private
   */
  async* executeLocalJSXCommand(parsedCommand, context, executionId) {
    yield this.createProgressResult(executionId, 'Executing JSX command...');
    
    try {
      // @todo: 实现JSX命令执行逻辑
      // 这里应该调用实际的JSX执行引擎
      
      yield this.createSuccessResult(executionId, 'JSX command executed successfully');
    } catch (error) {
      yield this.createErrorResult(executionId, `JSX execution failed: ${error.message}`);
    }
  }
  
  /**
   * 执行斜杠命令
   * @param {Object} parsedCommand - 解析后的命令
   * @param {Object} context - 执行上下文
   * @param {string} executionId - 执行ID
   * @returns {AsyncGenerator} 执行结果生成器
   * @private
   * @original: 原始文件L46513-46580中的kq8函数
   */
  async* executeSlashCommand(parsedCommand, context, executionId) {
    const { command, args } = parsedCommand;
    
    yield this.createProgressResult(executionId, `Executing /${command.name}...`);
    
    try {
      // 获取命令提示
      const prompts = await command.getPromptForCommand(args, context);
      
      // 创建命令消息
      const commandMessage = [
        `<command-message>${command.userFacingName()} is ${command.progressMessage}…</command-message>`,
        `<command-name>/${command.userFacingName()}</command-name>`,
        args ? `<command-args>${args}</command-args>` : null
      ].filter(Boolean).join('\n');
      
      // 获取允许的工具
      const allowedTools = this.getAllowedTools(command.allowedTools || []);
      
      // 执行命令逻辑
      if (command.execute) {
        const result = await command.execute(args, context);
        yield this.createSuccessResult(executionId, result);
      } else {
        // 如果没有execute方法，返回提示信息
        yield this.createSuccessResult(executionId, {
          prompts,
          commandMessage,
          allowedTools
        });
      }
      
    } catch (error) {
      yield this.createErrorResult(executionId, `Command /${command.name} failed: ${error.message}`);
    }
  }
  
  /**
   * 执行文本命令
   * @param {Object} parsedCommand - 解析后的命令
   * @param {Object} context - 执行上下文
   * @param {string} executionId - 执行ID
   * @returns {AsyncGenerator} 执行结果生成器
   * @private
   */
  async* executeTextCommand(parsedCommand, context, executionId) {
    // 文本命令直接返回内容
    yield this.createSuccessResult(executionId, {
      type: 'text',
      content: parsedCommand.content
    });
  }
  
  /**
   * 获取允许的工具列表
   * @param {Array} toolNames - 工具名称列表
   * @returns {Array} 工具对象列表
   * @private
   * @original: 原始文件L46517中的st函数调用
   */
  getAllowedTools(toolNames) {
    // @todo: 实现工具获取逻辑
    // 这里应该从工具注册表中获取对应的工具对象
    return toolNames.map(name => ({ name, type: 'tool' }));
  }
  
  /**
   * 创建成功结果
   * @param {string} executionId - 执行ID
   * @param {*} result - 执行结果
   * @returns {Object} 结果对象
   * @private
   */
  createSuccessResult(executionId, result) {
    return {
      type: 'command_result',
      executionId,
      status: 'success',
      result,
      timestamp: Date.now()
    };
  }
  
  /**
   * 创建错误结果
   * @param {string} executionId - 执行ID
   * @param {string} errorMessage - 错误消息
   * @returns {Object} 错误结果对象
   * @private
   */
  createErrorResult(executionId, errorMessage) {
    return {
      type: 'command_result',
      executionId,
      status: 'error',
      error: errorMessage,
      timestamp: Date.now()
    };
  }
  
  /**
   * 创建进度结果
   * @param {string} executionId - 执行ID
   * @param {string} message - 进度消息
   * @returns {Object} 进度结果对象
   * @private
   */
  createProgressResult(executionId, message) {
    return {
      type: 'command_progress',
      executionId,
      message,
      timestamp: Date.now()
    };
  }
  
  /**
   * 记录命令执行
   * @param {Object} parsedCommand - 解析后的命令
   * @param {string} executionId - 执行ID
   * @param {string} status - 执行状态
   * @param {number} duration - 执行时长
   * @param {Error} error - 错误对象（如果有）
   * @private
   */
  recordExecution(parsedCommand, executionId, status, duration, error = null) {
    const record = {
      executionId,
      commandType: parsedCommand.type,
      commandName: parsedCommand.commandName || parsedCommand.type,
      args: parsedCommand.args || '',
      status,
      duration,
      error: error ? error.message : null,
      timestamp: Date.now()
    };
    
    this.executionHistory.push(record);
    
    // 限制历史记录数量
    if (this.executionHistory.length > 1000) {
      this.executionHistory = this.executionHistory.slice(-1000);
    }
  }
  
  /**
   * 获取执行统计
   * @returns {Object} 统计信息
   */
  getExecutionStats() {
    const total = this.executionHistory.length;
    const successful = this.executionHistory.filter(r => r.status === 'completed').length;
    const failed = this.executionHistory.filter(r => r.status === 'failed').length;
    
    const commandUsage = {};
    this.executionHistory.forEach(record => {
      if (!commandUsage[record.commandName]) {
        commandUsage[record.commandName] = { total: 0, successful: 0, failed: 0 };
      }
      commandUsage[record.commandName].total++;
      if (record.status === 'completed') {
        commandUsage[record.commandName].successful++;
      } else if (record.status === 'failed') {
        commandUsage[record.commandName].failed++;
      }
    });
    
    const avgDuration = total > 0 
      ? this.executionHistory.reduce((sum, r) => sum + r.duration, 0) / total 
      : 0;
    
    return {
      total,
      successful,
      failed,
      successRate: total > 0 ? (successful / total) * 100 : 0,
      averageDuration: avgDuration,
      commandUsage,
      activeCommandsCount: this.activeCommands.size,
      registeredCommandsCount: this.commands.size
    };
  }
  
  /**
   * 清理执行历史
   */
  clearExecutionHistory() {
    this.executionHistory = [];
  }
  
  /**
   * 取消所有活动命令
   */
  cancelAllActiveCommands() {
    this.activeCommands.clear();
    this.commandQueue = [];
  }
  
  /**
   * 检查命令是否正在执行
   * @param {string} executionId - 执行ID
   * @returns {boolean} 是否正在执行
   */
  isCommandActive(executionId) {
    return this.activeCommands.has(executionId);
  }
  
  /**
   * 获取活动命令列表
   * @returns {Array} 活动命令执行ID列表
   */
  getActiveCommands() {
    return Array.from(this.activeCommands);
  }
  
  /**
   * 添加命令到队列
   * @param {Object} parsedCommand - 解析后的命令
   * @param {Object} context - 执行上下文
   */
  queueCommand(parsedCommand, context) {
    this.commandQueue.push({
      parsedCommand,
      context,
      queuedAt: Date.now()
    });
  }
  
  /**
   * 处理命令队列
   * @returns {AsyncGenerator} 处理结果生成器
   */
  async* processCommandQueue() {
    while (this.commandQueue.length > 0 && this.activeCommands.size < this.maxConcurrentCommands) {
      const queuedCommand = this.commandQueue.shift();
      if (queuedCommand) {
        yield* this.executeCommand(queuedCommand.parsedCommand, queuedCommand.context);
      }
    }
  }
}

// 创建默认命令处理器实例
export const commandProcessor = new CommandProcessor();

/**
 * 注册命令的简单函数
 * @param {Object} command - 命令对象
 */
export function registerCommand(command) {
  return commandProcessor.registerCommand(command);
}

/**
 * 解析命令的简单函数
 * @param {string} input - 用户输入
 * @returns {Object} 解析结果
 */
export function parseCommand(input) {
  return commandProcessor.parseCommand(input);
}

/**
 * 执行命令的简单函数
 * @param {string} input - 用户输入
 * @param {Object} context - 执行上下文
 * @returns {AsyncGenerator} 执行结果生成器
 */
export async function* executeCommand(input, context) {
  const parsedCommand = commandProcessor.parseCommand(input);
  yield* commandProcessor.executeCommand(parsedCommand, context);
}
