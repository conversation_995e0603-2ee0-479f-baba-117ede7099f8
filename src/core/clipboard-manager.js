/**
 * 剪贴板管理器模块
 * @original: 原始文件中的剪贴板操作相关函数
 */

import { execSync } from 'child_process';
import { getPlatform } from '../utils/string-utils.js';

/**
 * 剪贴板管理器类
 * @original: 原始文件L50113-50142中的剪贴板操作逻辑
 */
export class ClipboardManager {
  constructor() {
    this.platform = getPlatform();
    this.commands = this.getClipboardCommands();
  }
  
  /**
   * 获取不同平台的剪贴板命令
   * @returns {Array} 命令数组
   * @private
   * @original: 原始文件L50115-50121中的命令配置
   */
  getClipboardCommands() {
    const commandMap = {
      macos: ['pbcopy'],
      linux: ['xclip -selection clipboard', 'wl-copy'],
      wsl: ['clip.exe'],
      windows: ['clip'],
      unknown: ['xclip -selection clipboard', 'wl-copy']
    };
    
    return commandMap[this.platform] || commandMap.unknown;
  }
  
  /**
   * 复制文本到剪贴板
   * @param {string} text - 要复制的文本
   * @returns {boolean} 是否复制成功
   * @original: 原始文件L50113-50131中的euB函数
   */
  copyToClipboard(text) {
    for (const command of this.commands) {
      try {
        execSync(command, {
          input: text,
          encoding: 'utf-8'
        });
        return true;
      } catch (error) {
        console.error(`Failed to execute clipboard command "${command}": ${error}`);
        continue;
      }
    }
    
    console.error(`Failed to copy to clipboard on ${this.platform}`);
    return false;
  }
  
  /**
   * 从剪贴板读取文本（如果支持）
   * @returns {string|null} 剪贴板内容，如果失败则返回null
   */
  readFromClipboard() {
    const readCommands = {
      macos: ['pbpaste'],
      linux: ['xclip -selection clipboard -o', 'wl-paste'],
      windows: ['powershell -command "Get-Clipboard"'],
      wsl: ['powershell.exe -command "Get-Clipboard"'],
      unknown: ['xclip -selection clipboard -o', 'wl-paste']
    };
    
    const commands = readCommands[this.platform] || readCommands.unknown;
    
    for (const command of commands) {
      try {
        const result = execSync(command, {
          encoding: 'utf-8'
        });
        return result.toString().trim();
      } catch (error) {
        console.error(`Failed to execute clipboard read command "${command}": ${error}`);
        continue;
      }
    }
    
    console.error(`Failed to read from clipboard on ${this.platform}`);
    return null;
  }
  
  /**
   * 检查剪贴板是否可用
   * @returns {boolean} 剪贴板是否可用
   */
  isClipboardAvailable() {
    try {
      // 尝试复制一个空字符串来测试剪贴板是否可用
      return this.copyToClipboard('');
    } catch {
      return false;
    }
  }
  
  /**
   * 获取剪贴板错误消息
   * @returns {string} 错误消息
   * @original: 原始文件L50133-50142中的AmB函数
   */
  getClipboardErrorMessage() {
    const errorMessages = {
      macos: 'Failed to copy to clipboard. Make sure the `pbcopy` command is available on your system and try again.',
      windows: 'Failed to copy to clipboard. Make sure the `clip` command is available on your system and try again.',
      wsl: 'Failed to copy to clipboard. Make sure the `clip.exe` command is available in your WSL environment and try again.',
      linux: 'Failed to copy to clipboard. Make sure `xclip` or `wl-copy` is installed on your system and try again.',
      unknown: 'Failed to copy to clipboard. Make sure `xclip` or `wl-copy` is installed on your system and try again.'
    };
    
    return errorMessages[this.platform] || errorMessages.unknown;
  }
  
  /**
   * 复制文件内容到剪贴板
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否复制成功
   */
  copyFileToClipboard(filePath) {
    try {
      const fs = require('fs');
      const content = fs.readFileSync(filePath, 'utf-8');
      return this.copyToClipboard(content);
    } catch (error) {
      console.error(`Failed to copy file ${filePath} to clipboard:`, error);
      return false;
    }
  }
  
  /**
   * 复制JSON对象到剪贴板
   * @param {Object} obj - 要复制的对象
   * @param {boolean} pretty - 是否格式化，默认为true
   * @returns {boolean} 是否复制成功
   */
  copyObjectToClipboard(obj, pretty = true) {
    try {
      const jsonString = pretty ? JSON.stringify(obj, null, 2) : JSON.stringify(obj);
      return this.copyToClipboard(jsonString);
    } catch (error) {
      console.error('Failed to copy object to clipboard:', error);
      return false;
    }
  }
  
  /**
   * 复制数组到剪贴板（每行一个元素）
   * @param {Array} array - 要复制的数组
   * @param {string} separator - 分隔符，默认为换行符
   * @returns {boolean} 是否复制成功
   */
  copyArrayToClipboard(array, separator = '\n') {
    try {
      const text = array.join(separator);
      return this.copyToClipboard(text);
    } catch (error) {
      console.error('Failed to copy array to clipboard:', error);
      return false;
    }
  }
  
  /**
   * 复制命令输出到剪贴板
   * @param {string} command - 要执行的命令
   * @returns {boolean} 是否复制成功
   */
  copyCommandOutputToClipboard(command) {
    try {
      const output = execSync(command, { encoding: 'utf-8' });
      return this.copyToClipboard(output);
    } catch (error) {
      console.error(`Failed to copy command output to clipboard: ${error}`);
      return false;
    }
  }
  
  /**
   * 获取剪贴板内容的统计信息
   * @returns {Object|null} 统计信息
   */
  getClipboardStats() {
    const content = this.readFromClipboard();
    
    if (!content) {
      return null;
    }
    
    const lines = content.split('\n');
    const words = content.split(/\s+/).filter(word => word.length > 0);
    const characters = content.length;
    const charactersNoSpaces = content.replace(/\s/g, '').length;
    
    return {
      lines: lines.length,
      words: words.length,
      characters,
      charactersNoSpaces,
      isEmpty: content.trim().length === 0
    };
  }
  
  /**
   * 清空剪贴板
   * @returns {boolean} 是否清空成功
   */
  clearClipboard() {
    return this.copyToClipboard('');
  }
  
  /**
   * 检查剪贴板内容是否为JSON
   * @returns {boolean} 是否为有效JSON
   */
  isClipboardContentJSON() {
    const content = this.readFromClipboard();
    
    if (!content) {
      return false;
    }
    
    try {
      JSON.parse(content);
      return true;
    } catch {
      return false;
    }
  }
  
  /**
   * 从剪贴板解析JSON
   * @returns {Object|null} 解析后的对象，如果失败则返回null
   */
  parseClipboardJSON() {
    const content = this.readFromClipboard();
    
    if (!content) {
      return null;
    }
    
    try {
      return JSON.parse(content);
    } catch (error) {
      console.error('Failed to parse clipboard content as JSON:', error);
      return null;
    }
  }
  
  /**
   * 检查剪贴板内容是否为URL
   * @returns {boolean} 是否为有效URL
   */
  isClipboardContentURL() {
    const content = this.readFromClipboard();
    
    if (!content) {
      return false;
    }
    
    try {
      new URL(content.trim());
      return true;
    } catch {
      return false;
    }
  }
  
  /**
   * 从剪贴板获取URL
   * @returns {URL|null} URL对象，如果失败则返回null
   */
  getClipboardURL() {
    const content = this.readFromClipboard();
    
    if (!content) {
      return null;
    }
    
    try {
      return new URL(content.trim());
    } catch {
      return null;
    }
  }
}

// 创建默认剪贴板管理器实例
export const clipboardManager = new ClipboardManager();

/**
 * 简单的复制到剪贴板函数
 * @param {string} text - 要复制的文本
 * @returns {boolean} 是否复制成功
 * @original: 原始文件L50113-50131中的euB函数
 */
export function copyToClipboard(text) {
  return clipboardManager.copyToClipboard(text);
}

/**
 * 简单的从剪贴板读取函数
 * @returns {string|null} 剪贴板内容
 */
export function readFromClipboard() {
  return clipboardManager.readFromClipboard();
}

/**
 * 获取剪贴板错误消息
 * @returns {string} 错误消息
 * @original: 原始文件L50133-50142中的AmB函数
 */
export function getClipboardErrorMessage() {
  return clipboardManager.getClipboardErrorMessage();
}
