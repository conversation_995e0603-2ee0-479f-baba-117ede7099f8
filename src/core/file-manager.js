/**
 * 文件管理器模块
 * @original: 原始文件中的文件操作相关函数
 */

import fs from 'fs';
import path from 'path';
import { homedir } from 'os';
import { isValidPath, sanitizePath, getFileExtension } from '../utils/file-utils.js';

/**
 * 文件管理器类
 * @original: 原始文件中的文件操作逻辑
 */
export class FileManager {
  constructor() {
    this.readFileState = new Map();
    this.fileWatchers = new Map();
  }
  
  /**
   * 解析路径（支持~和相对路径）
   * @param {string} filePath - 文件路径
   * @param {string} basePath - 基础路径，默认为当前工作目录
   * @returns {string} 解析后的绝对路径
   * @original: 原始文件L50597-50602中的pN8函数
   */
  resolvePath(filePath, basePath = process.cwd()) {
    if (!filePath) return basePath;
    if (filePath === '~') return homedir();
    if (filePath.startsWith('~/')) return path.join(homedir(), filePath.substring(2));
    if (path.isAbsolute(filePath)) return filePath;
    return path.resolve(basePath, filePath);
  }
  
  /**
   * 解析输入路径信息
   * @param {string} input - 输入路径
   * @param {string} basePath - 基础路径
   * @returns {Object} 包含目录和前缀的对象
   * @original: 原始文件L50604-50619中的iN8函数
   */
  parseInputPath(input, basePath) {
    if (!input) {
      return {
        directory: basePath || process.cwd(),
        prefix: ''
      };
    }
    
    const resolvedPath = this.resolvePath(input, basePath);
    
    if (input.endsWith('/') || input.endsWith(path.sep)) {
      return {
        directory: resolvedPath,
        prefix: ''
      };
    }
    
    const directory = path.dirname(resolvedPath);
    const prefix = path.basename(input);
    
    return {
      directory,
      prefix
    };
  }
  
  /**
   * 读取目录内容
   * @param {string} dirPath - 目录路径
   * @returns {Array} 目录内容数组
   * @original: 原始文件L50621-50633中的nN8函数
   */
  readDirectory(dirPath) {
    try {
      return fs.readdirSync(dirPath, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory() && !dirent.name.startsWith('.'))
        .map(dirent => ({
          name: dirent.name,
          path: path.join(dirPath, dirent.name),
          type: 'directory'
        }))
        .slice(0, 100);
    } catch (error) {
      console.error(error instanceof Error ? error : new Error(String(error)));
      return [];
    }
  }
  
  /**
   * 读取文件内容
   * @param {string} filePath - 文件路径
   * @param {string} encoding - 编码，默认为'utf8'
   * @returns {string} 文件内容
   */
  readFile(filePath, encoding = 'utf8') {
    const resolvedPath = this.resolvePath(filePath);
    
    if (!fs.existsSync(resolvedPath)) {
      throw new Error(`File not found: ${filePath}`);
    }
    
    const content = fs.readFileSync(resolvedPath, { encoding });
    
    // 记录文件读取状态
    this.readFileState.set(resolvedPath, {
      content,
      timestamp: fs.statSync(resolvedPath).mtimeMs
    });
    
    return content;
  }
  
  /**
   * 写入文件内容
   * @param {string} filePath - 文件路径
   * @param {string} content - 文件内容
   * @param {string} encoding - 编码，默认为'utf8'
   * @returns {Object} 写入结果
   * @original: 原始文件L30595-30647中的写入逻辑
   */
  writeFile(filePath, content, encoding = 'utf8') {
    const resolvedPath = this.resolvePath(filePath);
    const dirPath = path.dirname(resolvedPath);
    const fileExists = fs.existsSync(resolvedPath);
    
    // 创建目录（如果不存在）
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    
    // 检查文件是否已被修改
    if (fileExists) {
      const fileState = this.readFileState.get(resolvedPath);
      if (!fileState) {
        throw new Error('File has not been read yet. Read it first before writing to it.');
      }
      
      const currentMtime = fs.statSync(resolvedPath).mtimeMs;
      if (currentMtime > fileState.timestamp) {
        throw new Error('File has been modified since read. Read it again before attempting to write it.');
      }
    }
    
    // 写入文件
    fs.writeFileSync(resolvedPath, content, { encoding });
    
    // 更新文件状态
    this.readFileState.set(resolvedPath, {
      content,
      timestamp: fs.statSync(resolvedPath).mtimeMs
    });
    
    return {
      type: fileExists ? 'update' : 'create',
      filePath: resolvedPath,
      content
    };
  }
  
  /**
   * 检查文件是否存在
   * @param {string} filePath - 文件路径
   * @returns {boolean} 文件是否存在
   */
  fileExists(filePath) {
    const resolvedPath = this.resolvePath(filePath);
    return fs.existsSync(resolvedPath);
  }
  
  /**
   * 检查路径是否为文件
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否为文件
   * @original: 原始文件L31752-31758中的pW8函数
   */
  isFile(filePath) {
    const resolvedPath = this.resolvePath(filePath);
    try {
      return fs.existsSync(resolvedPath) && fs.statSync(resolvedPath).isFile();
    } catch {
      return false;
    }
  }
  
  /**
   * 检查路径是否为目录
   * @param {string} dirPath - 目录路径
   * @returns {boolean} 是否为目录
   */
  isDirectory(dirPath) {
    const resolvedPath = this.resolvePath(dirPath);
    try {
      return fs.existsSync(resolvedPath) && fs.statSync(resolvedPath).isDirectory();
    } catch {
      return false;
    }
  }
  
  /**
   * 获取文件统计信息
   * @param {string} filePath - 文件路径
   * @returns {Object} 文件统计信息
   */
  getFileStats(filePath) {
    const resolvedPath = this.resolvePath(filePath);
    
    if (!fs.existsSync(resolvedPath)) {
      throw new Error(`File not found: ${filePath}`);
    }
    
    const stats = fs.statSync(resolvedPath);
    
    return {
      size: stats.size,
      isFile: stats.isFile(),
      isDirectory: stats.isDirectory(),
      mtime: stats.mtime,
      ctime: stats.ctime,
      atime: stats.atime,
      mode: stats.mode
    };
  }
  
  /**
   * 删除文件
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否删除成功
   */
  deleteFile(filePath) {
    const resolvedPath = this.resolvePath(filePath);
    
    if (!fs.existsSync(resolvedPath)) {
      return false;
    }
    
    try {
      fs.unlinkSync(resolvedPath);
      this.readFileState.delete(resolvedPath);
      return true;
    } catch (error) {
      console.error(`Failed to delete file ${filePath}:`, error);
      return false;
    }
  }
  
  /**
   * 复制文件
   * @param {string} sourcePath - 源文件路径
   * @param {string} destPath - 目标文件路径
   * @returns {boolean} 是否复制成功
   */
  copyFile(sourcePath, destPath) {
    const resolvedSource = this.resolvePath(sourcePath);
    const resolvedDest = this.resolvePath(destPath);
    
    if (!fs.existsSync(resolvedSource)) {
      throw new Error(`Source file not found: ${sourcePath}`);
    }
    
    try {
      const destDir = path.dirname(resolvedDest);
      if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
      }
      
      fs.copyFileSync(resolvedSource, resolvedDest);
      return true;
    } catch (error) {
      console.error(`Failed to copy file from ${sourcePath} to ${destPath}:`, error);
      return false;
    }
  }
  
  /**
   * 移动文件
   * @param {string} sourcePath - 源文件路径
   * @param {string} destPath - 目标文件路径
   * @returns {boolean} 是否移动成功
   */
  moveFile(sourcePath, destPath) {
    const resolvedSource = this.resolvePath(sourcePath);
    const resolvedDest = this.resolvePath(destPath);
    
    if (!fs.existsSync(resolvedSource)) {
      throw new Error(`Source file not found: ${sourcePath}`);
    }
    
    try {
      const destDir = path.dirname(resolvedDest);
      if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
      }
      
      fs.renameSync(resolvedSource, resolvedDest);
      
      // 更新文件状态映射
      const fileState = this.readFileState.get(resolvedSource);
      if (fileState) {
        this.readFileState.delete(resolvedSource);
        this.readFileState.set(resolvedDest, fileState);
      }
      
      return true;
    } catch (error) {
      console.error(`Failed to move file from ${sourcePath} to ${destPath}:`, error);
      return false;
    }
  }
  
  /**
   * 创建目录
   * @param {string} dirPath - 目录路径
   * @param {boolean} recursive - 是否递归创建，默认为true
   * @returns {boolean} 是否创建成功
   */
  createDirectory(dirPath, recursive = true) {
    const resolvedPath = this.resolvePath(dirPath);
    
    try {
      fs.mkdirSync(resolvedPath, { recursive });
      return true;
    } catch (error) {
      console.error(`Failed to create directory ${dirPath}:`, error);
      return false;
    }
  }
  
  /**
   * 删除目录
   * @param {string} dirPath - 目录路径
   * @param {boolean} recursive - 是否递归删除，默认为false
   * @returns {boolean} 是否删除成功
   */
  deleteDirectory(dirPath, recursive = false) {
    const resolvedPath = this.resolvePath(dirPath);
    
    if (!fs.existsSync(resolvedPath)) {
      return false;
    }
    
    try {
      fs.rmSync(resolvedPath, { recursive, force: true });
      return true;
    } catch (error) {
      console.error(`Failed to delete directory ${dirPath}:`, error);
      return false;
    }
  }
  
  /**
   * 监听文件变化
   * @param {string} filePath - 文件路径
   * @param {Function} callback - 回调函数
   * @returns {Function} 取消监听的函数
   */
  watchFile(filePath, callback) {
    const resolvedPath = this.resolvePath(filePath);
    
    if (this.fileWatchers.has(resolvedPath)) {
      this.fileWatchers.get(resolvedPath).close();
    }
    
    const watcher = fs.watch(resolvedPath, (eventType, filename) => {
      callback(eventType, filename, resolvedPath);
    });
    
    this.fileWatchers.set(resolvedPath, watcher);
    
    return () => {
      watcher.close();
      this.fileWatchers.delete(resolvedPath);
    };
  }
  
  /**
   * 停止监听所有文件
   */
  stopAllWatchers() {
    for (const watcher of this.fileWatchers.values()) {
      watcher.close();
    }
    this.fileWatchers.clear();
  }
  
  /**
   * 获取文件的MIME类型
   * @param {string} filePath - 文件路径
   * @returns {string} MIME类型
   */
  getMimeType(filePath) {
    const ext = getFileExtension(filePath).toLowerCase();
    
    const mimeTypes = {
      '.js': 'application/javascript',
      '.json': 'application/json',
      '.html': 'text/html',
      '.css': 'text/css',
      '.txt': 'text/plain',
      '.md': 'text/markdown',
      '.xml': 'application/xml',
      '.pdf': 'application/pdf',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.zip': 'application/zip',
      '.tar': 'application/x-tar',
      '.gz': 'application/gzip'
    };
    
    return mimeTypes[ext] || 'application/octet-stream';
  }
  
  /**
   * 检查文件是否为文本文件
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否为文本文件
   */
  isTextFile(filePath) {
    const mimeType = this.getMimeType(filePath);
    return mimeType.startsWith('text/') || 
           mimeType === 'application/javascript' ||
           mimeType === 'application/json' ||
           mimeType === 'application/xml';
  }
  
  /**
   * 获取文件编码
   * @param {string} filePath - 文件路径
   * @returns {string} 文件编码
   */
  getFileEncoding(filePath) {
    // 简化的编码检测，实际项目中可能需要更复杂的检测逻辑
    if (!this.isTextFile(filePath)) {
      return 'binary';
    }
    
    try {
      const buffer = fs.readFileSync(this.resolvePath(filePath));
      
      // 检查BOM
      if (buffer.length >= 3 && buffer[0] === 0xEF && buffer[1] === 0xBB && buffer[2] === 0xBF) {
        return 'utf8';
      }
      
      if (buffer.length >= 2 && buffer[0] === 0xFF && buffer[1] === 0xFE) {
        return 'utf16le';
      }
      
      if (buffer.length >= 2 && buffer[0] === 0xFE && buffer[1] === 0xFF) {
        return 'utf16be';
      }
      
      // 默认使用UTF-8
      return 'utf8';
    } catch {
      return 'utf8';
    }
  }
  
  /**
   * 搜索文件内容
   * @param {string} filePath - 文件路径
   * @param {string|RegExp} pattern - 搜索模式
   * @param {Object} options - 选项
   * @returns {Array} 搜索结果
   */
  searchInFile(filePath, pattern, options = {}) {
    const {
      caseSensitive = false,
      maxResults = 100,
      includeLineNumbers = true
    } = options;
    
    const content = this.readFile(filePath);
    const lines = content.split('\n');
    const results = [];
    
    const regex = pattern instanceof RegExp ? pattern : 
                  new RegExp(pattern, caseSensitive ? 'g' : 'gi');
    
    for (let i = 0; i < lines.length && results.length < maxResults; i++) {
      const line = lines[i];
      const matches = line.match(regex);
      
      if (matches) {
        results.push({
          lineNumber: includeLineNumbers ? i + 1 : undefined,
          line: line.trim(),
          matches
        });
      }
    }
    
    return results;
  }
  
  /**
   * 获取文件列表（递归）
   * @param {string} dirPath - 目录路径
   * @param {Object} options - 选项
   * @returns {Array} 文件列表
   */
  getFileList(dirPath, options = {}) {
    const {
      recursive = true,
      includeHidden = false,
      extensions = null,
      maxDepth = 10
    } = options;
    
    const resolvedPath = this.resolvePath(dirPath);
    const files = [];
    
    const traverse = (currentPath, depth = 0) => {
      if (depth > maxDepth) return;
      
      try {
        const items = fs.readdirSync(currentPath, { withFileTypes: true });
        
        for (const item of items) {
          if (!includeHidden && item.name.startsWith('.')) {
            continue;
          }
          
          const itemPath = path.join(currentPath, item.name);
          const relativePath = path.relative(resolvedPath, itemPath);
          
          if (item.isFile()) {
            if (extensions) {
              const ext = path.extname(item.name);
              if (!extensions.includes(ext)) {
                continue;
              }
            }
            
            files.push({
              name: item.name,
              path: itemPath,
              relativePath,
              type: 'file',
              size: fs.statSync(itemPath).size
            });
          } else if (item.isDirectory() && recursive) {
            traverse(itemPath, depth + 1);
          }
        }
      } catch (error) {
        console.error(`Error reading directory ${currentPath}:`, error);
      }
    };
    
    traverse(resolvedPath);
    return files;
  }
  
  /**
   * 创建备份文件
   * @param {string} filePath - 文件路径
   * @param {string} backupSuffix - 备份后缀，默认为'.bak'
   * @returns {string} 备份文件路径
   */
  createBackup(filePath, backupSuffix = '.bak') {
    const resolvedPath = this.resolvePath(filePath);
    
    if (!fs.existsSync(resolvedPath)) {
      throw new Error(`File not found: ${filePath}`);
    }
    
    const backupPath = resolvedPath + backupSuffix;
    fs.copyFileSync(resolvedPath, backupPath);
    
    return backupPath;
  }
  
  /**
   * 恢复备份文件
   * @param {string} filePath - 原文件路径
   * @param {string} backupSuffix - 备份后缀，默认为'.bak'
   * @returns {boolean} 是否恢复成功
   */
  restoreBackup(filePath, backupSuffix = '.bak') {
    const resolvedPath = this.resolvePath(filePath);
    const backupPath = resolvedPath + backupSuffix;
    
    if (!fs.existsSync(backupPath)) {
      throw new Error(`Backup file not found: ${backupPath}`);
    }
    
    try {
      fs.copyFileSync(backupPath, resolvedPath);
      
      // 更新文件状态
      const content = fs.readFileSync(resolvedPath, 'utf8');
      this.readFileState.set(resolvedPath, {
        content,
        timestamp: fs.statSync(resolvedPath).mtimeMs
      });
      
      return true;
    } catch (error) {
      console.error(`Failed to restore backup for ${filePath}:`, error);
      return false;
    }
  }
  
  /**
   * 清理文件状态缓存
   * @param {string} filePath - 文件路径，如果不提供则清理所有
   */
  clearFileState(filePath) {
    if (filePath) {
      const resolvedPath = this.resolvePath(filePath);
      this.readFileState.delete(resolvedPath);
    } else {
      this.readFileState.clear();
    }
  }
  
  /**
   * 获取文件状态
   * @param {string} filePath - 文件路径
   * @returns {Object|null} 文件状态
   */
  getFileState(filePath) {
    const resolvedPath = this.resolvePath(filePath);
    return this.readFileState.get(resolvedPath) || null;
  }
}

// 创建默认文件管理器实例
export const fileManager = new FileManager();

/**
 * 创建带有特定配置的文件管理器
 * @param {Object} options - 配置选项
 * @returns {FileManager} 文件管理器实例
 */
export function createFileManager(options = {}) {
  return new FileManager(options);
}
