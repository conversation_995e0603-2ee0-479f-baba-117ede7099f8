/**
 * UI管理器模块
 * @original: 原始文件中的UI组件和交互相关函数
 */

import React from 'react';
import { formatDuration } from '../utils/date-utils.js';
import { formatFileSize } from '../utils/string-utils.js';

/**
 * UI组件类型定义
 */
export const UI_COMPONENT_TYPES = {
  MESSAGE: 'message',
  TOOL_USE: 'tool_use',
  PERMISSION: 'permission',
  DIFF: 'diff',
  SELECTOR: 'selector',
  PROGRESS: 'progress'
};

/**
 * 消息类型定义
 * @original: 原始文件L30016-30062中的消息类型处理
 */
export const MESSAGE_TYPES = {
  ATTACHMENT: 'attachment',
  ASSISTANT: 'assistant',
  USER: 'user',
  SYSTEM: 'system'
};

/**
 * UI管理器类
 * @original: 原始文件中的UI组件管理逻辑
 */
export class UIManager {
  constructor() {
    this.componentRegistry = new Map();
    this.eventHandlers = new Map();
    this.keyBindings = new Map();
    this.theme = 'default';
  }
  
  /**
   * 注册UI组件
   * @param {string} name - 组件名称
   * @param {Function} component - 组件函数
   */
  registerComponent(name, component) {
    this.componentRegistry.set(name, component);
  }
  
  /**
   * 获取UI组件
   * @param {string} name - 组件名称
   * @returns {Function|null} 组件函数
   */
  getComponent(name) {
    return this.componentRegistry.get(name) || null;
  }
  
  /**
   * 渲染消息组件
   * @param {Object} messageData - 消息数据
   * @param {Object} options - 渲染选项
   * @returns {React.Element} 渲染的组件
   * @original: 原始文件L30001-30062中的zS函数
   */
  renderMessage(messageData, options = {}) {
    const {
      message,
      messages,
      addMargin = true,
      tools,
      verbose = false,
      erroredToolUseIDs = new Set(),
      inProgressToolUseIDs = new Set(),
      resolvedToolUseIDs = new Set(),
      progressMessagesForMessage = [],
      shouldAnimate = true,
      shouldShowDot = true,
      style = 'default',
      width
    } = { ...messageData, ...options };
    
    switch (message.type) {
      case MESSAGE_TYPES.ATTACHMENT:
        return this.renderAttachmentMessage(message, { addMargin, verbose });
        
      case MESSAGE_TYPES.ASSISTANT:
        return this.renderAssistantMessage(message, {
          addMargin,
          tools,
          verbose,
          erroredToolUseIDs,
          inProgressToolUseIDs,
          resolvedToolUseIDs,
          progressMessagesForMessage,
          shouldAnimate,
          shouldShowDot,
          width
        });
        
      case MESSAGE_TYPES.USER:
        return this.renderUserMessage(message, {
          messages,
          addMargin,
          tools,
          progressMessagesForMessage,
          style,
          verbose
        });
        
      case MESSAGE_TYPES.SYSTEM:
        return this.renderSystemMessage(message, { addMargin, verbose });
        
      default:
        console.error(`Unknown message type: ${message.type}`);
        return null;
    }
  }
  
  /**
   * 渲染附件消息
   * @param {Object} message - 消息对象
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   */
  renderAttachmentMessage(message, options) {
    // 实现附件消息渲染逻辑
    const attachment = message.attachment;
    if (!attachment) {
      return React.createElement('div', { className: 'attachment-message error' },
        'Invalid attachment message'
      );
    }

    const attachmentStyle = {
      padding: '10px',
      border: '1px solid #ddd',
      borderRadius: '4px',
      backgroundColor: '#f9f9f9',
      margin: '5px 0'
    };

    return React.createElement('div', {
      className: 'attachment-message',
      style: attachmentStyle
    }, [
      React.createElement('div', {
        key: 'header',
        style: { fontWeight: 'bold', marginBottom: '5px' }
      }, `📎 ${attachment.name || 'Attachment'}`),

      attachment.size && React.createElement('div', {
        key: 'size',
        style: { fontSize: '12px', color: '#666' }
      }, `Size: ${this.formatFileSize(attachment.size)}`),

      attachment.type && React.createElement('div', {
        key: 'type',
        style: { fontSize: '12px', color: '#666' }
      }, `Type: ${attachment.type}`)
    ]);
  }
  
  /**
   * 渲染助手消息
   * @param {Object} message - 消息对象
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   * @original: 原始文件L30024-30040中的助手消息渲染
   */
  renderAssistantMessage(message, options) {
    return React.createElement('div', 
      { 
        className: 'assistant-message',
        style: { flexDirection: 'column', width: '100%' }
      },
      message.message.content.map((content, index) => 
        this.renderAssistantContent(content, { ...options, key: index })
      )
    );
  }
  
  /**
   * 渲染助手内容
   * @param {Object} content - 内容对象
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   * @original: 原始文件L30099-30145中的IW8函数
   */
  renderAssistantContent(content, options) {
    switch (content.type) {
      case 'tool_use':
        return this.renderToolUseComponent(content, options);
        
      case 'text':
        return this.renderTextComponent(content, options);
        
      case 'redacted_thinking':
        return this.renderRedactedThinking(options);
        
      case 'thinking':
        return this.renderThinking(content, options);
        
      default:
        console.error(`Unable to render content type: ${content.type}`);
        return null;
    }
  }
  
  /**
   * 渲染用户消息
   * @param {Object} message - 消息对象
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   * @original: 原始文件L30041-30055中的用户消息渲染
   */
  renderUserMessage(message, options) {
    return React.createElement('div',
      {
        className: 'user-message',
        style: { flexDirection: 'column', width: '100%' }
      },
      message.message.content.map((content, index) =>
        this.renderUserContent(content, { ...options, message, key: index })
      )
    );
  }
  
  /**
   * 渲染用户内容
   * @param {Object} content - 内容对象
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   * @original: 原始文件L30064-30097中的FW8函数
   */
  renderUserContent(content, options) {
    switch (content.type) {
      case 'text':
        return this.renderUserText(content, options);
        
      case 'tool_result':
        return this.renderToolResult(content, options);
        
      default:
        return null;
    }
  }
  
  /**
   * 渲染系统消息
   * @param {Object} message - 消息对象
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   */
  renderSystemMessage(message, options) {
    return React.createElement('div', 
      { className: 'system-message' },
      message.message.content
    );
  }
  
  /**
   * 渲染权限确认组件
   * @param {Object} permissionData - 权限数据
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @original: 原始文件L31002-31165中的权限确认UI
   */
  renderPermissionConfirm(permissionData, options = {}) {
    const {
      toolUseConfirm,
      onAllow,
      onReject,
      verbose = false
    } = permissionData;
    
    const toolName = toolUseConfirm.tool.name;
    const input = toolUseConfirm.input;
    
    // 根据工具类型渲染不同的权限确认界面
    switch (toolName) {
      case 'Edit':
        return this.renderEditPermissionConfirm(toolUseConfirm, options);
        
      case 'Write':
        return this.renderWritePermissionConfirm(toolUseConfirm, options);
        
      case 'MultiEdit':
        return this.renderMultiEditPermissionConfirm(toolUseConfirm, options);
        
      default:
        return this.renderGenericPermissionConfirm(toolUseConfirm, options);
    }
  }
  
  /**
   * 渲染编辑权限确认
   * @param {Object} toolUseConfirm - 工具使用确认对象
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   * @original: 原始文件L31002-31165中的ojB函数
   */
  renderEditPermissionConfirm(toolUseConfirm, options) {
    const { file_path, old_string, new_string, replace_all = false } = toolUseConfirm.input;
    
    return React.createElement('div',
      {
        className: 'permission-confirm edit-permission',
        style: {
          flexDirection: 'column',
          borderStyle: 'round',
          borderColor: 'permission',
          marginTop: 1,
          padding: 1
        }
      },
      React.createElement('div', { className: 'permission-title' },
        React.createElement('span', { style: { fontWeight: 'bold', color: 'permission' } }, 'Edit file')
      ),
      this.renderEditPreview(file_path, old_string, new_string, replace_all, options),
      this.renderPermissionOptions(file_path, 'edit', options)
    );
  }
  
  /**
   * 渲染写入权限确认
   * @param {Object} toolUseConfirm - 工具使用确认对象
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   * @original: 原始文件L31591-31741中的FyB函数
   */
  renderWritePermissionConfirm(toolUseConfirm, options) {
    const { file_path, content } = toolUseConfirm.input;
    const fileExists = this.checkFileExists(file_path);
    
    return React.createElement('div',
      {
        className: 'permission-confirm write-permission',
        style: {
          flexDirection: 'column',
          borderStyle: 'round',
          borderColor: 'permission',
          marginTop: 1,
          padding: 1
        }
      },
      React.createElement('div', { className: 'permission-title' },
        React.createElement('span', { style: { fontWeight: 'bold', color: 'permission' } }, 
          fileExists ? 'Edit file' : 'Create file'
        )
      ),
      this.renderFilePreview(file_path, content, options),
      this.renderPermissionOptions(file_path, 'edit', options)
    );
  }
  
  /**
   * 渲染通用权限确认
   * @param {Object} toolUseConfirm - 工具使用确认对象
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   * @original: 原始文件L31405-31510中的Sb1函数
   */
  renderGenericPermissionConfirm(toolUseConfirm, options) {
    const toolName = toolUseConfirm.tool.userFacingName(toolUseConfirm.input);
    
    return React.createElement('div',
      {
        className: 'permission-confirm generic-permission',
        style: {
          flexDirection: 'column',
          borderStyle: 'round',
          borderColor: 'permission',
          marginTop: 1,
          padding: 1
        }
      },
      React.createElement('div', { className: 'permission-title' },
        React.createElement('span', { style: { fontWeight: 'bold', color: 'permission' } }, 'Tool use')
      ),
      React.createElement('div', { className: 'tool-description' },
        React.createElement('span', null, toolName),
        React.createElement('span', { style: { color: 'secondaryText' } }, toolUseConfirm.description)
      ),
      this.renderGenericPermissionOptions(options)
    );
  }
  
  /**
   * 渲染编辑预览
   * @param {string} filePath - 文件路径
   * @param {string} oldString - 旧字符串
   * @param {string} newString - 新字符串
   * @param {boolean} replaceAll - 是否替换所有
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   */
  renderEditPreview(filePath, oldString, newString, replaceAll, options) {
    // 实现编辑预览逻辑，显示将要进行的更改
    const previewStyle = {
      border: '1px solid #ccc',
      borderRadius: '4px',
      padding: '10px',
      margin: '10px 0',
      backgroundColor: '#f8f8f8',
      fontFamily: 'monospace',
      fontSize: '12px'
    };

    const diffStyle = {
      margin: '10px 0',
      padding: '5px',
      borderRadius: '3px'
    };

    const removedStyle = {
      ...diffStyle,
      backgroundColor: '#ffebee',
      color: '#c62828'
    };

    const addedStyle = {
      ...diffStyle,
      backgroundColor: '#e8f5e8',
      color: '#2e7d32'
    };

    return React.createElement('div', {
      className: 'edit-preview',
      style: previewStyle
    }, [
      React.createElement('div', { style: { fontWeight: 'bold' } }, 
        `File: ${this.getDisplayPath(filePath, options.verbose)}`
      ),
      React.createElement('div', { className: 'edit-details' },
        `Replace${replaceAll ? ' all' : ''}: "${oldString}" → "${newString}"`
      )
    );
  }
  
  /**
   * 渲染文件预览
   * @param {string} filePath - 文件路径
   * @param {string} content - 文件内容
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   * @original: 原始文件L31545-31590中的GyB函数
   */
  renderFilePreview(filePath, content, options) {
    const displayPath = this.getDisplayPath(filePath, options.verbose);
    const language = this.getFileLanguage(filePath);
    
    return React.createElement('div',
      {
        className: 'file-preview',
        style: {
          borderColor: 'secondaryBorder',
          borderStyle: 'round',
          flexDirection: 'column',
          padding: 1
        }
      },
      React.createElement('div', { className: 'file-header' },
        React.createElement('span', { style: { fontWeight: 'bold' } }, displayPath)
      ),
      React.createElement('div', { className: 'file-content' },
        this.renderCodeBlock(content || '(No content)', language)
      )
    );
  }
  
  /**
   * 渲染权限选项
   * @param {string} filePath - 文件路径
   * @param {string} operationType - 操作类型
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   * @original: 原始文件L30989-31001中的tK函数
   */
  renderPermissionOptions(filePath, operationType, options) {
    const permissionOptions = this.getPermissionOptions(filePath, operationType, options);
    
    return React.createElement('div', { className: 'permission-options' },
      React.createElement('div', null, 'Do you want to proceed?'),
      React.createElement('div', { className: 'option-list' },
        permissionOptions.map((option, index) =>
          React.createElement('div', 
            { 
              key: index, 
              className: 'permission-option',
              onClick: () => this.handlePermissionChoice(option.value, options)
            },
            option.label
          )
        )
      )
    );
  }
  
  /**
   * 获取权限选项
   * @param {string} filePath - 文件路径
   * @param {string} operationType - 操作类型
   * @param {Object} options - 选项
   * @returns {Array} 权限选项数组
   * @private
   * @original: 原始文件L30989-31001中的tK函数
   */
  getPermissionOptions(filePath, operationType, options) {
    const baseOptions = [
      { label: 'Yes', value: 'yes' },
      { label: 'No, and tell Claude what to do differently (esc)', value: 'no' }
    ];
    
    if (operationType === 'edit') {
      const directoryPath = this.getDirectoryFromPath(filePath);
      const dontAskLabel = `Yes, add ${directoryPath} and don't ask again this session`;
      
      return [
        baseOptions[0],
        { label: dontAskLabel, value: 'yes-dont-ask-again' },
        baseOptions[1]
      ];
    }
    
    return baseOptions;
  }
  
  /**
   * 渲染通用权限选项
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   */
  renderGenericPermissionOptions(options) {
    const genericOptions = [
      { label: 'Yes', value: 'yes' },
      { label: "Yes, and don't ask again for this tool", value: 'yes-dont-ask-again' },
      { label: 'No, and tell Claude what to do differently (esc)', value: 'no' }
    ];
    
    return React.createElement('div', { className: 'permission-options' },
      React.createElement('div', null, 'Do you want to proceed?'),
      React.createElement('div', { className: 'option-list' },
        genericOptions.map((option, index) =>
          React.createElement('div',
            {
              key: index,
              className: 'permission-option',
              onClick: () => this.handlePermissionChoice(option.value, options)
            },
            option.label
          )
        )
      )
    );
  }
  
  /**
   * 渲染代码块
   * @param {string} code - 代码内容
   * @param {string} language - 编程语言
   * @returns {React.Element} 渲染的组件
   * @private
   */
  renderCodeBlock(code, language = 'text') {
    return React.createElement('pre',
      {
        className: `code-block language-${language}`,
        style: {
          backgroundColor: '#f5f5f5',
          padding: '10px',
          borderRadius: '4px',
          overflow: 'auto'
        }
      },
      React.createElement('code', null, code)
    );
  }
  
  /**
   * 渲染工具使用组件
   * @param {Object} content - 内容对象
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   */
  renderToolUseComponent(content, options) {
    return React.createElement('div', { className: 'tool-use-component' },
      `Tool: ${content.name}`,
      content.input && React.createElement('pre', null, JSON.stringify(content.input, null, 2))
    );
  }
  
  /**
   * 渲染文本组件
   * @param {Object} content - 内容对象
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   */
  renderTextComponent(content, options) {
    return React.createElement('div', { className: 'text-component' }, content.text);
  }
  
  /**
   * 渲染工具结果
   * @param {Object} content - 内容对象
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   */
  renderToolResult(content, options) {
    return React.createElement('div', { className: 'tool-result' },
      React.createElement('div', { className: 'tool-result-header' }, 'Tool Result'),
      React.createElement('pre', null, content.content)
    );
  }
  
  /**
   * 渲染思考过程
   * @param {Object} content - 内容对象
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   */
  renderThinking(content, options) {
    return React.createElement('div', 
      { 
        className: 'thinking-component',
        style: { fontStyle: 'italic', color: '#666' }
      },
      'Thinking: ', content.text
    );
  }
  
  /**
   * 渲染被编辑的思考过程
   * @param {Object} options - 选项
   * @returns {React.Element} 渲染的组件
   * @private
   */
  renderRedactedThinking(options) {
    return React.createElement('div',
      {
        className: 'redacted-thinking',
        style: { fontStyle: 'italic', color: '#999' }
      },
      '[Thinking process redacted]'
    );
  }
  
  /**
   * 获取显示路径
   * @param {string} filePath - 文件路径
   * @param {boolean} verbose - 是否详细显示
   * @returns {string} 显示路径
   * @private
   */
  getDisplayPath(filePath, verbose = false) {
    if (verbose) {
      return filePath;
    }
    
    // 返回相对路径
    const path = require('path');
    return path.relative(process.cwd(), filePath);
  }
  
  /**
   * 获取文件语言
   * @param {string} filePath - 文件路径
   * @returns {string} 编程语言
   * @private
   */
  getFileLanguage(filePath) {
    const path = require('path');
    const ext = path.extname(filePath).toLowerCase();
    
    const languageMap = {
      '.js': 'javascript',
      '.ts': 'typescript',
      '.jsx': 'jsx',
      '.tsx': 'tsx',
      '.py': 'python',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'c',
      '.cs': 'csharp',
      '.php': 'php',
      '.rb': 'ruby',
      '.go': 'go',
      '.rs': 'rust',
      '.html': 'html',
      '.css': 'css',
      '.scss': 'scss',
      '.json': 'json',
      '.xml': 'xml',
      '.yaml': 'yaml',
      '.yml': 'yaml',
      '.md': 'markdown',
      '.sh': 'bash',
      '.sql': 'sql'
    };
    
    return languageMap[ext] || 'text';
  }
  
  /**
   * 检查文件是否存在
   * @param {string} filePath - 文件路径
   * @returns {boolean} 文件是否存在
   * @private
   */
  checkFileExists(filePath) {
    const fs = require('fs');
    return fs.existsSync(filePath);
  }
  
  /**
   * 从路径获取目录
   * @param {string} filePath - 文件路径
   * @returns {string} 目录路径
   * @private
   */
  getDirectoryFromPath(filePath) {
    const path = require('path');
    return path.dirname(filePath);
  }
  
  /**
   * 处理权限选择
   * @param {string} choice - 选择值
   * @param {Object} options - 选项
   * @private
   */
  handlePermissionChoice(choice, options) {
    if (options.onChoice) {
      options.onChoice(choice);
    }
  }
  
  /**
   * 设置主题
   * @param {string} theme - 主题名称
   */
  setTheme(theme) {
    this.theme = theme;
  }
  
  /**
   * 获取当前主题
   * @returns {string} 当前主题
   */
  getTheme() {
    return this.theme;
  }
  
  /**
   * 注册键盘绑定
   * @param {string} key - 键名
   * @param {Function} handler - 处理函数
   */
  registerKeyBinding(key, handler) {
    this.keyBindings.set(key, handler);
  }
  
  /**
   * 处理键盘事件
   * @param {string} key - 键名
   * @param {Object} modifiers - 修饰键
   * @returns {boolean} 是否处理了事件
   */
  handleKeyEvent(key, modifiers = {}) {
    const binding = this.keyBindings.get(key);
    if (binding) {
      binding(key, modifiers);
      return true;
    }
    return false;
  }
  
  /**
   * 清除所有键盘绑定
   */
  clearKeyBindings() {
    this.keyBindings.clear();
  }
}

// 创建默认UI管理器实例
export const uiManager = new UIManager();

/**
 * 渲染消息的简单函数
 * @param {Object} messageData - 消息数据
 * @param {Object} options - 选项
 * @returns {React.Element} 渲染的组件
 */
export function renderMessage(messageData, options = {}) {
  return uiManager.renderMessage(messageData, options);
}

/**
 * 渲染权限确认的简单函数
 * @param {Object} permissionData - 权限数据
 * @param {Object} options - 选项
 * @returns {React.Element} 渲染的组件
 */
export function renderPermissionConfirm(permissionData, options = {}) {
  return uiManager.renderPermissionConfirm(permissionData, options);
}
