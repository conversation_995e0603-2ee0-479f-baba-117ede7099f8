/**
 * SDK流式处理模块
 * @original: 原始文件中的SDK流式对话处理相关函数
 */

import { generateRandomString } from '../utils/string-utils.js';
import { formatDuration } from '../utils/date-utils.js';
import { executeCommand, executeCommandAsync } from '../utils/process-utils.js';

/**
 * 流式消息类型定义
 * @original: 原始文件L55588-55629中的消息类型处理
 */
export const STREAM_MESSAGE_TYPES = {
  SYSTEM: 'system',
  ASSISTANT: 'assistant',
  USER: 'user',
  PROGRESS: 'progress',
  RESULT: 'result',
  CONTROL_REQUEST: 'control_request',
  CONTROL_RESPONSE: 'control_response'
};

/**
 * 流式结果子类型定义
 * @original: 原始文件L55575-55586中的结果子类型
 */
export const STREAM_RESULT_SUBTYPES = {
  SUCCESS: 'success',
  ERROR_DURING_EXECUTION: 'error_during_execution',
  ERROR_MAX_TURNS: 'error_max_turns'
};

/**
 * 控制请求子类型定义
 * @original: 原始文件L55548-55558中的控制请求处理
 */
export const CONTROL_REQUEST_SUBTYPES = {
  INTERRUPT: 'interrupt',
  INITIALIZE: 'initialize',
  CAN_USE_TOOL: 'can_use_tool'
};

/**
 * 流式输入处理器类
 * @original: 原始文件L55226-55323中的GT0类
 */
export class StreamInputProcessor {
  constructor(inputStream) {
    this.input = inputStream;
    this.pendingRequests = new Map();
    this.inputClosed = false;
    this.structuredInput = this.read();
  }
  
  /**
   * 读取并处理输入流
   * @returns {AsyncGenerator} 结构化输入生成器
   * @original: 原始文件L55235-55254中的read方法
   */
  async* read() {
    let buffer = '';
    
    try {
      for await (const chunk of this.input) {
        buffer += chunk;
        
        let newlineIndex;
        while ((newlineIndex = buffer.indexOf('\n')) !== -1) {
          const line = buffer.slice(0, newlineIndex);
          buffer = buffer.slice(newlineIndex + 1);
          
          const processedLine = this.processLine(line);
          if (processedLine) {
            yield processedLine;
          }
        }
      }
      
      // 处理最后一行（如果没有换行符结尾）
      if (buffer) {
        const processedLine = this.processLine(buffer);
        if (processedLine) {
          yield processedLine;
        }
      }
    } finally {
      this.inputClosed = true;
      
      // 拒绝所有待处理的请求
      for (const request of this.pendingRequests.values()) {
        request.reject(new Error('Tool permission stream closed before response received'));
      }
    }
  }
  
  /**
   * 处理单行输入
   * @param {string} line - 输入行
   * @returns {Object|null} 处理后的消息
   * @private
   * @original: 原始文件L55255-55286中的processLine方法
   */
  processLine(line) {
    try {
      const message = JSON.parse(line);
      
      // 处理控制响应
      if (message.type === STREAM_MESSAGE_TYPES.CONTROL_RESPONSE) {
        const pendingRequest = this.pendingRequests.get(message.response.request_id);
        if (!pendingRequest) {
          console.error(`No pending request for ID: ${message.response.request_id}`);
          return null;
        }
        
        this.pendingRequests.delete(message.response.request_id);
        
        if (message.response.subtype === 'error') {
          pendingRequest.reject(new Error(message.response.error));
          return null;
        }
        
        const response = message.response.response;
        if (pendingRequest.schema) {
          try {
            pendingRequest.resolve(pendingRequest.schema.parse(response));
          } catch (error) {
            pendingRequest.reject(error);
          }
        } else {
          pendingRequest.resolve({});
        }
        return null;
      }
      
      // 验证消息类型
      if (message.type !== STREAM_MESSAGE_TYPES.USER && 
          message.type !== STREAM_MESSAGE_TYPES.CONTROL_REQUEST) {
        this.throwError(`Expected message type 'user' or 'control', got '${message.type}'`);
      }
      
      // 处理控制请求
      if (message.type === STREAM_MESSAGE_TYPES.CONTROL_REQUEST) {
        if (!message.request) {
          this.throwError('Missing request on control_request');
        }
        return message;
      }
      
      // 验证用户消息
      if (message.message.role !== 'user') {
        this.throwError(`Expected message role 'user', got '${message.message.role}'`);
      }
      
      return message;
      
    } catch (error) {
      console.error(`Error parsing streaming input line: ${line}: ${error}`);
      process.exit(1);
    }
  }
  
  /**
   * 创建工具使用权限检查器
   * @returns {Function} 权限检查函数
   * @original: 原始文件L55287-55322中的createCanUseTool方法
   */
  createCanUseTool() {
    return async (tool, input, context, assistantMessage, toolUseId) => {
      // 首先尝试默认权限检查
      const defaultResult = await this.defaultPermissionCheck(tool, input, context, assistantMessage, toolUseId);
      if (defaultResult.behavior === 'allow' || defaultResult.behavior === 'deny') {
        return defaultResult;
      }
      
      // 如果需要用户确认，发送控制请求
      const requestId = generateRandomString(16);
      const controlRequest = {
        type: STREAM_MESSAGE_TYPES.CONTROL_REQUEST,
        request_id: requestId,
        request: {
          subtype: CONTROL_REQUEST_SUBTYPES.CAN_USE_TOOL,
          tool_name: tool.name,
          input: input
        }
      };
      
      if (this.inputClosed) {
        return this.createPermissionResult('deny', 
          'Tool permission stream closed before permission prompt could be sent', 
          tool.name);
      }
      
      // 发送控制请求
      console.log(JSON.stringify(controlRequest));
      
      // 等待响应
      return new Promise((resolve, reject) => {
        this.pendingRequests.set(requestId, {
          resolve: (result) => resolve(result),
          reject,
          schema: this.getPermissionSchema()
        });
      }).then(result => {
        return this.createPermissionResult(result.behavior, result.message, tool.name);
      }).catch(error => {
        return this.createPermissionResult('deny', String(error), tool.name);
      });
    };
  }
  
  /**
   * 默认权限检查
   * @param {Object} tool - 工具对象
   * @param {Object} input - 输入参数
   * @param {Object} context - 上下文
   * @param {Object} assistantMessage - 助手消息
   * @param {string} toolUseId - 工具使用ID
   * @returns {Promise<Object>} 权限检查结果
   * @private
   */
  async defaultPermissionCheck(tool, input, context, assistantMessage, toolUseId) {
    // 实现默认权限检查逻辑
    try {
      // 检查工具是否在允许列表中
      const allowedTools = context.allowedTools || [];
      if (allowedTools.length > 0 && !allowedTools.includes(tool.name)) {
        return { behavior: 'deny', reason: 'Tool not in allowed list' };
      }

      // 检查工具是否在拒绝列表中
      const deniedTools = context.deniedTools || [];
      if (deniedTools.includes(tool.name)) {
        return { behavior: 'deny', reason: 'Tool is explicitly denied' };
      }

      // 检查危险操作
      const dangerousPatterns = [
        /rm\s+-rf/,
        /sudo/,
        /chmod\s+777/,
        /passwd/,
        /shutdown/,
        /reboot/
      ];

      const inputStr = JSON.stringify(input).toLowerCase();
      for (const pattern of dangerousPatterns) {
        if (pattern.test(inputStr)) {
          return { behavior: 'ask', reason: 'Potentially dangerous operation detected' };
        }
      }

      // 默认行为：对于文件操作询问，其他允许
      const fileOperationTools = ['Edit', 'Write', 'Delete', 'Move', 'Copy'];
      if (fileOperationTools.includes(tool.name)) {
        return { behavior: 'ask', reason: 'File operation requires confirmation' };
      }

      return { behavior: 'allow' };

    } catch (error) {
      console.warn('Permission check failed:', error);
      return { behavior: 'ask', reason: 'Permission check error' };
    }
  }
  
  /**
   * 创建权限结果
   * @param {string} behavior - 权限行为
   * @param {string} message - 消息
   * @param {string} toolName - 工具名称
   * @returns {Object} 权限结果
   * @private
   */
  createPermissionResult(behavior, message, toolName) {
    const decisionReason = {
      type: 'permissionPromptTool',
      permissionPromptToolName: toolName,
      toolResult: { behavior, message }
    };
    
    switch (behavior) {
      case 'allow':
        return {
          behavior: 'allow',
          updatedInput: {},
          decisionReason
        };
      case 'deny':
        return {
          behavior: 'deny',
          message,
          decisionReason,
          ruleSuggestions: null
        };
      default:
        return {
          behavior: 'deny',
          message: 'Unknown permission behavior',
          decisionReason,
          ruleSuggestions: null
        };
    }
  }
  
  /**
   * 获取权限模式
   * @returns {Object} 权限验证模式
   * @private
   */
  getPermissionSchema() {
    // 实现权限验证模式
    return {
      type: 'object',
      properties: {
        behavior: {
          type: 'string',
          enum: ['allow', 'deny', 'ask'],
          description: 'Permission behavior'
        },
        reason: {
          type: 'string',
          description: 'Reason for the permission decision'
        },
        conditions: {
          type: 'object',
          properties: {
            allowedTools: {
              type: 'array',
              items: { type: 'string' },
              description: 'List of allowed tools'
            },
            deniedTools: {
              type: 'array',
              items: { type: 'string' },
              description: 'List of denied tools'
            },
            requireConfirmation: {
              type: 'boolean',
              description: 'Whether to require user confirmation'
            }
          }
        }
      },
      required: ['behavior']
    };
  }
  
  /**
   * 抛出错误并退出
   * @param {string} message - 错误消息
   * @private
   */
  throwError(message) {
    console.error(message);
    process.exit(1);
  }
}

/**
 * 流式输出处理器类
 * @original: 原始文件L55327-55387中的FT0类
 */
export class StreamOutputProcessor {
  constructor(onReturn = null) {
    this.returned = onReturn;
    this.queue = [];
    this.readResolve = null;
    this.readReject = null;
    this.isDone = false;
    this.hasError = null;
    this.started = false;
  }
  
  /**
   * 异步迭代器接口
   * @returns {StreamOutputProcessor} 自身
   */
  [Symbol.asyncIterator]() {
    if (this.started) {
      throw new Error('Stream can only be iterated once');
    }
    this.started = true;
    return this;
  }
  
  /**
   * 获取下一个值
   * @returns {Promise<Object>} 迭代器结果
   */
  next() {
    // 如果队列中有值，立即返回
    if (this.queue.length > 0) {
      return Promise.resolve({
        done: false,
        value: this.queue.shift()
      });
    }
    
    // 如果已完成，返回完成状态
    if (this.isDone) {
      return Promise.resolve({
        done: true,
        value: undefined
      });
    }
    
    // 如果有错误，抛出错误
    if (this.hasError) {
      return Promise.reject(this.hasError);
    }
    
    // 等待新值
    return new Promise((resolve, reject) => {
      this.readResolve = resolve;
      this.readReject = reject;
    });
  }
  
  /**
   * 将值加入队列
   * @param {*} value - 要加入的值
   */
  enqueue(value) {
    if (this.readResolve) {
      const resolve = this.readResolve;
      this.readResolve = undefined;
      this.readReject = undefined;
      resolve({
        done: false,
        value
      });
    } else {
      this.queue.push(value);
    }
  }
  
  /**
   * 标记流为完成状态
   */
  done() {
    this.isDone = true;
    
    if (this.readResolve) {
      const resolve = this.readResolve;
      this.readResolve = undefined;
      this.readReject = undefined;
      resolve({
        done: true,
        value: undefined
      });
    }
  }
  
  /**
   * 设置错误状态
   * @param {Error} error - 错误对象
   */
  error(error) {
    this.hasError = error;
    
    if (this.readReject) {
      const reject = this.readReject;
      this.readResolve = undefined;
      this.readReject = undefined;
      reject(error);
    }
  }
  
  /**
   * 返回迭代器结果
   * @returns {Promise<Object>} 完成状态
   */
  return() {
    this.isDone = true;
    
    if (this.returned) {
      this.returned();
    }
    
    return Promise.resolve({
      done: true,
      value: undefined
    });
  }
}

/**
 * SDK流式对话处理器
 * @original: 原始文件L55390-55587中的XcB函数
 */
export class SDKStreamProcessor {
  constructor() {
    this.defaultConcurrencyLimit = 10;
    this.defaultTimeout = 300000; // 5分钟
  }
  
  /**
   * 处理流式对话
   * @param {Object} options - 处理选项
   * @returns {AsyncGenerator} 流式结果生成器
   * @original: 原始文件L55390-55587中的XcB函数
   */
  async* processStream(options) {
    const {
      commands,
      permissionContext,
      prompt,
      cwd,
      tools,
      mcpClients = [],
      verbose = false,
      maxTurns,
      canUseTool,
      mutableMessages = [],
      customSystemPrompt,
      appendSystemPrompt,
      userSpecifiedModel,
      fallbackModel,
      getQueuedCommands = () => [],
      removeQueuedCommands = () => {},
      abortController
    } = options;
    
    // 设置环境变量
    if (!process.env.CLAUDE_CODE_ENTRYPOINT) {
      process.env.CLAUDE_CODE_ENTRYPOINT = 'sdk-cli';
    }
    
    // 设置工作目录
    if (cwd) {
      process.chdir(cwd);
    }
    
    const startTime = Date.now();
    const model = userSpecifiedModel ? this.resolveModel(userSpecifiedModel) : this.getDefaultModel();
    
    // 初始化系统提示、工具和资源
    const [systemPrompts, apiConfig, mcpResources] = await Promise.all([
      this.buildSystemPrompts(tools, model, Array.from(permissionContext.additionalWorkingDirectories.keys()), mcpClients, permissionContext),
      this.getAPIConfig(),
      this.getMCPResources()
    ]);
    
    const allSystemPrompts = [
      ...(customSystemPrompt ? [customSystemPrompt] : systemPrompts),
      ...(appendSystemPrompt ? [appendSystemPrompt] : [])
    ];
    
    // 转换消息格式
    const internalMessages = this.convertToInternalFormat(mutableMessages);
    const maxThinkingTokens = this.calculateThinkingTokens(internalMessages);
    
    // 创建执行上下文
    const context = this.createExecutionContext({
      messages: internalMessages,
      commands,
      tools,
      verbose,
      model,
      maxThinkingTokens,
      mcpClients,
      mcpResources,
      permissionContext,
      abortController,
      getQueuedCommands,
      removeQueuedCommands
    });
    
    // 处理初始提示
    const {
      messages: processedMessages,
      shouldQuery,
      allowedTools,
      maxThinkingTokens: updatedThinkingTokens,
      model: updatedModel
    } = await this.processInitialPrompt(prompt, context, internalMessages);
    
    // 更新用户消息到可变消息数组
    for (const message of processedMessages) {
      if (message.type === 'user') {
        mutableMessages.push({
          type: message.type,
          message: message.message,
          session_id: this.generateSessionId(),
          parent_tool_use_id: null
        });
      }
    }
    
    const allMessages = [...internalMessages, ...processedMessages];
    
    // 更新权限上下文
    const updatedPermissionContext = {
      ...permissionContext,
      alwaysAllowRules: {
        ...permissionContext.alwaysAllowRules,
        command: allowedTools
      }
    };
    
    const finalModel = updatedModel ?? model;
    
    // 更新执行上下文
    const updatedContext = {
      ...context,
      options: {
        ...context.options,
        mainLoopModel: finalModel,
        maxThinkingTokens: updatedThinkingTokens
      },
      getToolPermissionContext: () => updatedPermissionContext
    };
    
    // 发送初始化消息
    yield {
      type: STREAM_MESSAGE_TYPES.SYSTEM,
      subtype: 'init',
      cwd: cwd || process.cwd(),
      session_id: this.generateSessionId(),
      tools: tools.map(tool => tool.name),
      mcp_servers: mcpClients.map(client => ({
        name: client.name,
        status: client.type
      })),
      model: finalModel,
      permissionMode: updatedPermissionContext.mode,
      slash_commands: commands.map(cmd => cmd.name),
      apiKeySource: this.getAPIKeySource()
    };
    
    // 如果不需要查询，直接返回成功结果
    if (!shouldQuery) {
      yield {
        type: STREAM_MESSAGE_TYPES.RESULT,
        subtype: STREAM_RESULT_SUBTYPES.SUCCESS,
        is_error: false,
        duration_ms: Date.now() - startTime,
        duration_api_ms: this.getAPITime(),
        num_turns: allMessages.length - 1,
        result: '',
        session_id: this.generateSessionId(),
        total_cost_usd: this.getTotalCost(),
        usage: this.getUsageStats()
      };
      return;
    }
    
    // 执行主对话循环
    let usage = this.getUsageStats();
    let turnCount = 0;
    
    for await (const result of this.executeMainLoop(allMessages, allSystemPrompts, apiConfig, mcpResources, canUseTool, updatedContext, fallbackModel)) {
      if (result.type === 'assistant' || result.type === 'user') {
        allMessages.push(result);
        await this.saveMessages(allMessages);
      }
      
      switch (result.type) {
        case 'assistant':
        case 'progress':
        case 'user':
          yield* this.convertToSDKFormat(result);
          break;
        case 'stream_event':
          if (result.event.type === 'message_start') {
            usage = this.mergeUsage(usage, result.event.message.usage);
          }
          if (result.event.type === 'message_delta') {
            usage = this.mergeUsage(usage, result.event.usage);
          }
          break;
        case 'attachment':
        case 'stream_request_start':
        case 'system':
          break;
      }
      
      // 检查最大轮次限制
      if (result.type === 'user' && maxTurns && ++turnCount >= maxTurns) {
        yield {
          type: STREAM_MESSAGE_TYPES.RESULT,
          subtype: STREAM_RESULT_SUBTYPES.ERROR_MAX_TURNS,
          duration_ms: Date.now() - startTime,
          duration_api_ms: this.getAPITime(),
          is_error: false,
          num_turns: turnCount,
          session_id: this.generateSessionId(),
          total_cost_usd: this.getTotalCost(),
          usage
        };
        return;
      }
    }
    
    // 处理最终结果
    const lastMessage = this.getLastMessage(allMessages);
    if (!lastMessage || lastMessage.type !== 'assistant') {
      yield {
        type: STREAM_MESSAGE_TYPES.RESULT,
        subtype: STREAM_RESULT_SUBTYPES.ERROR_DURING_EXECUTION,
        duration_ms: Date.now() - startTime,
        duration_api_ms: this.getAPITime(),
        is_error: false,
        num_turns: turnCount,
        session_id: this.generateSessionId(),
        total_cost_usd: this.getTotalCost(),
        usage
      };
      return;
    }
    
    const firstContent = this.getFirstContent(lastMessage.message.content);
    if (firstContent?.type !== 'text' && 
        firstContent?.type !== 'thinking' && 
        firstContent?.type !== 'redacted_thinking') {
      throw new Error(`Expected first content item to be text or thinking, but got ${JSON.stringify(lastMessage.message.content[0], null, 2)}`);
    }
    
    yield {
      type: STREAM_MESSAGE_TYPES.RESULT,
      subtype: STREAM_RESULT_SUBTYPES.SUCCESS,
      is_error: Boolean(lastMessage.isApiErrorMessage),
      duration_ms: Date.now() - startTime,
      duration_api_ms: this.getAPITime(),
      num_turns: allMessages.length - 1,
      result: firstContent.type === 'text' ? firstContent.text : '',
      session_id: this.generateSessionId(),
      total_cost_usd: this.getTotalCost(),
      usage
    };
  }
  
  /**
   * 转换为SDK格式
   * @param {Object} message - 内部消息格式
   * @returns {Generator} SDK格式消息生成器
   * @private
   * @original: 原始文件L55588-55629中的vM8函数
   */
  * convertToSDKFormat(message) {
    switch (message.type) {
      case 'assistant':
        for (const normalizedMessage of this.normalizeMessages([message])) {
          yield {
            type: 'assistant',
            message: normalizedMessage.message,
            parent_tool_use_id: null,
            session_id: this.generateSessionId()
          };
        }
        return;
        
      case 'progress':
        if (message.data.type !== 'agent_progress') return;
        
        for (const normalizedMessage of this.normalizeMessages([message.data.message])) {
          switch (normalizedMessage.type) {
            case 'assistant':
              yield {
                type: 'assistant',
                message: normalizedMessage.message,
                parent_tool_use_id: message.parentToolUseID,
                session_id: this.generateSessionId()
              };
              break;
            case 'user':
              yield {
                type: 'user',
                message: normalizedMessage.message,
                parent_tool_use_id: message.parentToolUseID,
                session_id: this.generateSessionId()
              };
              break;
          }
        }
        break;
        
      case 'user':
        for (const normalizedMessage of this.normalizeMessages([message])) {
          yield {
            type: 'user',
            message: normalizedMessage.message,
            parent_tool_use_id: null,
            session_id: this.generateSessionId()
          };
        }
        return;
    }
  }
  
  /**
   * 转换为内部格式
   * @param {Array} messages - SDK格式消息数组
   * @returns {Array} 内部格式消息数组
   * @private
   * @original: 原始文件L55630-55652中的IT0函数
   */
  convertToInternalFormat(messages) {
    return messages.flatMap(message => {
      switch (message.type) {
        case 'assistant':
          return [{
            type: 'assistant',
            message: message.message,
            uuid: generateRandomString(16),
            requestId: undefined,
            timestamp: new Date().toISOString()
          }];
        case 'user':
          return [{
            type: 'user',
            message: message.message,
            uuid: generateRandomString(16),
            timestamp: new Date().toISOString()
          }];
        default:
          return [];
      }
    });
  }
  
  // 辅助方法（需要实现）
  resolveModel(model) { return model; }
  getDefaultModel() { return 'claude-3-sonnet-20240229'; }
  buildSystemPrompts() { return Promise.resolve([]); }
  getAPIConfig() { return Promise.resolve({}); }
  getMCPResources() { return Promise.resolve({}); }
  calculateThinkingTokens() { return 0; }
  createExecutionContext(options) { return options; }
  processInitialPrompt() { return Promise.resolve({ messages: [], shouldQuery: true }); }
  executeMainLoop() { return []; }
  generateSessionId() { return generateRandomString(16); }
  getAPIKeySource() { return 'environment'; }
  getAPITime() { return 0; }
  getTotalCost() { return 0; }
  getUsageStats() { return {}; }
  saveMessages() { return Promise.resolve(); }
  mergeUsage(a, b) { return { ...a, ...b }; }
  getLastMessage(messages) { return messages[messages.length - 1]; }
  getFirstContent(content) { return content[0]; }
  normalizeMessages(messages) { return messages; }
}

// 创建默认SDK流处理器实例
export const sdkStreamProcessor = new SDKStreamProcessor();

/**
 * 处理流式对话的简单函数
 * @param {Object} options - 处理选项
 * @returns {AsyncGenerator} 流式结果生成器
 */
export async function* processSDKStream(options) {
  yield* sdkStreamProcessor.processStream(options);
}

/**
 * 创建流式输入处理器的简单函数
 * @param {ReadableStream} inputStream - 输入流
 * @returns {StreamInputProcessor} 输入处理器实例
 */
export function createStreamInputProcessor(inputStream) {
  return new StreamInputProcessor(inputStream);
}

/**
 * 创建流式输出处理器的简单函数
 * @param {Function} onReturn - 返回回调
 * @returns {StreamOutputProcessor} 输出处理器实例
 */
export function createStreamOutputProcessor(onReturn = null) {
  return new StreamOutputProcessor(onReturn);
}
