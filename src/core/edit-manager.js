/**
 * 文件编辑管理器模块
 * @original: 原始文件中的文件编辑相关函数
 */

import { diffManager } from './diff-manager.js';
import { fileExists, readFileContent, writeFileContent, getFileEncoding, getLineEnding } from '../utils/file-utils.js';
import { normalizePath, getDirectoryPath } from '../utils/path-utils.js';

/**
 * 编辑操作类型定义
 */
export const EDIT_OPERATIONS = {
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
  MULTI_EDIT: 'multi_edit'
};

/**
 * 编辑验证错误代码
 * @original: 原始文件L21454-21556中的验证错误处理
 */
export const EDIT_ERROR_CODES = {
  SAME_CONTENT: 1,
  IGNORED_FILE: 2,
  FILE_EXISTS: 3,
  FILE_NOT_FOUND: 4,
  NOTEBOOK_FILE: 5,
  FILE_NOT_READ: 6,
  FILE_MODIFIED: 7,
  STRING_NOT_FOUND: 8,
  MULTIPLE_MATCHES: 9
};

/**
 * 文件编辑管理器类
 * @original: 原始文件L21088-21127中的Jv函数和相关编辑逻辑
 */
export class EditManager {
  constructor() {
    this.readFileState = new Map(); // 存储已读取文件的状态
    this.editHistory = []; // 编辑历史记录
  }
  
  /**
   * 设置文件读取状态
   * @param {string} filePath - 文件路径
   * @param {string} content - 文件内容
   * @param {number} timestamp - 时间戳
   */
  setFileReadState(filePath, content, timestamp) {
    this.readFileState.set(filePath, {
      content,
      timestamp
    });
  }
  
  /**
   * 获取文件读取状态
   * @param {string} filePath - 文件路径
   * @returns {Object|null} 文件状态
   */
  getFileReadState(filePath) {
    return this.readFileState.get(filePath) || null;
  }
  
  /**
   * 验证单个编辑操作
   * @param {Object} editParams - 编辑参数
   * @param {Object} options - 选项
   * @returns {Object} 验证结果
   * @original: 原始文件L21446-21556中的validateInput方法
   */
  validateEdit(editParams, options = {}) {
    const { file_path, old_string, new_string, replace_all = false } = editParams;
    
    // 检查内容是否相同
    if (old_string === new_string) {
      return {
        valid: false,
        errorCode: EDIT_ERROR_CODES.SAME_CONTENT,
        message: 'old_string and new_string are exactly the same.'
      };
    }
    
    const absolutePath = normalizePath(file_path);
    
    // 检查文件是否被忽略
    if (this.isFileIgnored(absolutePath)) {
      return {
        valid: false,
        errorCode: EDIT_ERROR_CODES.IGNORED_FILE,
        message: 'File is in a directory that is ignored by project configuration.'
      };
    }
    
    const fileExistsFlag = fileExists(absolutePath);
    
    // 处理新文件创建
    if (fileExistsFlag && old_string === '') {
      const existingContent = readFileContent(absolutePath).trim();
      if (existingContent !== '') {
        return {
          valid: false,
          errorCode: EDIT_ERROR_CODES.FILE_EXISTS,
          message: 'Cannot create new file - file already exists.'
        };
      }
      return { valid: true };
    }
    
    // 处理文件不存在的情况
    if (!fileExistsFlag) {
      if (old_string === '') {
        return { valid: true }; // 创建新文件
      }
      
      return {
        valid: false,
        errorCode: EDIT_ERROR_CODES.FILE_NOT_FOUND,
        message: 'File does not exist.'
      };
    }
    
    // 检查是否为Jupyter Notebook
    if (absolutePath.endsWith('.ipynb')) {
      return {
        valid: false,
        errorCode: EDIT_ERROR_CODES.NOTEBOOK_FILE,
        message: 'File is a Jupyter Notebook. Use NotebookEdit tool instead.'
      };
    }
    
    // 检查文件是否已读取
    const readState = this.getFileReadState(absolutePath);
    if (!readState) {
      return {
        valid: false,
        errorCode: EDIT_ERROR_CODES.FILE_NOT_READ,
        message: 'File has not been read yet. Read it first before editing.'
      };
    }
    
    // 检查文件是否被修改
    const currentStats = this.getFileStats(absolutePath);
    if (currentStats.mtimeMs > readState.timestamp) {
      return {
        valid: false,
        errorCode: EDIT_ERROR_CODES.FILE_MODIFIED,
        message: 'File has been modified since read. Read it again before editing.'
      };
    }
    
    // 检查要替换的字符串是否存在
    const currentContent = readFileContent(absolutePath);
    const actualOldString = diffManager.findBetterMatch(currentContent, old_string);
    
    if (!actualOldString) {
      return {
        valid: false,
        errorCode: EDIT_ERROR_CODES.STRING_NOT_FOUND,
        message: `String to replace not found in file: ${old_string}`
      };
    }
    
    // 检查是否有多个匹配
    const occurrences = currentContent.split(actualOldString).length - 1;
    if (occurrences > 1 && !replace_all) {
      return {
        valid: false,
        errorCode: EDIT_ERROR_CODES.MULTIPLE_MATCHES,
        message: `Found ${occurrences} matches but replace_all is false. Provide more context or set replace_all to true.`
      };
    }
    
    return {
      valid: true,
      actualOldString,
      occurrences
    };
  }
  
  /**
   * 执行单个编辑操作
   * @param {Object} editParams - 编辑参数
   * @param {Object} options - 选项
   * @returns {Object} 编辑结果
   * @original: 原始文件L21574-21618中的call方法
   */
  async performEdit(editParams, options = {}) {
    const { file_path, old_string, new_string, replace_all = false } = editParams;
    
    // 验证编辑操作
    const validation = this.validateEdit(editParams, options);
    if (!validation.valid) {
      throw new Error(validation.message);
    }
    
    const absolutePath = normalizePath(file_path);
    const originalContent = fileExists(absolutePath) ? readFileContent(absolutePath) : '';
    
    // 执行编辑
    const actualOldString = validation.actualOldString || old_string;
    const { patch, updatedFile } = this.applyStringEdit({
      filePath: absolutePath,
      fileContents: originalContent,
      oldString: actualOldString,
      newString: new_string,
      replaceAll: replace_all
    });
    
    // 保存文件
    await this.saveEditedFile(absolutePath, updatedFile);
    
    // 更新读取状态
    const newStats = this.getFileStats(absolutePath);
    this.setFileReadState(absolutePath, updatedFile, newStats.mtimeMs);
    
    // 记录编辑历史
    this.recordEdit({
      filePath: file_path,
      operation: old_string === '' ? EDIT_OPERATIONS.CREATE : EDIT_OPERATIONS.UPDATE,
      oldString: actualOldString,
      newString: new_string,
      originalContent,
      patch,
      timestamp: Date.now()
    });
    
    return {
      filePath: file_path,
      oldString: actualOldString,
      newString: new_string,
      originalFile: originalContent,
      structuredPatch: patch,
      userModified: options.userModified || false,
      replaceAll: replace_all
    };
  }
  
  /**
   * 执行多重编辑操作
   * @param {Object} multiEditParams - 多重编辑参数
   * @param {Object} options - 选项
   * @returns {Object} 编辑结果
   * @original: 原始文件L21088-21127中的Jv函数
   */
  async performMultiEdit(multiEditParams, options = {}) {
    const { file_path, edits } = multiEditParams;
    
    if (!edits || edits.length === 0) {
      throw new Error('At least one edit is required');
    }
    
    const absolutePath = normalizePath(file_path);
    const originalContent = fileExists(absolutePath) ? readFileContent(absolutePath) : '';
    
    // 验证所有编辑操作
    for (let i = 0; i < edits.length; i++) {
      const edit = edits[i];
      const validation = this.validateEdit({
        file_path,
        old_string: edit.old_string,
        new_string: edit.new_string,
        replace_all: edit.replace_all
      }, options);
      
      if (!validation.valid) {
        throw new Error(`Edit ${i + 1} failed validation: ${validation.message}`);
      }
    }
    
    // 应用所有编辑
    const { patch, updatedFile } = this.applyMultipleEdits({
      filePath: absolutePath,
      fileContents: originalContent,
      edits
    });
    
    // 保存文件
    await this.saveEditedFile(absolutePath, updatedFile);
    
    // 更新读取状态
    const newStats = this.getFileStats(absolutePath);
    this.setFileReadState(absolutePath, updatedFile, newStats.mtimeMs);
    
    // 记录编辑历史
    this.recordEdit({
      filePath: file_path,
      operation: EDIT_OPERATIONS.MULTI_EDIT,
      edits,
      originalContent,
      patch,
      timestamp: Date.now()
    });
    
    return {
      filePath: file_path,
      originalFileContents: originalContent,
      structuredPatch: patch,
      edits,
      userModified: options.userModified || false
    };
  }
  
  /**
   * 应用字符串编辑
   * @param {Object} params - 编辑参数
   * @returns {Object} 编辑结果
   * @private
   * @original: 原始文件L21071-21087中的o$0函数
   */
  applyStringEdit(params) {
    const { filePath, fileContents, oldString, newString, replaceAll = false } = params;
    
    return this.applyMultipleEdits({
      filePath,
      fileContents,
      edits: [{
        old_string: oldString,
        new_string: newString,
        replace_all: replaceAll
      }]
    });
  }
  
  /**
   * 应用多个编辑操作
   * @param {Object} params - 编辑参数
   * @returns {Object} 编辑结果
   * @private
   * @original: 原始文件L21088-21127中的Jv函数
   */
  applyMultipleEdits(params) {
    const { filePath, fileContents, edits } = params;
    
    let currentContent = fileContents;
    const appliedStrings = [];
    
    // 特殊情况：空文件且只有一个空编辑
    if (!fileContents && edits.length === 1 && 
        edits[0].old_string === '' && edits[0].new_string === '') {
      const patch = diffManager.createStructuredPatch(filePath, fileContents, currentContent);
      return { patch, updatedFile: '' };
    }
    
    // 依次应用每个编辑
    for (const edit of edits) {
      const trimmedOldString = edit.old_string.replace(/\n+$/, '');
      
      // 检查是否与之前的编辑冲突
      for (const appliedString of appliedStrings) {
        if (trimmedOldString !== '' && appliedString.includes(trimmedOldString)) {
          throw new Error('Cannot edit file: old_string is a substring of a new_string from a previous edit.');
        }
      }
      
      const previousContent = currentContent;
      
      // 应用编辑
      if (edit.old_string === '') {
        currentContent = edit.new_string;
      } else {
        currentContent = this.replaceString(
          currentContent,
          edit.old_string,
          edit.new_string,
          edit.replace_all
        );
      }
      
      // 检查编辑是否成功
      if (currentContent === previousContent) {
        throw new Error('String not found in file. Failed to apply edit.');
      }
      
      appliedStrings.push(edit.new_string);
    }
    
    // 检查最终结果
    if (currentContent === fileContents) {
      throw new Error('Original and edited file match exactly. Failed to apply edit.');
    }
    
    // 生成补丁
    const patch = diffManager.createStructuredPatch(filePath, fileContents, currentContent);
    
    return {
      patch,
      updatedFile: currentContent
    };
  }
  
  /**
   * 替换字符串
   * @param {string} content - 内容
   * @param {string} oldString - 旧字符串
   * @param {string} newString - 新字符串
   * @param {boolean} replaceAll - 是否替换所有
   * @returns {string} 替换后的内容
   * @private
   * @original: 原始文件L21063-21070中的aMB函数
   */
  replaceString(content, oldString, newString, replaceAll = false) {
    const replaceFn = replaceAll 
      ? (text, search, replace) => text.replaceAll(search, replace)
      : (text, search, replace) => text.replace(search, replace);
    
    // 处理空字符串替换的特殊情况
    if (newString === '') {
      if (!oldString.endsWith('\n') && content.includes(oldString + '\n')) {
        return replaceFn(content, oldString + '\n', newString);
      }
    }
    
    return replaceFn(content, oldString, newString);
  }
  
  /**
   * 保存编辑后的文件
   * @param {string} filePath - 文件路径
   * @param {string} content - 文件内容
   * @returns {Promise<void>}
   * @private
   */
  async saveEditedFile(filePath, content) {
    // 确保目录存在
    const dirPath = getDirectoryPath(filePath);
    await this.ensureDirectoryExists(dirPath);
    
    // 获取文件编码和行结束符
    const encoding = fileExists(filePath) ? getFileEncoding(filePath) : 'utf8';
    const lineEnding = fileExists(filePath) ? getLineEnding(filePath) : 'LF';
    
    // 写入文件
    await writeFileContent(filePath, content, { encoding, lineEnding });
  }
  
  /**
   * 确保目录存在
   * @param {string} dirPath - 目录路径
   * @returns {Promise<void>}
   * @private
   */
  async ensureDirectoryExists(dirPath) {
    const fs = await import('fs');
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }
  
  /**
   * 获取文件统计信息
   * @param {string} filePath - 文件路径
   * @returns {Object} 文件统计信息
   * @private
   */
  getFileStats(filePath) {
    const fs = require('fs');
    return fs.statSync(filePath);
  }
  
  /**
   * 检查文件是否被忽略
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否被忽略
   * @private
   * @todo: 需要实现具体的忽略规则检查逻辑
   */
  isFileIgnored(filePath) {
    // @todo: 实现基于.gitignore或项目配置的文件忽略检查
    return false;
  }
  
  /**
   * 记录编辑操作
   * @param {Object} editRecord - 编辑记录
   * @private
   */
  recordEdit(editRecord) {
    this.editHistory.push({
      ...editRecord,
      id: this.generateEditId()
    });
    
    // 限制历史记录数量
    if (this.editHistory.length > 1000) {
      this.editHistory = this.editHistory.slice(-1000);
    }
  }
  
  /**
   * 生成编辑ID
   * @returns {string} 编辑ID
   * @private
   */
  generateEditId() {
    return `edit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 获取编辑历史
   * @param {number} limit - 限制数量
   * @returns {Array} 编辑历史
   */
  getEditHistory(limit = 100) {
    return this.editHistory.slice(-limit);
  }
  
  /**
   * 清除编辑历史
   */
  clearEditHistory() {
    this.editHistory = [];
  }
  
  /**
   * 检查编辑是否等价
   * @param {Object} editA - 编辑A
   * @param {Object} editB - 编辑B
   * @returns {boolean} 是否等价
   * @original: 原始文件L21289-21297中的Kv1函数
   */
  areEditsEquivalent(editA, editB) {
    if (editA.file_path !== editB.file_path) {
      return false;
    }
    
    // 检查编辑数组是否完全相同
    if (editA.edits.length === editB.edits.length && 
        editA.edits.every((edit, index) => {
          const otherEdit = editB.edits[index];
          return otherEdit && 
                 edit.old_string === otherEdit.old_string &&
                 edit.new_string === otherEdit.new_string &&
                 edit.replace_all === otherEdit.replace_all;
        })) {
      return true;
    }
    
    // 检查应用结果是否相同
    const fileContent = fileExists(editA.file_path) ? readFileContent(editA.file_path) : '';
    return this.areEditResultsEquivalent(editA.edits, editB.edits, fileContent);
  }
  
  /**
   * 检查编辑结果是否等价
   * @param {Array} editsA - 编辑数组A
   * @param {Array} editsB - 编辑数组B
   * @param {string} originalContent - 原始内容
   * @returns {boolean} 是否等价
   * @private
   * @original: 原始文件L21258-21288中的e68函数
   */
  areEditResultsEquivalent(editsA, editsB, originalContent) {
    if (editsA.length === editsB.length && 
        editsA.every((edit, index) => {
          const otherEdit = editsB[index];
          return otherEdit &&
                 edit.old_string === otherEdit.old_string &&
                 edit.new_string === otherEdit.new_string &&
                 edit.replace_all === otherEdit.replace_all;
        })) {
      return true;
    }
    
    let resultA = null;
    let resultB = null;
    let errorA = null;
    let errorB = null;
    
    // 尝试应用编辑A
    try {
      const { updatedFile } = this.applyMultipleEdits({
        filePath: 'temp',
        fileContents: originalContent,
        edits: editsA
      });
      resultA = updatedFile;
    } catch (error) {
      errorA = error.message;
    }
    
    // 尝试应用编辑B
    try {
      const { updatedFile } = this.applyMultipleEdits({
        filePath: 'temp',
        fileContents: originalContent,
        edits: editsB
      });
      resultB = updatedFile;
    } catch (error) {
      errorB = error.message;
    }
    
    // 如果都失败，检查错误是否相同
    if (errorA !== null && errorB !== null) {
      return errorA === errorB;
    }
    
    // 如果一个成功一个失败，则不等价
    if (errorA !== null || errorB !== null) {
      return false;
    }
    
    // 比较最终结果
    return resultA === resultB;
  }
  
  /**
   * 创建编辑预览
   * @param {Object} editParams - 编辑参数
   * @returns {Object} 预览结果
   */
  createEditPreview(editParams) {
    const { file_path, old_string, new_string, replace_all = false } = editParams;
    
    const absolutePath = normalizePath(file_path);
    const originalContent = fileExists(absolutePath) ? readFileContent(absolutePath) : '';
    
    try {
      const { patch } = this.applyStringEdit({
        filePath: absolutePath,
        fileContents: originalContent,
        oldString: old_string,
        newString: new_string,
        replaceAll: replace_all
      });
      
      return {
        success: true,
        patch,
        stats: diffManager.calculateDiffStats(patch)
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * 撤销最后一次编辑
   * @returns {Object|null} 撤销结果
   */
  async undoLastEdit() {
    if (this.editHistory.length === 0) {
      return null;
    }
    
    const lastEdit = this.editHistory[this.editHistory.length - 1];
    
    try {
      // 恢复原始内容
      await this.saveEditedFile(lastEdit.filePath, lastEdit.originalContent);
      
      // 更新读取状态
      const newStats = this.getFileStats(lastEdit.filePath);
      this.setFileReadState(lastEdit.filePath, lastEdit.originalContent, newStats.mtimeMs);
      
      // 移除历史记录
      this.editHistory.pop();
      
      return {
        success: true,
        filePath: lastEdit.filePath,
        operation: lastEdit.operation
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// 创建默认编辑管理器实例
export const editManager = new EditManager();

/**
 * 简单的文件编辑函数
 * @param {string} filePath - 文件路径
 * @param {string} oldString - 旧字符串
 * @param {string} newString - 新字符串
 * @param {boolean} replaceAll - 是否替换所有
 * @returns {Promise<Object>} 编辑结果
 */
export async function editFile(filePath, oldString, newString, replaceAll = false) {
  return editManager.performEdit({
    file_path: filePath,
    old_string: oldString,
    new_string: newString,
    replace_all: replaceAll
  });
}

/**
 * 多重文件编辑函数
 * @param {string} filePath - 文件路径
 * @param {Array} edits - 编辑数组
 * @returns {Promise<Object>} 编辑结果
 */
export async function multiEditFile(filePath, edits) {
  return editManager.performMultiEdit({
    file_path: filePath,
    edits
  });
}
