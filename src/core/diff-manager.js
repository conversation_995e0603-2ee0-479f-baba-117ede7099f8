/**
 * 差异管理器模块
 * @original: 原始文件中的文本差异比较和补丁生成相关函数
 */

/**
 * 基础差异算法类
 * @original: 原始文件L19900-20027中的_$类
 */
export class BaseDiff {
  constructor() {
    this.ignoreWhitespace = false;
  }
  
  /**
   * 计算两个文本的差异
   * @param {string} oldStr - 原始文本
   * @param {string} newStr - 新文本
   * @param {Object} options - 选项
   * @returns {Array} 差异数组
   * @original: 原始文件L19900-19950中的diff方法
   */
  diff(oldStr, newStr, options = {}) {
    const callback = options.callback;
    
    if (callback) {
      // 异步处理
      setTimeout(() => {
        const result = this.computeDiff(oldStr, newStr, options);
        callback(result);
      }, 0);
      return;
    } else {
      return this.computeDiff(oldStr, newStr, options);
    }
  }
  
  /**
   * 计算差异的核心算法
   * @param {string} oldStr - 原始文本
   * @param {string} newStr - 新文本
   * @param {Object} options - 选项
   * @returns {Array} 差异数组
   * @private
   */
  computeDiff(oldStr, newStr, options = {}) {
    const oldTokens = this.tokenize(oldStr, options);
    const newTokens = this.tokenize(newStr, options);
    
    const diffResult = this.computeLCS(oldTokens, newTokens, options);
    return this.buildChangeSet(diffResult, oldTokens, newTokens, options);
  }
  
  /**
   * 将文本分词
   * @param {string} text - 文本
   * @param {Object} options - 选项
   * @returns {Array} 分词结果
   * @original: 原始文件L20018-20020中的tokenize方法
   */
  tokenize(text, options = {}) {
    return Array.from(text);
  }
  
  /**
   * 连接分词结果
   * @param {Array} tokens - 分词数组
   * @returns {string} 连接后的文本
   * @original: 原始文件L20021-20023中的join方法
   */
  join(tokens) {
    return tokens.join('');
  }
  
  /**
   * 检查两个分词是否相等
   * @param {string} left - 左侧分词
   * @param {string} right - 右侧分词
   * @param {Object} options - 选项
   * @returns {boolean} 是否相等
   * @original: 原始文件L20007-20009中的equals方法
   */
  equals(left, right, options = {}) {
    if (options.comparator) {
      return options.comparator(left, right);
    }
    
    if (options.ignoreCase) {
      return left.toLowerCase() === right.toLowerCase();
    }
    
    return left === right;
  }
  
  /**
   * 移除空元素
   * @param {Array} array - 数组
   * @returns {Array} 移除空元素后的数组
   * @original: 原始文件L20010-20014中的removeEmpty方法
   */
  removeEmpty(array) {
    return array.filter(item => item);
  }
  
  /**
   * 输入转换
   * @param {*} value - 输入值
   * @returns {*} 转换后的值
   * @original: 原始文件L20015-20017中的castInput方法
   */
  castInput(value) {
    return value;
  }
  
  /**
   * 后处理差异结果
   * @param {Array} changeSet - 变更集
   * @returns {Array} 处理后的变更集
   * @original: 原始文件L20024-20026中的postProcess方法
   */
  postProcess(changeSet) {
    return changeSet;
  }
  
  /**
   * 计算最长公共子序列（LCS）
   * @param {Array} oldTokens - 原始分词
   * @param {Array} newTokens - 新分词
   * @param {Object} options - 选项
   * @returns {Object} LCS结果
   * @private
   */
  computeLCS(oldTokens, newTokens, options) {
    const oldLen = oldTokens.length;
    const newLen = newTokens.length;
    
    // 动态规划表
    const matrix = Array(oldLen + 1).fill(null).map(() => Array(newLen + 1).fill(0));
    
    // 填充LCS表
    for (let i = 1; i <= oldLen; i++) {
      for (let j = 1; j <= newLen; j++) {
        if (this.equals(oldTokens[i - 1], newTokens[j - 1], options)) {
          matrix[i][j] = matrix[i - 1][j - 1] + 1;
        } else {
          matrix[i][j] = Math.max(matrix[i - 1][j], matrix[i][j - 1]);
        }
      }
    }
    
    return { matrix, oldLen, newLen };
  }
  
  /**
   * 构建变更集
   * @param {Object} lcsResult - LCS结果
   * @param {Array} oldTokens - 原始分词
   * @param {Array} newTokens - 新分词
   * @param {Object} options - 选项
   * @returns {Array} 变更集
   * @private
   */
  buildChangeSet(lcsResult, oldTokens, newTokens, options) {
    const { matrix, oldLen, newLen } = lcsResult;
    const changes = [];
    
    let i = oldLen;
    let j = newLen;
    
    while (i > 0 || j > 0) {
      if (i > 0 && j > 0 && this.equals(oldTokens[i - 1], newTokens[j - 1], options)) {
        changes.unshift({
          value: oldTokens[i - 1],
          added: false,
          removed: false
        });
        i--;
        j--;
      } else if (j > 0 && (i === 0 || matrix[i][j - 1] >= matrix[i - 1][j])) {
        changes.unshift({
          value: newTokens[j - 1],
          added: true,
          removed: false
        });
        j--;
      } else if (i > 0) {
        changes.unshift({
          value: oldTokens[i - 1],
          added: false,
          removed: true
        });
        i--;
      }
    }
    
    return this.postProcess(changes);
  }
}

/**
 * 字符级差异算法
 * @original: 原始文件L20052中的QZ3实例
 */
export class CharDiff extends BaseDiff {
  tokenize(text) {
    return Array.from(text);
  }
  
  join(tokens) {
    return tokens.join('');
  }
}

/**
 * 单词级差异算法
 * @original: 原始文件L20104-20146中的Jv1类
 */
export class WordDiff extends BaseDiff {
  constructor() {
    super();
    this.wordPattern = /[a-zA-Z0-9_\u{C0}-\u{FF}\u{D8}-\u{F6}\u{F8}-\u{2C6}\u{2C8}-\u{2D7}\u{2DE}-\u{2FF}\u{1E00}-\u{1EFF}]+|\s+|[^a-zA-Z0-9_\u{C0}-\u{FF}\u{D8}-\u{F6}\u{F8}-\u{2C6}\u{2C8}-\u{2D7}\u{2DE}-\u{2FF}\u{1E00}-\u{1EFF}]/ug;
  }
  
  /**
   * 单词分词
   * @param {string} text - 文本
   * @param {Object} options - 选项
   * @returns {Array} 分词结果
   * @original: 原始文件L20109-20128中的tokenize方法
   */
  tokenize(text, options = {}) {
    let tokens;
    
    if (options.intlSegmenter) {
      if (options.intlSegmenter.resolvedOptions().granularity !== 'word') {
        throw new Error('The segmenter passed must have a granularity of "word"');
      }
      tokens = Array.from(options.intlSegmenter.segment(text), segment => segment.segment);
    } else {
      tokens = text.match(this.wordPattern) || [];
    }
    
    const result = [];
    let lastToken = null;
    
    tokens.forEach(token => {
      if (/\s/.test(token)) {
        if (lastToken == null) {
          result.push(token);
        } else {
          result.push(result.pop() + token);
        }
      } else if (/\s/.test(lastToken)) {
        if (result[result.length - 1] === lastToken) {
          result.push(result.pop() + token);
        } else {
          result.push(lastToken + token);
        }
      } else {
        result.push(token);
      }
      lastToken = token;
    });
    
    return result;
  }
  
  /**
   * 连接单词分词
   * @param {Array} tokens - 分词数组
   * @returns {string} 连接后的文本
   * @original: 原始文件L20129-20133中的join方法
   */
  join(tokens) {
    return tokens.map((token, index) => {
      if (index === 0) {
        return token;
      } else {
        return token.replace(/^\s+/, '');
      }
    }).join('');
  }
  
  /**
   * 检查单词是否相等
   * @param {string} left - 左侧单词
   * @param {string} right - 右侧单词
   * @param {Object} options - 选项
   * @returns {boolean} 是否相等
   * @original: 原始文件L20105-20108中的equals方法
   */
  equals(left, right, options = {}) {
    if (options.ignoreCase) {
      left = left.toLowerCase();
      right = right.toLowerCase();
    }
    return left.trim() === right.trim();
  }
}

/**
 * 行级差异算法
 * @original: 原始文件L20192-20218中的Xv1类
 */
export class LineDiff extends BaseDiff {
  /**
   * 行分词
   * @param {string} text - 文本
   * @param {Object} options - 选项
   * @returns {Array} 行数组
   * @original: 原始文件L20193-20204中的tokenize方法
   */
  tokenize(text, options = {}) {
    if (options.stripTrailingCr) {
      text = text.replace(/\r\n/g, '\n');
    }
    
    const lines = text.split(/(\n|\r\n)/);
    
    // 移除最后的空元素
    if (!lines[lines.length - 1]) {
      lines.pop();
    }
    
    const result = [];
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (i % 2 && !options.newlineIsToken) {
        // 合并换行符到前一行
        result[result.length - 1] += line;
      } else {
        result.push(line);
      }
    }
    
    return result;
  }
  
  /**
   * 检查行是否相等
   * @param {string} left - 左侧行
   * @param {string} right - 右侧行
   * @param {Object} options - 选项
   * @returns {boolean} 是否相等
   * @original: 原始文件L20205-20218中的equals方法
   */
  equals(left, right, options = {}) {
    if (options.ignoreWhitespace) {
      if (!options.newlineIsToken || !left.includes('\n')) {
        left = left.trim();
      }
      if (!options.newlineIsToken || !right.includes('\n')) {
        right = right.trim();
      }
    } else if (options.ignoreNewlineAtEof && !options.newlineIsToken) {
      if (left.endsWith('\n')) {
        left = left.slice(0, -1);
      }
      if (right.endsWith('\n')) {
        right = right.slice(0, -1);
      }
    }
    
    return BaseDiff.prototype.equals.call(this, left, right, options);
  }
}

/**
 * 差异管理器类
 * @original: 原始文件中的差异处理相关函数
 */
export class DiffManager {
  constructor() {
    this.charDiff = new CharDiff();
    this.wordDiff = new WordDiff();
    this.lineDiff = new LineDiff();
  }
  
  /**
   * 计算字符级差异
   * @param {string} oldText - 原始文本
   * @param {string} newText - 新文本
   * @param {Object} options - 选项
   * @returns {Array} 差异结果
   */
  diffChars(oldText, newText, options = {}) {
    return this.charDiff.diff(oldText, newText, options);
  }
  
  /**
   * 计算单词级差异
   * @param {string} oldText - 原始文本
   * @param {string} newText - 新文本
   * @param {Object} options - 选项
   * @returns {Array} 差异结果
   * @original: 原始文件L20189-20191中的xMB函数
   */
  diffWords(oldText, newText, options = {}) {
    return this.wordDiff.diff(oldText, newText, {
      ignoreCase: false,
      ...options
    });
  }
  
  /**
   * 计算行级差异
   * @param {string} oldText - 原始文本
   * @param {string} newText - 新文本
   * @param {Object} options - 选项
   * @returns {Array} 差异结果
   * @original: 原始文件L20219-20221中的jMB函数
   */
  diffLines(oldText, newText, options = {}) {
    return this.lineDiff.diff(oldText, newText, options);
  }
  
  /**
   * 生成统一差异格式补丁
   * @param {string} oldFileName - 原始文件名
   * @param {string} newFileName - 新文件名
   * @param {string} oldStr - 原始内容
   * @param {string} newStr - 新内容
   * @param {string} oldHeader - 原始文件头
   * @param {string} newHeader - 新文件头
   * @param {Object} options - 选项
   * @returns {Object} 补丁对象
   * @original: 原始文件L20352-20431中的cZ1函数
   */
  createPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options = {}) {
    if (!options.callback) {
      return this.generatePatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options);
    } else {
      const callback = options.callback;
      this.diffLines(oldStr, newStr, {
        ...options,
        callback: (diff) => {
          const patch = this.generatePatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, { ...options, diff });
          callback(patch);
        }
      });
    }
  }
  
  /**
   * 生成补丁的核心逻辑
   * @param {string} oldFileName - 原始文件名
   * @param {string} newFileName - 新文件名
   * @param {string} oldStr - 原始内容
   * @param {string} newStr - 新内容
   * @param {string} oldHeader - 原始文件头
   * @param {string} newHeader - 新文件头
   * @param {Object} options - 选项
   * @returns {Object} 补丁对象
   * @private
   */
  generatePatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options = {}) {
    const diff = options.diff || this.diffLines(oldStr, newStr, options);
    
    if (!diff) return;
    
    // 添加结束标记
    diff.push({
      value: '',
      lines: []
    });
    
    const hunks = [];
    let oldRangeStart = 0;
    let newRangeStart = 0;
    let curRange = [];
    let oldLine = 1;
    let newLine = 1;
    
    const contextSize = options.context || 4;
    
    for (let i = 0; i < diff.length; i++) {
      const current = diff[i];
      const lines = current.lines || this.splitLines(current.value);
      current.lines = lines;
      
      if (current.added || current.removed) {
        // 开始新的hunk
        if (!oldRangeStart) {
          oldRangeStart = oldLine;
          newRangeStart = newLine;
          
          // 添加上下文
          const prev = diff[i - 1];
          if (prev) {
            const contextLines = contextSize > 0 ? this.addContext(prev.lines.slice(-contextSize)) : [];
            oldRangeStart -= contextLines.length;
            newRangeStart -= contextLines.length;
            curRange.push(...contextLines);
          }
        }
        
        // 添加变更行
        curRange.push(...lines.map(line => (current.added ? '+' : '-') + line));
        
        if (current.added) {
          newLine += lines.length;
        } else {
          oldLine += lines.length;
        }
      } else {
        // 处理未变更的行
        if (oldRangeStart) {
          // 在hunk中，添加上下文
          if (lines.length <= contextSize * 2 && i < diff.length - 2) {
            curRange.push(...this.addContext(lines));
          } else {
            // 结束当前hunk
            const contextLines = Math.min(lines.length, contextSize);
            curRange.push(...this.addContext(lines.slice(0, contextLines)));
            
            const hunk = {
              oldStart: oldRangeStart,
              oldLines: oldLine - oldRangeStart + contextLines,
              newStart: newRangeStart,
              newLines: newLine - newRangeStart + contextLines,
              lines: curRange
            };
            
            hunks.push(hunk);
            oldRangeStart = 0;
            newRangeStart = 0;
            curRange = [];
          }
        }
        
        oldLine += lines.length;
        newLine += lines.length;
      }
    }
    
    return {
      oldFileName,
      newFileName,
      oldHeader,
      newHeader,
      hunks
    };
  }
  
  /**
   * 分割文本为行
   * @param {string} text - 文本
   * @returns {Array} 行数组
   * @private
   * @original: 原始文件L20433-20442中的k68函数
   */
  splitLines(text) {
    const endsWithNewline = text.endsWith('\n');
    const lines = text.split('\n').map(line => line + '\n');
    
    if (endsWithNewline) {
      lines.pop();
    } else {
      lines.push(lines.pop().slice(0, -1));
    }
    
    return lines;
  }
  
  /**
   * 添加上下文行
   * @param {Array} lines - 行数组
   * @returns {Array} 带上下文前缀的行数组
   * @private
   */
  addContext(lines) {
    return lines.map(line => ' ' + line);
  }
  
  /**
   * 应用补丁到文本
   * @param {string} text - 原始文本
   * @param {Array} edits - 编辑数组
   * @returns {string} 应用补丁后的文本
   * @original: 原始文件L21063-21127中的aMB函数
   */
  applyEdits(text, edits) {
    let result = text;
    
    for (const edit of edits) {
      const { old_string, new_string, replace_all = false } = edit;
      
      if (replace_all) {
        result = result.replaceAll(old_string, new_string);
      } else {
        result = result.replace(old_string, new_string);
      }
    }
    
    return result;
  }
  
  /**
   * 创建结构化补丁
   * @param {string} filePath - 文件路径
   * @param {string} oldContent - 原始内容
   * @param {string} newContent - 新内容
   * @param {Object} options - 选项
   * @returns {Array} 结构化补丁数组
   * @original: 原始文件L20989-21003中的pMB函数
   */
  createStructuredPatch(filePath, oldContent, newContent, options = {}) {
    const { ignoreWhitespace = false, singleHunk = false } = options;
    
    // 转义特殊字符
    const escapedOld = this.escapeSpecialChars(oldContent);
    const escapedNew = this.escapeSpecialChars(newContent);
    
    const patch = this.createPatch(
      filePath,
      filePath,
      escapedOld,
      escapedNew,
      undefined,
      undefined,
      {
        ignoreWhitespace,
        context: singleHunk ? 100000 : 3
      }
    );
    
    return patch.hunks.map(hunk => ({
      ...hunk,
      lines: hunk.lines.map(line => this.unescapeSpecialChars(line))
    }));
  }
  
  /**
   * 转义特殊字符
   * @param {string} text - 文本
   * @returns {string} 转义后的文本
   * @private
   * @original: 原始文件L20970-20972中的lZ1函数
   */
  escapeSpecialChars(text) {
    return text.replaceAll('&', '<<:AMPERSAND_TOKEN:>>').replaceAll('$', '<<:DOLLAR_TOKEN:>>');
  }
  
  /**
   * 反转义特殊字符
   * @param {string} text - 文本
   * @returns {string} 反转义后的文本
   * @private
   * @original: 原始文件L20973-20975中的lMB函数
   */
  unescapeSpecialChars(text) {
    return text.replaceAll('<<:AMPERSAND_TOKEN:>>', '&').replaceAll('<<:DOLLAR_TOKEN:>>', '$');
  }
  
  /**
   * 计算差异统计
   * @param {Array} patch - 补丁数组
   * @param {string} newContent - 新内容（用于创建文件的情况）
   * @returns {Object} 统计信息
   * @original: 原始文件L20976-20988中的fm函数
   */
  calculateDiffStats(patch, newContent) {
    let addedLines = 0;
    let removedLines = 0;
    
    if (patch.length === 0 && newContent) {
      addedLines = newContent.split(/\r?\n/).length;
    } else {
      addedLines = patch.reduce((sum, hunk) => 
        sum + hunk.lines.filter(line => line.startsWith('+')).length, 0
      );
      removedLines = patch.reduce((sum, hunk) => 
        sum + hunk.lines.filter(line => line.startsWith('-')).length, 0
      );
    }
    
    return {
      addedLines,
      removedLines,
      totalChanges: addedLines + removedLines
    };
  }
  
  /**
   * 验证编辑操作
   * @param {string} content - 文件内容
   * @param {string} oldString - 要替换的字符串
   * @param {string} newString - 新字符串
   * @param {boolean} replaceAll - 是否替换所有
   * @returns {Object} 验证结果
   */
  validateEdit(content, oldString, newString, replaceAll = false) {
    if (oldString === newString) {
      return {
        valid: false,
        error: 'old_string and new_string are identical'
      };
    }
    
    if (!content.includes(oldString)) {
      return {
        valid: false,
        error: 'old_string not found in file content'
      };
    }
    
    const occurrences = content.split(oldString).length - 1;
    if (occurrences > 1 && !replaceAll) {
      return {
        valid: false,
        error: `Found ${occurrences} matches but replace_all is false`
      };
    }
    
    return {
      valid: true,
      occurrences
    };
  }
  
  /**
   * 查找字符串的更好匹配
   * @param {string} content - 文件内容
   * @param {string} searchString - 搜索字符串
   * @returns {string|null} 找到的匹配字符串
   * @original: 原始文件L21045-21051中的O11函数
   */
  findBetterMatch(content, searchString) {
    if (content.includes(searchString)) {
      return searchString;
    }
    
    // 尝试规范化引号
    const normalizedSearch = this.normalizeQuotes(searchString);
    const normalizedContent = this.normalizeQuotes(content);
    
    const index = normalizedContent.indexOf(normalizedSearch);
    if (index !== -1) {
      return content.substring(index, index + searchString.length);
    }
    
    return null;
  }
  
  /**
   * 规范化引号
   * @param {string} text - 文本
   * @returns {string} 规范化后的文本
   * @private
   * @original: 原始文件L21042-21044中的nMB函数
   */
  normalizeQuotes(text) {
    return text
      .replaceAll(''', "'")
      .replaceAll(''', "'")
      .replaceAll('"', '"')
      .replaceAll('"', '"');
  }
  
  /**
   * 格式化差异显示
   * @param {Array} diff - 差异数组
   * @returns {string} 格式化后的差异文本
   */
  formatDiff(diff) {
    return diff.map(change => {
      const prefix = change.added ? '+' : change.removed ? '-' : ' ';
      return prefix + change.value;
    }).join('');
  }
  
  /**
   * 获取差异摘要
   * @param {Array} diff - 差异数组
   * @returns {Object} 差异摘要
   */
  getDiffSummary(diff) {
    let added = 0;
    let removed = 0;
    let unchanged = 0;
    
    for (const change of diff) {
      if (change.added) {
        added += change.value.length;
      } else if (change.removed) {
        removed += change.value.length;
      } else {
        unchanged += change.value.length;
      }
    }
    
    return {
      added,
      removed,
      unchanged,
      total: added + removed + unchanged,
      changeRatio: (added + removed) / (added + removed + unchanged)
    };
  }
}

// 创建默认差异管理器实例
export const diffManager = new DiffManager();

/**
 * 简单的差异计算函数
 * @param {string} oldText - 原始文本
 * @param {string} newText - 新文本
 * @param {string} type - 差异类型：'chars', 'words', 'lines'
 * @param {Object} options - 选项
 * @returns {Array} 差异结果
 */
export function calculateDiff(oldText, newText, type = 'lines', options = {}) {
  switch (type) {
    case 'chars':
      return diffManager.diffChars(oldText, newText, options);
    case 'words':
      return diffManager.diffWords(oldText, newText, options);
    case 'lines':
      return diffManager.diffLines(oldText, newText, options);
    default:
      throw new Error(`Unknown diff type: ${type}`);
  }
}

/**
 * 创建文件补丁
 * @param {string} filePath - 文件路径
 * @param {string} oldContent - 原始内容
 * @param {string} newContent - 新内容
 * @param {Object} options - 选项
 * @returns {Array} 结构化补丁
 * @original: 原始文件L20989-21003中的pMB函数
 */
export function createFilePatch(filePath, oldContent, newContent, options = {}) {
  return diffManager.createStructuredPatch(filePath, oldContent, newContent, options);
}
