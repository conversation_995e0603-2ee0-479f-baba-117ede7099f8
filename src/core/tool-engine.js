/**
 * 工具执行引擎模块
 * @original: 原始文件中的工具调用和执行相关函数
 */

import { generateRandomString } from '../utils/string-utils.js';
import { formatDuration } from '../utils/date-utils.js';

/**
 * 工具执行状态定义
 * @original: 原始文件L45137-45661中的工具执行状态
 */
export const TOOL_EXECUTION_STATES = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

/**
 * 工具类型定义
 */
export const TOOL_TYPES = {
  BUILT_IN: 'built_in',
  MCP: 'mcp',
  CUSTOM: 'custom'
};

/**
 * 权限行为定义
 * @original: 原始文件L45438-45447中的权限行为处理
 */
export const PERMISSION_BEHAVIORS = {
  ALLOW: 'allow',
  DENY: 'deny',
  ASK: 'ask'
};

/**
 * 工具执行引擎类
 * @original: 原始文件L45137-45661中的工具执行逻辑
 */
export class ToolEngine {
  constructor() {
    this.tools = new Map();
    this.executionHistory = [];
    this.inProgressToolUses = new Set();
    this.toolDecisions = new Map();
    this.concurrencyLimit = 10; // 最大并发工具数
    this.executionTimeout = 300000; // 5分钟超时
  }
  
  /**
   * 注册工具
   * @param {Object} tool - 工具对象
   */
  registerTool(tool) {
    if (!tool.name) {
      throw new Error('Tool must have a name');
    }
    
    this.tools.set(tool.name, {
      ...tool,
      registeredAt: Date.now(),
      type: tool.isMcp ? TOOL_TYPES.MCP : TOOL_TYPES.BUILT_IN
    });
  }
  
  /**
   * 注销工具
   * @param {string} toolName - 工具名称
   */
  unregisterTool(toolName) {
    this.tools.delete(toolName);
  }
  
  /**
   * 获取工具
   * @param {string} toolName - 工具名称
   * @returns {Object|null} 工具对象
   */
  getTool(toolName) {
    return this.tools.get(toolName) || null;
  }
  
  /**
   * 获取所有工具
   * @returns {Array} 工具列表
   */
  getAllTools() {
    return Array.from(this.tools.values());
  }
  
  /**
   * 执行工具
   * @param {Object} toolUse - 工具使用对象
   * @param {Object} context - 执行上下文
   * @param {Function} permissionChecker - 权限检查函数
   * @param {Object} assistantMessage - 助手消息
   * @returns {AsyncGenerator} 执行结果生成器
   * @original: 原始文件L45315-45366中的XuB函数
   */
  async* executeToolUse(toolUse, context, permissionChecker, assistantMessage) {
    const { id: toolUseId, name: toolName, input } = toolUse;
    
    // 查找工具
    const tool = this.getTool(toolName);
    if (!tool) {
      yield this.createErrorResult(toolUseId, `No such tool available: ${toolName}`);
      return;
    }
    
    // 标记为进行中
    this.inProgressToolUses.add(toolUseId);
    
    try {
      // 检查中断
      if (context.abortController.signal.aborted) {
        yield this.createCancelledResult(toolUseId);
        return;
      }
      
      // 执行工具
      yield* this.executeToolWithValidation(tool, toolUseId, input, context, permissionChecker, assistantMessage);
      
    } catch (error) {
      yield this.createErrorResult(toolUseId, this.formatError(error));
    } finally {
      // 清理
      this.inProgressToolUses.delete(toolUseId);
      this.toolDecisions.delete(toolUseId);
    }
  }
  
  /**
   * 执行带验证的工具
   * @param {Object} tool - 工具对象
   * @param {string} toolUseId - 工具使用ID
   * @param {Object} input - 输入参数
   * @param {Object} context - 执行上下文
   * @param {Function} permissionChecker - 权限检查函数
   * @param {Object} assistantMessage - 助手消息
   * @returns {AsyncGenerator} 执行结果生成器
   * @private
   * @original: 原始文件L45367-45661中的qq8函数
   */
  async* executeToolWithValidation(tool, toolUseId, input, context, permissionChecker, assistantMessage) {
    // 输入验证
    const validationResult = this.validateInput(tool, input);
    if (!validationResult.success) {
      yield this.createErrorResult(toolUseId, `InputValidationError: ${validationResult.error}`);
      return;
    }
    
    // 自定义验证
    if (tool.validateInput) {
      const customValidation = await tool.validateInput(validationResult.data, context);
      if (customValidation?.result === false) {
        yield this.createErrorResult(toolUseId, customValidation.message);
        return;
      }
    }
    
    let validatedInput = validationResult.data;
    
    // 执行前置钩子
    const preHookResult = await this.executePreToolHooks(tool, toolUseId, validatedInput, context);
    if (preHookResult.shouldStop) {
      if (preHookResult.error) {
        yield this.createErrorResult(toolUseId, preHookResult.error);
      }
      return;
    }
    
    // 权限检查
    let permissionResult;
    if (preHookResult.permissionOverride) {
      permissionResult = preHookResult.permissionOverride;
    } else {
      permissionResult = await permissionChecker(tool, validatedInput, context, assistantMessage, toolUseId);
    }
    
    if (permissionResult.behavior !== PERMISSION_BEHAVIORS.ALLOW) {
      const errorMessage = preHookResult.stopReason || permissionResult.message;
      yield this.createErrorResult(toolUseId, errorMessage);
      return;
    }
    
    // 更新输入（可能被权限检查修改）
    validatedInput = permissionResult.updatedInput;
    
    // 记录工具参数（用于分析）
    const toolParameters = this.extractToolParameters(tool, validatedInput);
    
    // 执行工具
    const startTime = Date.now();
    let result = null;
    
    try {
      const toolExecution = tool.call(validatedInput, {
        ...context,
        userModified: permissionResult.userModified ?? false
      }, permissionChecker, assistantMessage);
      
      for await (const executionResult of toolExecution) {
        switch (executionResult.type) {
          case 'result':
            result = executionResult.data;
            const duration = Date.now() - startTime;
            
            // 记录成功执行
            this.recordExecution(tool.name, toolUseId, 'success', duration, toolParameters);
            
            // 创建工具结果
            yield this.createSuccessResult(toolUseId, tool, result);
            
            // 处理补充内容
            if (executionResult.supplementalContent?.length > 0) {
              yield this.createSupplementalContent(executionResult.supplementalContent);
            }
            
            // 检查是否有停止信号
            if (preHookResult.stopReason) {
              yield this.createStopMessage(preHookResult.stopReason, toolUseId);
            }
            break;
            
          case 'progress':
            // 转发进度消息
            yield this.createProgressResult(executionResult.toolUseID, toolUseId, executionResult.data);
            break;
        }
      }
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // 记录失败执行
      this.recordExecution(tool.name, toolUseId, 'error', duration, toolParameters, error);
      
      throw error;
    }
    
    // 执行后置钩子
    await this.executePostToolHooks(tool, toolUseId, validatedInput, result, context);
  }
  
  /**
   * 验证工具输入
   * @param {Object} tool - 工具对象
   * @param {Object} input - 输入参数
   * @returns {Object} 验证结果
   * @private
   */
  validateInput(tool, input) {
    if (!tool.inputSchema) {
      return { success: true, data: input };
    }
    
    try {
      const parseResult = tool.inputSchema.safeParse(input);
      if (parseResult.success) {
        return { success: true, data: parseResult.data };
      } else {
        return { 
          success: false, 
          error: this.formatValidationError(tool.name, parseResult.error)
        };
      }
    } catch (error) {
      return { 
        success: false, 
        error: `Schema validation failed: ${error.message}`
      };
    }
  }
  
  /**
   * 执行前置钩子
   * @param {Object} tool - 工具对象
   * @param {string} toolUseId - 工具使用ID
   * @param {Object} input - 输入参数
   * @param {Object} context - 执行上下文
   * @returns {Promise<Object>} 钩子执行结果
   * @private
   * @original: 原始文件L45422-45484中的前置钩子逻辑
   */
  async executePreToolHooks(tool, toolUseId, input, context) {
    const result = {
      shouldStop: false,
      permissionOverride: null,
      stopReason: null,
      error: null
    };
    
    try {
      // @todo: 实现前置钩子系统
      // 这里应该调用注册的前置钩子
      
      return result;
    } catch (error) {
      return {
        shouldStop: true,
        error: `Pre-tool hook failed: ${this.formatError(error)}`
      };
    }
  }
  
  /**
   * 执行后置钩子
   * @param {Object} tool - 工具对象
   * @param {string} toolUseId - 工具使用ID
   * @param {Object} input - 输入参数
   * @param {*} result - 工具执行结果
   * @param {Object} context - 执行上下文
   * @returns {Promise<void>}
   * @private
   * @original: 原始文件L45616-45660中的后置钩子逻辑
   */
  async executePostToolHooks(tool, toolUseId, input, result, context) {
    try {
      // @todo: 实现后置钩子系统
      // 这里应该调用注册的后置钩子
      
    } catch (error) {
      console.warn(`PostToolUse hook failed: ${this.formatError(error)}`);
    }
  }
  
  /**
   * 提取工具参数（用于分析）
   * @param {Object} tool - 工具对象
   * @param {Object} input - 输入参数
   * @returns {Object} 提取的参数
   * @private
   * @original: 原始文件L45512-45528中的参数提取逻辑
   */
  extractToolParameters(tool, input) {
    const parameters = {};
    
    // 特殊处理Bash工具
    if (tool.name === 'Bash' && 'command' in input) {
      const bashInput = input;
      parameters.bash_command = bashInput.command.trim().split(/\s+/)[0] || '';
      parameters.full_command = bashInput.command;
      
      if (bashInput.timeout !== undefined) {
        parameters.timeout = bashInput.timeout;
      }
      if (bashInput.description !== undefined) {
        parameters.description = bashInput.description;
      }
      if (bashInput.sandbox !== undefined) {
        parameters.sandbox = bashInput.sandbox;
      }
    }
    
    return parameters;
  }
  
  /**
   * 记录工具执行
   * @param {string} toolName - 工具名称
   * @param {string} toolUseId - 工具使用ID
   * @param {string} status - 执行状态
   * @param {number} duration - 执行时长
   * @param {Object} parameters - 工具参数
   * @param {Error} error - 错误对象（如果有）
   * @private
   */
  recordExecution(toolName, toolUseId, status, duration, parameters = {}, error = null) {
    const record = {
      toolName,
      toolUseId,
      status,
      duration,
      parameters,
      error: error ? error.message : null,
      timestamp: Date.now()
    };
    
    this.executionHistory.push(record);
    
    // 限制历史记录数量
    if (this.executionHistory.length > 1000) {
      this.executionHistory = this.executionHistory.slice(-1000);
    }
  }
  
  /**
   * 创建成功结果
   * @param {string} toolUseId - 工具使用ID
   * @param {Object} tool - 工具对象
   * @param {*} result - 执行结果
   * @returns {Object} 结果对象
   * @private
   */
  createSuccessResult(toolUseId, tool, result) {
    return {
      type: 'user',
      message: {
        content: [tool.mapToolResultToToolResultBlockParam(result, toolUseId)],
        toolUseResult: result
      }
    };
  }
  
  /**
   * 创建错误结果
   * @param {string} toolUseId - 工具使用ID
   * @param {string} errorMessage - 错误消息
   * @returns {Object} 错误结果对象
   * @private
   */
  createErrorResult(toolUseId, errorMessage) {
    return {
      type: 'user',
      message: {
        content: [{
          type: 'tool_result',
          content: `<tool_use_error>${errorMessage}</tool_use_error>`,
          is_error: true,
          tool_use_id: toolUseId
        }],
        toolUseResult: `Error: ${errorMessage}`
      }
    };
  }
  
  /**
   * 创建取消结果
   * @param {string} toolUseId - 工具使用ID
   * @returns {Object} 取消结果对象
   * @private
   */
  createCancelledResult(toolUseId) {
    return {
      type: 'user',
      message: {
        content: [{
          type: 'tool_result',
          content: 'Tool execution was cancelled by user',
          is_error: true,
          tool_use_id: toolUseId
        }],
        toolUseResult: 'Tool execution was cancelled by user'
      }
    };
  }
  
  /**
   * 创建进度结果
   * @param {string} toolUseId - 工具使用ID
   * @param {string} parentToolUseId - 父工具使用ID
   * @param {*} data - 进度数据
   * @returns {Object} 进度结果对象
   * @private
   */
  createProgressResult(toolUseId, parentToolUseId, data) {
    return {
      type: 'progress',
      toolUseID: toolUseId,
      parentToolUseID: parentToolUseId,
      data
    };
  }
  
  /**
   * 创建补充内容
   * @param {Array} content - 补充内容
   * @returns {Object} 补充内容对象
   * @private
   */
  createSupplementalContent(content) {
    return {
      type: 'user',
      message: {
        content,
        isMeta: true
      }
    };
  }
  
  /**
   * 创建停止消息
   * @param {string} reason - 停止原因
   * @param {string} toolUseId - 工具使用ID
   * @returns {Object} 停止消息对象
   * @private
   */
  createStopMessage(reason, toolUseId) {
    return {
      type: 'system',
      message: {
        content: reason || 'Execution stopped by hook',
        level: 'warning',
        toolUseId,
        preventContinuation: true
      }
    };
  }
  
  /**
   * 格式化验证错误
   * @param {string} toolName - 工具名称
   * @param {Object} error - 验证错误
   * @returns {string} 格式化的错误消息
   * @private
   * @original: 原始文件L45684-45717中的JuB函数
   */
  formatValidationError(toolName, error) {
    const requiredParams = error.errors
      .filter(e => e.code === 'invalid_type' && e.received === 'undefined' && e.message === 'Required')
      .map(e => String(e.path[0]));
    
    const unexpectedParams = error.errors
      .filter(e => e.code === 'unrecognized_keys')
      .flatMap(e => e.keys);
    
    const typeErrors = error.errors
      .filter(e => e.code === 'invalid_type' && e.received !== 'undefined' && e.message !== 'Required')
      .map(e => ({
        param: String(e.path[0]),
        expected: e.expected,
        received: e.received
      }));
    
    const issues = [];
    
    if (requiredParams.length > 0) {
      issues.push(...requiredParams.map(param => `The required parameter \`${param}\` is missing`));
    }
    
    if (unexpectedParams.length > 0) {
      issues.push(...unexpectedParams.map(param => `An unexpected parameter \`${param}\` was provided`));
    }
    
    if (typeErrors.length > 0) {
      issues.push(...typeErrors.map(({ param, expected, received }) => 
        `The parameter \`${param}\` type is expected as \`${expected}\` but provided as \`${received}\``
      ));
    }
    
    if (issues.length > 0) {
      return `${toolName} failed due to the following ${issues.length > 1 ? 'issues' : 'issue'}:\n${issues.join('\n')}`;
    }
    
    return error.message;
  }
  
  /**
   * 格式化错误
   * @param {Error} error - 错误对象
   * @returns {string} 格式化的错误消息
   * @private
   * @original: 原始文件L45662-45683中的HO0函数
   */
  formatError(error) {
    if (typeof error === 'string') {
      return error;
    }
    
    if (!(error instanceof Error)) {
      return String(error);
    }
    
    const parts = [error.message];
    
    // 添加stderr和stdout（如果存在）
    if ('stderr' in error && typeof error.stderr === 'string') {
      parts.push(error.stderr);
    }
    if ('stdout' in error && typeof error.stdout === 'string') {
      parts.push(error.stdout);
    }
    
    const fullMessage = parts.filter(Boolean).join('\n').trim() || 'Error';
    
    // 截断过长的错误消息
    if (fullMessage.length <= 10000) {
      return fullMessage;
    }
    
    const truncateLength = 5000;
    const start = fullMessage.slice(0, truncateLength);
    const end = fullMessage.slice(-truncateLength);
    
    return `${start}\n\n... [${fullMessage.length - 10000} characters truncated] ...\n\n${end}`;
  }
  
  /**
   * 获取执行统计
   * @returns {Object} 统计信息
   */
  getExecutionStats() {
    const total = this.executionHistory.length;
    const successful = this.executionHistory.filter(r => r.status === 'success').length;
    const failed = this.executionHistory.filter(r => r.status === 'error').length;
    
    const toolUsage = {};
    this.executionHistory.forEach(record => {
      if (!toolUsage[record.toolName]) {
        toolUsage[record.toolName] = { total: 0, successful: 0, failed: 0 };
      }
      toolUsage[record.toolName].total++;
      if (record.status === 'success') {
        toolUsage[record.toolName].successful++;
      } else if (record.status === 'error') {
        toolUsage[record.toolName].failed++;
      }
    });
    
    const avgDuration = total > 0 
      ? this.executionHistory.reduce((sum, r) => sum + r.duration, 0) / total 
      : 0;
    
    return {
      total,
      successful,
      failed,
      successRate: total > 0 ? (successful / total) * 100 : 0,
      averageDuration: avgDuration,
      toolUsage,
      inProgressCount: this.inProgressToolUses.size,
      registeredToolsCount: this.tools.size
    };
  }
  
  /**
   * 清理执行历史
   */
  clearExecutionHistory() {
    this.executionHistory = [];
  }
  
  /**
   * 取消所有进行中的工具执行
   */
  cancelAllInProgress() {
    this.inProgressToolUses.clear();
    this.toolDecisions.clear();
  }
  
  /**
   * 检查工具是否正在执行
   * @param {string} toolUseId - 工具使用ID
   * @returns {boolean} 是否正在执行
   */
  isToolInProgress(toolUseId) {
    return this.inProgressToolUses.has(toolUseId);
  }
  
  /**
   * 获取进行中的工具列表
   * @returns {Array} 进行中的工具使用ID列表
   */
  getInProgressTools() {
    return Array.from(this.inProgressToolUses);
  }
}

// 创建默认工具引擎实例
export const toolEngine = new ToolEngine();

/**
 * 注册工具的简单函数
 * @param {Object} tool - 工具对象
 */
export function registerTool(tool) {
  return toolEngine.registerTool(tool);
}

/**
 * 执行工具的简单函数
 * @param {Object} toolUse - 工具使用对象
 * @param {Object} context - 执行上下文
 * @param {Function} permissionChecker - 权限检查函数
 * @param {Object} assistantMessage - 助手消息
 * @returns {AsyncGenerator} 执行结果生成器
 */
export async function* executeTool(toolUse, context, permissionChecker, assistantMessage) {
  yield* toolEngine.executeToolUse(toolUse, context, permissionChecker, assistantMessage);
}
