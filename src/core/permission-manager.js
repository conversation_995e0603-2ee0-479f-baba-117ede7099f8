/**
 * 权限管理器模块
 * @original: 原始文件中的权限检查和管理相关函数
 */

import fs from 'fs';
import path from 'path';
import { SYSTEM_CONFIG } from '../config/index.js';

/**
 * 权限管理器类
 * @original: 原始文件中的权限管理逻辑
 */
export class PermissionManager {
  constructor() {
    this.dangerouslySkipPermissions = SYSTEM_CONFIG.DANGEROUSLY_SKIP_PERMISSIONS;
    this.permissionCache = new Map();
  }
  
  /**
   * 检查文件读取权限
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否有读取权限
   * @original: 原始文件L31752-31758中的pW8函数
   */
  canReadFile(filePath) {
    if (this.dangerouslySkipPermissions) {
      return true;
    }
    
    try {
      const resolvedPath = path.resolve(filePath);
      
      // 检查缓存
      const cacheKey = `read:${resolvedPath}`;
      if (this.permissionCache.has(cacheKey)) {
        return this.permissionCache.get(cacheKey);
      }
      
      // 检查文件是否存在且可读
      fs.accessSync(resolvedPath, fs.constants.F_OK | fs.constants.R_OK);
      
      this.permissionCache.set(cacheKey, true);
      return true;
    } catch (error) {
      this.permissionCache.set(`read:${path.resolve(filePath)}`, false);
      return false;
    }
  }
  
  /**
   * 检查文件写入权限
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否有写入权限
   */
  canWriteFile(filePath) {
    if (this.dangerouslySkipPermissions) {
      return true;
    }
    
    try {
      const resolvedPath = path.resolve(filePath);
      
      // 检查缓存
      const cacheKey = `write:${resolvedPath}`;
      if (this.permissionCache.has(cacheKey)) {
        return this.permissionCache.get(cacheKey);
      }
      
      // 如果文件存在，检查写入权限
      if (fs.existsSync(resolvedPath)) {
        fs.accessSync(resolvedPath, fs.constants.W_OK);
      } else {
        // 如果文件不存在，检查目录的写入权限
        const dirPath = path.dirname(resolvedPath);
        fs.accessSync(dirPath, fs.constants.W_OK);
      }
      
      this.permissionCache.set(cacheKey, true);
      return true;
    } catch (error) {
      this.permissionCache.set(`write:${path.resolve(filePath)}`, false);
      return false;
    }
  }
  
  /**
   * 检查目录访问权限
   * @param {string} dirPath - 目录路径
   * @returns {boolean} 是否有访问权限
   */
  canAccessDirectory(dirPath) {
    if (this.dangerouslySkipPermissions) {
      return true;
    }
    
    try {
      const resolvedPath = path.resolve(dirPath);
      
      // 检查缓存
      const cacheKey = `access:${resolvedPath}`;
      if (this.permissionCache.has(cacheKey)) {
        return this.permissionCache.get(cacheKey);
      }
      
      fs.accessSync(resolvedPath, fs.constants.F_OK | fs.constants.R_OK);
      
      this.permissionCache.set(cacheKey, true);
      return true;
    } catch (error) {
      this.permissionCache.set(`access:${path.resolve(dirPath)}`, false);
      return false;
    }
  }
  
  /**
   * 检查执行权限
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否有执行权限
   */
  canExecuteFile(filePath) {
    if (this.dangerouslySkipPermissions) {
      return true;
    }
    
    try {
      const resolvedPath = path.resolve(filePath);
      
      // 检查缓存
      const cacheKey = `execute:${resolvedPath}`;
      if (this.permissionCache.has(cacheKey)) {
        return this.permissionCache.get(cacheKey);
      }
      
      fs.accessSync(resolvedPath, fs.constants.F_OK | fs.constants.X_OK);
      
      this.permissionCache.set(cacheKey, true);
      return true;
    } catch (error) {
      this.permissionCache.set(`execute:${path.resolve(filePath)}`, false);
      return false;
    }
  }
  
  /**
   * 检查路径是否安全（防止路径遍历攻击）
   * @param {string} filePath - 文件路径
   * @param {string} basePath - 基础路径
   * @returns {boolean} 路径是否安全
   */
  isPathSafe(filePath, basePath) {
    try {
      const resolvedPath = path.resolve(basePath, filePath);
      const resolvedBase = path.resolve(basePath);
      
      return resolvedPath.startsWith(resolvedBase);
    } catch {
      return false;
    }
  }
  
  /**
   * 检查文件是否在允许的目录中
   * @param {string} filePath - 文件路径
   * @param {Array} allowedDirectories - 允许的目录列表
   * @returns {boolean} 是否在允许的目录中
   */
  isFileInAllowedDirectory(filePath, allowedDirectories) {
    if (this.dangerouslySkipPermissions) {
      return true;
    }
    
    const resolvedPath = path.resolve(filePath);
    
    return allowedDirectories.some(allowedDir => {
      const resolvedAllowedDir = path.resolve(allowedDir);
      return resolvedPath.startsWith(resolvedAllowedDir);
    });
  }
  
  /**
   * 检查文件扩展名是否被允许
   * @param {string} filePath - 文件路径
   * @param {Array} allowedExtensions - 允许的扩展名列表
   * @returns {boolean} 扩展名是否被允许
   */
  isFileExtensionAllowed(filePath, allowedExtensions) {
    const ext = path.extname(filePath).toLowerCase();
    return allowedExtensions.includes(ext);
  }
  
  /**
   * 检查文件大小是否在限制内
   * @param {string} filePath - 文件路径
   * @param {number} maxSize - 最大大小（字节）
   * @returns {boolean} 文件大小是否在限制内
   */
  isFileSizeAllowed(filePath, maxSize) {
    try {
      const stats = fs.statSync(path.resolve(filePath));
      return stats.size <= maxSize;
    } catch {
      return false;
    }
  }
  
  /**
   * 获取文件权限信息
   * @param {string} filePath - 文件路径
   * @returns {Object} 权限信息
   */
  getFilePermissions(filePath) {
    try {
      const resolvedPath = path.resolve(filePath);
      const stats = fs.statSync(resolvedPath);
      const mode = stats.mode;
      
      return {
        readable: !!(mode & fs.constants.S_IRUSR),
        writable: !!(mode & fs.constants.S_IWUSR),
        executable: !!(mode & fs.constants.S_IXUSR),
        mode: mode.toString(8),
        owner: {
          read: !!(mode & fs.constants.S_IRUSR),
          write: !!(mode & fs.constants.S_IWUSR),
          execute: !!(mode & fs.constants.S_IXUSR)
        },
        group: {
          read: !!(mode & fs.constants.S_IRGRP),
          write: !!(mode & fs.constants.S_IWGRP),
          execute: !!(mode & fs.constants.S_IXGRP)
        },
        others: {
          read: !!(mode & fs.constants.S_IROTH),
          write: !!(mode & fs.constants.S_IWOTH),
          execute: !!(mode & fs.constants.S_IXOTH)
        }
      };
    } catch (error) {
      throw new Error(`Failed to get permissions for ${filePath}: ${error.message}`);
    }
  }
  
  /**
   * 设置文件权限
   * @param {string} filePath - 文件路径
   * @param {string|number} mode - 权限模式
   * @returns {boolean} 是否设置成功
   */
  setFilePermissions(filePath, mode) {
    if (this.dangerouslySkipPermissions) {
      console.warn('Skipping permission change due to DANGEROUSLY_SKIP_PERMISSIONS');
      return true;
    }
    
    try {
      const resolvedPath = path.resolve(filePath);
      fs.chmodSync(resolvedPath, mode);
      
      // 清除相关缓存
      this.clearPermissionCache(resolvedPath);
      
      return true;
    } catch (error) {
      console.error(`Failed to set permissions for ${filePath}: ${error.message}`);
      return false;
    }
  }
  
  /**
   * 检查是否为隐藏文件
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否为隐藏文件
   */
  isHiddenFile(filePath) {
    const basename = path.basename(filePath);
    return basename.startsWith('.');
  }
  
  /**
   * 检查是否为系统文件
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否为系统文件
   */
  isSystemFile(filePath) {
    const systemPaths = [
      '/System',
      '/Library',
      '/usr/bin',
      '/usr/sbin',
      '/bin',
      '/sbin',
      'C:\\Windows',
      'C:\\Program Files',
      'C:\\Program Files (x86)'
    ];
    
    const resolvedPath = path.resolve(filePath);
    
    return systemPaths.some(systemPath => {
      return resolvedPath.startsWith(systemPath);
    });
  }
  
  /**
   * 检查是否为临时文件
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否为临时文件
   */
  isTempFile(filePath) {
    const tempExtensions = ['.tmp', '.temp', '.bak', '.swp', '.swo'];
    const ext = path.extname(filePath).toLowerCase();
    
    return tempExtensions.includes(ext) || 
           filePath.includes('/tmp/') || 
           filePath.includes('\\Temp\\');
  }
  
  /**
   * 验证文件操作权限
   * @param {string} filePath - 文件路径
   * @param {string} operation - 操作类型：'read', 'write', 'execute', 'delete'
   * @param {Object} options - 选项
   * @returns {Object} 验证结果
   */
  validateFileOperation(filePath, operation, options = {}) {
    const result = {
      allowed: false,
      reason: '',
      warnings: []
    };
    
    try {
      const resolvedPath = path.resolve(filePath);
      
      // 检查路径安全性
      if (options.basePath && !this.isPathSafe(filePath, options.basePath)) {
        result.reason = 'Path traversal detected';
        return result;
      }
      
      // 检查系统文件
      if (this.isSystemFile(resolvedPath)) {
        result.reason = 'Operation on system files is not allowed';
        return result;
      }
      
      // 检查允许的目录
      if (options.allowedDirectories && !this.isFileInAllowedDirectory(resolvedPath, options.allowedDirectories)) {
        result.reason = 'File is not in allowed directories';
        return result;
      }
      
      // 检查文件扩展名
      if (options.allowedExtensions && !this.isFileExtensionAllowed(resolvedPath, options.allowedExtensions)) {
        result.reason = 'File extension is not allowed';
        return result;
      }
      
      // 检查文件大小
      if (options.maxFileSize && fs.existsSync(resolvedPath) && !this.isFileSizeAllowed(resolvedPath, options.maxFileSize)) {
        result.reason = 'File size exceeds limit';
        return result;
      }
      
      // 检查具体操作权限
      switch (operation) {
        case 'read':
          if (!this.canReadFile(resolvedPath)) {
            result.reason = 'No read permission';
            return result;
          }
          break;
          
        case 'write':
          if (!this.canWriteFile(resolvedPath)) {
            result.reason = 'No write permission';
            return result;
          }
          break;
          
        case 'execute':
          if (!this.canExecuteFile(resolvedPath)) {
            result.reason = 'No execute permission';
            return result;
          }
          break;
          
        case 'delete':
          if (!this.canWriteFile(path.dirname(resolvedPath))) {
            result.reason = 'No delete permission';
            return result;
          }
          break;
          
        default:
          result.reason = `Unknown operation: ${operation}`;
          return result;
      }
      
      // 添加警告
      if (this.isHiddenFile(resolvedPath)) {
        result.warnings.push('Operating on hidden file');
      }
      
      if (this.isTempFile(resolvedPath)) {
        result.warnings.push('Operating on temporary file');
      }
      
      result.allowed = true;
      return result;
      
    } catch (error) {
      result.reason = `Permission check failed: ${error.message}`;
      return result;
    }
  }
  
  /**
   * 检查目录创建权限
   * @param {string} dirPath - 目录路径
   * @param {Object} options - 选项
   * @returns {Object} 验证结果
   */
  validateDirectoryCreation(dirPath, options = {}) {
    const result = {
      allowed: false,
      reason: '',
      warnings: []
    };
    
    try {
      const resolvedPath = path.resolve(dirPath);
      
      // 检查路径安全性
      if (options.basePath && !this.isPathSafe(dirPath, options.basePath)) {
        result.reason = 'Path traversal detected';
        return result;
      }
      
      // 检查是否在系统目录
      if (this.isSystemFile(resolvedPath)) {
        result.reason = 'Cannot create directory in system location';
        return result;
      }
      
      // 检查父目录权限
      const parentDir = path.dirname(resolvedPath);
      if (!this.canWriteFile(parentDir)) {
        result.reason = 'No permission to create directory in parent location';
        return result;
      }
      
      // 检查目录是否已存在
      if (fs.existsSync(resolvedPath)) {
        if (fs.statSync(resolvedPath).isDirectory()) {
          result.warnings.push('Directory already exists');
        } else {
          result.reason = 'Path exists but is not a directory';
          return result;
        }
      }
      
      result.allowed = true;
      return result;
      
    } catch (error) {
      result.reason = `Directory creation check failed: ${error.message}`;
      return result;
    }
  }
  
  /**
   * 获取安全的文件路径
   * @param {string} filePath - 原始文件路径
   * @param {string} basePath - 基础路径
   * @returns {string} 安全的文件路径
   */
  getSafeFilePath(filePath, basePath) {
    const resolvedBase = path.resolve(basePath);
    const resolvedPath = path.resolve(basePath, filePath);
    
    // 确保路径在基础目录内
    if (!resolvedPath.startsWith(resolvedBase)) {
      throw new Error('Path traversal detected');
    }
    
    return resolvedPath;
  }
  
  /**
   * 清除权限缓存
   * @param {string} filePath - 文件路径，如果不提供则清除所有缓存
   */
  clearPermissionCache(filePath) {
    if (filePath) {
      const resolvedPath = path.resolve(filePath);
      const keysToDelete = [];
      
      for (const key of this.permissionCache.keys()) {
        if (key.includes(resolvedPath)) {
          keysToDelete.push(key);
        }
      }
      
      keysToDelete.forEach(key => this.permissionCache.delete(key));
    } else {
      this.permissionCache.clear();
    }
  }
  
  /**
   * 获取权限摘要
   * @param {string} filePath - 文件路径
   * @returns {Object} 权限摘要
   */
  getPermissionSummary(filePath) {
    const resolvedPath = path.resolve(filePath);
    
    return {
      path: resolvedPath,
      exists: fs.existsSync(resolvedPath),
      canRead: this.canReadFile(resolvedPath),
      canWrite: this.canWriteFile(resolvedPath),
      canExecute: this.canExecuteFile(resolvedPath),
      isHidden: this.isHiddenFile(resolvedPath),
      isSystem: this.isSystemFile(resolvedPath),
      isTemp: this.isTempFile(resolvedPath),
      permissions: fs.existsSync(resolvedPath) ? this.getFilePermissions(resolvedPath) : null
    };
  }
  
  /**
   * 批量检查文件权限
   * @param {Array} filePaths - 文件路径数组
   * @param {string} operation - 操作类型
   * @param {Object} options - 选项
   * @returns {Array} 检查结果数组
   */
  batchValidatePermissions(filePaths, operation, options = {}) {
    return filePaths.map(filePath => ({
      filePath,
      ...this.validateFileOperation(filePath, operation, options)
    }));
  }
  
  /**
   * 设置权限跳过模式
   * @param {boolean} skip - 是否跳过权限检查
   */
  setSkipPermissions(skip) {
    this.dangerouslySkipPermissions = skip;
    
    if (skip) {
      console.warn('⚠️  WARNING: Permission checks are disabled. This is dangerous!');
    }
  }
  
  /**
   * 获取权限跳过状态
   * @returns {boolean} 是否跳过权限检查
   */
  isSkippingPermissions() {
    return this.dangerouslySkipPermissions;
  }
}

// 创建默认权限管理器实例
export const permissionManager = new PermissionManager();

/**
 * 简单的权限检查函数
 * @param {string} filePath - 文件路径
 * @param {string} operation - 操作类型
 * @param {Object} options - 选项
 * @returns {boolean} 是否有权限
 */
export function hasPermission(filePath, operation, options = {}) {
  const result = permissionManager.validateFileOperation(filePath, operation, options);
  return result.allowed;
}

/**
 * 检查文件读取权限
 * @param {string} filePath - 文件路径
 * @returns {boolean} 是否有读取权限
 * @original: 原始文件L31752-31758中的pW8函数
 */
export function canReadFile(filePath) {
  return permissionManager.canReadFile(filePath);
}

/**
 * 检查文件写入权限
 * @param {string} filePath - 文件路径
 * @returns {boolean} 是否有写入权限
 */
export function canWriteFile(filePath) {
  return permissionManager.canWriteFile(filePath);
}
