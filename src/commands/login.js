/**
 * /login 命令实现
 * @original: 原始文件L39083-39101中的ghB函数和L38691-38990中的ov组件
 */

import React from 'react';

/**
 * 登录状态类型
 */
export const LOGIN_STATES = {
  IDLE: 'idle',
  READY_TO_START: 'ready_to_start',
  WAITING_FOR_LOGIN: 'waiting_for_login',
  CREATING_API_KEY: 'creating_api_key',
  SUCCESS: 'success',
  ERROR: 'error',
  ABOUT_TO_RETRY: 'about_to_retry'
};

/**
 * 登录方法类型
 */
export const LOGIN_METHODS = {
  CLAUDE_AI: 'claudeai',
  CONSOLE: 'console'
};

/**
 * 模拟OAuth流程管理器
 */
class OAuthFlowManager {
  constructor() {
    this.state = null;
    this.codeVerifier = null;
  }
  
  async startOAuthFlow(onUrlReady, options = {}) {
    const { loginWithClaudeAi = false, inferenceOnly = false, expiresIn } = options;
    
    // 生成OAuth参数
    const state = this.generateState();
    const codeVerifier = this.generateCodeVerifier();
    this.state = state;
    this.codeVerifier = codeVerifier;
    
    // 构建授权URL
    const baseUrl = loginWithClaudeAi ? 
      'https://claude.ai/oauth/authorize' : 
      'https://console.anthropic.com/oauth/authorize';
    
    const params = new URLSearchParams({
      client_id: 'claude-code',
      response_type: 'code',
      redirect_uri: 'http://localhost:3000/callback',
      state,
      code_challenge: this.generateCodeChallenge(codeVerifier),
      code_challenge_method: 'S256'
    });
    
    if (inferenceOnly) {
      params.append('scope', 'inference');
    }
    
    if (expiresIn) {
      params.append('expires_in', expiresIn.toString());
    }
    
    const authUrl = `${baseUrl}?${params.toString()}`;
    
    // 通知URL准备就绪
    onUrlReady(authUrl);
    
    // 模拟OAuth流程完成
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟成功的OAuth响应
        resolve({
          accessToken: 'mock_access_token_' + Date.now(),
          refreshToken: 'mock_refresh_token_' + Date.now(),
          scopes: inferenceOnly ? ['inference'] : ['full_access'],
          expiresAt: Date.now() + (expiresIn || 3600) * 1000
        });
      }, 2000);
    });
  }
  
  handleManualAuthCodeInput({ authorizationCode, state }) {
    if (state !== this.state) {
      throw new Error('Invalid state parameter');
    }
    
    // 模拟处理手动输入的授权码
    console.log('Processing manual auth code:', authorizationCode);
  }
  
  generateState() {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }
  
  generateCodeVerifier() {
    return Math.random().toString(36).substring(2, 50);
  }
  
  generateCodeChallenge(verifier) {
    // 简化的code challenge生成
    return btoa(verifier).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
  }
}

/**
 * 文本输入组件
 * @param {Object} props - 组件属性
 */
function TextInput({ value, onChange, onSubmit, placeholder, columns = 50 }) {
  const handleKeyPress = React.useCallback((event) => {
    if (event.key === 'Enter') {
      onSubmit(value);
    }
  }, [value, onSubmit]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  return React.createElement('input', {
    type: 'text',
    value,
    onChange: (e) => onChange(e.target.value),
    placeholder,
    style: {
      width: `${Math.min(columns, 80)}ch`,
      padding: '4px 8px',
      backgroundColor: '#333',
      color: '#ffffff',
      border: '1px solid #555',
      borderRadius: '3px',
      fontFamily: 'monospace'
    },
    autoFocus: true
  });
}

/**
 * 登录组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L38691-38990中的ov组件
 */
function LoginComponent({ onDone, startingMessage, mode = "login", forceLoginMethod }) {
  const [loginState, setLoginState] = React.useState(() => {
    if (mode === "setup-token") {
      return { state: LOGIN_STATES.READY_TO_START };
    }
    if (forceLoginMethod === LOGIN_METHODS.CLAUDE_AI || forceLoginMethod === LOGIN_METHODS.CONSOLE) {
      return { state: LOGIN_STATES.READY_TO_START };
    }
    return { state: LOGIN_STATES.IDLE };
  });
  
  const [inputValue, setInputValue] = React.useState("");
  const [showManualInput, setShowManualInput] = React.useState(false);
  const [oauthManager] = React.useState(() => new OAuthFlowManager());
  const [loginWithClaudeAi, setLoginWithClaudeAi] = React.useState(() => {
    return mode === "setup-token" || forceLoginMethod === LOGIN_METHODS.CLAUDE_AI;
  });
  
  const forceMethodMessage = React.useMemo(() => {
    if (forceLoginMethod === LOGIN_METHODS.CLAUDE_AI) {
      return "Login method pre-selected: Subscription Plan (Claude Pro/Max)";
    }
    if (forceLoginMethod === LOGIN_METHODS.CONSOLE) {
      return "Login method pre-selected: API Usage Billing (Anthropic Console)";
    }
    return null;
  }, [forceLoginMethod]);
  
  // 处理手动输入的授权码
  const handleManualCodeInput = React.useCallback(async (code, url) => {
    try {
      const [authCode, state] = code.split('#');
      if (!authCode || !state) {
        setLoginState({
          state: LOGIN_STATES.ERROR,
          message: "Invalid code. Please make sure the full code was copied",
          toRetry: {
            state: LOGIN_STATES.WAITING_FOR_LOGIN,
            url
          }
        });
        return;
      }
      
      oauthManager.handleManualAuthCodeInput({
        authorizationCode: authCode,
        state
      });
    } catch (error) {
      setLoginState({
        state: LOGIN_STATES.ERROR,
        message: error.message,
        toRetry: {
          state: LOGIN_STATES.WAITING_FOR_LOGIN,
          url
        }
      });
    }
  }, [oauthManager]);
  
  // 开始OAuth流程
  const startOAuthFlow = React.useCallback(async () => {
    try {
      const tokenResult = await oauthManager.startOAuthFlow(
        (url) => {
          setLoginState({
            state: LOGIN_STATES.WAITING_FOR_LOGIN,
            url
          });
          // 3秒后显示手动输入选项
          setTimeout(() => setShowManualInput(true), 3000);
        },
        {
          loginWithClaudeAi,
          inferenceOnly: mode === "setup-token",
          expiresIn: mode === "setup-token" ? 31536000 : undefined // 1年
        }
      );
      
      setLoginState({
        state: LOGIN_STATES.CREATING_API_KEY
      });
      
      // 模拟API密钥创建
      setTimeout(() => {
        if (mode === "setup-token") {
          setLoginState({
            state: LOGIN_STATES.SUCCESS,
            token: tokenResult.accessToken
          });
        } else {
          setLoginState({
            state: LOGIN_STATES.SUCCESS
          });
        }
      }, 1000);
      
    } catch (error) {
      const isTokenExchangeError = error.message.includes("Token exchange failed");
      setLoginState({
        state: LOGIN_STATES.ERROR,
        message: isTokenExchangeError ? 
          "Failed to exchange authorization code for access token. Please try again." : 
          error.message,
        toRetry: mode === "setup-token" ? 
          { state: LOGIN_STATES.READY_TO_START } : 
          { state: LOGIN_STATES.IDLE }
      });
    }
  }, [oauthManager, loginWithClaudeAi, mode]);
  
  // 自动开始OAuth流程
  React.useEffect(() => {
    if (loginState.state === LOGIN_STATES.READY_TO_START) {
      const timer = setTimeout(() => {
        startOAuthFlow();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [loginState.state, startOAuthFlow]);
  
  // 处理重试
  React.useEffect(() => {
    if (loginState.state === LOGIN_STATES.ABOUT_TO_RETRY) {
      const timer = setTimeout(() => {
        setLoginState(loginState.nextState);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [loginState]);
  
  // 处理键盘事件
  const handleKeyPress = React.useCallback((event) => {
    if (event.key === 'Enter') {
      if (loginState.state === LOGIN_STATES.SUCCESS && mode !== "setup-token") {
        onDone();
      } else if (loginState.state === LOGIN_STATES.ERROR && loginState.toRetry) {
        setInputValue("");
        setLoginState({
          state: LOGIN_STATES.ABOUT_TO_RETRY,
          nextState: loginState.toRetry
        });
      }
    }
  }, [loginState, mode, onDone]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  // 渲染不同状态的UI
  const renderContent = () => {
    switch (loginState.state) {
      case LOGIN_STATES.IDLE:
        return React.createElement('div', {
          style: { padding: '10px' }
        }, [
          React.createElement('h2', {
            key: 'title',
            style: { color: '#ffff00', marginBottom: '10px' }
          }, startingMessage || "Claude Code can now be used with your Claude subscription or billed based on API usage through your Console account."),
          
          React.createElement('h3', {
            key: 'subtitle',
            style: { color: '#ffffff', marginBottom: '15px' }
          }, 'Select login method:'),
          
          React.createElement('div', {
            key: 'options',
            style: { marginBottom: '10px' }
          }, [
            React.createElement('div', {
              key: 'claudeai',
              onClick: () => {
                setLoginWithClaudeAi(true);
                setLoginState({ state: LOGIN_STATES.READY_TO_START });
              },
              style: {
                padding: '10px',
                border: '1px solid #555',
                borderRadius: '4px',
                marginBottom: '10px',
                cursor: 'pointer',
                backgroundColor: '#1a1a1a'
              }
            }, [
              React.createElement('div', {
                key: 'label',
                style: { fontWeight: 'bold', color: '#ffffff' }
              }, 'Claude account with subscription'),
              React.createElement('div', {
                key: 'desc',
                style: { color: '#888888', fontSize: '12px' }
              }, 'Starting at $20/mo for Pro, $100/mo for Max - Best value, predictable pricing')
            ]),
            
            React.createElement('div', {
              key: 'console',
              onClick: () => {
                setLoginWithClaudeAi(false);
                setLoginState({ state: LOGIN_STATES.READY_TO_START });
              },
              style: {
                padding: '10px',
                border: '1px solid #555',
                borderRadius: '4px',
                cursor: 'pointer',
                backgroundColor: '#1a1a1a'
              }
            }, [
              React.createElement('div', {
                key: 'label',
                style: { fontWeight: 'bold', color: '#ffffff' }
              }, 'Anthropic Console account'),
              React.createElement('div', {
                key: 'desc',
                style: { color: '#888888', fontSize: '12px' }
              }, 'API usage billing')
            ])
          ])
        ]);
        
      case LOGIN_STATES.WAITING_FOR_LOGIN:
        return React.createElement('div', {
          style: { padding: '10px' }
        }, [
          forceMethodMessage && React.createElement('div', {
            key: 'force-method',
            style: { color: '#888888', marginBottom: '10px' }
          }, forceMethodMessage),
          
          !showManualInput && React.createElement('div', {
            key: 'opening',
            style: { color: '#ffff00', marginBottom: '10px' }
          }, '🔄 Opening browser to sign in…'),
          
          showManualInput && React.createElement('div', {
            key: 'manual',
            style: { marginBottom: '10px' }
          }, [
            React.createElement('div', {
              key: 'prompt',
              style: { color: '#ffffff', marginBottom: '5px' }
            }, 'Paste code here if prompted > '),
            React.createElement(TextInput, {
              key: 'input',
              value: inputValue,
              onChange: setInputValue,
              onSubmit: (code) => handleManualCodeInput(code, loginState.url),
              placeholder: 'Paste authorization code here...',
              columns: 50
            })
          ]),
          
          showManualInput && loginState.url && React.createElement('div', {
            key: 'url',
            style: { marginTop: '15px' }
          }, [
            React.createElement('div', {
              key: 'url-label',
              style: { color: '#888888', marginBottom: '5px' }
            }, "Browser didn't open? Use the url below to sign in:"),
            React.createElement('div', {
              key: 'url-value',
              style: { 
                color: '#888888', 
                fontSize: '12px',
                wordBreak: 'break-all',
                backgroundColor: '#1a1a1a',
                padding: '5px',
                borderRadius: '3px'
              }
            }, loginState.url)
          ])
        ]);
        
      case LOGIN_STATES.CREATING_API_KEY:
        return React.createElement('div', {
          style: { padding: '10px', color: '#ffff00' }
        }, '🔄 Creating API key for Claude Code…');
        
      case LOGIN_STATES.ABOUT_TO_RETRY:
        return React.createElement('div', {
          style: { padding: '10px', color: '#ffff00' }
        }, 'Retrying…');
        
      case LOGIN_STATES.SUCCESS:
        if (mode === "setup-token" && loginState.token) {
          return React.createElement('div', {
            style: { padding: '10px' }
          }, [
            React.createElement('div', {
              key: 'success',
              style: { color: '#00ff00', marginBottom: '15px' }
            }, '✓ Long-lived authentication token created successfully!'),
            
            React.createElement('div', {
              key: 'token-label',
              style: { color: '#ffffff', marginBottom: '5px' }
            }, 'Your OAuth token (valid for 1 year):'),
            
            React.createElement('div', {
              key: 'token-value',
              style: { 
                color: '#ffff00', 
                backgroundColor: '#1a1a1a',
                padding: '10px',
                borderRadius: '4px',
                fontFamily: 'monospace',
                wordBreak: 'break-all',
                marginBottom: '10px'
              }
            }, loginState.token),
            
            React.createElement('div', {
              key: 'warning1',
              style: { color: '#888888', fontSize: '12px', marginBottom: '5px' }
            }, 'Store this token securely. You won\'t be able to see it again.'),
            
            React.createElement('div', {
              key: 'warning2',
              style: { color: '#888888', fontSize: '12px' }
            }, 'Use this token by setting: export CLAUDE_CODE_OAUTH_TOKEN=<token>')
          ]);
        } else {
          return React.createElement('div', {
            style: { padding: '10px' }
          }, [
            React.createElement('div', {
              key: 'success',
              style: { color: '#00ff00', marginBottom: '10px' }
            }, 'Login successful. Press Enter to continue…')
          ]);
        }
        
      case LOGIN_STATES.ERROR:
        return React.createElement('div', {
          style: { padding: '10px' }
        }, [
          React.createElement('div', {
            key: 'error',
            style: { color: '#ff0000', marginBottom: '10px' }
          }, `OAuth error: ${loginState.message}`),
          
          loginState.toRetry && React.createElement('div', {
            key: 'retry',
            style: { color: '#ffff00' }
          }, 'Press Enter to retry.')
        ]);
        
      default:
        return React.createElement('div', {
          style: { padding: '10px', color: '#888888' }
        }, 'Initializing...');
    }
  };
  
  return React.createElement('div', {
    style: {
      border: '1px solid #ffff00',
      borderRadius: '8px',
      backgroundColor: '#000000',
      color: '#ffffff',
      fontFamily: 'monospace',
      minHeight: '200px'
    }
  }, renderContent());
}

/**
 * 登录容器组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L39102-39115中的tR0组件
 */
function LoginContainer({ onDone, startingMessage }) {
  const handleKeyPress = React.useCallback((event) => {
    if (event.key === 'Escape') {
      onDone(false);
    }
  }, [onDone]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  return React.createElement('div', {
    style: { padding: '10px' }
  }, [
    React.createElement(LoginComponent, {
      key: 'login',
      onDone: () => onDone(true),
      startingMessage
    }),
    
    React.createElement('div', {
      key: 'help',
      style: {
        marginTop: '10px',
        color: '#888888',
        fontSize: '12px',
        textAlign: 'center'
      }
    }, 'Press Escape to cancel')
  ]);
}

/**
 * /login 命令定义
 * @original: 原始文件L39083-39101中的ghB函数
 */
export const loginCommand = {
  type: "local-jsx",
  name: "login",
  description: "Sign in with your Anthropic account",
  isEnabled: () => !process.env.DISABLE_LOGIN_COMMAND,
  isHidden: false,
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @returns {Promise<React.Element>} React组件
   */
  async call(onDone, context) {
    // 清除现有认证状态
    if (context.clearAuth) {
      context.clearAuth();
    }
    
    return React.createElement(LoginContainer, {
      onDone: (success) => {
        if (context.onChangeAPIKey) {
          context.onChangeAPIKey();
        }
        onDone(success ? "Login successful" : "Login interrupted");
      }
    });
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "login";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /login

Sign in with your Anthropic account to use Claude Code.

Login Options:
1. Claude Account with Subscription
   - Use your Claude Pro or Max subscription
   - Predictable monthly pricing ($20/mo Pro, $100/mo Max)
   - Best value for regular usage

2. Anthropic Console Account
   - Pay-per-use API billing
   - Flexible for occasional usage
   - Billed based on actual API consumption

The login process will:
1. Open your browser to the appropriate login page
2. Guide you through the authentication process
3. Create an API key for Claude Code to use
4. Save your authentication for future sessions

If the browser doesn't open automatically, you can manually copy and paste the authorization code when prompted.

Your authentication information is stored securely and can be cleared at any time using the /logout command.
    `.trim();
  }
};
