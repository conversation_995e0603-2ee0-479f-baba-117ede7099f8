/**
 * /permissions 命令实现
 * @original: 原始文件L44009-44027中的Kq8对象和L43820-44008中的ugB组件
 */

import React from 'react';

/**
 * 权限规则行为类型
 */
export const RULE_BEHAVIORS = {
  ALLOW: 'allow',
  DENY: 'deny'
};

/**
 * 权限标签页类型
 */
export const PERMISSION_TABS = {
  ALLOW: 'allow',
  DENY: 'deny',
  WORKSPACE: 'workspace'
};

/**
 * 配置作用域
 */
export const CONFIG_SCOPES = [
  'localSettings',
  'projectSettings', 
  'userSettings'
];

/**
 * 获取标签页显示名称
 * @param {string} tab - 标签页类型
 * @returns {string} 显示名称
 */
function getTabDisplayName(tab) {
  switch (tab) {
    case PERMISSION_TABS.ALLOW:
      return 'Allow';
    case PERMISSION_TABS.DENY:
      return 'Deny';
    case PERMISSION_TABS.WORKSPACE:
      return 'Workspace';
    default:
      return tab;
  }
}

/**
 * 获取标签页描述
 * @param {string} tab - 标签页类型
 * @returns {string} 描述文本
 */
function getTabDescription(tab) {
  switch (tab) {
    case PERMISSION_TABS.ALLOW:
      return 'Claude Code will always allow requests to use allowed tools.';
    case PERMISSION_TABS.DENY:
      return 'Claude Code will always reject requests to use denied tools.';
    case PERMISSION_TABS.WORKSPACE:
      return 'Claude Code can read files in the workspace, and make edits when auto-accept edits is on.';
    default:
      return '';
  }
}

/**
 * 格式化权限规则显示
 * @param {Object} ruleValue - 规则值
 * @returns {string} 格式化后的规则字符串
 */
function formatRuleValue(ruleValue) {
  if (typeof ruleValue === 'string') {
    return ruleValue;
  }
  
  if (ruleValue && typeof ruleValue === 'object') {
    if (ruleValue.toolName) {
      if (ruleValue.ruleContent) {
        return `${ruleValue.toolName}(${ruleValue.ruleContent})`;
      }
      return ruleValue.toolName;
    }
  }
  
  return String(ruleValue);
}

/**
 * 解析工具规则字符串
 * @param {string} ruleString - 规则字符串
 * @returns {Object} 解析后的规则对象
 */
function parseToolRule(ruleString) {
  const trimmed = ruleString.trim();
  
  // 检查是否包含参数
  const match = trimmed.match(/^([^(]+)\(([^)]*)\)$/);
  if (match) {
    return {
      toolName: match[1].trim(),
      ruleContent: match[2].trim() || undefined
    };
  }
  
  return {
    toolName: trimmed,
    ruleContent: undefined
  };
}

/**
 * 权限标签页头部组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L43666-43682中的hgB组件
 */
function PermissionTabHeader({ selectedTab }) {
  return React.createElement('div', {
    style: { marginBottom: '10px' }
  }, [
    React.createElement('div', {
      key: 'tabs',
      style: {
        display: 'flex',
        gap: '10px',
        marginBottom: '5px'
      }
    }, [
      React.createElement('span', {
        key: 'label',
        style: { 
          fontWeight: 'bold',
          color: '#ffff00'
        }
      }, 'Permissions:'),
      
      ...Object.values(PERMISSION_TABS).map(tab => 
        React.createElement('span', {
          key: tab,
          style: {
            padding: '2px 8px',
            backgroundColor: selectedTab === tab ? '#ffff00' : 'transparent',
            color: selectedTab === tab ? '#000000' : '#ffffff',
            fontWeight: selectedTab === tab ? 'bold' : 'normal',
            borderRadius: '3px'
          }
        }, ` ${getTabDisplayName(tab)} `)
      )
    ]),
    
    React.createElement('div', {
      key: 'description',
      style: {
        color: '#cccccc',
        fontSize: '12px'
      }
    }, getTabDescription(selectedTab))
  ]);
}

/**
 * 添加规则对话框组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L43528-43593中的vgB组件
 */
function AddRuleDialog({ onCancel, onSubmit, ruleBehavior }) {
  const [ruleText, setRuleText] = React.useState('');
  
  const handleKeyPress = React.useCallback((event) => {
    if (event.key === 'Escape') {
      onCancel();
    } else if (event.key === 'Enter') {
      const trimmed = ruleText.trim();
      if (trimmed.length === 0) return;
      
      const parsedRule = parseToolRule(trimmed);
      onSubmit(parsedRule, ruleBehavior);
    }
  }, [ruleText, onCancel, onSubmit, ruleBehavior]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  return React.createElement('div', {
    style: {
      padding: '15px',
      border: '1px solid #ffff00',
      borderRadius: '4px',
      backgroundColor: '#1a1a1a'
    }
  }, [
    React.createElement('h3', {
      key: 'title',
      style: { color: '#ffff00', marginBottom: '10px' }
    }, `Add ${ruleBehavior} rule`),
    
    React.createElement('div', {
      key: 'input-container',
      style: { marginBottom: '10px' }
    }, [
      React.createElement('input', {
        key: 'input',
        type: 'text',
        value: ruleText,
        onChange: (e) => setRuleText(e.target.value),
        placeholder: 'Enter tool name or pattern (e.g., Edit, Bash(git:*), Write(*.js))',
        style: {
          width: '100%',
          padding: '8px',
          backgroundColor: '#333',
          color: '#ffffff',
          border: '1px solid #555',
          borderRadius: '3px',
          fontFamily: 'monospace'
        },
        autoFocus: true
      })
    ]),
    
    React.createElement('div', {
      key: 'examples',
      style: {
        marginBottom: '10px',
        fontSize: '12px',
        color: '#888888'
      }
    }, [
      React.createElement('div', { key: 'ex1' }, 'Examples:'),
      React.createElement('div', { key: 'ex2' }, '• Edit - Allow/deny all file edits'),
      React.createElement('div', { key: 'ex3' }, '• Bash(git:*) - Allow/deny git commands'),
      React.createElement('div', { key: 'ex4' }, '• Write(*.js) - Allow/deny writing JS files')
    ]),
    
    React.createElement('div', {
      key: 'help',
      style: {
        color: '#888888',
        fontSize: '12px'
      }
    }, 'Enter to submit • Esc to cancel')
  ]);
}

/**
 * 规则详情对话框组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L43758-43819中的Cq8组件
 */
function RuleDetailsDialog({ rule, onDelete, onCancel }) {
  const handleKeyPress = React.useCallback((event) => {
    if (event.key === 'Escape') {
      onCancel();
    } else if (event.key === 'Delete' || event.key === 'd') {
      onDelete();
    }
  }, [onCancel, onDelete]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  const getBehaviorColor = (behavior) => {
    switch (behavior) {
      case RULE_BEHAVIORS.ALLOW:
        return '#00ff00';
      case RULE_BEHAVIORS.DENY:
        return '#ff0000';
      default:
        return '#ffffff';
    }
  };
  
  return React.createElement('div', {
    style: {
      padding: '15px',
      border: '1px solid #333',
      borderRadius: '4px',
      backgroundColor: '#1a1a1a'
    }
  }, [
    React.createElement('h3', {
      key: 'title',
      style: { color: '#ffff00', marginBottom: '10px' }
    }, 'Rule Details'),
    
    React.createElement('div', {
      key: 'rule-info',
      style: { marginBottom: '15px' }
    }, [
      React.createElement('div', {
        key: 'rule-text',
        style: {
          fontSize: '16px',
          fontWeight: 'bold',
          marginBottom: '5px'
        }
      }, formatRuleValue(rule.ruleValue)),
      
      React.createElement('div', {
        key: 'behavior',
        style: {
          color: getBehaviorColor(rule.ruleBehavior),
          marginBottom: '5px'
        }
      }, `Behavior: ${rule.ruleBehavior}`),
      
      React.createElement('div', {
        key: 'source',
        style: {
          color: '#888888',
          fontSize: '12px'
        }
      }, `Source: ${rule.source || 'unknown'}`)
    ]),
    
    React.createElement('div', {
      key: 'actions',
      style: { marginBottom: '10px' }
    }, [
      React.createElement('button', {
        key: 'delete',
        onClick: onDelete,
        style: {
          padding: '8px 16px',
          backgroundColor: '#ff0000',
          color: '#ffffff',
          border: 'none',
          borderRadius: '3px',
          cursor: 'pointer',
          marginRight: '10px'
        }
      }, 'Delete Rule'),
      
      React.createElement('button', {
        key: 'cancel',
        onClick: onCancel,
        style: {
          padding: '8px 16px',
          backgroundColor: '#333',
          color: '#ffffff',
          border: '1px solid #555',
          borderRadius: '3px',
          cursor: 'pointer'
        }
      }, 'Cancel')
    ]),
    
    React.createElement('div', {
      key: 'help',
      style: {
        color: '#888888',
        fontSize: '12px'
      }
    }, 'D or Delete key to delete • Esc to cancel')
  ]);
}

/**
 * 工作区管理组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L43596-43665中的fgB组件
 */
function WorkspaceManager({ onExit, getToolPermissionContext, onRequestAddDirectory, onRequestRemoveDirectory }) {
  const context = getToolPermissionContext();
  const directories = React.useMemo(() => {
    return Array.from(context.additionalWorkingDirectories?.keys() || []).map(path => ({
      path,
      isCurrent: false,
      isDeletable: true
    }));
  }, [context.additionalWorkingDirectories]);
  
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  
  const options = React.useMemo(() => {
    const opts = [
      { label: 'Add directory…', value: 'add-directory' }
    ];
    
    directories.forEach(dir => {
      opts.push({
        label: dir.path,
        value: dir.path
      });
    });
    
    return opts;
  }, [directories]);
  
  const handleKeyPress = React.useCallback((event) => {
    switch (event.key) {
      case 'Escape':
        onExit();
        break;
      case 'ArrowUp':
        setSelectedIndex(prev => Math.max(0, prev - 1));
        break;
      case 'ArrowDown':
        setSelectedIndex(prev => Math.min(options.length - 1, prev + 1));
        break;
      case 'Enter':
        const selectedOption = options[selectedIndex];
        if (selectedOption?.value === 'add-directory') {
          onRequestAddDirectory();
        } else {
          const dir = directories.find(d => d.path === selectedOption?.value);
          if (dir && dir.isDeletable) {
            onRequestRemoveDirectory(dir.path);
          }
        }
        break;
    }
  }, [selectedIndex, options, directories, onExit, onRequestAddDirectory, onRequestRemoveDirectory]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  return React.createElement('div', {
    style: { padding: '10px' }
  }, [
    React.createElement('h3', {
      key: 'title',
      style: { color: '#ffff00', marginBottom: '10px' }
    }, 'Workspace Directories'),
    
    React.createElement('div', {
      key: 'options',
      style: { marginBottom: '10px' }
    }, options.map((option, index) => 
      React.createElement('div', {
        key: option.value,
        style: {
          padding: '5px',
          backgroundColor: index === selectedIndex ? '#333' : 'transparent',
          color: index === selectedIndex ? '#00ff00' : '#ffffff',
          cursor: 'pointer'
        }
      }, `${index === selectedIndex ? '► ' : '  '}${option.label}`)
    )),
    
    React.createElement('div', {
      key: 'help',
      style: {
        color: '#888888',
        fontSize: '12px'
      }
    }, '↑/↓ to select • Enter to add/remove • Esc to close')
  ]);
}

/**
 * 权限管理主组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L43820-44008中的ugB组件
 */
function PermissionManager({ onExit, getToolPermissionContext, setToolPermissionContext }) {
  const [messages, setMessages] = React.useState([]);
  const [context, setContext] = React.useState(getToolPermissionContext());
  const [selectedTab, setSelectedTab] = React.useState(PERMISSION_TABS.ALLOW);
  const [selectedRule, setSelectedRule] = React.useState(null);
  const [showAddDialog, setShowAddDialog] = React.useState(false);
  const [pendingRule, setPendingRule] = React.useState(null);
  
  const updateContext = React.useCallback((newContext) => {
    setContext(newContext);
    setToolPermissionContext(newContext);
  }, [setToolPermissionContext]);
  
  // 模拟获取权限规则
  const allowRules = React.useMemo(() => {
    const rules = new Map();
    // 这里应该从实际的权限上下文中获取允许规则
    // 暂时使用模拟数据
    return rules;
  }, [context]);
  
  const denyRules = React.useMemo(() => {
    const rules = new Map();
    // 这里应该从实际的权限上下文中获取拒绝规则
    // 暂时使用模拟数据
    return rules;
  }, [context]);
  
  const currentRules = React.useMemo(() => {
    switch (selectedTab) {
      case PERMISSION_TABS.ALLOW:
        return allowRules;
      case PERMISSION_TABS.DENY:
        return denyRules;
      case PERMISSION_TABS.WORKSPACE:
        return new Map();
      default:
        return new Map();
    }
  }, [selectedTab, allowRules, denyRules]);
  
  const options = React.useMemo(() => {
    const opts = [];
    
    if (selectedTab !== PERMISSION_TABS.WORKSPACE) {
      opts.push({
        label: 'Add a new rule…',
        value: 'add-new-rule'
      });
    }
    
    const sortedKeys = Array.from(currentRules.keys()).sort((a, b) => {
      const ruleA = currentRules.get(a);
      const ruleB = currentRules.get(b);
      if (ruleA && ruleB) {
        const textA = formatRuleValue(ruleA.ruleValue).toLowerCase();
        const textB = formatRuleValue(ruleB.ruleValue).toLowerCase();
        return textA.localeCompare(textB);
      }
      return 0;
    });
    
    sortedKeys.forEach(key => {
      const rule = currentRules.get(key);
      if (rule) {
        opts.push({
          label: formatRuleValue(rule.ruleValue),
          value: key
        });
      }
    });
    
    return opts;
  }, [currentRules, selectedTab]);
  
  const handleTabChange = React.useCallback((direction) => {
    const tabs = Object.values(PERMISSION_TABS);
    const currentIndex = tabs.indexOf(selectedTab);
    let newIndex;
    
    if (direction === 'next') {
      newIndex = (currentIndex + 1) % tabs.length;
    } else {
      newIndex = (currentIndex - 1 + tabs.length) % tabs.length;
    }
    
    setSelectedTab(tabs[newIndex]);
  }, [selectedTab]);
  
  const handleKeyPress = React.useCallback((event) => {
    if (selectedRule || showAddDialog || pendingRule) return;
    
    if (event.key === 'Tab' || event.key === 'ArrowRight') {
      event.preventDefault();
      handleTabChange('next');
    } else if (event.key === 'ArrowLeft') {
      event.preventDefault();
      handleTabChange('prev');
    }
  }, [selectedRule, showAddDialog, pendingRule, handleTabChange]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  const handleOptionSelect = React.useCallback((value) => {
    if (value === 'add-new-rule') {
      setShowAddDialog(true);
    } else {
      const rule = currentRules.get(value);
      if (rule) {
        setSelectedRule(rule);
      }
    }
  }, [currentRules]);
  
  const handleAddRule = React.useCallback((ruleValue, ruleBehavior) => {
    setPendingRule({ ruleValue, ruleBehavior });
    setShowAddDialog(false);
  }, []);
  
  const handleConfirmAddRule = React.useCallback((rules) => {
    setPendingRule(null);
    rules.forEach(rule => {
      setMessages(prev => [...prev, `Added ${rule.ruleBehavior} rule ${formatRuleValue(rule.ruleValue)}`]);
    });
  }, []);
  
  const handleDeleteRule = React.useCallback(() => {
    if (!selectedRule) return;
    
    // 这里应该实际删除规则
    setMessages(prev => [...prev, `Deleted ${selectedRule.ruleBehavior} rule ${formatRuleValue(selectedRule.ruleValue)}`]);
    setSelectedRule(null);
  }, [selectedRule]);
  
  // 渲染不同的对话框
  if (selectedRule) {
    return React.createElement(RuleDetailsDialog, {
      rule: selectedRule,
      onDelete: handleDeleteRule,
      onCancel: () => setSelectedRule(null)
    });
  }
  
  if (showAddDialog) {
    return React.createElement(AddRuleDialog, {
      onCancel: () => setShowAddDialog(false),
      onSubmit: handleAddRule,
      ruleBehavior: selectedTab === PERMISSION_TABS.ALLOW ? RULE_BEHAVIORS.ALLOW : RULE_BEHAVIORS.DENY
    });
  }
  
  if (pendingRule) {
    return React.createElement('div', {
      style: { padding: '20px', textAlign: 'center' }
    }, [
      React.createElement('div', {
        key: 'message',
        style: { color: '#ffff00', marginBottom: '10px' }
      }, 'Adding rule...'),
      React.createElement('div', {
        key: 'rule',
        style: { color: '#ffffff' }
      }, `${pendingRule.ruleBehavior}: ${formatRuleValue(pendingRule.ruleValue)}`)
    ]);
  }
  
  // 渲染主界面
  return React.createElement('div', {
    style: {
      padding: '10px',
      border: '1px solid #ffff00',
      borderRadius: '4px',
      backgroundColor: '#000000',
      color: '#ffffff',
      fontFamily: 'monospace'
    }
  }, [
    React.createElement(PermissionTabHeader, {
      key: 'header',
      selectedTab
    }),
    
    selectedTab === PERMISSION_TABS.WORKSPACE 
      ? React.createElement(WorkspaceManager, {
          key: 'workspace',
          onExit,
          getToolPermissionContext,
          onRequestAddDirectory: () => {
            setMessages(prev => [...prev, 'Add directory functionality not implemented']);
          },
          onRequestRemoveDirectory: (path) => {
            setMessages(prev => [...prev, `Remove directory ${path} functionality not implemented`]);
          }
        })
      : React.createElement('div', {
          key: 'rules',
          style: { marginBottom: '10px' }
        }, options.length > 0 ? options.map((option, index) => 
          React.createElement('div', {
            key: option.value,
            onClick: () => handleOptionSelect(option.value),
            style: {
              padding: '5px',
              cursor: 'pointer',
              backgroundColor: 'transparent',
              color: '#ffffff'
            }
          }, option.label)
        ) : React.createElement('div', {
          style: { color: '#888888', textAlign: 'center' }
        }, `No ${selectedTab} rules configured`)),
    
    React.createElement('div', {
      key: 'help',
      style: {
        borderTop: '1px solid #333',
        paddingTop: '10px',
        color: '#888888',
        fontSize: '12px'
      }
    }, 'Tab to select tab • Enter to confirm • Esc to cancel')
  ]);
}

/**
 * /permissions 命令定义
 * @original: 原始文件L44009-44027中的Kq8对象
 */
export const permissionsCommand = {
  type: "local-jsx",
  name: "permissions",
  aliases: ["allowed-tools"],
  description: "Manage allow & deny tool permission rules",
  isEnabled: () => true,
  isHidden: false,
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @returns {Promise<React.Element>} React组件
   */
  async call(onDone, context) {
    return React.createElement(PermissionManager, {
      onExit: onDone,
      getToolPermissionContext: context.getToolPermissionContext,
      setToolPermissionContext: context.setToolPermissionContext
    });
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "permissions";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /permissions

Manage tool permission rules for Claude Code.

The permissions interface has three tabs:

Allow Tab:
- Configure tools that Claude Code will always allow
- Add rules like "Edit", "Bash(git:*)", "Write(*.js)"
- These tools will be used without asking for permission

Deny Tab:
- Configure tools that Claude Code will always deny
- Add rules to block specific tools or patterns
- These tools will never be used, even if requested

Workspace Tab:
- Manage additional working directories
- Add directories that Claude Code can access
- Remove directories from the workspace

Rule Syntax:
- Tool name only: "Edit" (applies to all uses of the tool)
- With parameters: "Bash(git:*)" (applies to specific patterns)
- File patterns: "Write(*.js)" (applies to specific file types)

Navigation:
- Tab/→: Switch between tabs
- ↑/↓: Navigate options
- Enter: Select option or add rule
- Escape: Close interface

Permission rules are saved to your configuration and persist across sessions.
    `.trim();
  }
};
