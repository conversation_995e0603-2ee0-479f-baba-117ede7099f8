/**
 * /mcp 命令实现
 * @original: 原始文件L41628-41654中的$$8对象和L41457-41572中的ZO0、GO0组件
 */

import React from 'react';
import { mcpService } from '../services/mcp-service.js';

/**
 * MCP服务器状态类型
 */
export const MCP_SERVER_STATUS = {
  CONNECTED: 'connected',
  PENDING: 'pending',
  FAILED: 'failed',
  NEEDS_AUTH: 'needs-auth',
  DISCONNECTED: 'disconnected'
};

/**
 * MCP传输类型
 */
export const MCP_TRANSPORT_TYPES = {
  STDIO: 'stdio',
  SSE: 'sse',
  HTTP: 'http'
};

/**
 * 获取服务器状态图标
 * @param {string} status - 服务器状态
 * @returns {string} 状态图标
 */
function getStatusIcon(status) {
  switch (status) {
    case MCP_SERVER_STATUS.CONNECTED:
      return '✅';
    case MCP_SERVER_STATUS.PENDING:
      return '🔄';
    case MCP_SERVER_STATUS.FAILED:
      return '❌';
    case MCP_SERVER_STATUS.NEEDS_AUTH:
      return '🔐';
    case MCP_SERVER_STATUS.DISCONNECTED:
      return '⚫';
    default:
      return '❓';
  }
}

/**
 * 获取传输类型显示名称
 * @param {string} transport - 传输类型
 * @returns {string} 显示名称
 */
function getTransportDisplayName(transport) {
  switch (transport) {
    case MCP_TRANSPORT_TYPES.STDIO:
      return 'Standard I/O';
    case MCP_TRANSPORT_TYPES.SSE:
      return 'Server-Sent Events';
    case MCP_TRANSPORT_TYPES.HTTP:
      return 'HTTP';
    default:
      return transport || 'Unknown';
  }
}

/**
 * MCP服务器重连组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L41574-41627中的GO0组件
 */
function MCPServerReconnect({ serverName, onComplete }) {
  const [isConnecting, setIsConnecting] = React.useState(true);
  const [error, setError] = React.useState(null);
  
  React.useEffect(() => {
    async function reconnectServer() {
      try {
        // 检查服务器是否存在
        const servers = mcpService.getConnectionStats().servers;
        const server = servers.find(s => s.name === serverName);
        
        if (!server) {
          setError(`MCP server "${serverName}" not found`);
          setIsConnecting(false);
          return;
        }
        
        // 尝试重连
        const result = await mcpService.connectServer(serverName);
        
        switch (result.status) {
          case MCP_SERVER_STATUS.CONNECTED:
            onComplete(`Successfully reconnected to ${serverName}`);
            break;
          case MCP_SERVER_STATUS.NEEDS_AUTH:
            setError(`${serverName} requires authentication`);
            setIsConnecting(false);
            onComplete(`${serverName} requires authentication. Use /mcp to authenticate.`);
            break;
          case MCP_SERVER_STATUS.PENDING:
          case MCP_SERVER_STATUS.FAILED:
            setError(`Failed to reconnect to ${serverName}`);
            setIsConnecting(false);
            onComplete(`Failed to reconnect to ${serverName}`);
            break;
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err);
        setError(errorMessage);
        setIsConnecting(false);
        onComplete(`Error: ${errorMessage}`);
      }
    }
    
    reconnectServer();
  }, [serverName, onComplete]);
  
  if (isConnecting) {
    return React.createElement('div', {
      style: {
        padding: '10px',
        display: 'flex',
        alignItems: 'center',
        color: '#ffff00'
      }
    }, [
      React.createElement('span', { key: 'spinner' }, '🔄 '),
      React.createElement('span', { key: 'text' }, `Reconnecting to ${serverName}...`)
    ]);
  }
  
  if (error) {
    return React.createElement('div', {
      style: {
        padding: '10px',
        color: '#ff0000'
      }
    }, [
      React.createElement('div', {
        key: 'error',
        style: { marginBottom: '5px' }
      }, `❌ Failed to reconnect to ${serverName}`),
      React.createElement('div', {
        key: 'details',
        style: { color: '#888888', fontSize: '12px' }
      }, `Error: ${error}`)
    ]);
  }
  
  return null;
}

/**
 * MCP服务器管理主界面
 * @param {Object} props - 组件属性
 * @original: 原始文件L41457-41572中的ZO0组件
 */
function MCPServerManager({ onComplete }) {
  const [view, setView] = React.useState({ type: 'list' });
  const [servers, setServers] = React.useState([]);
  const [loading, setLoading] = React.useState(true);
  
  // 加载服务器列表
  React.useEffect(() => {
    async function loadServers() {
      try {
        const stats = mcpService.getConnectionStats();
        const serverList = await Promise.all(
          stats.servers
            .filter(server => server.name !== 'ide')
            .sort((a, b) => a.name.localeCompare(b.name))
            .map(async (server) => {
              const scope = server.config?.scope || 'unknown';
              const isSSE = server.config?.type === 'sse';
              const isHTTP = server.config?.type === 'http';
              
              let isAuthenticated = undefined;
              if (isSSE || isHTTP) {
                try {
                  // 检查认证状态
                  isAuthenticated = await mcpService.checkAuthentication(server.name);
                } catch (error) {
                  isAuthenticated = false;
                }
              }
              
              return {
                name: server.name,
                client: server,
                scope,
                transport: isSSE ? 'sse' : isHTTP ? 'http' : 'stdio',
                isAuthenticated,
                config: server.config,
                status: server.status || MCP_SERVER_STATUS.DISCONNECTED,
                tools: server.tools || [],
                resources: server.resources || []
              };
            })
        );
        
        setServers(serverList);
      } catch (error) {
        console.error('Failed to load MCP servers:', error);
      } finally {
        setLoading(false);
      }
    }
    
    loadServers();
  }, []);
  
  const handleKeyPress = React.useCallback((event) => {
    if (event.key === 'Escape') {
      if (view.type === 'list') {
        onComplete();
      } else {
        setView({ type: 'list' });
      }
    }
  }, [view.type, onComplete]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  if (loading) {
    return React.createElement('div', {
      style: {
        padding: '20px',
        textAlign: 'center',
        color: '#ffff00'
      }
    }, '🔄 Loading MCP servers...');
  }
  
  // 渲染不同的视图
  switch (view.type) {
    case 'server-details':
      return React.createElement(ServerDetailsView, {
        server: view.server,
        onBack: () => setView({ type: 'list' })
      });
      
    case 'server-tools':
      return React.createElement(ServerToolsView, {
        server: view.server,
        onBack: () => setView({ type: 'server-details', server: view.server })
      });
      
    case 'server-resources':
      return React.createElement(ServerResourcesView, {
        server: view.server,
        onBack: () => setView({ type: 'server-tools', server: view.server })
      });
      
    default:
      return React.createElement(ServerListView, {
        servers,
        onServerSelect: (server) => setView({ type: 'server-details', server }),
        onComplete
      });
  }
}

/**
 * 服务器列表视图
 * @param {Object} props - 组件属性
 */
function ServerListView({ servers, onServerSelect, onComplete }) {
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  
  const handleKeyPress = React.useCallback((event) => {
    switch (event.key) {
      case 'ArrowUp':
        setSelectedIndex(prev => Math.max(0, prev - 1));
        break;
      case 'ArrowDown':
        setSelectedIndex(prev => Math.min(servers.length - 1, prev + 1));
        break;
      case 'Enter':
        if (servers[selectedIndex]) {
          onServerSelect(servers[selectedIndex]);
        }
        break;
      case 'Escape':
        onComplete();
        break;
    }
  }, [selectedIndex, servers, onServerSelect, onComplete]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  if (servers.length === 0) {
    return React.createElement('div', {
      style: {
        padding: '20px',
        textAlign: 'center',
        color: '#888888'
      }
    }, [
      React.createElement('h2', {
        key: 'title',
        style: { color: '#ffff00', marginBottom: '10px' }
      }, 'MCP Servers'),
      React.createElement('p', { key: 'empty' }, 'No MCP servers configured.'),
      React.createElement('p', {
        key: 'help',
        style: { fontSize: '12px', marginTop: '10px' }
      }, 'Use `claude mcp add` to add a server.')
    ]);
  }
  
  return React.createElement('div', {
    style: {
      padding: '10px',
      fontFamily: 'monospace'
    }
  }, [
    // 标题
    React.createElement('h2', {
      key: 'title',
      style: { color: '#00ff00', marginBottom: '15px' }
    }, 'MCP Servers'),
    
    // 服务器列表
    React.createElement('div', {
      key: 'servers',
      style: { marginBottom: '15px' }
    }, servers.map((server, index) => {
      const isSelected = index === selectedIndex;
      
      return React.createElement('div', {
        key: server.name,
        style: {
          padding: '8px',
          backgroundColor: isSelected ? '#333' : 'transparent',
          border: '1px solid #444',
          marginBottom: '5px',
          borderRadius: '4px'
        }
      }, [
        React.createElement('div', {
          key: 'header',
          style: {
            display: 'flex',
            alignItems: 'center',
            marginBottom: '5px'
          }
        }, [
          React.createElement('span', {
            key: 'status',
            style: { marginRight: '8px' }
          }, getStatusIcon(server.status)),
          React.createElement('span', {
            key: 'name',
            style: {
              fontWeight: 'bold',
              color: isSelected ? '#00ff00' : '#ffffff'
            }
          }, server.name),
          React.createElement('span', {
            key: 'scope',
            style: {
              marginLeft: '10px',
              color: '#888888',
              fontSize: '12px'
            }
          }, `(${server.scope})`)
        ]),
        
        React.createElement('div', {
          key: 'details',
          style: {
            fontSize: '12px',
            color: '#cccccc',
            marginLeft: '20px'
          }
        }, [
          React.createElement('div', { key: 'transport' }, 
            `Transport: ${getTransportDisplayName(server.transport)}`
          ),
          server.isAuthenticated !== undefined && React.createElement('div', { 
            key: 'auth' 
          }, `Authenticated: ${server.isAuthenticated ? 'Yes' : 'No'}`),
          React.createElement('div', { key: 'tools' }, 
            `Tools: ${server.tools.length}`
          ),
          React.createElement('div', { key: 'resources' }, 
            `Resources: ${server.resources.length}`
          )
        ])
      ]);
    })),
    
    // 帮助信息
    React.createElement('div', {
      key: 'help',
      style: {
        borderTop: '1px solid #333',
        paddingTop: '10px',
        color: '#888888',
        fontSize: '12px'
      }
    }, '↑/↓ to select • Enter to view details • Esc to close')
  ]);
}

/**
 * 服务器详情视图
 * @param {Object} props - 组件属性
 */
function ServerDetailsView({ server, onBack }) {
  const handleKeyPress = React.useCallback((event) => {
    if (event.key === 'Escape') {
      onBack();
    }
  }, [onBack]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  return React.createElement('div', {
    style: {
      padding: '10px',
      fontFamily: 'monospace'
    }
  }, [
    React.createElement('h2', {
      key: 'title',
      style: { color: '#00ff00', marginBottom: '15px' }
    }, `MCP Server: ${server.name}`),
    
    React.createElement('div', {
      key: 'details',
      style: { marginBottom: '15px' }
    }, [
      React.createElement('div', { key: 'status' }, 
        `Status: ${getStatusIcon(server.status)} ${server.status}`
      ),
      React.createElement('div', { key: 'scope' }, 
        `Scope: ${server.scope}`
      ),
      React.createElement('div', { key: 'transport' }, 
        `Transport: ${getTransportDisplayName(server.transport)}`
      ),
      server.isAuthenticated !== undefined && React.createElement('div', { 
        key: 'auth' 
      }, `Authenticated: ${server.isAuthenticated ? 'Yes' : 'No'}`),
      React.createElement('div', { key: 'tools' }, 
        `Available Tools: ${server.tools.length}`
      ),
      React.createElement('div', { key: 'resources' }, 
        `Available Resources: ${server.resources.length}`
      )
    ]),
    
    React.createElement('div', {
      key: 'help',
      style: {
        borderTop: '1px solid #333',
        paddingTop: '10px',
        color: '#888888',
        fontSize: '12px'
      }
    }, 'Esc to go back')
  ]);
}

/**
 * 服务器工具视图
 * @param {Object} props - 组件属性
 */
function ServerToolsView({ server, onBack }) {
  const handleKeyPress = React.useCallback((event) => {
    if (event.key === 'Escape') {
      onBack();
    }
  }, [onBack]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  return React.createElement('div', {
    style: {
      padding: '10px',
      fontFamily: 'monospace'
    }
  }, [
    React.createElement('h2', {
      key: 'title',
      style: { color: '#00ff00', marginBottom: '15px' }
    }, `Tools: ${server.name}`),
    
    React.createElement('div', {
      key: 'tools',
      style: { marginBottom: '15px' }
    }, server.tools.length > 0 ? server.tools.map((tool, index) => 
      React.createElement('div', {
        key: index,
        style: {
          padding: '5px',
          border: '1px solid #444',
          marginBottom: '5px',
          borderRadius: '4px'
        }
      }, [
        React.createElement('div', {
          key: 'name',
          style: { fontWeight: 'bold', color: '#ffff00' }
        }, tool.name),
        tool.description && React.createElement('div', {
          key: 'desc',
          style: { color: '#cccccc', fontSize: '12px' }
        }, tool.description)
      ])
    ) : React.createElement('div', {
      style: { color: '#888888', textAlign: 'center' }
    }, 'No tools available')),
    
    React.createElement('div', {
      key: 'help',
      style: {
        borderTop: '1px solid #333',
        paddingTop: '10px',
        color: '#888888',
        fontSize: '12px'
      }
    }, 'Esc to go back')
  ]);
}

/**
 * 服务器资源视图
 * @param {Object} props - 组件属性
 */
function ServerResourcesView({ server, onBack }) {
  const handleKeyPress = React.useCallback((event) => {
    if (event.key === 'Escape') {
      onBack();
    }
  }, [onBack]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  return React.createElement('div', {
    style: {
      padding: '10px',
      fontFamily: 'monospace'
    }
  }, [
    React.createElement('h2', {
      key: 'title',
      style: { color: '#00ff00', marginBottom: '15px' }
    }, `Resources: ${server.name}`),
    
    React.createElement('div', {
      key: 'resources',
      style: { marginBottom: '15px' }
    }, server.resources.length > 0 ? server.resources.map((resource, index) => 
      React.createElement('div', {
        key: index,
        style: {
          padding: '5px',
          border: '1px solid #444',
          marginBottom: '5px',
          borderRadius: '4px'
        }
      }, [
        React.createElement('div', {
          key: 'uri',
          style: { fontWeight: 'bold', color: '#ffff00' }
        }, resource.uri),
        resource.name && React.createElement('div', {
          key: 'name',
          style: { color: '#cccccc', fontSize: '12px' }
        }, resource.name),
        resource.description && React.createElement('div', {
          key: 'desc',
          style: { color: '#888888', fontSize: '12px' }
        }, resource.description)
      ])
    ) : React.createElement('div', {
      style: { color: '#888888', textAlign: 'center' }
    }, 'No resources available')),
    
    React.createElement('div', {
      key: 'help',
      style: {
        borderTop: '1px solid #333',
        paddingTop: '10px',
        color: '#888888',
        fontSize: '12px'
      }
    }, 'Esc to go back')
  ]);
}

/**
 * /mcp 命令定义
 * @original: 原始文件L41628-41654中的$$8对象
 */
export const mcpCommand = {
  type: "local-jsx",
  name: "mcp",
  description: "Manage MCP servers",
  isEnabled: () => true,
  isHidden: false,
  argumentHint: "[reconnect <server-name>]",
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @param {string} args - 命令参数
   * @returns {Promise<React.Element>} React组件
   */
  async call(onDone, context, args) {
    if (args) {
      const parts = args.trim().split(/\s+/);
      if (parts[0] === 'reconnect' && parts[1]) {
        const serverName = parts.slice(1).join(' ');
        return React.createElement(MCPServerReconnect, {
          serverName,
          onComplete: onDone
        });
      }
    }
    
    return React.createElement(MCPServerManager, {
      onComplete: onDone
    });
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "mcp";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /mcp [reconnect <server-name>]

Manage MCP (Model Context Protocol) servers.

Commands:
  /mcp                    # Show MCP server management interface
  /mcp reconnect <name>   # Reconnect to a specific MCP server

The MCP management interface allows you to:
- View all configured MCP servers and their status
- Check server connection status and authentication
- View available tools and resources for each server
- Reconnect to failed servers

Server Status Icons:
  ✅ Connected    - Server is connected and ready
  🔄 Pending      - Connection in progress
  ❌ Failed       - Connection failed
  🔐 Needs Auth   - Server requires authentication
  ⚫ Disconnected - Server is not connected

Navigation:
- ↑/↓ arrows: Navigate between servers
- Enter: View server details
- Escape: Go back or close

For adding or removing MCP servers, use the CLI commands:
  claude mcp add <name> <command>
  claude mcp remove <name>
  claude mcp list
    `.trim();
  }
};
