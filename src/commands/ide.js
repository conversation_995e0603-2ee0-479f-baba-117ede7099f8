/**
 * /ide 命令实现
 * @original: 原始文件L38016-38093中的W$8对象和L37874-37970中的F$8、Y$8组件
 */

import React from 'react';

/**
 * IDE类型常量
 */
export const IDE_TYPES = {
  VSCODE: 'vscode',
  CURSOR: 'cursor',
  WINDSURF: 'windsurf',
  INTELLIJ: 'intellij',
  PYCHARM: 'pycharm',
  WEBSTORM: 'webstorm',
  PHPSTORM: 'phpstorm',
  RUBYMINE: 'rubymine',
  CLION: 'clion',
  GOLAND: 'goland',
  RIDER: 'rider',
  ANDROID_STUDIO: 'android_studio'
};

/**
 * IDE连接状态
 */
export const IDE_CONNECTION_STATUS = {
  CONNECTED: 'connected',
  PENDING: 'pending',
  FAILED: 'failed',
  NEEDS_AUTH: 'needs-auth',
  DISCONNECTED: 'disconnected'
};

/**
 * 获取IDE显示名称
 * @param {string} ideType - IDE类型
 * @returns {string} 显示名称
 */
function getIDEDisplayName(ideType) {
  const displayNames = {
    [IDE_TYPES.VSCODE]: 'Visual Studio Code',
    [IDE_TYPES.CURSOR]: 'Cursor',
    [IDE_TYPES.WINDSURF]: 'Windsurf',
    [IDE_TYPES.INTELLIJ]: 'IntelliJ IDEA',
    [IDE_TYPES.PYCHARM]: 'PyCharm',
    [IDE_TYPES.WEBSTORM]: 'WebStorm',
    [IDE_TYPES.PHPSTORM]: 'PhpStorm',
    [IDE_TYPES.RUBYMINE]: 'RubyMine',
    [IDE_TYPES.CLION]: 'CLion',
    [IDE_TYPES.GOLAND]: 'GoLand',
    [IDE_TYPES.RIDER]: 'Rider',
    [IDE_TYPES.ANDROID_STUDIO]: 'Android Studio'
  };
  
  return displayNames[ideType] || ideType;
}

/**
 * 检查是否为JetBrains IDE
 * @param {string} ideType - IDE类型
 * @returns {boolean} 是否为JetBrains IDE
 */
function isJetBrainsIDE(ideType) {
  const jetbrainsIDEs = [
    IDE_TYPES.INTELLIJ,
    IDE_TYPES.PYCHARM,
    IDE_TYPES.WEBSTORM,
    IDE_TYPES.PHPSTORM,
    IDE_TYPES.RUBYMINE,
    IDE_TYPES.CLION,
    IDE_TYPES.GOLAND,
    IDE_TYPES.RIDER,
    IDE_TYPES.ANDROID_STUDIO
  ];
  
  return jetbrainsIDEs.includes(ideType);
}

/**
 * 模拟获取运行中的IDE列表
 * @returns {Array} 运行中的IDE列表
 */
async function getRunningIDEs() {
  // 模拟检测运行中的IDE
  return [
    {
      name: 'Visual Studio Code',
      type: IDE_TYPES.VSCODE,
      port: 3001,
      url: 'http://localhost:3001',
      workspaceFolders: ['/Users/<USER>/projects/my-project'],
      authToken: 'mock_token_123',
      ideRunningInWindows: false,
      isValid: true
    },
    {
      name: 'Cursor',
      type: IDE_TYPES.CURSOR,
      port: 3002,
      url: 'http://localhost:3002',
      workspaceFolders: ['/Users/<USER>/projects/another-project'],
      authToken: 'mock_token_456',
      ideRunningInWindows: false,
      isValid: true
    }
  ];
}

/**
 * 模拟安装IDE扩展
 * @param {string} ideType - IDE类型
 * @returns {Promise<boolean>} 安装结果
 */
async function installIDEExtension(ideType) {
  // 模拟扩展安装过程
  await new Promise(resolve => setTimeout(resolve, 1000));
  return true;
}

/**
 * IDE选择器组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L37971-38015中的Y$8组件
 */
function IDESelector({ runningIDEs, onSelectIDE, onDone }) {
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  
  const options = runningIDEs.map(ide => ({
    label: getIDEDisplayName(ide.type),
    value: ide.type,
    ide
  }));
  
  const handleKeyPress = React.useCallback((event) => {
    switch (event.key) {
      case 'ArrowUp':
        setSelectedIndex(prev => Math.max(0, prev - 1));
        break;
      case 'ArrowDown':
        setSelectedIndex(prev => Math.min(options.length - 1, prev + 1));
        break;
      case 'Enter':
        if (options[selectedIndex]) {
          onSelectIDE(options[selectedIndex].ide.type);
        }
        break;
      case 'Escape':
        onDone();
        break;
    }
  }, [selectedIndex, options, onSelectIDE, onDone]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  return React.createElement('div', {
    style: {
      padding: '15px',
      border: '2px solid #8800ff',
      borderRadius: '8px',
      backgroundColor: '#000000',
      color: '#ffffff',
      fontFamily: 'monospace'
    }
  }, [
    React.createElement('h2', {
      key: 'title',
      style: { color: '#8800ff', marginBottom: '10px' }
    }, 'Select IDE to install extension:'),
    
    React.createElement('div', {
      key: 'options',
      style: { marginBottom: '15px' }
    }, options.map((option, index) => 
      React.createElement('div', {
        key: option.value,
        style: {
          padding: '8px',
          backgroundColor: index === selectedIndex ? '#333' : 'transparent',
          color: index === selectedIndex ? '#8800ff' : '#ffffff',
          cursor: 'pointer',
          borderRadius: '4px'
        }
      }, `${index === selectedIndex ? '► ' : '  '}${option.label}`)
    )),
    
    React.createElement('div', {
      key: 'help',
      style: {
        color: '#888888',
        fontSize: '12px'
      }
    }, '↑/↓ to select • Enter to confirm • Esc to cancel')
  ]);
}

/**
 * IDE连接管理器组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L37874-37964中的F$8组件
 */
function IDEConnectionManager({ availableIDEs, unavailableIDEs, selectedIDE, onClose, onSelect }) {
  const [selectedPort, setSelectedPort] = React.useState(selectedIDE?.port?.toString() ?? "None");
  const [showAutoConnectDialog, setShowAutoConnectDialog] = React.useState(false);
  
  // 创建IDE选项列表
  const ideOptions = React.useMemo(() => {
    const nameCounts = availableIDEs.reduce((counts, ide) => {
      counts[ide.name] = (counts[ide.name] || 0) + 1;
      return counts;
    }, {});
    
    const options = availableIDEs.map(ide => {
      let label = ide.name;
      
      // 如果有多个同名IDE，显示工作区路径
      if (nameCounts[ide.name] > 1 && ide.workspaceFolders.length > 0) {
        const workspaces = ide.workspaceFolders.map(folder => {
          const cwd = process.cwd();
          return folder.startsWith(cwd + '/') ? folder.slice(cwd.length + 1) : folder;
        }).join(', ');
        label += `: ${workspaces}`;
      }
      
      return {
        label,
        value: ide.port.toString(),
        ide
      };
    });
    
    options.push({
      label: 'None',
      value: 'None',
      ide: null
    });
    
    return options;
  }, [availableIDEs]);
  
  const [selectedIndex, setSelectedIndex] = React.useState(() => {
    const index = ideOptions.findIndex(option => option.value === selectedPort);
    return index >= 0 ? index : 0;
  });
  
  const handleSelection = React.useCallback((port) => {
    if (port === "None") {
      onSelect(null);
    } else {
      const selectedIDE = availableIDEs.find(ide => ide.port === parseInt(port));
      if (selectedIDE) {
        // 检查是否需要显示自动连接对话框
        const shouldShowDialog = false; // 简化处理，实际应该检查配置
        if (shouldShowDialog) {
          setShowAutoConnectDialog(true);
        } else {
          onSelect(selectedIDE);
        }
      }
    }
  }, [availableIDEs, onSelect]);
  
  const handleKeyPress = React.useCallback((event) => {
    if (showAutoConnectDialog) return;
    
    switch (event.key) {
      case 'ArrowUp':
        setSelectedIndex(prev => {
          const newIndex = Math.max(0, prev - 1);
          setSelectedPort(ideOptions[newIndex]?.value || "None");
          return newIndex;
        });
        break;
      case 'ArrowDown':
        setSelectedIndex(prev => {
          const newIndex = Math.min(ideOptions.length - 1, prev + 1);
          setSelectedPort(ideOptions[newIndex]?.value || "None");
          return newIndex;
        });
        break;
      case 'Enter':
        handleSelection(selectedPort);
        break;
      case 'Escape':
        onClose();
        break;
    }
  }, [selectedIndex, selectedPort, ideOptions, handleSelection, onClose, showAutoConnectDialog]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  if (showAutoConnectDialog) {
    return React.createElement('div', {
      style: { padding: '20px', textAlign: 'center', color: '#ffff00' }
    }, [
      React.createElement('div', {
        key: 'message',
        style: { marginBottom: '10px' }
      }, 'Setting up auto-connect...'),
      React.createElement('div', {
        key: 'action',
        style: { color: '#888888', fontSize: '12px' }
      }, 'This will be configured automatically.')
    ]);
  }
  
  return React.createElement('div', {
    style: {
      padding: '15px',
      border: '2px solid #8800ff',
      borderRadius: '8px',
      backgroundColor: '#000000',
      color: '#ffffff',
      fontFamily: 'monospace'
    }
  }, [
    // 标题和描述
    React.createElement('div', {
      key: 'header',
      style: { marginBottom: '15px' }
    }, [
      React.createElement('h2', {
        key: 'title',
        style: { color: '#8800ff', marginBottom: '5px' }
      }, 'Select IDE'),
      React.createElement('p', {
        key: 'description',
        style: { color: '#cccccc', margin: 0 }
      }, 'Connect to an IDE for integrated development features.')
    ]),
    
    // 无可用IDE的提示
    availableIDEs.length === 0 && React.createElement('div', {
      key: 'no-ides',
      style: {
        padding: '10px',
        backgroundColor: '#1a1a1a',
        borderRadius: '4px',
        marginBottom: '15px'
      }
    }, [
      React.createElement('p', {
        key: 'message',
        style: { color: '#ffff00', margin: 0 }
      }, 'No available IDEs detected.'),
      React.createElement('p', {
        key: 'instruction',
        style: { color: '#888888', fontSize: '12px', margin: '5px 0 0 0' }
      }, 'Make sure your IDE has the Claude Code extension or plugin installed and is running.')
    ]),
    
    // IDE选项列表
    availableIDEs.length > 0 && React.createElement('div', {
      key: 'options',
      style: { marginBottom: '15px' }
    }, ideOptions.map((option, index) => 
      React.createElement('div', {
        key: option.value,
        style: {
          padding: '8px',
          backgroundColor: index === selectedIndex ? '#333' : 'transparent',
          color: index === selectedIndex ? '#8800ff' : '#ffffff',
          cursor: 'pointer',
          borderRadius: '4px',
          marginBottom: '2px'
        }
      }, `${index === selectedIndex ? '► ' : '  '}${option.label}`)
    )),
    
    // 自动连接提示
    availableIDEs.length > 0 && React.createElement('div', {
      key: 'tip',
      style: {
        color: '#888888',
        fontSize: '12px',
        marginBottom: '10px'
      }
    }, '※ Tip: You can enable auto-connect to IDE in /config or with the --ide flag'),
    
    // 不匹配的IDE列表
    unavailableIDEs.length > 0 && React.createElement('div', {
      key: 'unavailable',
      style: { marginBottom: '15px' }
    }, [
      React.createElement('p', {
        key: 'unavailable-title',
        style: { color: '#888888', fontSize: '12px', margin: '0 0 5px 0' }
      }, `Found ${unavailableIDEs.length} other running IDE(s). However, their workspace/project directories do not match the current cwd.`),
      
      React.createElement('div', {
        key: 'unavailable-list',
        style: { marginLeft: '15px' }
      }, unavailableIDEs.map((ide, index) => 
        React.createElement('div', {
          key: index,
          style: { color: '#666666', fontSize: '11px' }
        }, `• ${ide.name}: ${ide.workspaceFolders.join(', ')}`)
      ))
    ]),
    
    // 帮助信息
    React.createElement('div', {
      key: 'help',
      style: {
        borderTop: '1px solid #333',
        paddingTop: '10px',
        color: '#888888',
        fontSize: '12px'
      }
    }, availableIDEs.length > 0 ? '↑/↓ to select • Enter to confirm • Esc to exit' : 'Esc to exit')
  ]);
}

/**
 * /ide 命令定义
 * @original: 原始文件L38016-38093中的W$8对象
 */
export const ideCommand = {
  type: "local-jsx",
  name: "ide",
  description: "Manage IDE integrations and show status",
  isEnabled: () => true,
  isHidden: false,
  argumentHint: "[open]",
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @param {string} args - 命令参数
   * @returns {Promise<React.Element>} React组件
   */
  async call(onDone, context, args) {
    try {
      // 获取运行中的IDE列表
      const allIDEs = await getRunningIDEs();
      const availableIDEs = allIDEs.filter(ide => ide.isValid);
      const unavailableIDEs = allIDEs.filter(ide => !ide.isValid);
      
      // 如果没有可用的IDE且有安装扩展的回调
      if (availableIDEs.length === 0 && context.onInstallIDEExtension) {
        const runningIDEs = [IDE_TYPES.VSCODE, IDE_TYPES.CURSOR]; // 模拟检测到的IDE
        
        const handleInstallExtension = async (ideType) => {
          try {
            const success = await installIDEExtension(ideType);
            if (success) {
              const displayName = getIDEDisplayName(ideType);
              if (isJetBrainsIDE(ideType)) {
                onDone(`Installed plugin to ${displayName}\nPlease restart your IDE completely for it to take effect`);
              } else {
                onDone(`Installed extension to ${displayName}`);
              }
            } else {
              onDone(`Failed to install extension to ${getIDEDisplayName(ideType)}`);
            }
          } catch (error) {
            onDone(`Error installing extension: ${error.message}`);
          }
        };
        
        // 如果有多个运行中的IDE，显示选择器
        if (runningIDEs.length > 1) {
          return React.createElement(IDESelector, {
            runningIDEs: runningIDEs.map(type => ({ type })),
            onSelectIDE: handleInstallExtension,
            onDone: () => onDone("No IDE selected.")
          });
        } else if (runningIDEs.length === 1) {
          // 自动安装到唯一的IDE
          setTimeout(() => handleInstallExtension(runningIDEs[0]), 100);
          return React.createElement('div', {
            style: { padding: '20px', color: '#ffff00' }
          }, `Installing extension to ${getIDEDisplayName(runningIDEs[0])}...`);
        }
      }
      
      // 查找当前选中的IDE
      const currentIDE = context.options?.dynamicMcpConfig?.ide ? 
        availableIDEs.find(ide => ide.url === context.options.dynamicMcpConfig.ide.url) : 
        null;
      
      return React.createElement(IDEConnectionManager, {
        availableIDEs,
        unavailableIDEs,
        selectedIDE: currentIDE,
        onClose: () => onDone(),
        onSelect: async (selectedIDE) => {
          try {
            if (!context.onChangeDynamicMcpConfig) {
              onDone("Error connecting to IDE.");
              return;
            }
            
            const newConfig = { ...(context.options?.dynamicMcpConfig || {}) };
            
            if (currentIDE) {
              delete newConfig.ide;
            }
            
            if (!selectedIDE) {
              onDone(currentIDE ? `Disconnected from ${currentIDE.name}.` : "No IDE selected.");
            } else {
              newConfig.ide = {
                type: selectedIDE.url.startsWith("ws:") ? "ws-ide" : "sse-ide",
                url: selectedIDE.url,
                ideName: selectedIDE.name,
                authToken: selectedIDE.authToken,
                ideRunningInWindows: selectedIDE.ideRunningInWindows,
                scope: "dynamic"
              };
              onDone(`Connected to ${selectedIDE.name}.`);
            }
            
            context.onChangeDynamicMcpConfig(newConfig);
          } catch (error) {
            onDone("Error connecting to IDE.");
          }
        }
      });
      
    } catch (error) {
      onDone(`Error managing IDE integration: ${error.message}`);
      return null;
    }
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "ide";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /ide [open]

Manage IDE integrations and show connection status.

The IDE command helps you:
- Connect to running IDEs with Claude Code extensions
- Install extensions to detected IDEs
- Manage IDE connection settings
- View IDE integration status

Supported IDEs:
- Visual Studio Code (with Claude Code extension)
- Cursor (with Claude Code extension)
- Windsurf (with Claude Code extension)
- JetBrains IDEs (with Claude Code plugin):
  • IntelliJ IDEA
  • PyCharm
  • WebStorm
  • PhpStorm
  • RubyMine
  • CLion
  • GoLand
  • Rider
  • Android Studio

Features:
- Automatic IDE detection
- Extension/plugin installation
- Workspace folder matching
- Auto-connect configuration
- Connection status monitoring

Navigation:
- ↑/↓ arrows: Navigate between IDEs
- Enter: Connect to selected IDE
- Escape: Close without connecting

For automatic IDE connection on startup, use:
  claude --ide
  
Or enable auto-connect in /config settings.
    `.trim();
  }
};
