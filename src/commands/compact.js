/**
 * /compact 命令实现
 * @original: 原始文件L35380-35407中的$H8对象和相关函数
 */

/**
 * 压缩对话历史的核心函数
 * @param {Array} messages - 消息数组
 * @param {Object} context - 执行上下文
 * @param {boolean} preserveRecent - 是否保留最近的消息
 * @param {string} customInstructions - 自定义压缩指令
 * @returns {Promise<Object>} 压缩结果
 * @original: 原始文件L35395-35397中的Nf1和Lb1函数调用
 */
async function compactConversationHistory(messages, context, preserveRecent = false, customInstructions = '') {
  if (!messages || !Array.isArray(messages) || messages.length === 0) {
    throw new Error("No messages to compact");
  }

  try {
    // 准备消息进行压缩
    const messagesToCompress = await prepareMessagesForCompaction(messages);
    
    // 执行压缩操作
    const compactionResult = await performCompaction(
      messagesToCompress, 
      context, 
      preserveRecent, 
      customInstructions
    );
    
    return compactionResult;
    
  } catch (error) {
    if (context.abortController?.signal.aborted) {
      throw new Error("Compaction canceled.");
    }
    
    // 检查是否是特定的错误类型
    if (error.message === 'RATE_LIMIT_EXCEEDED') {
      throw new Error('RATE_LIMIT_EXCEEDED');
    }
    
    throw new Error(`Error during compaction: ${error.message}`);
  }
}

/**
 * 准备消息进行压缩
 * @param {Array} messages - 原始消息数组
 * @returns {Promise<Array>} 准备好的消息数组
 * @original: 原始文件L35395中的Nf1函数调用
 */
async function prepareMessagesForCompaction(messages) {
  // 过滤和清理消息
  const cleanedMessages = messages.filter(message => {
    // 排除系统消息和空消息
    if (!message || message.type === 'system' || !message.content) {
      return false;
    }
    
    // 排除过短的消息
    if (typeof message.content === 'string' && message.content.trim().length < 10) {
      return false;
    }
    
    return true;
  });
  
  // 合并连续的相同角色消息
  const mergedMessages = [];
  let currentMessage = null;
  
  for (const message of cleanedMessages) {
    if (currentMessage && currentMessage.role === message.role) {
      // 合并相同角色的连续消息
      currentMessage.content += '\n\n' + message.content;
    } else {
      if (currentMessage) {
        mergedMessages.push(currentMessage);
      }
      currentMessage = { ...message };
    }
  }
  
  if (currentMessage) {
    mergedMessages.push(currentMessage);
  }
  
  return mergedMessages;
}

/**
 * 执行压缩操作
 * @param {Array} messages - 要压缩的消息
 * @param {Object} context - 执行上下文
 * @param {boolean} preserveRecent - 是否保留最近的消息
 * @param {string} customInstructions - 自定义压缩指令
 * @returns {Promise<Object>} 压缩结果
 * @original: 原始文件L35396中的Lb1函数调用
 */
async function performCompaction(messages, context, preserveRecent, customInstructions) {
  const compactionPrompt = createCompactionPrompt(messages, customInstructions);
  
  try {
    // 调用AI进行压缩
    const compactionResponse = await callAIForCompaction(compactionPrompt, context);
    
    // 处理压缩结果
    const compactedSummary = processCompactionResponse(compactionResponse);
    
    // 创建新的消息数组
    const newMessages = [];
    
    // 添加压缩后的摘要
    newMessages.push({
      role: 'system',
      content: `[Conversation Summary]\n${compactedSummary}`,
      type: 'summary',
      timestamp: new Date().toISOString(),
      originalMessageCount: messages.length
    });
    
    // 如果需要保留最近的消息
    if (preserveRecent && messages.length > 0) {
      const recentMessages = messages.slice(-3); // 保留最后3条消息
      newMessages.push(...recentMessages);
    }
    
    return {
      success: true,
      originalMessageCount: messages.length,
      compactedMessageCount: newMessages.length,
      summary: compactedSummary,
      newMessages,
      userDisplayMessage: customInstructions ? 
        `Compacted with custom instructions: ${customInstructions}` : 
        'Conversation compacted successfully'
    };
    
  } catch (error) {
    throw new Error(`Compaction failed: ${error.message}`);
  }
}

/**
 * 创建压缩提示
 * @param {Array} messages - 消息数组
 * @param {string} customInstructions - 自定义指令
 * @returns {string} 压缩提示
 */
function createCompactionPrompt(messages, customInstructions) {
  const messageText = messages.map(msg => {
    const role = msg.role || 'unknown';
    const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
    return `[${role.toUpperCase()}]: ${content}`;
  }).join('\n\n');
  
  let prompt = `Please create a comprehensive summary of the following conversation. Focus on:
1. Key topics discussed
2. Important decisions made
3. Technical details and solutions
4. Action items or next steps
5. Context that would be valuable for continuing the conversation

`;

  if (customInstructions) {
    prompt += `Additional instructions: ${customInstructions}\n\n`;
  }
  
  prompt += `Conversation to summarize:\n\n${messageText}`;
  
  return prompt;
}

/**
 * 调用AI进行压缩
 * @param {string} prompt - 压缩提示
 * @param {Object} context - 执行上下文
 * @returns {Promise<string>} AI响应
 */
async function callAIForCompaction(prompt, context) {
  // 这里应该调用实际的AI服务
  // 暂时返回模拟的压缩结果
  
  if (context.aiService && typeof context.aiService.generateSummary === 'function') {
    return await context.aiService.generateSummary(prompt);
  }
  
  // 模拟压缩结果
  return `This conversation covered various topics and technical discussions. Key points include problem-solving approaches, code implementations, and collaborative decision-making. The conversation maintained a focus on practical solutions and clear communication.`;
}

/**
 * 处理压缩响应
 * @param {string} response - AI响应
 * @returns {string} 处理后的摘要
 */
function processCompactionResponse(response) {
  if (!response || typeof response !== 'string') {
    return 'Conversation summary unavailable';
  }
  
  // 清理和格式化响应
  let summary = response.trim();
  
  // 移除可能的标记
  summary = summary.replace(/^\[SUMMARY\]|\[\/SUMMARY\]$/gi, '');
  summary = summary.replace(/^Summary:|^Conversation Summary:/gi, '');
  summary = summary.trim();
  
  // 确保摘要不为空
  if (!summary) {
    return 'Conversation summary generated but content is empty';
  }
  
  return summary;
}

/**
 * 清除相关缓存
 * @original: 原始文件L35397-35398中的缓存清除调用
 */
function clearRelatedCaches() {
  try {
    // 清除对话缓存
    if (typeof window !== 'undefined' && window.OX && window.OX.cache && window.OX.cache.clear) {
      window.OX.cache.clear();
    }
    
    // 清除其他相关缓存
    if (typeof window !== 'undefined' && window.ZW && window.ZW.cache && window.ZW.cache.clear) {
      window.ZW.cache.clear();
    }
    
    // 清除本地存储中的缓存数据
    if (typeof localStorage !== 'undefined') {
      const cacheKeys = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('conversation_cache_')) {
          cacheKeys.push(key);
        }
      }
      
      cacheKeys.forEach(key => {
        localStorage.removeItem(key);
      });
    }
    
  } catch (error) {
    console.warn('Failed to clear some caches:', error);
  }
}

/**
 * /compact 命令定义
 * @original: 原始文件L35380-35407中的$H8对象
 */
export const compactCommand = {
  type: "local",
  name: "compact",
  description: "Clear conversation history but keep a summary in context. Optional: /compact [instructions for summarization]",
  isEnabled: () => true,
  isHidden: false,
  argumentHint: "<optional custom summarization instructions>",
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @param {string} args - 命令参数
   * @returns {Promise<string>} 执行结果
   */
  async call(onDone, context, args) {
    const { abortController, messages } = context;
    
    if (!messages || messages.length === 0) {
      throw new Error("No messages to compact");
    }
    
    const customInstructions = args ? args.trim() : '';
    
    try {
      // 执行压缩
      const result = await compactConversationHistory(
        messages, 
        context, 
        false, 
        customInstructions
      );
      
      // 更新上下文中的消息
      if (context.updateMessages && typeof context.updateMessages === 'function') {
        context.updateMessages(result.newMessages);
      } else {
        // 直接替换消息数组
        messages.length = 0;
        messages.push(...result.newMessages);
      }
      
      // 清除相关缓存
      clearRelatedCaches();
      
      // 构建结果消息
      let resultMessage = "Compacted. ctrl+r to see full summary";
      
      if (result.userDisplayMessage) {
        resultMessage += `\n${result.userDisplayMessage}`;
      }
      
      // 添加统计信息
      resultMessage += `\n(${result.originalMessageCount} messages → ${result.compactedMessageCount} messages)`;
      
      return resultMessage;
      
    } catch (error) {
      if (abortController?.signal.aborted) {
        throw new Error("Compaction canceled.");
      } else if (error.message === 'RATE_LIMIT_EXCEEDED') {
        throw new Error('RATE_LIMIT_EXCEEDED');
      } else {
        console.error('Compaction error:', error);
        throw new Error(`Error during compaction: ${error.message}`);
      }
    }
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "compact";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /compact [custom instructions]

Clear conversation history but keep a summary in context.

This command will:
- Analyze your conversation history
- Generate an intelligent summary using AI
- Replace the full history with the summary
- Free up context space while preserving important information

Arguments:
  custom instructions    Optional instructions for how to summarize
                        (e.g., "focus on technical details" or "emphasize decisions made")

Examples:
  /compact                           # Standard compaction
  /compact focus on code examples    # Custom summarization focus
  /compact keep technical details    # Emphasize technical information

Note: This operation cannot be undone. The original conversation history will be replaced with a summary.
    `.trim();
  }
};
