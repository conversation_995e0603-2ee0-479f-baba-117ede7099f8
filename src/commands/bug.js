/**
 * /bug 命令实现
 * @original: 原始文件L35103-35121中的DH8对象和反馈提交逻辑
 */

import React from 'react';

/**
 * 收集系统信息用于bug报告
 * @returns {Object} 系统信息
 */
function collectSystemInfo() {
  return {
    platform: process.platform,
    nodeVersion: process.version,
    claudeCodeVersion: process.env.CLAUDE_CODE_VERSION || 'unknown',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    environment: process.env.NODE_ENV || 'production',
    entrypoint: process.env.CLAUDE_CODE_ENTRYPOINT || 'unknown'
  };
}

/**
 * 收集会话信息
 * @param {Array} messages - 消息数组
 * @returns {Object} 会话信息
 */
function collectSessionInfo(messages) {
  const sessionInfo = {
    messageCount: messages ? messages.length : 0,
    sessionDuration: 0,
    lastActivity: null
  };
  
  if (messages && messages.length > 0) {
    const firstMessage = messages[0];
    const lastMessage = messages[messages.length - 1];
    
    if (firstMessage.timestamp && lastMessage.timestamp) {
      sessionInfo.sessionDuration = new Date(lastMessage.timestamp) - new Date(firstMessage.timestamp);
      sessionInfo.lastActivity = lastMessage.timestamp;
    }
  }
  
  return sessionInfo;
}

/**
 * 生成bug报告模板
 * @param {Object} systemInfo - 系统信息
 * @param {Object} sessionInfo - 会话信息
 * @returns {string} bug报告模板
 */
function generateBugReportTemplate(systemInfo, sessionInfo) {
  return `## Bug Report

### Description
[Please describe the issue you encountered]

### Steps to Reproduce
1. [First step]
2. [Second step]
3. [Third step]

### Expected Behavior
[What you expected to happen]

### Actual Behavior
[What actually happened]

### Screenshots/Logs
[If applicable, add screenshots or error logs]

### System Information
- Platform: ${systemInfo.platform}
- Node.js Version: ${systemInfo.nodeVersion}
- Claude Code Version: ${systemInfo.claudeCodeVersion}
- Environment: ${systemInfo.environment}
- Uptime: ${Math.floor(systemInfo.uptime / 60)} minutes
- Memory Usage: ${Math.floor(systemInfo.memory.rss / 1024 / 1024)} MB

### Session Information
- Message Count: ${sessionInfo.messageCount}
- Session Duration: ${Math.floor(sessionInfo.sessionDuration / 1000 / 60)} minutes
- Last Activity: ${sessionInfo.lastActivity || 'N/A'}

### Additional Context
[Add any other context about the problem here]
`;
}

/**
 * Bug报告表单组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L35112-35115中的EvB组件
 */
function BugReportForm({ messages, onDone }) {
  const [bugReport, setBugReport] = React.useState('');
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [email, setEmail] = React.useState('');
  const [category, setCategory] = React.useState('bug');
  const [priority, setPriority] = React.useState('medium');
  
  // 初始化bug报告模板
  React.useEffect(() => {
    const systemInfo = collectSystemInfo();
    const sessionInfo = collectSessionInfo(messages);
    const template = generateBugReportTemplate(systemInfo, sessionInfo);
    setBugReport(template);
  }, [messages]);
  
  const handleSubmit = React.useCallback(async () => {
    if (!bugReport.trim()) {
      alert('Please fill in the bug report details');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // 准备提交数据
      const submitData = {
        type: 'bug_report',
        category,
        priority,
        email: email.trim(),
        report: bugReport,
        systemInfo: collectSystemInfo(),
        sessionInfo: collectSessionInfo(messages),
        timestamp: new Date().toISOString()
      };
      
      // 模拟提交到反馈系统
      const success = await submitBugReport(submitData);
      
      if (success) {
        onDone('Bug report submitted successfully. Thank you for your feedback!');
      } else {
        onDone('Failed to submit bug report. Please try again later.');
      }
      
    } catch (error) {
      console.error('Error submitting bug report:', error);
      onDone(`Error submitting bug report: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  }, [bugReport, category, priority, email, messages, onDone]);
  
  const handleCancel = React.useCallback(() => {
    onDone('Bug report cancelled');
  }, [onDone]);
  
  const handleKeyPress = React.useCallback((event) => {
    if (event.key === 'Escape') {
      handleCancel();
    } else if (event.ctrlKey && event.key === 'Enter') {
      handleSubmit();
    }
  }, [handleCancel, handleSubmit]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [handleKeyPress]);
  
  return React.createElement('div', {
    style: {
      padding: '20px',
      maxWidth: '800px',
      margin: '0 auto',
      fontFamily: 'monospace'
    }
  }, [
    // 标题
    React.createElement('h1', {
      key: 'title',
      style: { marginBottom: '20px', color: '#ff6b6b' }
    }, 'Submit Bug Report'),
    
    // 表单字段
    React.createElement('div', {
      key: 'form',
      style: { marginBottom: '20px' }
    }, [
      // 邮箱
      React.createElement('div', {
        key: 'email-field',
        style: { marginBottom: '15px' }
      }, [
        React.createElement('label', {
          key: 'email-label',
          style: { display: 'block', marginBottom: '5px', color: '#ffff00' }
        }, 'Email (optional):'),
        React.createElement('input', {
          key: 'email-input',
          type: 'email',
          value: email,
          onChange: (e) => setEmail(e.target.value),
          placeholder: '<EMAIL>',
          style: {
            width: '100%',
            padding: '8px',
            backgroundColor: '#1a1a1a',
            color: '#ffffff',
            border: '1px solid #333',
            borderRadius: '4px'
          }
        })
      ]),
      
      // 分类
      React.createElement('div', {
        key: 'category-field',
        style: { marginBottom: '15px' }
      }, [
        React.createElement('label', {
          key: 'category-label',
          style: { display: 'block', marginBottom: '5px', color: '#ffff00' }
        }, 'Category:'),
        React.createElement('select', {
          key: 'category-select',
          value: category,
          onChange: (e) => setCategory(e.target.value),
          style: {
            width: '200px',
            padding: '8px',
            backgroundColor: '#1a1a1a',
            color: '#ffffff',
            border: '1px solid #333',
            borderRadius: '4px'
          }
        }, [
          React.createElement('option', { key: 'bug', value: 'bug' }, 'Bug Report'),
          React.createElement('option', { key: 'feature', value: 'feature' }, 'Feature Request'),
          React.createElement('option', { key: 'improvement', value: 'improvement' }, 'Improvement'),
          React.createElement('option', { key: 'documentation', value: 'documentation' }, 'Documentation'),
          React.createElement('option', { key: 'other', value: 'other' }, 'Other')
        ])
      ]),
      
      // 优先级
      React.createElement('div', {
        key: 'priority-field',
        style: { marginBottom: '15px' }
      }, [
        React.createElement('label', {
          key: 'priority-label',
          style: { display: 'block', marginBottom: '5px', color: '#ffff00' }
        }, 'Priority:'),
        React.createElement('select', {
          key: 'priority-select',
          value: priority,
          onChange: (e) => setPriority(e.target.value),
          style: {
            width: '200px',
            padding: '8px',
            backgroundColor: '#1a1a1a',
            color: '#ffffff',
            border: '1px solid #333',
            borderRadius: '4px'
          }
        }, [
          React.createElement('option', { key: 'low', value: 'low' }, 'Low'),
          React.createElement('option', { key: 'medium', value: 'medium' }, 'Medium'),
          React.createElement('option', { key: 'high', value: 'high' }, 'High'),
          React.createElement('option', { key: 'critical', value: 'critical' }, 'Critical')
        ])
      ]),
      
      // Bug报告内容
      React.createElement('div', {
        key: 'report-field',
        style: { marginBottom: '15px' }
      }, [
        React.createElement('label', {
          key: 'report-label',
          style: { display: 'block', marginBottom: '5px', color: '#ffff00' }
        }, 'Bug Report:'),
        React.createElement('textarea', {
          key: 'report-textarea',
          value: bugReport,
          onChange: (e) => setBugReport(e.target.value),
          rows: 20,
          style: {
            width: '100%',
            padding: '10px',
            backgroundColor: '#1a1a1a',
            color: '#ffffff',
            border: '1px solid #333',
            borderRadius: '4px',
            fontFamily: 'monospace',
            fontSize: '12px',
            resize: 'vertical'
          }
        })
      ])
    ]),
    
    // 按钮
    React.createElement('div', {
      key: 'buttons',
      style: { 
        display: 'flex', 
        gap: '10px',
        justifyContent: 'flex-end'
      }
    }, [
      React.createElement('button', {
        key: 'cancel',
        onClick: handleCancel,
        disabled: isSubmitting,
        style: {
          padding: '10px 20px',
          backgroundColor: '#666666',
          color: '#ffffff',
          border: 'none',
          borderRadius: '4px',
          cursor: isSubmitting ? 'not-allowed' : 'pointer'
        }
      }, 'Cancel'),
      React.createElement('button', {
        key: 'submit',
        onClick: handleSubmit,
        disabled: isSubmitting,
        style: {
          padding: '10px 20px',
          backgroundColor: isSubmitting ? '#666666' : '#ff6b6b',
          color: '#ffffff',
          border: 'none',
          borderRadius: '4px',
          cursor: isSubmitting ? 'not-allowed' : 'pointer'
        }
      }, isSubmitting ? 'Submitting...' : 'Submit Report')
    ]),
    
    // 帮助信息
    React.createElement('div', {
      key: 'help',
      style: {
        marginTop: '20px',
        padding: '15px',
        backgroundColor: '#0a0a0a',
        border: '1px solid #333',
        borderRadius: '4px',
        color: '#888888',
        fontSize: '12px'
      }
    }, [
      React.createElement('p', { key: 'help1' }, 'Tips for a good bug report:'),
      React.createElement('ul', { key: 'help-list' }, [
        React.createElement('li', { key: 'tip1' }, 'Be specific about what you were trying to do'),
        React.createElement('li', { key: 'tip2' }, 'Include exact error messages if any'),
        React.createElement('li', { key: 'tip3' }, 'Describe the steps to reproduce the issue'),
        React.createElement('li', { key: 'tip4' }, 'Mention if this worked before or is a new issue')
      ]),
      React.createElement('p', { key: 'help2', style: { marginTop: '10px' } }, 
        'Keyboard shortcuts: Ctrl+Enter to submit, Escape to cancel'
      )
    ])
  ]);
}

/**
 * 提交bug报告
 * @param {Object} reportData - 报告数据
 * @returns {Promise<boolean>} 是否成功提交
 */
async function submitBugReport(reportData) {
  try {
    // 这里应该调用实际的反馈API
    // 暂时模拟提交过程
    
    console.log('Submitting bug report:', reportData);
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟成功提交
    return true;
    
  } catch (error) {
    console.error('Failed to submit bug report:', error);
    return false;
  }
}

/**
 * /bug 命令定义
 * @original: 原始文件L35103-35121中的DH8对象
 */
export const bugCommand = {
  type: "local-jsx",
  name: "bug",
  description: "Submit feedback about Claude Code",
  isEnabled: () => !(
    process.env.CLAUDE_CODE_USE_BEDROCK || 
    process.env.CLAUDE_CODE_USE_VERTEX || 
    process.env.DISABLE_BUG_COMMAND || 
    process.env.CLAUDE_CODE_DISABLE_NONESSENTIAL_TRAFFIC
  ),
  isHidden: false,
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @param {string} args - 命令参数
   * @returns {Promise<React.Element>} React组件
   */
  async call(onDone, context, args) {
    const { messages } = context;
    
    return React.createElement(BugReportForm, {
      messages: messages || [],
      onDone
    });
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "bug";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /bug

Submit feedback about Claude Code including bug reports, feature requests, and general feedback.

This command opens an interactive form where you can:
- Describe the issue or feedback
- Specify the category (bug, feature request, improvement, etc.)
- Set priority level
- Provide your email for follow-up (optional)

The form automatically includes:
- System information (platform, versions, memory usage)
- Session information (message count, duration)
- Timestamp and environment details

Tips for effective bug reports:
- Be specific about what you were trying to accomplish
- Include exact error messages if any occurred
- Describe the steps to reproduce the issue
- Mention if this worked before or is a new issue

Keyboard shortcuts:
- Ctrl+Enter: Submit the report
- Escape: Cancel and close the form
    `.trim();
  }
};
