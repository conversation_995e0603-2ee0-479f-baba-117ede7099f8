/**
 * /model 命令实现
 * @original: 原始文件L50375-50405中的DmB对象和L35508-35551中的Mf1组件
 */

import React from 'react';

/**
 * 模型选项常量
 */
export const MODEL_OPTIONS = {
  DEFAULT: null,
  SONNET: 'claude-3-5-sonnet-20241022',
  HAIKU: 'claude-3-5-haiku-20241022',
  OPUS: 'claude-3-opus-20240229'
};

/**
 * 特殊值常量
 */
const NO_PREFERENCE = "__NO_PREFERENCE__";

/**
 * 帮助关键词
 */
const HELP_KEYWORDS = ["help", "-h", "--help"];
const STATUS_KEYWORDS = ["list", "show", "display", "current", "view", "get", "check", "describe", "print", "version", "about", "status", "?"];

/**
 * 获取可用模型选项
 * @returns {Array} 模型选项数组
 * @original: 原始文件L5717-5730中的aw2函数
 */
function getAvailableModels() {
  const models = [
    {
      value: null,
      label: "Default (recommended)",
      description: "Use the recommended model for your account"
    },
    {
      value: MODEL_OPTIONS.SONNET,
      label: "Sonnet",
      description: "Claude 3.5 Sonnet - Best for most tasks"
    },
    {
      value: MODEL_OPTIONS.HAIKU,
      label: "Haiku",
      description: "Claude 3.5 Haiku - Fast and efficient"
    }
  ];
  
  // 只有特定用户可以使用Opus
  const canUseOpus = !process.env.CLAUDE_PRO_USER || process.env.CLAUDE_MAX_USER;
  if (canUseOpus) {
    models.push({
      value: MODEL_OPTIONS.OPUS,
      label: "Opus",
      description: "Claude 3 Opus - Most capable model"
    });
  }
  
  return models;
}

/**
 * 获取模型显示名称
 * @param {string|null} modelName - 模型名称
 * @returns {string} 显示名称
 * @original: 原始文件L5731-5740中的tg函数
 */
function getModelDisplayName(modelName) {
  if (modelName === null) {
    return "Default (recommended)";
  }
  
  const models = getAvailableModels();
  const model = models.find(m => m.value === modelName);
  
  if (model) {
    return model.label;
  }
  
  // 自定义模型
  return `${modelName} (custom)`;
}

/**
 * 验证模型名称
 * @param {string} modelName - 模型名称
 * @returns {boolean} 是否有效
 */
function validateModelName(modelName) {
  if (!modelName || typeof modelName !== 'string') {
    return false;
  }
  
  // 检查是否是已知模型
  const availableModels = getAvailableModels();
  return availableModels.some(model => model.value === modelName) || 
         modelName.startsWith('claude-');
}

/**
 * 模型选择器组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L35508-35551中的Mf1组件
 */
function ModelSelector({ initial, onSelect }) {
  const [selectedValue, setSelectedValue] = React.useState(
    initial === null ? NO_PREFERENCE : initial
  );
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  
  const availableModels = React.useMemo(() => getAvailableModels(), []);
  
  // 映射模型选项，将null值转换为NO_PREFERENCE
  const options = React.useMemo(() => 
    availableModels.map(model => ({
      ...model,
      value: model.value === null ? NO_PREFERENCE : model.value
    }))
  , [availableModels]);
  
  React.useEffect(() => {
    const currentIndex = options.findIndex(option => option.value === selectedValue);
    if (currentIndex >= 0) {
      setSelectedIndex(currentIndex);
    }
  }, [selectedValue, options]);
  
  const handleKeyPress = React.useCallback((event) => {
    switch (event.key) {
      case 'ArrowUp':
        setSelectedIndex(prev => {
          const newIndex = Math.max(0, prev - 1);
          setSelectedValue(options[newIndex]?.value || NO_PREFERENCE);
          return newIndex;
        });
        break;
        
      case 'ArrowDown':
        setSelectedIndex(prev => {
          const newIndex = Math.min(options.length - 1, prev + 1);
          setSelectedValue(options[newIndex]?.value || NO_PREFERENCE);
          return newIndex;
        });
        break;
        
      case 'Enter':
      case ' ':
        const finalValue = selectedValue === NO_PREFERENCE ? null : selectedValue;
        onSelect(finalValue);
        break;
        
      case 'Escape':
        onSelect(initial);
        break;
    }
  }, [selectedIndex, selectedValue, options, onSelect, initial]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  return React.createElement('div', {
    style: {
      padding: '10px',
      border: '2px solid #ffff00',
      borderRadius: '8px',
      backgroundColor: '#000000',
      color: '#ffffff',
      fontFamily: 'monospace'
    }
  }, [
    // 标题和描述
    React.createElement('div', {
      key: 'header',
      style: { marginBottom: '15px' }
    }, [
      React.createElement('h2', {
        key: 'title',
        style: { 
          color: '#ffff00', 
          marginBottom: '5px',
          fontWeight: 'bold'
        }
      }, 'Select Model'),
      React.createElement('p', {
        key: 'description',
        style: { 
          color: '#cccccc', 
          fontSize: '14px',
          margin: 0
        }
      }, 'Switch between Claude models. Applies to this session and future Claude Code sessions.')
    ]),
    
    // 模型选项列表
    React.createElement('div', {
      key: 'options',
      style: { marginBottom: '15px' }
    }, options.map((option, index) => {
      const isSelected = index === selectedIndex;
      
      return React.createElement('div', {
        key: option.value,
        style: {
          padding: '8px 12px',
          backgroundColor: isSelected ? '#333333' : 'transparent',
          border: isSelected ? '1px solid #ffff00' : '1px solid transparent',
          borderRadius: '4px',
          marginBottom: '5px',
          cursor: 'pointer'
        }
      }, [
        React.createElement('div', {
          key: 'label',
          style: {
            fontWeight: 'bold',
            color: isSelected ? '#00ff00' : '#ffffff',
            marginBottom: '2px'
          }
        }, `${isSelected ? '► ' : '  '}${option.label}`),
        
        React.createElement('div', {
          key: 'description',
          style: {
            fontSize: '12px',
            color: '#888888',
            marginLeft: isSelected ? '12px' : '10px'
          }
        }, option.description)
      ]);
    })),
    
    // 帮助信息
    React.createElement('div', {
      key: 'help',
      style: {
        borderTop: '1px solid #333',
        paddingTop: '10px',
        color: '#888888',
        fontSize: '12px'
      }
    }, '↑/↓ to select • Enter/Space to confirm • Esc to cancel')
  ]);
}

/**
 * 模型状态显示组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L50366-50374中的kN8组件
 */
function ModelStatus({ onDone, currentModel }) {
  React.useEffect(() => {
    const displayName = getModelDisplayName(currentModel);
    const timer = setTimeout(() => {
      onDone(`Current model: ${displayName}`);
    }, 0);
    
    return () => clearTimeout(timer);
  }, [onDone, currentModel]);
  
  return null;
}

/**
 * 模型设置组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L50352-50365中的yN8组件
 */
function ModelSetter({ args, onDone, onModelChange }) {
  React.useEffect(() => {
    const modelName = args === "default" ? null : args;
    
    // 检查Opus限制
    if (process.env.CLAUDE_PRO_USER && !process.env.CLAUDE_MAX_USER && 
        modelName && modelName.toLowerCase().includes("opus")) {
      onDone("Invalid model. Claude Pro users are not currently able to use Opus in Claude Code. The current model is now Sonnet.");
      return;
    }
    
    // 验证模型名称
    if (modelName && !validateModelName(modelName)) {
      onDone(`Invalid model name: ${modelName}. Use /model to see available models.`);
      return;
    }
    
    const timer = setTimeout(() => {
      onModelChange(modelName);
      const displayName = getModelDisplayName(modelName);
      onDone(`Set model to ${displayName}`);
    }, 0);
    
    return () => clearTimeout(timer);
  }, [args, onDone, onModelChange]);
  
  return null;
}

/**
 * /model 命令定义
 * @original: 原始文件L50375-50405中的DmB对象
 */
export const modelCommand = {
  type: "local-jsx",
  name: "model",
  description: "Set the AI model for Claude Code",
  isEnabled: () => true,
  isHidden: false,
  argumentHint: "[model]",
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @param {string} args - 命令参数
   * @returns {Promise<React.Element>} React组件
   */
  async call(onDone, context, args) {
    const trimmedArgs = args?.trim() || "";
    
    // 处理状态查询
    if (STATUS_KEYWORDS.includes(trimmedArgs)) {
      return React.createElement(ModelStatus, {
        onDone,
        currentModel: context.currentModel || null
      });
    }
    
    // 处理帮助请求
    if (HELP_KEYWORDS.includes(trimmedArgs)) {
      const timer = setTimeout(() => {
        onDone("Run /model to open the model selection menu, or /model [modelName] to set the model.");
      }, 0);
      return null;
    }
    
    // 处理直接设置模型
    if (trimmedArgs) {
      return React.createElement(ModelSetter, {
        args: trimmedArgs,
        onDone,
        onModelChange: (modelName) => {
          // 更新上下文中的模型
          if (context.setCurrentModel) {
            context.setCurrentModel(modelName);
          }
          
          // 保存到配置
          try {
            const config = JSON.parse(localStorage.getItem('claudeCodeConfig') || '{}');
            config.model = modelName;
            localStorage.setItem('claudeCodeConfig', JSON.stringify(config));
          } catch (error) {
            console.warn('Failed to save model to config:', error);
          }
        }
      });
    }
    
    // 显示模型选择界面
    return React.createElement(ModelSelector, {
      initial: context.currentModel || null,
      onSelect: (modelName) => {
        // 更新上下文中的模型
        if (context.setCurrentModel) {
          context.setCurrentModel(modelName);
        }
        
        // 保存到配置
        try {
          const config = JSON.parse(localStorage.getItem('claudeCodeConfig') || '{}');
          config.model = modelName;
          localStorage.setItem('claudeCodeConfig', JSON.stringify(config));
        } catch (error) {
          console.warn('Failed to save model to config:', error);
        }
        
        const displayName = getModelDisplayName(modelName);
        onDone(`Set model to ${displayName}`);
      }
    });
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "model";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /model [model-name]

Set the AI model for Claude Code.

Commands:
  /model                    # Open model selection menu
  /model [model-name]       # Set specific model
  /model current            # Show current model
  /model list               # Show available models

Available Models:
  default                   # Use recommended model (default)
  claude-3-5-sonnet-20241022  # Sonnet - Best for most tasks
  claude-3-5-haiku-20241022   # Haiku - Fast and efficient
  claude-3-opus-20240229      # Opus - Most capable (Max users only)

Examples:
  /model                    # Open interactive selection
  /model sonnet             # Set to Sonnet
  /model default            # Reset to default
  /model current            # Show current model

The model selection applies to the current session and is saved for future sessions. Custom model names can be specified with the --model command line option.

Note: Claude Pro users cannot use Opus models. Upgrade to Claude Max for access to all models.
    `.trim();
  }
};
