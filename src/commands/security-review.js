/**
 * /security-review 命令实现
 * @original: 原始文件L43201-43240中的SgB对象和L43010-43200中的Bq8模板
 */

/**
 * 安全审查模板内容
 * @original: 原始文件L43010-43200中的Bq8常量
 */
const SECURITY_REVIEW_TEMPLATE = `---
allowed-tools: <PERSON><PERSON>(git diff:*), <PERSON><PERSON>(git status:*), <PERSON><PERSON>(git log:*), <PERSON><PERSON>(git show:*), <PERSON><PERSON>(git remote show:*), Read, G<PERSON>b, Grep, LS, Task
description: Complete a security review of the pending changes on the current branch
---

You are a senior security engineer conducting a focused security review of the changes on this branch.

GIT STATUS:

\`\`\`
!\`git status\`
\`\`\`

FILES MODIFIED:

\`\`\`
!\`git diff --name-only origin/HEAD...\`
\`\`\`

COMMITS:

\`\`\`
!\`git log --no-decorate origin/HEAD...\`
\`\`\`

DIFF CONTENT:

\`\`\`
!\`git diff --merge-base origin/HEAD\`
\`\`\`

Review the complete diff above. This contains all code changes in the PR.


OBJECTIVE:
Perform a security-focused code review to identify HIGH-CONFIDENCE security vulnerabilities that could have real exploitation potential. This is not a general code review - focus ONLY on security implications newly added by this PR. Do not comment on existing security concerns.

CRITICAL INSTRUCTIONS:
1. MINIMIZE FALSE POSITIVES: Only flag issues where you're >80% confident of actual exploitability
2. AVOID NOISE: Skip theoretical issues, style concerns, or low-impact findings
3. FOCUS ON IMPACT: Prioritize vulnerabilities that could lead to unauthorized access, data breaches, or system compromise
4. EXCLUSIONS: Do NOT report the following issue types:
   - Denial of Service (DOS) vulnerabilities, even if they allow service disruption
   - Secrets or sensitive data stored on disk (these are handled by other processes)
   - Rate limiting or resource exhaustion issues

SECURITY CATEGORIES TO EXAMINE:

**Input Validation Vulnerabilities:**
- SQL injection via unsanitized user input
- Command injection in system calls or subprocesses
- Path traversal attacks through file operations
- Cross-site scripting (XSS) in web applications
- LDAP injection, XML injection, or other injection attacks
- Deserialization of untrusted data

**Authentication & Authorization Flaws:**
- Broken authentication mechanisms
- Privilege escalation vulnerabilities
- Insecure direct object references
- Missing authorization checks
- Session management issues
- JWT token vulnerabilities

**Cryptographic Issues:**
- Use of weak or broken cryptographic algorithms
- Hardcoded cryptographic keys or secrets
- Improper key management
- Insufficient entropy in random number generation
- Timing attacks on cryptographic operations

**Network Security:**
- Server-side request forgery (SSRF)
- Insecure network protocols or configurations
- Missing TLS/SSL validation
- Open redirects that could be exploited

**Data Exposure:**
- Information disclosure through error messages
- Sensitive data in logs or debug output
- Unprotected API endpoints
- Missing data sanitization before output

**Business Logic Flaws:**
- Race conditions with security implications
- Logic flaws that bypass security controls
- Inconsistent validation across different code paths

FALSE POSITIVE FILTERING:

For each potential vulnerability you identify, apply these filters:

1. **Exploitability Check**: Can this actually be exploited by an attacker? Consider:
   - Is the vulnerable code path reachable?
   - Are there existing mitigations in place?
   - What level of access would an attacker need?

2. **Impact Assessment**: What's the real-world impact?
   - Could this lead to data breach, system compromise, or unauthorized access?
   - Is this a theoretical issue or a practical threat?

3. **Context Analysis**: Consider the application context:
   - Is this internal-only code with limited exposure?
   - Are there compensating controls elsewhere?
   - Is this a development/testing environment vs production?

4. **Confidence Level**: Rate your confidence (1-10) that this is a real, exploitable vulnerability
   - Only report findings with confidence >= 8

ANALYSIS METHODOLOGY:

START ANALYSIS:

Begin your analysis now. Do this in 3 steps:

1. Use a sub-task to identify vulnerabilities. Use the repository exploration tools to understand the codebase context, then analyze the PR changes for security implications. In the prompt for this sub-task, include all of the above.
2. Then for each vulnerability identified by the above sub-task, create a new sub-task to filter out false-positives. Launch these sub-tasks as parallel sub-tasks. In the prompt for these sub-tasks, include everything in the "FALSE POSITIVE FILTERING" instructions.
3. Filter out any vulnerabilities where the sub-task reported a confidence less than 8.

Your final reply must contain the markdown report and nothing else.`;

/**
 * 解析允许的工具列表
 * @param {string} toolsString - 工具字符串
 * @returns {Array} 解析后的工具数组
 */
function parseAllowedTools(toolsString) {
  if (!toolsString) return [];
  
  return toolsString.split(',').map(tool => tool.trim()).filter(Boolean);
}

/**
 * 解析模板前置信息
 * @param {string} template - 模板内容
 * @returns {Object} 解析结果
 */
function parseTemplate(template) {
  const frontmatterMatch = template.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);
  
  if (!frontmatterMatch) {
    return {
      frontmatter: {},
      content: template
    };
  }
  
  const frontmatterText = frontmatterMatch[1];
  const content = frontmatterMatch[2];
  
  const frontmatter = {};
  frontmatterText.split('\n').forEach(line => {
    const colonIndex = line.indexOf(':');
    if (colonIndex > 0) {
      const key = line.substring(0, colonIndex).trim();
      const value = line.substring(colonIndex + 1).trim();
      frontmatter[key] = value;
    }
  });
  
  return { frontmatter, content };
}

/**
 * 处理模板内容中的动态指令
 * @param {string} content - 模板内容
 * @param {Object} context - 执行上下文
 * @param {string} commandName - 命令名称
 * @returns {Promise<string>} 处理后的内容
 */
async function processTemplate(content, context, commandName) {
  // 这里应该处理模板中的动态指令，如 !`git status`
  // 为了简化，我们直接返回原始内容
  // 在实际实现中，这里会执行git命令并替换结果
  
  let processedContent = content;
  
  // 替换动态命令占位符
  processedContent = processedContent.replace(/!\`([^`]+)\`/g, (match, command) => {
    return `Execute: ${command}`;
  });
  
  return processedContent;
}

/**
 * 验证Git仓库状态
 * @returns {Promise<Object>} 验证结果
 */
async function validateGitRepository() {
  try {
    // 检查是否在Git仓库中
    const { execSync } = require('child_process');
    
    try {
      execSync('git rev-parse --is-inside-work-tree', { stdio: 'ignore' });
    } catch {
      return {
        valid: false,
        error: 'Not in a Git repository',
        message: 'Security review requires being in a Git repository.'
      };
    }
    
    // 检查是否有远程分支
    try {
      execSync('git remote show origin', { stdio: 'ignore' });
    } catch {
      return {
        valid: false,
        error: 'No remote repository',
        message: 'Security review requires a remote repository (origin) to compare changes.'
      };
    }
    
    // 检查是否有待审查的更改
    try {
      const status = execSync('git status --porcelain', { encoding: 'utf8' });
      const diff = execSync('git diff --name-only origin/HEAD...', { encoding: 'utf8' });
      
      if (!status.trim() && !diff.trim()) {
        return {
          valid: false,
          error: 'No changes to review',
          message: 'No pending changes found on the current branch.'
        };
      }
    } catch {
      // 如果无法获取diff，可能是因为没有origin/HEAD，这是可以接受的
    }
    
    return { valid: true };
    
  } catch (error) {
    return {
      valid: false,
      error: 'Git validation failed',
      message: `Failed to validate Git repository: ${error.message}`
    };
  }
}

/**
 * /security-review 命令定义
 * @original: 原始文件L43201-43240中的SgB对象
 */
export const securityReviewCommand = {
  type: "prompt",
  name: "security-review",
  description: "Complete a security review of the pending changes on the current branch",
  isEnabled: () => true,
  isHidden: false,
  progressMessage: "analyzing code changes for security risks",
  
  /**
   * 获取命令提示
   * @param {string} args - 命令参数
   * @param {Object} context - 执行上下文
   * @returns {Promise<Array>} 提示消息数组
   */
  async getPromptForCommand(args, context) {
    try {
      // 验证Git仓库状态
      const gitValidation = await validateGitRepository();
      if (!gitValidation.valid) {
        return [{
          type: "text",
          text: `❌ ${gitValidation.error}: ${gitValidation.message}\n\nTo perform a security review:\n• Navigate to a Git repository\n• Ensure you have a remote origin configured\n• Make sure there are changes to review on the current branch\n\nYou can check your Git status with: git status`
        }];
      }
      
      // 解析模板
      const template = parseTemplate(SECURITY_REVIEW_TEMPLATE);
      const allowedTools = parseAllowedTools(template.frontmatter["allowed-tools"]);
      
      // 获取工具权限上下文
      const toolPermissionContext = context.getToolPermissionContext ? context.getToolPermissionContext() : {};
      
      // 创建增强的权限上下文，允许安全审查所需的工具
      const enhancedContext = {
        ...context,
        getToolPermissionContext() {
          return {
            ...toolPermissionContext,
            alwaysAllowRules: {
              ...toolPermissionContext.alwaysAllowRules,
              command: allowedTools
            }
          };
        }
      };
      
      // 处理模板内容
      const processedContent = await processTemplate(template.content, enhancedContext, "security-review");
      
      return [{
        type: "text",
        text: processedContent
      }];
      
    } catch (error) {
      return [{
        type: "text",
        text: `Error setting up security review: ${error.message}\n\nPlease ensure you have:\n• Git installed and accessible\n• Proper repository setup with remote origin\n• Changes to review on the current branch`
      }];
    }
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "security-review";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /security-review

Complete a comprehensive security review of the pending changes on the current branch.

Prerequisites:
- Must be in a Git repository with remote origin
- Must have pending changes to review (committed or uncommitted)
- Git must be installed and accessible

Security Review Process:
1. **Change Analysis**: Examines all modified files and commits
2. **Vulnerability Detection**: Identifies potential security issues
3. **False Positive Filtering**: Applies rigorous filtering to minimize noise
4. **Impact Assessment**: Evaluates real-world exploitability

Security Categories Examined:
• **Input Validation**: SQL injection, command injection, XSS, path traversal
• **Authentication & Authorization**: Broken auth, privilege escalation, session issues
• **Cryptographic Issues**: Weak algorithms, key management, entropy problems
• **Network Security**: SSRF, insecure protocols, TLS validation
• **Data Exposure**: Information disclosure, sensitive data leaks
• **Business Logic**: Race conditions, logic flaws, validation bypasses

Review Methodology:
- Uses sub-tasks for systematic vulnerability identification
- Applies parallel false-positive filtering
- Requires 80%+ confidence for vulnerability reporting
- Focuses on high-impact, exploitable issues only

Output Format:
- Structured markdown report
- Vulnerability details with confidence ratings
- Exploitability analysis and impact assessment
- Actionable remediation recommendations

Exclusions:
- Denial of Service vulnerabilities
- Secrets stored on disk (handled separately)
- Rate limiting issues
- Theoretical or low-impact findings

The security review is designed to identify real, exploitable vulnerabilities while minimizing false positives and noise.
    `.trim();
  }
};
