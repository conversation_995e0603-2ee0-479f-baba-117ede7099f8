/**
 * /status 命令实现
 * @original: 原始文件L42688-42705中的r$8对象和状态显示逻辑
 */

import React from 'react';
import { formatDuration, formatBytes } from '../utils/string-utils.js';

/**
 * 获取系统状态信息
 * @param {Object} context - 执行上下文
 * @returns {Object} 系统状态
 */
function getSystemStatus(context) {
  const status = {
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    platform: process.platform,
    nodeVersion: process.version,
    pid: process.pid
  };
  
  // 添加CPU使用率（如果可用）
  if (process.cpuUsage) {
    status.cpuUsage = process.cpuUsage();
  }
  
  return status;
}

/**
 * 获取应用程序状态
 * @param {Object} context - 执行上下文
 * @returns {Object} 应用程序状态
 */
function getApplicationStatus(context) {
  const status = {
    version: process.env.CLAUDE_CODE_VERSION || 'unknown',
    environment: process.env.NODE_ENV || 'development',
    entrypoint: process.env.CLAUDE_CODE_ENTRYPOINT || 'unknown'
  };
  
  // 添加会话信息
  if (context.sessionId) {
    status.sessionId = context.sessionId;
  }
  
  if (context.messages) {
    status.messageCount = context.messages.length;
  }
  
  return status;
}

/**
 * 获取API连接状态
 * @param {Object} context - 执行上下文
 * @returns {Object} API状态
 */
function getAPIStatus(context) {
  const status = {
    connected: false,
    lastCheck: new Date().toISOString(),
    endpoint: 'unknown',
    model: 'unknown'
  };
  
  // 检查API连接状态
  if (context.apiClient) {
    status.connected = context.apiClient.isConnected || false;
    status.endpoint = context.apiClient.endpoint || 'unknown';
    status.model = context.apiClient.currentModel || 'unknown';
    
    if (context.apiClient.lastRequestTime) {
      status.lastRequest = context.apiClient.lastRequestTime;
    }
    
    if (context.apiClient.requestCount) {
      status.requestCount = context.apiClient.requestCount;
    }
  }
  
  return status;
}

/**
 * 获取工具状态
 * @param {Object} context - 执行上下文
 * @returns {Object} 工具状态
 */
function getToolStatus(context) {
  const status = {
    totalTools: 0,
    enabledTools: 0,
    activeExecutions: 0,
    totalExecutions: 0
  };
  
  // 从工具引擎获取状态
  if (context.toolEngine) {
    const toolStats = context.toolEngine.getExecutionStats();
    status.totalTools = toolStats.registeredToolsCount || 0;
    status.enabledTools = toolStats.enabledToolsCount || 0;
    status.activeExecutions = toolStats.activeExecutionsCount || 0;
    status.totalExecutions = toolStats.total || 0;
    status.successfulExecutions = toolStats.successful || 0;
    status.failedExecutions = toolStats.failed || 0;
  }
  
  return status;
}

/**
 * 获取MCP状态
 * @param {Object} context - 执行上下文
 * @returns {Object} MCP状态
 */
function getMCPStatus(context) {
  const status = {
    totalServers: 0,
    connectedServers: 0,
    failedServers: 0,
    servers: []
  };
  
  // 从MCP服务获取状态
  if (context.mcpService) {
    const mcpStats = context.mcpService.getConnectionStats();
    status.totalServers = mcpStats.total || 0;
    status.connectedServers = mcpStats.connected || 0;
    status.failedServers = mcpStats.failed || 0;
    status.servers = mcpStats.servers || [];
  }
  
  return status;
}

/**
 * 获取IDE集成状态
 * @param {Object} context - 执行上下文
 * @returns {Object} IDE状态
 */
function getIDEStatus(context) {
  const status = {
    connected: false,
    type: 'none',
    version: 'unknown'
  };
  
  // 从IDE安装状态获取信息
  if (context.ideInstallationStatus) {
    status.connected = context.ideInstallationStatus.connected || false;
    status.type = context.ideInstallationStatus.type || 'none';
    status.version = context.ideInstallationStatus.version || 'unknown';
  }
  
  return status;
}

/**
 * 状态显示组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L42695-42699中的s$8组件
 */
function StatusDisplay({ onClose, ideInstallationStatus, context }) {
  const [refreshCount, setRefreshCount] = React.useState(0);
  
  // 获取所有状态信息
  const systemStatus = React.useMemo(() => getSystemStatus(context), [context, refreshCount]);
  const appStatus = React.useMemo(() => getApplicationStatus(context), [context, refreshCount]);
  const apiStatus = React.useMemo(() => getAPIStatus(context), [context, refreshCount]);
  const toolStatus = React.useMemo(() => getToolStatus(context), [context, refreshCount]);
  const mcpStatus = React.useMemo(() => getMCPStatus(context), [context, refreshCount]);
  const ideStatus = React.useMemo(() => getIDEStatus({ ideInstallationStatus }), [ideInstallationStatus, refreshCount]);
  
  const handleRefresh = React.useCallback(() => {
    setRefreshCount(count => count + 1);
  }, []);
  
  const handleKeyPress = React.useCallback((event) => {
    if (event.key === 'Escape') {
      onClose();
    } else if (event.key === 'r' || event.key === 'R') {
      handleRefresh();
    }
  }, [onClose, handleRefresh]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [handleKeyPress]);
  
  const StatusSection = ({ title, data, color = '#00ff00' }) => 
    React.createElement('div', {
      style: { marginBottom: '20px' }
    }, [
      React.createElement('h3', {
        key: 'title',
        style: { color, marginBottom: '10px', borderBottom: '1px solid #333' }
      }, title),
      React.createElement('div', {
        key: 'content',
        style: { marginLeft: '10px', fontFamily: 'monospace' }
      }, Object.entries(data).map(([key, value]) => 
        React.createElement('div', {
          key,
          style: { marginBottom: '5px' }
        }, [
          React.createElement('span', {
            key: 'key',
            style: { color: '#ffff00', minWidth: '150px', display: 'inline-block' }
          }, `${key}:`),
          React.createElement('span', {
            key: 'value',
            style: { color: '#ffffff' }
          }, formatStatusValue(key, value))
        ])
      ))
    ]);
  
  return React.createElement('div', {
    style: {
      padding: '20px',
      maxWidth: '800px',
      margin: '0 auto',
      fontFamily: 'monospace',
      backgroundColor: '#000000',
      color: '#ffffff'
    }
  }, [
    // 标题
    React.createElement('h1', {
      key: 'title',
      style: { marginBottom: '20px', color: '#00ff00', textAlign: 'center' }
    }, 'Claude Code Status'),
    
    // 刷新按钮
    React.createElement('div', {
      key: 'controls',
      style: { marginBottom: '20px', textAlign: 'center' }
    }, [
      React.createElement('button', {
        key: 'refresh',
        onClick: handleRefresh,
        style: {
          padding: '8px 16px',
          backgroundColor: '#1a1a1a',
          color: '#ffffff',
          border: '1px solid #333',
          cursor: 'pointer',
          marginRight: '10px'
        }
      }, 'Refresh (R)'),
      React.createElement('span', {
        key: 'timestamp',
        style: { color: '#888888', fontSize: '12px' }
      }, `Last updated: ${new Date().toLocaleTimeString()}`)
    ]),
    
    // 系统状态
    React.createElement(StatusSection, {
      key: 'system',
      title: 'System Information',
      data: {
        'Platform': systemStatus.platform,
        'Node.js Version': systemStatus.nodeVersion,
        'Process ID': systemStatus.pid,
        'Uptime': formatDuration(systemStatus.uptime * 1000),
        'Memory Usage': formatBytes(systemStatus.memory.rss),
        'Heap Used': formatBytes(systemStatus.memory.heapUsed),
        'Heap Total': formatBytes(systemStatus.memory.heapTotal)
      },
      color: '#00ff00'
    }),
    
    // 应用程序状态
    React.createElement(StatusSection, {
      key: 'application',
      title: 'Application Status',
      data: {
        'Version': appStatus.version,
        'Environment': appStatus.environment,
        'Entry Point': appStatus.entrypoint,
        'Session ID': appStatus.sessionId || 'N/A',
        'Message Count': appStatus.messageCount || 0
      },
      color: '#ffff00'
    }),
    
    // API状态
    React.createElement(StatusSection, {
      key: 'api',
      title: 'API Connection',
      data: {
        'Status': apiStatus.connected ? '✅ Connected' : '❌ Disconnected',
        'Endpoint': apiStatus.endpoint,
        'Model': apiStatus.model,
        'Last Request': apiStatus.lastRequest ? new Date(apiStatus.lastRequest).toLocaleString() : 'N/A',
        'Request Count': apiStatus.requestCount || 0
      },
      color: '#00ffff'
    }),
    
    // 工具状态
    React.createElement(StatusSection, {
      key: 'tools',
      title: 'Tool Engine',
      data: {
        'Total Tools': toolStatus.totalTools,
        'Enabled Tools': toolStatus.enabledTools,
        'Active Executions': toolStatus.activeExecutions,
        'Total Executions': toolStatus.totalExecutions,
        'Successful': toolStatus.successfulExecutions || 0,
        'Failed': toolStatus.failedExecutions || 0
      },
      color: '#ff00ff'
    }),
    
    // MCP状态
    React.createElement(StatusSection, {
      key: 'mcp',
      title: 'MCP Servers',
      data: {
        'Total Servers': mcpStatus.totalServers,
        'Connected': mcpStatus.connectedServers,
        'Failed': mcpStatus.failedServers,
        'Server List': mcpStatus.servers.length > 0 ? mcpStatus.servers.join(', ') : 'None'
      },
      color: '#ff8800'
    }),
    
    // IDE状态
    React.createElement(StatusSection, {
      key: 'ide',
      title: 'IDE Integration',
      data: {
        'Status': ideStatus.connected ? '✅ Connected' : '❌ Not Connected',
        'Type': ideStatus.type,
        'Version': ideStatus.version
      },
      color: '#8800ff'
    }),
    
    // 底部帮助
    React.createElement('div', {
      key: 'footer',
      style: {
        borderTop: '1px solid #333',
        paddingTop: '15px',
        textAlign: 'center',
        color: '#888888',
        fontSize: '14px'
      }
    }, 'Press R to refresh • Press Escape to close')
  ]);
}

/**
 * 格式化状态值
 * @param {string} key - 键名
 * @param {*} value - 值
 * @returns {string} 格式化后的值
 */
function formatStatusValue(key, value) {
  if (value === null || value === undefined) {
    return 'N/A';
  }
  
  if (typeof value === 'boolean') {
    return value ? '✅ Yes' : '❌ No';
  }
  
  if (typeof value === 'number') {
    if (key.toLowerCase().includes('memory') || key.toLowerCase().includes('bytes')) {
      return formatBytes(value);
    }
    if (key.toLowerCase().includes('time') || key.toLowerCase().includes('duration')) {
      return formatDuration(value);
    }
    return value.toLocaleString();
  }
  
  return String(value);
}

/**
 * /status 命令定义
 * @original: 原始文件L42688-42705中的r$8对象
 */
export const statusCommand = {
  type: "local-jsx",
  name: "status",
  description: "Show Claude Code status including version, model, account, API connectivity, and tool statuses",
  isEnabled: () => true,
  isHidden: false,
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @param {string} args - 命令参数
   * @returns {Promise<React.Element>} React组件
   */
  async call(onDone, context, args) {
    return React.createElement(StatusDisplay, {
      onClose: onDone,
      ideInstallationStatus: context.options?.ideInstallationStatus,
      context: context
    });
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "status";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /status

Show comprehensive Claude Code status information.

This command displays:
- System information (platform, Node.js version, memory usage)
- Application status (version, environment, session info)
- API connection status and statistics
- Tool engine status and execution statistics
- MCP server connection status
- IDE integration status

Interactive controls:
- Press R to refresh all status information
- Press Escape to close the status display

The status display updates in real-time and provides detailed insights into the current state of your Claude Code installation.
    `.trim();
  }
};
