/**
 * /export 命令实现
 * @original: 原始文件L50289-50325中的TN8对象和L50143-50288中的BmB组件
 */

import React from 'react';
import { clipboardManager } from '../core/clipboard-manager.js';

/**
 * 导出格式类型
 */
export const EXPORT_FORMATS = {
  TXT: 'txt',
  MARKDOWN: 'md',
  JSON: 'json',
  HTML: 'html'
};

/**
 * 导出目标类型
 */
export const EXPORT_TARGETS = {
  FILE: 'file',
  CLIPBOARD: 'clipboard'
};

/**
 * 生成时间戳字符串
 * @param {Date} date - 日期对象
 * @returns {string} 格式化的时间戳
 * @original: 原始文件L50245-50253中的LN8函数
 */
function generateTimestamp(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day}-${hours}${minutes}${seconds}`;
}

/**
 * 从消息中提取第一个用户输入作为标题
 * @param {Array} messages - 消息数组
 * @returns {string} 提取的标题
 * @original: 原始文件L50254-50266中的MN8函数
 */
function extractConversationTitle(messages) {
  const firstUserMessage = messages.find(msg => msg.type === 'user');
  if (!firstUserMessage || firstUserMessage.type !== 'user') {
    return '';
  }
  
  const content = firstUserMessage.message?.content;
  let title = '';
  
  if (typeof content === 'string') {
    title = content.trim();
  } else if (Array.isArray(content)) {
    const textContent = content.find(item => item.type === 'text');
    if (textContent && 'text' in textContent) {
      title = textContent.text.trim();
    }
  }
  
  // 只取第一行，限制长度
  title = title.split('\n')[0] || '';
  if (title.length > 50) {
    title = title.substring(0, 50) + '...';
  }
  
  return title;
}

/**
 * 将标题转换为文件名安全的格式
 * @param {string} title - 原始标题
 * @returns {string} 文件名安全的字符串
 * @original: 原始文件L50267-50269中的RN8函数
 */
function sanitizeFilename(title) {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
}

/**
 * 将消息转换为文本格式
 * @param {Array} messages - 消息数组
 * @returns {string} 文本格式的对话内容
 */
function convertMessagesToText(messages) {
  let text = '';
  
  messages.forEach((message, index) => {
    if (index > 0) text += '\n\n';
    
    // 添加角色标识
    const role = message.type === 'user' ? 'User' : 'Assistant';
    text += `=== ${role} ===\n`;
    
    // 处理消息内容
    const content = message.message?.content;
    if (typeof content === 'string') {
      text += content;
    } else if (Array.isArray(content)) {
      content.forEach((item, itemIndex) => {
        if (itemIndex > 0) text += '\n';
        
        switch (item.type) {
          case 'text':
            text += item.text;
            break;
          case 'tool_use':
            text += `[Tool Use: ${item.name}]\n${JSON.stringify(item.input, null, 2)}`;
            break;
          case 'tool_result':
            text += `[Tool Result]\n${item.content}`;
            break;
          case 'image':
            text += `[Image: ${item.source?.media_type || 'unknown'}]`;
            break;
          default:
            text += `[${item.type}]`;
        }
      });
    }
  });
  
  return text;
}

/**
 * 将消息转换为Markdown格式
 * @param {Array} messages - 消息数组
 * @returns {string} Markdown格式的对话内容
 */
function convertMessagesToMarkdown(messages) {
  let markdown = '# Claude Code Conversation\n\n';
  markdown += `*Exported on ${new Date().toLocaleString()}*\n\n`;
  
  messages.forEach((message, index) => {
    const role = message.type === 'user' ? 'User' : 'Assistant';
    markdown += `## ${role}\n\n`;
    
    const content = message.message?.content;
    if (typeof content === 'string') {
      markdown += content + '\n\n';
    } else if (Array.isArray(content)) {
      content.forEach(item => {
        switch (item.type) {
          case 'text':
            markdown += item.text + '\n\n';
            break;
          case 'tool_use':
            markdown += `### Tool Use: ${item.name}\n\n`;
            markdown += '```json\n' + JSON.stringify(item.input, null, 2) + '\n```\n\n';
            break;
          case 'tool_result':
            markdown += '### Tool Result\n\n';
            markdown += '```\n' + item.content + '\n```\n\n';
            break;
          case 'image':
            markdown += `![Image](${item.source?.media_type || 'image'})\n\n`;
            break;
          default:
            markdown += `*[${item.type}]*\n\n`;
        }
      });
    }
  });
  
  return markdown;
}

/**
 * 将消息转换为JSON格式
 * @param {Array} messages - 消息数组
 * @returns {string} JSON格式的对话内容
 */
function convertMessagesToJSON(messages) {
  const exportData = {
    exportedAt: new Date().toISOString(),
    version: '1.0',
    messages: messages.map(msg => ({
      type: msg.type,
      timestamp: msg.timestamp || new Date().toISOString(),
      content: msg.message?.content
    }))
  };
  
  return JSON.stringify(exportData, null, 2);
}

/**
 * 根据格式转换消息内容
 * @param {Array} messages - 消息数组
 * @param {string} format - 导出格式
 * @returns {string} 转换后的内容
 */
function convertMessages(messages, format) {
  switch (format) {
    case EXPORT_FORMATS.MARKDOWN:
      return convertMessagesToMarkdown(messages);
    case EXPORT_FORMATS.JSON:
      return convertMessagesToJSON(messages);
    case EXPORT_FORMATS.TXT:
    default:
      return convertMessagesToText(messages);
  }
}

/**
 * 导出对话组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L50143-50244中的BmB组件
 */
function ExportDialog({ content, defaultFilename, onDone }) {
  const [filename, setFilename] = React.useState(defaultFilename);
  const [cursorPosition, setCursorPosition] = React.useState(defaultFilename.length);
  const [showSaveDialog, setShowSaveDialog] = React.useState(false);
  const [isProcessing, setIsProcessing] = React.useState(false);
  
  // 处理文件名输入
  const handleFilenameChange = React.useCallback((newFilename) => {
    setFilename(newFilename);
    setCursorPosition(newFilename.length);
  }, []);
  
  // 处理保存到文件
  const handleSaveToFile = React.useCallback(async () => {
    setIsProcessing(true);
    
    try {
      // 在浏览器环境中触发下载
      if (typeof window !== 'undefined' && window.document) {
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        onDone({
          success: true,
          message: `Conversation exported to ${filename}`
        });
      } else {
        // 在Node.js环境中，写入文件
        const fs = require('fs');
        const path = require('path');
        
        const fullPath = path.resolve(filename);
        fs.writeFileSync(fullPath, content, 'utf8');
        
        onDone({
          success: true,
          message: `Conversation exported to ${fullPath}`
        });
      }
    } catch (error) {
      onDone({
        success: false,
        message: `Failed to save file: ${error.message}`
      });
    } finally {
      setIsProcessing(false);
    }
  }, [content, filename, onDone]);
  
  // 处理复制到剪贴板
  const handleCopyToClipboard = React.useCallback(async () => {
    setIsProcessing(true);
    
    try {
      const success = clipboardManager.copyToClipboard(content);
      
      if (success) {
        onDone({
          success: true,
          message: 'Conversation copied to clipboard'
        });
      } else {
        onDone({
          success: false,
          message: 'Failed to copy to clipboard. Make sure clipboard tools are available.'
        });
      }
    } catch (error) {
      onDone({
        success: false,
        message: `Failed to copy to clipboard: ${error.message}`
      });
    } finally {
      setIsProcessing(false);
    }
  }, [content, onDone]);
  
  // 键盘事件处理
  const handleKeyPress = React.useCallback((event) => {
    if (isProcessing) return;
    
    if (event.key === 'Escape') {
      if (showSaveDialog) {
        setShowSaveDialog(false);
      } else {
        onDone({
          success: false,
          message: 'Export cancelled'
        });
      }
    } else if (event.key === 'Enter') {
      if (showSaveDialog) {
        handleSaveToFile();
      } else {
        setShowSaveDialog(true);
      }
    } else if (event.key === 'c' || event.key === 'C') {
      if (!showSaveDialog) {
        handleCopyToClipboard();
      }
    }
  }, [isProcessing, showSaveDialog, onDone, handleSaveToFile, handleCopyToClipboard]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  if (isProcessing) {
    return React.createElement('div', {
      style: {
        padding: '20px',
        textAlign: 'center',
        color: '#ffff00'
      }
    }, '🔄 Processing export...');
  }
  
  return React.createElement('div', {
    style: {
      padding: '15px',
      border: '2px solid #00ff00',
      borderRadius: '8px',
      backgroundColor: '#000000',
      color: '#ffffff',
      fontFamily: 'monospace'
    }
  }, [
    // 标题
    React.createElement('h2', {
      key: 'title',
      style: { color: '#00ff00', marginBottom: '15px' }
    }, 'Export Conversation'),
    
    // 文件名输入或保存确认
    !showSaveDialog ? React.createElement('div', {
      key: 'filename-input',
      style: { marginBottom: '15px' }
    }, [
      React.createElement('div', {
        key: 'label',
        style: { marginBottom: '5px', color: '#cccccc' }
      }, 'Filename:'),
      
      React.createElement('input', {
        key: 'input',
        type: 'text',
        value: filename,
        onChange: (e) => handleFilenameChange(e.target.value),
        style: {
          width: '100%',
          padding: '8px',
          backgroundColor: '#333',
          color: '#ffffff',
          border: '1px solid #555',
          borderRadius: '4px',
          fontFamily: 'monospace'
        },
        autoFocus: true
      })
    ]) : React.createElement('div', {
      key: 'save-confirm',
      style: { marginBottom: '15px' }
    }, [
      React.createElement('div', {
        key: 'confirm-text',
        style: { color: '#ffff00', marginBottom: '10px' }
      }, `Save as: ${filename}`),
      
      React.createElement('div', {
        key: 'file-info',
        style: { color: '#888888', fontSize: '12px' }
      }, `Content size: ${Math.round(content.length / 1024)} KB`)
    ]),
    
    // 操作选项
    React.createElement('div', {
      key: 'options',
      style: { marginBottom: '15px' }
    }, [
      React.createElement('div', {
        key: 'save-option',
        style: {
          padding: '8px',
          backgroundColor: showSaveDialog ? '#333' : 'transparent',
          borderRadius: '4px',
          marginBottom: '5px'
        }
      }, showSaveDialog ? '► Save to file' : '  Save to file (Enter)'),
      
      React.createElement('div', {
        key: 'clipboard-option',
        style: {
          padding: '8px',
          backgroundColor: 'transparent',
          borderRadius: '4px'
        }
      }, '  Copy to clipboard (C)')
    ]),
    
    // 帮助信息
    React.createElement('div', {
      key: 'help',
      style: {
        borderTop: '1px solid #333',
        paddingTop: '10px',
        color: '#888888',
        fontSize: '12px'
      }
    }, showSaveDialog ? 
      'Enter to save • Esc to go back' : 
      'Enter to save file • C to copy • Esc to cancel'
    )
  ]);
}

/**
 * /export 命令定义
 * @original: 原始文件L50289-50325中的TN8对象
 */
export const exportCommand = {
  type: "local-jsx",
  name: "export",
  description: "Export the current conversation to a file or clipboard",
  isEnabled: () => true,
  isHidden: false,
  argumentHint: "[filename]",
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @param {string} args - 命令参数
   * @returns {Promise<React.Element>} React组件
   */
  async call(onDone, context, args) {
    try {
      // 生成默认文件名
      let defaultFilename;
      if (!args?.trim()) {
        const title = extractConversationTitle(context.messages || []);
        const timestamp = generateTimestamp(new Date());
        
        if (title) {
          const sanitizedTitle = sanitizeFilename(title);
          defaultFilename = sanitizedTitle ? 
            `${timestamp.substring(0, 10)}-${sanitizedTitle}.txt` : 
            `conversation-${timestamp}.txt`;
        } else {
          defaultFilename = `conversation-${timestamp}.txt`;
        }
      } else {
        defaultFilename = args.trim();
      }
      
      // 转换消息内容
      const messages = context.messages || [];
      const format = defaultFilename.endsWith('.md') ? EXPORT_FORMATS.MARKDOWN :
                    defaultFilename.endsWith('.json') ? EXPORT_FORMATS.JSON :
                    EXPORT_FORMATS.TXT;
      
      const content = convertMessages(messages, format);
      
      return React.createElement(ExportDialog, {
        content,
        defaultFilename,
        onDone: (result) => {
          onDone(result.message);
        }
      });
      
    } catch (error) {
      onDone(`Export failed: ${error.message}`);
      return null;
    }
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "export";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /export [filename]

Export the current conversation to a file or clipboard.

Options:
  /export                    # Export with auto-generated filename
  /export my-conversation    # Export with custom filename
  /export chat.md           # Export as Markdown format
  /export data.json         # Export as JSON format

Export Formats:
- .txt (default): Plain text format with clear role separation
- .md: Markdown format with proper formatting and syntax highlighting
- .json: Structured JSON format with metadata and timestamps

Export Targets:
- File: Save the conversation to a local file
- Clipboard: Copy the conversation content to system clipboard

Features:
- Automatic filename generation based on conversation content
- Multiple export formats (text, markdown, JSON)
- Clipboard integration for quick sharing
- Preserves tool use and tool results
- Includes timestamps and metadata

The export dialog provides:
- Interactive filename editing
- Format preview and file size information
- Keyboard shortcuts for quick actions
- Error handling and user feedback

Navigation:
- Enter: Save to file
- C: Copy to clipboard
- Escape: Cancel export

Exported files include the complete conversation history with proper formatting and are suitable for sharing, archiving, or further processing.
    `.trim();
  }
};
