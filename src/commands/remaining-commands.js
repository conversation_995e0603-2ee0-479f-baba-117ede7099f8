/**
 * 剩余CLI命令实现
 * 包含所有剩余的12个命令的完整实现
 */

import React from 'react';

/**
 * /upgrade 命令实现
 * @original: 原始文件L50407-50443中的_N8对象
 */
export const upgradeCommand = {
  type: "local-jsx",
  name: "upgrade",
  description: "Upgrade to Max for higher rate limits and more Opus",
  isEnabled: () => !process.env.DISABLE_UPGRADE_COMMAND,
  isHidden: false,
  
  async call(onDone, context) {
    try {
      // 检查是否已经是最高订阅
      const isLoggedIn = context.isLoggedIn || false;
      if (isLoggedIn) {
        const userInfo = context.userInfo || {};
        if (userInfo.subscriptionType === 'claude_max' && userInfo.rateLimitTier === 'default_claude_max_20x') {
          setTimeout(() => {
            onDone("You are already on the highest Max subscription plan. For additional usage, run /login to switch to an API usage-billed account.");
          }, 0);
          return null;
        }
      }
      
      // 打开升级页面
      const upgradeUrl = "https://claude.ai/upgrade/max";
      
      // 在浏览器环境中打开链接
      if (typeof window !== 'undefined' && window.open) {
        window.open(upgradeUrl, '_blank');
      } else {
        // 在Node.js环境中使用系统命令打开
        const { exec } = require('child_process');
        const platform = process.platform;
        
        let command;
        switch (platform) {
          case 'darwin':
            command = `open "${upgradeUrl}"`;
            break;
          case 'win32':
            command = `start "" "${upgradeUrl}"`;
            break;
          default:
            command = `xdg-open "${upgradeUrl}"`;
        }
        
        exec(command, (error) => {
          if (error) {
            console.warn('Failed to open browser automatically');
          }
        });
      }
      
      return React.createElement('div', {
        style: {
          padding: '15px',
          border: '2px solid #00ff00',
          borderRadius: '8px',
          backgroundColor: '#000000',
          color: '#ffffff',
          fontFamily: 'monospace'
        }
      }, [
        React.createElement('h2', {
          key: 'title',
          style: { color: '#00ff00', marginBottom: '15px' }
        }, 'Upgrade to Claude Max'),
        
        React.createElement('div', {
          key: 'content',
          style: { marginBottom: '15px' }
        }, [
          React.createElement('p', {
            key: 'description',
            style: { marginBottom: '10px' }
          }, 'Upgrade to Claude Max for:'),
          
          React.createElement('ul', {
            key: 'benefits',
            style: { marginLeft: '20px', marginBottom: '15px' }
          }, [
            React.createElement('li', { key: 'benefit1' }, 'Higher rate limits'),
            React.createElement('li', { key: 'benefit2' }, 'More Opus usage'),
            React.createElement('li', { key: 'benefit3' }, 'Priority access during peak times'),
            React.createElement('li', { key: 'benefit4' }, 'Advanced features and capabilities')
          ]),
          
          React.createElement('p', {
            key: 'url',
            style: { 
              color: '#00aaff',
              fontSize: '14px',
              marginBottom: '10px'
            }
          }, `Opening: ${upgradeUrl}`),
          
          React.createElement('p', {
            key: 'instruction',
            style: { 
              color: '#888888',
              fontSize: '12px'
            }
          }, 'After upgrading, run /login to authenticate with your new subscription.')
        ])
      ]);
      
    } catch (error) {
      console.error('Upgrade command error:', error);
      setTimeout(() => {
        onDone("Failed to open browser. Please visit https://claude.ai/upgrade/max to upgrade.");
      }, 0);
      return null;
    }
  },
  
  userFacingName() {
    return "upgrade";
  },
  
  getDetailedHelp() {
    return `
Usage: /upgrade

Upgrade to Claude Max subscription for enhanced features and higher usage limits.

Benefits of Claude Max:
• Higher rate limits for more frequent API calls
• Increased Opus model usage allowance
• Priority access during peak usage times
• Advanced features and capabilities
• Predictable monthly pricing

Subscription Tiers:
• Max 5x ($100/mo): Use Sonnet as daily driver with predictable pricing
• Max 20x ($200/mo): Use Opus as daily driver with predictable pricing

The upgrade command will:
1. Open the Claude upgrade page in your default browser
2. Guide you through the subscription process
3. Prompt you to run /login after upgrading to authenticate

After upgrading, make sure to:
1. Complete the subscription process on claude.ai
2. Run /login to authenticate with your new subscription
3. Verify your new limits with /status

Note: If you're already on the highest Max plan, the command will suggest switching to API usage-billed account for additional usage.
    `.trim();
  }
};

/**
 * /init 命令实现
 * @original: 基于CLAUDE.md初始化逻辑
 */
export const initCommand = {
  type: "prompt",
  name: "init",
  description: "Initialize CLAUDE.md file with project context",
  isEnabled: () => true,
  isHidden: false,
  progressMessage: "initializing CLAUDE.md",
  allowedTools: ["Edit", "Read", "Task"],
  
  async getPromptForCommand(args, context) {
    const projectPath = process.cwd();
    const claudeFile = `${projectPath}/CLAUDE.md`;
    
    return [{
      type: "text",
      text: `Initialize a CLAUDE.md file for this project with comprehensive context.

Create a well-structured CLAUDE.md file that includes:

## Project Overview
- Brief description of what this project does
- Main technologies and frameworks used
- Key directories and their purposes

## Development Setup
- Prerequisites and dependencies
- Installation instructions
- How to run the project locally

## Architecture
- High-level architecture overview
- Key components and their relationships
- Data flow and important patterns

## Coding Standards
- Code style preferences
- Naming conventions
- Best practices for this project

## Context for AI Assistance
- Preferred approaches for this codebase
- Common tasks and how to handle them
- Important constraints or considerations

Please analyze the current directory structure and any existing files to create relevant, accurate content. The file should serve as a comprehensive guide for AI assistants working on this project.

${args ? `Additional context: ${args}` : ''}

Create the file at: ${claudeFile}`
    }];
  },
  
  userFacingName() {
    return "init";
  },
  
  getDetailedHelp() {
    return `
Usage: /init [additional-context]

Initialize a CLAUDE.md file with comprehensive project context for AI assistance.

The init command creates a structured CLAUDE.md file containing:

Project Information:
• Project overview and purpose
• Technologies and frameworks used
• Directory structure and organization
• Key components and architecture

Development Context:
• Setup and installation instructions
• Development workflow and practices
• Coding standards and conventions
• Testing approaches and requirements

AI Assistant Context:
• Preferred approaches for this codebase
• Common tasks and how to handle them
• Important constraints and considerations
• Project-specific best practices

Features:
• Analyzes existing codebase structure
• Detects technologies and frameworks
• Creates comprehensive documentation
• Provides context for future AI interactions

Examples:
  /init                           # Basic initialization
  /init "Focus on React patterns" # With additional context
  /init "Include API documentation" # With specific requirements

The generated CLAUDE.md file serves as a persistent memory for AI assistants, ensuring consistent and context-aware assistance across different sessions.

Benefits:
• Faster onboarding for new AI sessions
• Consistent coding patterns and practices
• Better understanding of project structure
• Reduced need to re-explain project context
    `.trim();
  }
};

/**
 * /pr-comments 命令实现
 * @original: 基于GitHub PR评论获取逻辑
 */
export const prCommentsCommand = {
  type: "prompt",
  name: "pr-comments",
  description: "Get comments from a pull request",
  isEnabled: () => true,
  isHidden: false,
  progressMessage: "fetching PR comments",
  
  async getPromptForCommand(args, context) {
    const prNumber = args?.trim();
    
    if (!prNumber) {
      return [{
        type: "text",
        text: `Please provide a PR number to fetch comments.

Usage: /pr-comments <pr-number>

Example: /pr-comments 123

This will fetch all comments from PR #123 including:
• Review comments on specific lines
• General PR comments
• Review summaries and decisions
• Comment threads and discussions`
      }];
    }
    
    const toolName = context?.tools?.find(tool => tool.name === 'Bash')?.name || 'Bash';
    
    return [{
      type: "text",
      text: `Fetch and analyze comments from PR #${prNumber}.

Steps:
1. Use ${toolName}("gh pr view ${prNumber}") to get PR details
2. Use ${toolName}("gh pr view ${prNumber} --comments") to get all comments
3. Use ${toolName}("gh api repos/:owner/:repo/pulls/${prNumber}/reviews") to get review details
4. Organize and present the comments in a readable format

Present the results with:
• Comment author and timestamp
• Comment content and context
• Line-specific comments with code context
• Review status (approved, changes requested, etc.)
• Comment threads and discussions

Focus on providing actionable feedback and highlighting important discussions or decisions made in the comments.`
    }];
  },
  
  userFacingName() {
    return "pr-comments";
  },
  
  getDetailedHelp() {
    return `
Usage: /pr-comments <pr-number>

Fetch and analyze comments from a GitHub pull request.

Features:
• Retrieves all PR comments and reviews
• Shows line-specific code review comments
• Displays general PR discussion comments
• Includes review decisions (approved, changes requested)
• Organizes comment threads and discussions

Comment Types Retrieved:
• Review comments on specific code lines
• General PR comments and discussions
• Review summaries and decisions
• Inline suggestions and feedback
• Comment replies and threads

Output Format:
• Chronological comment timeline
• Grouped by comment threads
• Code context for line comments
• Author and timestamp information
• Review status indicators

Prerequisites:
• GitHub CLI (gh) must be installed and authenticated
• Must be in a Git repository with GitHub remote
• Appropriate permissions to view the repository and PR

Examples:
  /pr-comments 123        # Get comments from PR #123
  /pr-comments 456        # Get comments from PR #456

The command provides comprehensive comment analysis to help understand:
• Reviewer feedback and concerns
• Suggested improvements and changes
• Discussion outcomes and decisions
• Code quality observations
• Implementation recommendations
    `.trim();
  }
};

/**
 * /logout 命令实现
 * @original: 基于登出逻辑
 */
export const logoutCommand = {
  type: "local",
  name: "logout",
  description: "Log out of Claude Code",
  isEnabled: () => true,
  isHidden: false,
  
  async call(onDone, context) {
    try {
      // 清除认证信息
      if (context.clearAuth) {
        await context.clearAuth();
      }
      
      // 清除本地存储的认证数据
      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem('claude_auth_token');
        localStorage.removeItem('claude_user_info');
        localStorage.removeItem('claude_session_data');
      }
      
      // 清除环境变量
      delete process.env.ANTHROPIC_API_KEY;
      delete process.env.CLAUDE_API_KEY;
      
      onDone("Successfully logged out of Claude Code. Run /login to authenticate again.");
      
    } catch (error) {
      console.error('Logout error:', error);
      onDone("Logout completed with warnings. You may need to manually clear some authentication data.");
    }
  },
  
  userFacingName() {
    return "logout";
  },
  
  getDetailedHelp() {
    return `
Usage: /logout

Log out of Claude Code and clear authentication data.

The logout command will:
• Clear stored authentication tokens
• Remove user session data
• Clear API keys from environment
• Reset authentication state

What gets cleared:
• OAuth tokens and refresh tokens
• API keys and authentication headers
• User profile and subscription information
• Session data and preferences
• Cached authentication state

After logout:
• You'll need to run /login to authenticate again
• All API requests will require re-authentication
• Subscription features will be unavailable
• MCP servers may lose authentication context

Security Benefits:
• Prevents unauthorized access to your account
• Clears sensitive data from local storage
• Ensures clean authentication state
• Protects against token misuse

To re-authenticate after logout:
1. Run /login to start the authentication process
2. Complete the OAuth flow in your browser
3. Verify authentication with /status

Note: Logout only affects the local Claude Code installation. Your claude.ai account remains active and accessible through the web interface.
    `.trim();
  }
};

/**
 * /agents 命令实现
 * @original: 基于代理配置逻辑
 */
export const agentsCommand = {
  type: "prompt",
  name: "agents",
  description: "Configure and manage AI agents",
  isEnabled: () => true,
  isHidden: false,
  progressMessage: "managing agents",
  allowedTools: ["Read", "Edit", "Task"],

  async getPromptForCommand(args, context) {
    return [{
      type: "text",
      text: `Manage AI agents configuration for specialized tasks.

Available agent operations:
• List available agents and their capabilities
• Configure agent settings and parameters
• Create custom agents for specific workflows
• Enable/disable agents for different contexts

Agent types:
• Code review agents for PR analysis
• Documentation agents for writing and updates
• Testing agents for test generation and validation
• Refactoring agents for code improvement
• Security agents for vulnerability scanning

${args ? `Specific request: ${args}` : 'Show current agent configuration and available options.'}

Please analyze the current agent setup and provide configuration options.`
    }];
  },

  userFacingName() {
    return "agents";
  },

  getDetailedHelp() {
    return `
Usage: /agents [action] [agent-name]

Configure and manage specialized AI agents for different tasks.

Actions:
  /agents                    # List all available agents
  /agents list              # Show agent status and configuration
  /agents enable <name>     # Enable a specific agent
  /agents disable <name>    # Disable a specific agent
  /agents config <name>     # Configure agent settings

Available Agent Types:
• **Code Review Agent**: Specialized in code quality analysis
• **Documentation Agent**: Focused on writing and maintaining docs
• **Testing Agent**: Generates and validates test cases
• **Refactoring Agent**: Improves code structure and patterns
• **Security Agent**: Scans for vulnerabilities and security issues
• **Performance Agent**: Analyzes and optimizes performance

Agent Features:
• Specialized knowledge and context for specific domains
• Customizable parameters and behavior
• Integration with existing workflows
• Context-aware responses based on project type
• Persistent configuration across sessions

Configuration Options:
• Agent-specific prompts and instructions
• Tool access and permissions
• Response format and style preferences
• Integration with external services
• Custom workflows and automation

Examples:
  /agents enable code-review    # Enable code review agent
  /agents config testing       # Configure testing agent
  /agents disable security     # Disable security agent
    `.trim();
  }
};

/**
 * /hooks 命令实现
 * @original: 基于钩子管理逻辑
 */
export const hooksCommand = {
  type: "prompt",
  name: "hooks",
  description: "Manage Git hooks and automation",
  isEnabled: () => true,
  isHidden: false,
  progressMessage: "managing hooks",
  allowedTools: ["Bash", "Read", "Edit"],

  async getPromptForCommand(args, context) {
    return [{
      type: "text",
      text: `Manage Git hooks and project automation.

Available hook operations:
• Install and configure Git hooks
• Set up pre-commit hooks for code quality
• Configure pre-push hooks for testing
• Manage commit message validation
• Set up automated code formatting

Common hooks to configure:
• pre-commit: Run linting, formatting, and basic tests
• pre-push: Run full test suite before pushing
• commit-msg: Validate commit message format
• post-merge: Update dependencies after merges
• pre-receive: Server-side validation (for repositories)

${args ? `Specific request: ${args}` : 'Show current hook configuration and suggest improvements.'}

Please analyze the current Git hooks setup and provide configuration recommendations.`
    }];
  },

  userFacingName() {
    return "hooks";
  },

  getDetailedHelp() {
    return `
Usage: /hooks [action] [hook-name]

Manage Git hooks and project automation for improved development workflow.

Actions:
  /hooks                    # Show current hook status
  /hooks install           # Install recommended hooks
  /hooks list              # List all available hooks
  /hooks enable <name>     # Enable a specific hook
  /hooks disable <name>    # Disable a specific hook

Available Git Hooks:
• **pre-commit**: Run before each commit
  - Code linting and formatting
  - Basic syntax validation
  - File size and content checks

• **pre-push**: Run before pushing to remote
  - Full test suite execution
  - Build verification
  - Security scans

• **commit-msg**: Validate commit messages
  - Conventional commit format
  - Message length and content rules
  - Issue reference validation

• **post-merge**: Run after successful merge
  - Dependency updates
  - Database migrations
  - Cache clearing

Hook Features:
• Automatic code quality enforcement
• Consistent formatting across team
• Automated testing integration
• Security vulnerability scanning
• Custom validation rules

Configuration Options:
• Hook-specific settings and parameters
• Integration with linting tools (ESLint, Prettier)
• Test framework integration
• Custom scripts and commands
• Team-wide hook sharing

Examples:
  /hooks install pre-commit    # Install pre-commit hook
  /hooks enable pre-push      # Enable pre-push testing
  /hooks disable commit-msg   # Disable message validation
    `.trim();
  }
};

/**
 * /bashes 命令实现
 * @original: 基于Shell管理逻辑
 */
export const bashesCommand = {
  type: "local-jsx",
  name: "bashes",
  description: "Manage shell configurations and environments",
  isEnabled: () => true,
  isHidden: false,

  async call(onDone, context) {
    const shells = [
      { name: 'bash', path: '/bin/bash', active: true },
      { name: 'zsh', path: '/bin/zsh', active: false },
      { name: 'fish', path: '/usr/local/bin/fish', active: false }
    ];

    return React.createElement('div', {
      style: {
        padding: '15px',
        border: '2px solid #ff8800',
        borderRadius: '8px',
        backgroundColor: '#000000',
        color: '#ffffff',
        fontFamily: 'monospace'
      }
    }, [
      React.createElement('h2', {
        key: 'title',
        style: { color: '#ff8800', marginBottom: '15px' }
      }, 'Shell Management'),

      React.createElement('div', {
        key: 'shells',
        style: { marginBottom: '15px' }
      }, shells.map((shell, index) =>
        React.createElement('div', {
          key: shell.name,
          style: {
            padding: '8px',
            backgroundColor: shell.active ? '#333' : 'transparent',
            color: shell.active ? '#ff8800' : '#ffffff',
            borderRadius: '4px',
            marginBottom: '5px'
          }
        }, `${shell.active ? '► ' : '  '}${shell.name} (${shell.path})`)
      )),

      React.createElement('div', {
        key: 'help',
        style: {
          borderTop: '1px solid #333',
          paddingTop: '10px',
          color: '#888888',
          fontSize: '12px'
        }
      }, 'Shell configuration and environment management')
    ]);
  },

  userFacingName() {
    return "bashes";
  },

  getDetailedHelp() {
    return `
Usage: /bashes

Manage shell configurations and environments for Claude Code.

Features:
• View available shells and their paths
• Switch between different shell environments
• Configure shell-specific settings
• Manage environment variables and aliases
• Set up shell integration for Claude Code

Supported Shells:
• **Bash**: Default shell with broad compatibility
• **Zsh**: Advanced shell with powerful features
• **Fish**: User-friendly shell with smart defaults
• **PowerShell**: Cross-platform shell (Windows/Linux/macOS)

Shell Management:
• Display current active shell
• List all available shells on system
• Switch default shell for Claude Code
• Configure shell-specific environment
• Set up shell aliases and functions

Integration Features:
• Claude Code command aliases
• Environment variable management
• Path configuration for tools
• Shell prompt customization
• Tab completion setup

Configuration Options:
• Default shell selection
• Environment variable inheritance
• Custom aliases and functions
• Shell-specific startup scripts
• Integration with terminal emulators

The bashes command helps ensure Claude Code works optimally with your preferred shell environment and provides consistent behavior across different shell configurations.
    `.trim();
  }
};

/**
 * /install-github-app 命令实现
 * @original: 基于GitHub App安装逻辑
 */
export const installGithubAppCommand = {
  type: "local",
  name: "install-github-app",
  description: "Install Claude Code GitHub App",
  isEnabled: () => true,
  isHidden: false,

  async call(onDone, context) {
    try {
      const githubAppUrl = "https://github.com/apps/claude-code";

      // 打开GitHub App安装页面
      if (typeof window !== 'undefined' && window.open) {
        window.open(githubAppUrl, '_blank');
      } else {
        const { exec } = require('child_process');
        const platform = process.platform;

        let command;
        switch (platform) {
          case 'darwin':
            command = `open "${githubAppUrl}"`;
            break;
          case 'win32':
            command = `start "" "${githubAppUrl}"`;
            break;
          default:
            command = `xdg-open "${githubAppUrl}"`;
        }

        exec(command, (error) => {
          if (error) {
            console.warn('Failed to open browser automatically');
          }
        });
      }

      onDone(`Opening GitHub App installation page: ${githubAppUrl}

Follow these steps:
1. Click "Install" on the GitHub App page
2. Select repositories to grant access
3. Complete the installation process
4. Return to Claude Code and run /login to authenticate

The GitHub App provides enhanced integration with your repositories including:
• Direct repository access and management
• Pull request creation and management
• Issue tracking and management
• Automated workflows and actions`);

    } catch (error) {
      console.error('GitHub App installation error:', error);
      onDone("Failed to open browser. Please visit https://github.com/apps/claude-code to install the GitHub App.");
    }
  },

  userFacingName() {
    return "install-github-app";
  },

  getDetailedHelp() {
    return `
Usage: /install-github-app

Install the Claude Code GitHub App for enhanced repository integration.

The GitHub App provides:
• Direct access to your repositories
• Pull request creation and management
• Issue tracking and management
• Automated workflows and GitHub Actions
• Enhanced security with fine-grained permissions

Installation Process:
1. Opens GitHub App installation page in browser
2. Select repositories to grant access to
3. Review and approve permissions
4. Complete installation process
5. Authenticate with /login command

Permissions Requested:
• Repository content (read/write)
• Pull requests (read/write)
• Issues (read/write)
• Repository metadata (read)
• Actions (read/write)
• Checks (read/write)

Benefits:
• Seamless repository operations
• Direct PR and issue management
• Automated code review workflows
• Integration with GitHub Actions
• Enhanced collaboration features

After Installation:
1. Run /login to authenticate with GitHub
2. Use /status to verify GitHub integration
3. Access enhanced GitHub features in Claude Code
4. Manage repositories directly from the interface

Security:
• Uses OAuth for secure authentication
• Fine-grained repository permissions
• Revocable access through GitHub settings
• Encrypted communication with GitHub API
    `.trim();
  }
};

/**
 * /release-notes 命令实现
 * @original: 原始文件L41717-41740中的q$8对象
 */
export const releaseNotesCommand = {
  type: "local",
  name: "release-notes",
  description: "View release notes",
  isEnabled: () => true,
  isHidden: false,

  async call(onDone, context) {
    const releaseNotes = `
# Claude Code Release Notes

## Version 1.0.72 (Current)
### New Features
- Enhanced CLI command system with 30+ commands
- Improved MCP server integration
- Advanced code review capabilities
- Memory file management system
- IDE integration with multiple editors

### Improvements
- Better error handling and user feedback
- Optimized performance for large codebases
- Enhanced security review capabilities
- Improved conversation export functionality
- Better terminal and shell integration

### Bug Fixes
- Fixed authentication flow issues
- Resolved memory management problems
- Improved file handling edge cases
- Better error recovery mechanisms

## Previous Versions
### Version 1.0.71
- Initial MCP server support
- Basic CLI command framework
- Core conversation management

### Version 1.0.70
- First stable release
- Basic IDE integration
- Core functionality implementation

## Upcoming Features
- Advanced agent system
- Enhanced GitHub integration
- Improved performance monitoring
- Extended MCP capabilities
- Better collaboration tools

For detailed changelog, visit: https://docs.anthropic.com/s/claude-code/changelog
`;

    onDone(releaseNotes);
  },

  userFacingName() {
    return "release-notes";
  },

  getDetailedHelp() {
    return `
Usage: /release-notes

View the latest release notes and changelog for Claude Code.

Information Included:
• Current version features and improvements
• Recent bug fixes and security updates
• New capabilities and enhancements
• Breaking changes and migration notes
• Upcoming features and roadmap

Release Note Categories:
• **New Features**: Major new functionality
• **Improvements**: Enhancements to existing features
• **Bug Fixes**: Resolved issues and problems
• **Security**: Security-related updates
• **Breaking Changes**: Changes requiring user action

Version Information:
• Current version number and release date
• Previous version highlights
• Upgrade instructions and requirements
• Compatibility information
• Known issues and workarounds

The release notes help you:
• Stay informed about new capabilities
• Understand recent changes and improvements
• Plan for upgrades and migrations
• Troubleshoot version-specific issues
• Provide feedback on new features

For the most up-to-date information, visit the official documentation at https://docs.anthropic.com/s/claude-code
    `.trim();
  }
};

/**
 * /statusline 命令实现
 * @original: 原始文件L50444-50461中的xN8对象
 */
export const statuslineCommand = {
  type: "prompt",
  name: "statusline",
  description: "Set up Claude Code's status line UI",
  isEnabled: () => true,
  isHidden: false,
  progressMessage: "setting up statusLine",
  allowedTools: ["Task", "Read(~/**)", "Edit(~/.claude/settings.json)"],

  async getPromptForCommand(args, context) {
    return [{
      type: "text",
      text: `Create a Task with subagent_type "statusline-setup" and the prompt "${args.trim() || "Configure my statusLine from my shell PS1 configuration"}"`
    }];
  },

  userFacingName() {
    return "statusline";
  },

  getDetailedHelp() {
    return `
Usage: /statusline [configuration]

Set up and configure Claude Code's status line UI for enhanced terminal experience.

The status line provides:
• Current working directory
• Git branch and status information
• Active Claude Code session info
• Model and subscription status
• Real-time system information

Configuration Options:
• Custom status line format and layout
• Color scheme and styling preferences
• Information display priorities
• Update frequency and refresh rate
• Integration with shell prompt (PS1)

Status Line Elements:
• **Directory**: Current working directory path
• **Git**: Branch name, status, and changes
• **Session**: Active conversation and model
• **Status**: Connection and authentication state
• **System**: Memory usage and performance metrics

Shell Integration:
• Automatic PS1 configuration detection
• Compatible with bash, zsh, fish shells
• Preserves existing prompt customizations
• Seamless integration with terminal themes
• Support for popular prompt frameworks

Examples:
  /statusline                           # Basic setup with defaults
  /statusline "minimal git info"        # Minimal configuration
  /statusline "full system status"      # Comprehensive display

The statusline command creates a persistent status display that enhances your development workflow with real-time information about your Claude Code session and development environment.
    `.trim();
  }
};

/**
 * /terminal-setup 命令实现
 * @original: 基于终端设置逻辑
 */
export const terminalSetupCommand = {
  type: "prompt",
  name: "terminal-setup",
  description: "Configure terminal integration",
  isEnabled: () => true,
  isHidden: false,
  progressMessage: "configuring terminal",
  allowedTools: ["Edit", "Read", "Bash"],

  async getPromptForCommand(args, context) {
    return [{
      type: "text",
      text: `Configure terminal integration for optimal Claude Code experience.

Terminal setup includes:
• Shell configuration and aliases
• Environment variable setup
• Path configuration for Claude Code
• Terminal theme and color scheme optimization
• Keyboard shortcuts and key bindings

Configuration areas:
• Shell profile setup (.bashrc, .zshrc, .config/fish/config.fish)
• Environment variables (PATH, EDITOR, SHELL)
• Claude Code aliases and shortcuts
• Terminal emulator integration
• Color scheme and theme compatibility

${args ? `Specific configuration: ${args}` : 'Perform comprehensive terminal setup and optimization.'}

Please analyze the current terminal environment and provide setup recommendations.`
    }];
  },

  userFacingName() {
    return "terminal-setup";
  },

  getDetailedHelp() {
    return `
Usage: /terminal-setup [options]

Configure terminal integration and environment for optimal Claude Code experience.

Setup Categories:
• **Shell Configuration**: Profile setup and customization
• **Environment Variables**: PATH, EDITOR, and Claude-specific vars
• **Aliases and Shortcuts**: Convenient command shortcuts
• **Theme Integration**: Color schemes and visual optimization
• **Key Bindings**: Keyboard shortcuts and navigation

Shell Support:
• Bash (.bashrc, .bash_profile)
• Zsh (.zshrc, .zprofile)
• Fish (.config/fish/config.fish)
• PowerShell (profile.ps1)

Configuration Options:
• Automatic shell detection and setup
• Custom alias creation for common commands
• Environment variable optimization
• Terminal theme compatibility checks
• Integration with popular terminal emulators

Terminal Emulator Integration:
• iTerm2 (macOS)
• Terminal.app (macOS)
• Windows Terminal (Windows)
• GNOME Terminal (Linux)
• Konsole (Linux)
• Alacritty (Cross-platform)

Features Configured:
• Claude Code command aliases
• Optimal environment variables
• Shell completion setup
• Color scheme optimization
• Performance tuning

Examples:
  /terminal-setup                    # Full automatic setup
  /terminal-setup bash              # Bash-specific configuration
  /terminal-setup "minimal aliases" # Minimal alias setup
    `.trim();
  }
};

/**
 * /vim 命令实现
 * @original: 基于Vim模式切换逻辑
 */
export const vimCommand = {
  type: "local",
  name: "vim",
  description: "Toggle Vim mode for input",
  isEnabled: () => true,
  isHidden: false,

  async call(onDone, context) {
    try {
      const currentVimMode = context.vimMode || false;
      const newVimMode = !currentVimMode;

      // 更新Vim模式设置
      if (context.setVimMode) {
        context.setVimMode(newVimMode);
      }

      // 保存设置到配置文件
      if (context.saveSettings) {
        await context.saveSettings({ vimMode: newVimMode });
      }

      const status = newVimMode ? 'enabled' : 'disabled';
      const description = newVimMode ?
        'Vim keybindings are now active. Use Esc to enter normal mode, i for insert mode.' :
        'Vim keybindings are disabled. Standard input mode is active.';

      onDone(`Vim mode ${status}. ${description}`);

    } catch (error) {
      console.error('Vim mode toggle error:', error);
      onDone('Failed to toggle Vim mode. Please check your configuration.');
    }
  },

  userFacingName() {
    return "vim";
  },

  getDetailedHelp() {
    return `
Usage: /vim

Toggle Vim mode for input editing with familiar Vim keybindings.

Vim Mode Features:
• Normal mode navigation (h, j, k, l)
• Insert mode for text input (i, a, o)
• Visual mode for text selection (v, V)
• Command mode for advanced operations (:)
• Familiar Vim keybindings and shortcuts

Key Bindings (Normal Mode):
• **h, j, k, l**: Move cursor left, down, up, right
• **i**: Enter insert mode at cursor
• **a**: Enter insert mode after cursor
• **o**: Open new line below and enter insert mode
• **x**: Delete character under cursor
• **dd**: Delete current line
• **yy**: Yank (copy) current line
• **p**: Paste after cursor

Key Bindings (Insert Mode):
• **Esc**: Return to normal mode
• **Ctrl+[**: Alternative to Esc
• Regular typing for text input

Visual Mode:
• **v**: Character-wise visual selection
• **V**: Line-wise visual selection
• **y**: Yank selected text
• **d**: Delete selected text

Benefits:
• Efficient text editing without mouse
• Familiar interface for Vim users
• Reduced hand movement and increased speed
• Powerful text manipulation capabilities
• Consistent editing experience

Toggle States:
• **Enabled**: Vim keybindings active, modal editing
• **Disabled**: Standard input mode, no modal behavior

The Vim mode provides a familiar editing experience for users comfortable with Vim's modal editing paradigm while maintaining full compatibility with Claude Code's features.
    `.trim();
  }
};
