/**
 * /add-dir 命令实现
 * @original: 原始文件L11114-11160中的RS6对象和相关函数
 */

import React from 'react';
import { resolve, isAbsolute, dirname } from 'path';
import { existsSync, statSync } from 'fs';
import { normalizePath } from '../utils/path-utils.js';
import { fileExists, isDirectory } from '../utils/file-utils.js';

/**
 * 添加目录结果类型
 */
export const ADD_DIR_RESULT_TYPES = {
  SUCCESS: 'success',
  NOT_FOUND: 'notFound',
  NOT_DIRECTORY: 'notDirectory',
  ALREADY_IN_WORKING_DIRECTORY: 'alreadyInWorkingDirectory',
  PERMISSION_DENIED: 'permissionDenied'
};

/**
 * 验证并添加工作目录
 * @param {string} directoryPath - 目录路径
 * @param {Object} permissionContext - 权限上下文
 * @returns {Object} 验证结果
 * @original: 原始文件L11123中的h71函数调用
 */
export function validateAndAddDirectory(directoryPath, permissionContext) {
  if (!directoryPath || typeof directoryPath !== 'string') {
    return {
      resultType: ADD_DIR_RESULT_TYPES.NOT_FOUND,
      directoryPath,
      message: 'Directory path is required'
    };
  }

  // 解析绝对路径
  const absolutePath = isAbsolute(directoryPath) 
    ? normalizePath(directoryPath)
    : normalizePath(resolve(process.cwd(), directoryPath));

  // 检查路径是否存在
  if (!fileExists(absolutePath)) {
    const parentDir = dirname(absolutePath);
    return {
      resultType: ADD_DIR_RESULT_TYPES.NOT_FOUND,
      directoryPath,
      absolutePath,
      parentDir,
      message: `Directory ${directoryPath} does not exist`
    };
  }

  // 检查是否为目录
  if (!isDirectory(absolutePath)) {
    const parentDir = dirname(absolutePath);
    return {
      resultType: ADD_DIR_RESULT_TYPES.NOT_DIRECTORY,
      directoryPath,
      absolutePath,
      parentDir,
      message: `${directoryPath} is not a directory`
    };
  }

  // 检查是否已在工作目录中
  const currentWorkingDirs = [
    process.cwd(),
    ...(permissionContext?.additionalWorkingDirectories || [])
  ];

  for (const workingDir of currentWorkingDirs) {
    if (absolutePath.startsWith(workingDir)) {
      return {
        resultType: ADD_DIR_RESULT_TYPES.ALREADY_IN_WORKING_DIRECTORY,
        directoryPath,
        absolutePath,
        workingDir,
        message: `${directoryPath} is already accessible within ${workingDir}`
      };
    }
  }

  // 创建更新后的权限上下文
  const updatedPermissionContext = {
    ...permissionContext,
    additionalWorkingDirectories: [
      ...(permissionContext?.additionalWorkingDirectories || []),
      absolutePath
    ]
  };

  return {
    resultType: ADD_DIR_RESULT_TYPES.SUCCESS,
    directoryPath,
    absolutePath,
    updatedPermissionContext,
    message: `Successfully validated ${directoryPath}`
  };
}

/**
 * 格式化添加目录结果消息
 * @param {Object} result - 验证结果
 * @returns {string} 格式化的消息
 * @original: 原始文件L11100-11113中的g71函数
 */
export function formatAddDirectoryResult(result) {
  const { bold } = require('../utils/string-utils.js');
  
  switch (result.resultType) {
    case ADD_DIR_RESULT_TYPES.NOT_FOUND:
      if (result.parentDir && fileExists(result.parentDir)) {
        return `${bold(result.directoryPath)} does not exist. Did you mean to add the parent directory ${bold(result.parentDir)}?`;
      }
      return `${bold(result.directoryPath)} does not exist.`;
      
    case ADD_DIR_RESULT_TYPES.NOT_DIRECTORY:
      if (result.parentDir) {
        return `${bold(result.directoryPath)} is not a directory. Did you mean to add the parent directory ${bold(result.parentDir)}?`;
      }
      return `${bold(result.directoryPath)} is not a directory.`;
      
    case ADD_DIR_RESULT_TYPES.ALREADY_IN_WORKING_DIRECTORY:
      return `${bold(result.directoryPath)} is already accessible within the existing working directory ${bold(result.workingDir)}.`;
      
    case ADD_DIR_RESULT_TYPES.SUCCESS:
      return `Added ${bold(result.absolutePath)} as a working directory.`;
      
    default:
      return `Unknown result type: ${result.resultType}`;
  }
}

/**
 * 错误消息组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L11126-11130中的MS6组件
 */
function AddDirectoryErrorMessage({ message, args, onDone }) {
  React.useEffect(() => {
    // 自动关闭错误消息
    const timer = setTimeout(() => {
      onDone();
    }, 100);
    
    return () => clearTimeout(timer);
  }, [onDone]);

  return React.createElement('div', {
    style: { color: 'red', marginBottom: '10px' }
  }, message);
}

/**
 * 添加目录确认组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L11132-11150中的Uj1组件
 */
function AddDirectoryConfirmation({ 
  directoryPath, 
  permissionContext, 
  setPermissionContext, 
  onAddDirectory 
}) {
  const handleConfirm = React.useCallback(() => {
    // 更新权限上下文
    const result = validateAndAddDirectory(directoryPath, permissionContext);
    
    if (result.resultType === ADD_DIR_RESULT_TYPES.SUCCESS) {
      setPermissionContext(result.updatedPermissionContext);
      
      // 保存到本地设置
      try {
        const currentSettings = JSON.parse(localStorage.getItem('localSettings') || '{}');
        const additionalDirectories = [
          ...(currentSettings.permissions?.additionalDirectories || []),
          result.absolutePath
        ];
        
        const updatedSettings = {
          ...currentSettings,
          permissions: {
            ...currentSettings.permissions,
            additionalDirectories: Array.from(new Set(additionalDirectories))
          }
        };
        
        localStorage.setItem('localSettings', JSON.stringify(updatedSettings));
        
        onAddDirectory(result.absolutePath, true);
      } catch (error) {
        console.error('Failed to save directory to local settings:', error);
        onAddDirectory(result.absolutePath, false);
      }
    }
  }, [directoryPath, permissionContext, setPermissionContext, onAddDirectory]);

  return React.createElement('div', {
    style: { padding: '10px' }
  }, [
    React.createElement('p', { key: 'message' }, 
      `Do you want to add ${directoryPath} as a working directory?`
    ),
    React.createElement('div', { 
      key: 'buttons',
      style: { marginTop: '10px' }
    }, [
      React.createElement('button', {
        key: 'confirm',
        onClick: handleConfirm,
        style: { marginRight: '10px' }
      }, 'Yes, add directory'),
      React.createElement('button', {
        key: 'cancel',
        onClick: () => onAddDirectory(null, false)
      }, 'Cancel')
    ])
  ]);
}

/**
 * /add-dir 命令定义
 * @original: 原始文件L11114-11160中的RS6对象
 */
export const addDirCommand = {
  type: "local-jsx",
  name: "add-dir",
  description: "Add a new working directory",
  argumentHint: "<path>",
  isEnabled: () => true,
  isHidden: false,
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @param {string} args - 命令参数
   * @returns {Promise<React.Element>} React组件
   */
  async call(onDone, context, args) {
    const directoryPath = args.trim();
    const result = validateAndAddDirectory(directoryPath, context.getToolPermissionContext());
    
    if (result.resultType !== ADD_DIR_RESULT_TYPES.SUCCESS) {
      const message = formatAddDirectoryResult(result);
      return React.createElement(AddDirectoryErrorMessage, {
        message,
        args,
        onDone: () => onDone(message)
      });
    }
    
    return React.createElement(AddDirectoryConfirmation, {
      directoryPath: result.absolutePath,
      permissionContext: context.getToolPermissionContext(),
      setPermissionContext: context.setToolPermissionContext,
      onAddDirectory: (addedPath, saved) => {
        if (addedPath) {
          context.setToolPermissionContext(result.updatedPermissionContext);
          let message = `Added ${addedPath} as a working directory`;
          if (saved) {
            message += ' and saved to settings';
          }
          onDone(message);
        } else {
          onDone('Operation cancelled');
        }
      }
    });
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "add-dir";
  }
};
