/**
 * /help 命令实现
 * @original: 原始文件L37793-37813中的G$8对象和帮助显示逻辑
 */

import React from 'react';

/**
 * 命令分类
 */
export const COMMAND_CATEGORIES = {
  CONVERSATION: 'Conversation Management',
  FILES: 'File Operations', 
  DEVELOPMENT: 'Development Tools',
  CONFIGURATION: 'Configuration',
  INTEGRATION: 'Integrations',
  SYSTEM: 'System'
};

/**
 * 获取命令分类
 * @param {Object} command - 命令对象
 * @returns {string} 分类名称
 */
function getCommandCategory(command) {
  const name = command.name;
  
  // 对话管理
  if (['clear', 'compact', 'resume', 'export'].includes(name)) {
    return COMMAND_CATEGORIES.CONVERSATION;
  }
  
  // 文件操作
  if (['add-dir', 'memory'].includes(name)) {
    return COMMAND_CATEGORIES.FILES;
  }
  
  // 开发工具
  if (['review', 'security-review', 'pr-comments', 'bashes', 'terminal-setup', 'vim'].includes(name)) {
    return COMMAND_CATEGORIES.DEVELOPMENT;
  }
  
  // 配置
  if (['config', 'permissions', 'hooks', 'agents', 'model'].includes(name)) {
    return COMMAND_CATEGORIES.CONFIGURATION;
  }
  
  // 集成
  if (['ide', 'mcp', 'install-github-app', 'statusline'].includes(name)) {
    return COMMAND_CATEGORIES.INTEGRATION;
  }
  
  // 系统
  if (['status', 'doctor', 'login', 'logout', 'upgrade', 'cost', 'bug', 'release-notes', 'init'].includes(name)) {
    return COMMAND_CATEGORIES.SYSTEM;
  }
  
  return 'Other';
}

/**
 * 格式化命令描述
 * @param {Object} command - 命令对象
 * @returns {string} 格式化的描述
 */
function formatCommandDescription(command) {
  let description = typeof command.description === 'function' 
    ? command.description() 
    : command.description || 'No description available';
    
  // 截断过长的描述
  if (description.length > 80) {
    description = description.substring(0, 77) + '...';
  }
  
  return description;
}

/**
 * 格式化命令使用提示
 * @param {Object} command - 命令对象
 * @returns {string} 使用提示
 */
function formatCommandUsage(command) {
  let usage = `/${command.name}`;
  
  if (command.argumentHint) {
    usage += ` ${command.argumentHint}`;
  }
  
  if (command.aliases && command.aliases.length > 0) {
    usage += ` (aliases: ${command.aliases.map(alias => `/${alias}`).join(', ')})`;
  }
  
  return usage;
}

/**
 * 帮助内容组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L37804-37807中的whB组件
 */
function HelpContent({ commands, onClose }) {
  const [selectedCategory, setSelectedCategory] = React.useState('all');
  const [searchTerm, setSearchTerm] = React.useState('');
  
  // 过滤和分组命令
  const filteredCommands = React.useMemo(() => {
    let filtered = commands.filter(command => {
      // 过滤隐藏命令
      if (command.isHidden) return false;
      
      // 检查是否启用
      if (typeof command.isEnabled === 'function' && !command.isEnabled()) {
        return false;
      }
      
      // 搜索过滤
      if (searchTerm) {
        const term = searchTerm.toLowerCase();
        const name = command.name.toLowerCase();
        const description = formatCommandDescription(command).toLowerCase();
        
        if (!name.includes(term) && !description.includes(term)) {
          return false;
        }
      }
      
      // 分类过滤
      if (selectedCategory !== 'all') {
        const category = getCommandCategory(command);
        if (category !== selectedCategory) {
          return false;
        }
      }
      
      return true;
    });
    
    // 按分类分组
    const grouped = {};
    filtered.forEach(command => {
      const category = getCommandCategory(command);
      if (!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(command);
    });
    
    // 排序每个分类内的命令
    Object.keys(grouped).forEach(category => {
      grouped[category].sort((a, b) => a.name.localeCompare(b.name));
    });
    
    return grouped;
  }, [commands, selectedCategory, searchTerm]);
  
  // 获取所有分类
  const categories = React.useMemo(() => {
    const cats = new Set(['all']);
    commands.forEach(command => {
      if (!command.isHidden && (typeof command.isEnabled !== 'function' || command.isEnabled())) {
        cats.add(getCommandCategory(command));
      }
    });
    return Array.from(cats);
  }, [commands]);
  
  const handleKeyPress = React.useCallback((event) => {
    if (event.key === 'Escape') {
      onClose();
    }
  }, [onClose]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [handleKeyPress]);
  
  return React.createElement('div', {
    style: {
      padding: '20px',
      maxWidth: '800px',
      margin: '0 auto',
      fontFamily: 'monospace'
    }
  }, [
    // 标题
    React.createElement('h1', {
      key: 'title',
      style: { marginBottom: '20px', color: '#00ff00' }
    }, 'Claude Code - Available Commands'),
    
    // 搜索框
    React.createElement('div', {
      key: 'search',
      style: { marginBottom: '15px' }
    }, [
      React.createElement('input', {
        key: 'search-input',
        type: 'text',
        placeholder: 'Search commands...',
        value: searchTerm,
        onChange: (e) => setSearchTerm(e.target.value),
        style: {
          padding: '8px',
          width: '300px',
          marginRight: '10px',
          backgroundColor: '#1a1a1a',
          color: '#ffffff',
          border: '1px solid #333'
        }
      }),
      React.createElement('select', {
        key: 'category-select',
        value: selectedCategory,
        onChange: (e) => setSelectedCategory(e.target.value),
        style: {
          padding: '8px',
          backgroundColor: '#1a1a1a',
          color: '#ffffff',
          border: '1px solid #333'
        }
      }, categories.map(category => 
        React.createElement('option', {
          key: category,
          value: category
        }, category === 'all' ? 'All Categories' : category)
      ))
    ]),
    
    // 命令列表
    React.createElement('div', {
      key: 'commands',
      style: { marginBottom: '20px' }
    }, Object.keys(filteredCommands).map(category => 
      React.createElement('div', {
        key: category,
        style: { marginBottom: '25px' }
      }, [
        React.createElement('h2', {
          key: 'category-title',
          style: { 
            color: '#ffff00', 
            marginBottom: '10px',
            borderBottom: '1px solid #333',
            paddingBottom: '5px'
          }
        }, category),
        
        React.createElement('div', {
          key: 'category-commands',
          style: { marginLeft: '10px' }
        }, filteredCommands[category].map(command => 
          React.createElement('div', {
            key: command.name,
            style: { 
              marginBottom: '10px',
              padding: '10px',
              backgroundColor: '#0a0a0a',
              border: '1px solid #333',
              borderRadius: '4px'
            }
          }, [
            React.createElement('div', {
              key: 'usage',
              style: { 
                color: '#00ffff',
                fontWeight: 'bold',
                marginBottom: '5px'
              }
            }, formatCommandUsage(command)),
            
            React.createElement('div', {
              key: 'description',
              style: { 
                color: '#cccccc',
                fontSize: '14px'
              }
            }, formatCommandDescription(command)),
            
            command.type && React.createElement('div', {
              key: 'type',
              style: { 
                color: '#888888',
                fontSize: '12px',
                marginTop: '5px'
              }
            }, `Type: ${command.type}`)
          ])
        ))
      ])
    )),
    
    // 底部帮助信息
    React.createElement('div', {
      key: 'footer',
      style: { 
        borderTop: '1px solid #333',
        paddingTop: '15px',
        color: '#888888',
        fontSize: '14px'
      }
    }, [
      React.createElement('p', { key: 'usage-info' }, 
        'Usage: Type /{command-name} followed by any required arguments'
      ),
      React.createElement('p', { key: 'help-info' }, 
        'For detailed help on a specific command, type: /{command-name} --help'
      ),
      React.createElement('p', { key: 'close-info' }, 
        'Press Escape to close this help screen'
      )
    ])
  ]);
}

/**
 * /help 命令定义
 * @original: 原始文件L37793-37813中的G$8对象
 */
export const helpCommand = {
  type: "local-jsx",
  name: "help",
  description: "Show help and available commands",
  isEnabled: () => true,
  isHidden: false,
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @param {string} args - 命令参数
   * @returns {Promise<React.Element>} React组件
   */
  async call(onDone, context, args) {
    const { options: { commands } } = context;
    
    // 如果指定了特定命令，显示该命令的详细帮助
    if (args && args.trim()) {
      const commandName = args.trim();
      const command = commands.find(cmd => cmd.name === commandName);
      
      if (command) {
        let helpText = `Help for /${command.name}\n\n`;
        helpText += `Description: ${formatCommandDescription(command)}\n`;
        helpText += `Usage: ${formatCommandUsage(command)}\n`;
        helpText += `Type: ${command.type}\n`;
        
        if (command.getDetailedHelp && typeof command.getDetailedHelp === 'function') {
          helpText += '\n' + command.getDetailedHelp();
        }
        
        onDone(helpText);
        return null;
      } else {
        onDone(`Command '${commandName}' not found. Use /help to see all available commands.`);
        return null;
      }
    }
    
    // 显示完整的帮助界面
    return React.createElement(HelpContent, {
      commands: commands || [],
      onClose: onDone
    });
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "help";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /help [command-name]

Show help and available commands.

Arguments:
  command-name    Optional. Show detailed help for a specific command

Examples:
  /help           # Show all available commands
  /help clear     # Show detailed help for the clear command
  /help config    # Show detailed help for the config command

The help interface includes:
- Search functionality to find specific commands
- Category filtering to browse commands by type
- Detailed descriptions and usage examples
- Keyboard shortcuts (Escape to close)
    `.trim();
  }
};
