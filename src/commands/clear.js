/**
 * /clear 命令实现
 * @original: 原始文件L35191-35205中的FH8对象和GH8函数
 */

/**
 * 清除对话历史的核心函数
 * @param {Object} context - 执行上下文
 * @returns {Promise<void>}
 * @original: 原始文件L35188-35190中的GH8函数调用
 */
async function clearConversationHistory(context) {
  try {
    // 清除消息历史
    if (context.messages) {
      context.messages.length = 0;
    }
    
    // 清除相关缓存
    if (typeof context.clearCache === 'function') {
      await context.clearCache();
    }
    
    // 清除会话状态
    if (typeof context.resetSession === 'function') {
      await context.resetSession();
    }
    
    // 触发UI更新
    if (typeof context.onHistoryCleared === 'function') {
      context.onHistoryCleared();
    }
    
    // 清除本地存储的对话数据
    try {
      if (typeof localStorage !== 'undefined') {
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (
            key.startsWith('conversation_') ||
            key.startsWith('chat_history_') ||
            key.startsWith('message_cache_')
          )) {
            keysToRemove.push(key);
          }
        }
        
        keysToRemove.forEach(key => {
          localStorage.removeItem(key);
        });
      }
    } catch (error) {
      console.warn('Failed to clear localStorage conversation data:', error);
    }
    
    // 清除会话存储的对话数据
    try {
      if (typeof sessionStorage !== 'undefined') {
        const keysToRemove = [];
        for (let i = 0; i < sessionStorage.length; i++) {
          const key = sessionStorage.key(i);
          if (key && (
            key.startsWith('conversation_') ||
            key.startsWith('chat_history_') ||
            key.startsWith('message_cache_')
          )) {
            keysToRemove.push(key);
          }
        }
        
        keysToRemove.forEach(key => {
          sessionStorage.removeItem(key);
        });
      }
    } catch (error) {
      console.warn('Failed to clear sessionStorage conversation data:', error);
    }
    
  } catch (error) {
    console.error('Error clearing conversation history:', error);
    throw error;
  }
}

/**
 * 获取清除确认消息
 * @returns {Promise<string[]>} 确认消息数组
 * @original: 原始文件L35188中的DU("clear")调用
 */
async function getClearConfirmationMessages() {
  const messages = [
    "This will clear your conversation history and free up context.",
    "All previous messages in this session will be permanently deleted.",
    "This action cannot be undone.",
    "",
    "Are you sure you want to continue? (y/N)"
  ];
  
  return messages;
}

/**
 * 处理用户确认输入
 * @param {string} input - 用户输入
 * @returns {boolean} 是否确认清除
 */
function parseConfirmationInput(input) {
  if (!input || typeof input !== 'string') {
    return false;
  }
  
  const normalized = input.trim().toLowerCase();
  return normalized === 'y' || normalized === 'yes';
}

/**
 * 清除操作的统计信息
 * @param {Object} context - 执行上下文
 * @returns {Object} 统计信息
 */
function getClearStats(context) {
  const stats = {
    messagesCleared: 0,
    cacheEntriesCleared: 0,
    storageKeysCleared: 0,
    timestamp: new Date().toISOString()
  };
  
  // 统计消息数量
  if (context.messages && Array.isArray(context.messages)) {
    stats.messagesCleared = context.messages.length;
  }
  
  // 统计缓存条目（如果有缓存管理器）
  if (context.cacheManager && typeof context.cacheManager.size === 'function') {
    stats.cacheEntriesCleared = context.cacheManager.size();
  }
  
  // 统计存储键数量
  try {
    if (typeof localStorage !== 'undefined') {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (
          key.startsWith('conversation_') ||
          key.startsWith('chat_history_') ||
          key.startsWith('message_cache_')
        )) {
          stats.storageKeysCleared++;
        }
      }
    }
  } catch (error) {
    // 忽略存储访问错误
  }
  
  return stats;
}

/**
 * 格式化清除结果消息
 * @param {Object} stats - 清除统计信息
 * @returns {string} 格式化的结果消息
 */
function formatClearResult(stats) {
  const parts = [];
  
  if (stats.messagesCleared > 0) {
    parts.push(`${stats.messagesCleared} messages`);
  }
  
  if (stats.cacheEntriesCleared > 0) {
    parts.push(`${stats.cacheEntriesCleared} cache entries`);
  }
  
  if (stats.storageKeysCleared > 0) {
    parts.push(`${stats.storageKeysCleared} storage keys`);
  }
  
  if (parts.length === 0) {
    return "Conversation history cleared (no data to remove)";
  }
  
  return `Conversation history cleared: ${parts.join(', ')} removed`;
}

/**
 * /clear 命令定义
 * @original: 原始文件L35191-35205中的FH8对象
 */
export const clearCommand = {
  type: "local",
  name: "clear",
  description: "Clear conversation history and free up context",
  aliases: ["reset"],
  isEnabled: () => true,
  isHidden: false,
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @param {string} args - 命令参数
   * @returns {Promise<string>} 执行结果
   */
  async call(onDone, context, args) {
    try {
      // 获取清除前的统计信息
      const statsBefore = getClearStats(context);
      
      // 如果有参数且为 --force 或 -f，跳过确认
      const forceMode = args && (args.trim() === '--force' || args.trim() === '-f');
      
      if (!forceMode) {
        // 获取确认消息
        const confirmationMessages = await getClearConfirmationMessages();
        
        // 如果上下文提供了确认机制，使用它
        if (typeof context.requestConfirmation === 'function') {
          const confirmed = await context.requestConfirmation(
            'Clear Conversation History',
            confirmationMessages.join('\n')
          );
          
          if (!confirmed) {
            return "Clear operation cancelled";
          }
        }
      }
      
      // 执行清除操作
      await clearConversationHistory(context);
      
      // 获取清除后的统计信息
      const statsAfter = getClearStats(context);
      const clearStats = {
        messagesCleared: statsBefore.messagesCleared,
        cacheEntriesCleared: statsBefore.cacheEntriesCleared,
        storageKeysCleared: statsBefore.storageKeysCleared,
        timestamp: new Date().toISOString()
      };
      
      // 格式化结果消息
      const resultMessage = formatClearResult(clearStats);
      
      // 记录清除操作
      if (typeof context.logOperation === 'function') {
        context.logOperation('clear', {
          stats: clearStats,
          forced: forceMode,
          timestamp: clearStats.timestamp
        });
      }
      
      return resultMessage;
      
    } catch (error) {
      console.error('Error executing clear command:', error);
      return `Error clearing conversation history: ${error.message}`;
    }
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "clear";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /clear [--force|-f]

Clear conversation history and free up context.

Options:
  --force, -f    Skip confirmation prompt and clear immediately

This command will:
- Remove all messages from the current conversation
- Clear related caches and temporary data
- Free up context space for new conversations
- Remove conversation data from local storage

Note: This action cannot be undone. All conversation history will be permanently lost.

Examples:
  /clear              # Clear with confirmation prompt
  /clear --force      # Clear immediately without confirmation
  /clear -f           # Same as --force
    `.trim();
  }
};
