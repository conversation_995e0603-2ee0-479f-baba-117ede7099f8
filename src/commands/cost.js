/**
 * /cost 命令实现
 * @original: 原始文件L36259-36273中的MH8对象和成本计算逻辑
 */

/**
 * 检查用户是否有订阅
 * @returns {boolean} 是否有订阅
 * @original: 原始文件L36266中的F9()函数调用
 */
function hasSubscription() {
  // 检查环境变量或配置
  if (process.env.CLAUDE_CODE_SUBSCRIPTION) {
    return true;
  }
  
  // 检查本地存储的订阅信息
  try {
    if (typeof localStorage !== 'undefined') {
      const userInfo = localStorage.getItem('userInfo');
      if (userInfo) {
        const parsed = JSON.parse(userInfo);
        return parsed.hasSubscription || parsed.subscriptionType === 'pro' || parsed.subscriptionType === 'max';
      }
    }
  } catch (error) {
    console.warn('Failed to check subscription status:', error);
  }
  
  return false;
}

/**
 * 获取订阅类型
 * @returns {string} 订阅类型
 * @original: 原始文件L36266中的A$1()函数调用
 */
function getSubscriptionType() {
  try {
    if (typeof localStorage !== 'undefined') {
      const userInfo = localStorage.getItem('userInfo');
      if (userInfo) {
        const parsed = JSON.parse(userInfo);
        return parsed.subscriptionType || 'Pro';
      }
    }
  } catch (error) {
    console.warn('Failed to get subscription type:', error);
  }
  
  return 'Pro';
}

/**
 * 计算API使用成本
 * @returns {Object} 成本信息
 * @original: 原始文件L36267中的r$0()函数调用
 */
function calculateUsageCost() {
  const costInfo = {
    totalCost: 0,
    totalTokens: 0,
    inputTokens: 0,
    outputTokens: 0,
    requestCount: 0,
    sessionDuration: 0,
    currency: 'USD',
    lastUpdated: new Date().toISOString()
  };
  
  try {
    // 从本地存储获取使用统计
    if (typeof localStorage !== 'undefined') {
      const usageStats = localStorage.getItem('apiUsageStats');
      if (usageStats) {
        const stats = JSON.parse(usageStats);
        
        costInfo.totalTokens = stats.totalTokens || 0;
        costInfo.inputTokens = stats.inputTokens || 0;
        costInfo.outputTokens = stats.outputTokens || 0;
        costInfo.requestCount = stats.requestCount || 0;
        costInfo.sessionDuration = stats.sessionDuration || 0;
        
        // 计算成本（基于Claude API定价）
        // 输入token: $0.003 per 1K tokens
        // 输出token: $0.015 per 1K tokens
        const inputCost = (costInfo.inputTokens / 1000) * 0.003;
        const outputCost = (costInfo.outputTokens / 1000) * 0.015;
        costInfo.totalCost = inputCost + outputCost;
      }
    }
    
    // 从会话存储获取当前会话的统计
    if (typeof sessionStorage !== 'undefined') {
      const sessionStats = sessionStorage.getItem('currentSessionStats');
      if (sessionStats) {
        const stats = JSON.parse(sessionStats);
        costInfo.sessionTokens = stats.tokens || 0;
        costInfo.sessionRequests = stats.requests || 0;
        costInfo.sessionCost = (stats.tokens / 1000) * 0.009; // 平均成本
      }
    }
    
  } catch (error) {
    console.warn('Failed to calculate usage cost:', error);
  }
  
  return costInfo;
}

/**
 * 格式化成本信息
 * @param {Object} costInfo - 成本信息
 * @returns {string} 格式化的成本报告
 */
function formatCostReport(costInfo) {
  const formatCurrency = (amount) => `$${amount.toFixed(4)}`;
  const formatNumber = (num) => num.toLocaleString();
  const formatDuration = (ms) => {
    const minutes = Math.floor(ms / 60000);
    const hours = Math.floor(minutes / 60);
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  };
  
  let report = `📊 API Usage and Cost Report\n`;
  report += `═══════════════════════════════════\n\n`;
  
  // 总体统计
  report += `💰 Total Cost: ${formatCurrency(costInfo.totalCost)}\n`;
  report += `🔢 Total Tokens: ${formatNumber(costInfo.totalTokens)}\n`;
  report += `📥 Input Tokens: ${formatNumber(costInfo.inputTokens)}\n`;
  report += `📤 Output Tokens: ${formatNumber(costInfo.outputTokens)}\n`;
  report += `📞 API Requests: ${formatNumber(costInfo.requestCount)}\n`;
  
  if (costInfo.sessionDuration > 0) {
    report += `⏱️  Session Duration: ${formatDuration(costInfo.sessionDuration)}\n`;
  }
  
  // 当前会话统计
  if (costInfo.sessionTokens || costInfo.sessionRequests) {
    report += `\n📋 Current Session:\n`;
    report += `   Tokens: ${formatNumber(costInfo.sessionTokens || 0)}\n`;
    report += `   Requests: ${formatNumber(costInfo.sessionRequests || 0)}\n`;
    report += `   Cost: ${formatCurrency(costInfo.sessionCost || 0)}\n`;
  }
  
  // 成本分析
  if (costInfo.totalCost > 0) {
    const avgCostPerRequest = costInfo.requestCount > 0 ? costInfo.totalCost / costInfo.requestCount : 0;
    const avgCostPerToken = costInfo.totalTokens > 0 ? costInfo.totalCost / costInfo.totalTokens : 0;
    
    report += `\n📈 Cost Analysis:\n`;
    report += `   Average per request: ${formatCurrency(avgCostPerRequest)}\n`;
    report += `   Average per token: ${formatCurrency(avgCostPerToken * 1000)} per 1K tokens\n`;
  }
  
  // 预估月度成本
  if (costInfo.sessionDuration > 0 && costInfo.totalCost > 0) {
    const costPerHour = costInfo.totalCost / (costInfo.sessionDuration / 3600000);
    const estimatedMonthlyCost = costPerHour * 24 * 30; // 假设每天24小时使用
    
    report += `\n📅 Estimated Monthly Cost:\n`;
    report += `   Based on current usage: ${formatCurrency(estimatedMonthlyCost)}\n`;
    report += `   (Assuming continuous usage)\n`;
  }
  
  report += `\n🕒 Last Updated: ${new Date(costInfo.lastUpdated).toLocaleString()}\n`;
  
  return report;
}

/**
 * 获取订阅消息
 * @param {string} subscriptionType - 订阅类型
 * @returns {string} 订阅消息
 */
function getSubscriptionMessage(subscriptionType) {
  return `🎉 With your ${subscriptionType} subscription, no need to monitor cost — your subscription includes Claude Code usage`;
}

/**
 * 重置使用统计
 * @returns {string} 重置结果消息
 */
function resetUsageStats() {
  try {
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('apiUsageStats');
    }
    
    if (typeof sessionStorage !== 'undefined') {
      sessionStorage.removeItem('currentSessionStats');
    }
    
    return 'Usage statistics have been reset';
  } catch (error) {
    return `Failed to reset usage statistics: ${error.message}`;
  }
}

/**
 * 导出使用统计
 * @returns {Object} 导出的统计数据
 */
function exportUsageStats() {
  const exportData = {
    exportDate: new Date().toISOString(),
    costInfo: calculateUsageCost(),
    systemInfo: {
      platform: process.platform,
      nodeVersion: process.version,
      claudeCodeVersion: process.env.CLAUDE_CODE_VERSION || 'unknown'
    }
  };
  
  return exportData;
}

/**
 * /cost 命令定义
 * @original: 原始文件L36259-36273中的MH8对象
 */
export const costCommand = {
  type: "local",
  name: "cost",
  description: "Show the total cost and duration of the current session",
  isEnabled: () => true,
  isHidden: false,
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @param {string} args - 命令参数
   * @returns {Promise<string>} 执行结果
   */
  async call(onDone, context, args) {
    try {
      // 处理命令参数
      const trimmedArgs = args ? args.trim().toLowerCase() : '';
      
      // 重置统计
      if (trimmedArgs === '--reset' || trimmedArgs === '-r') {
        return resetUsageStats();
      }
      
      // 导出统计
      if (trimmedArgs === '--export' || trimmedArgs === '-e') {
        const exportData = exportUsageStats();
        const filename = `claude-code-usage-${new Date().toISOString().split('T')[0]}.json`;
        
        // 如果在浏览器环境中，触发下载
        if (typeof window !== 'undefined' && window.document) {
          const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = filename;
          a.click();
          URL.revokeObjectURL(url);
          
          return `Usage statistics exported to ${filename}`;
        }
        
        // 在Node.js环境中，返回数据
        console.log('Usage statistics:', JSON.stringify(exportData, null, 2));
        return 'Usage statistics exported to console';
      }
      
      // 检查订阅状态
      if (hasSubscription()) {
        const subscriptionType = getSubscriptionType();
        return getSubscriptionMessage(subscriptionType);
      }
      
      // 计算并显示成本信息
      const costInfo = calculateUsageCost();
      return formatCostReport(costInfo);
      
    } catch (error) {
      console.error('Error in cost command:', error);
      return `Error calculating cost information: ${error.message}`;
    }
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "cost";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /cost [options]

Show the total cost and duration of the current session.

Options:
  --reset, -r     Reset usage statistics
  --export, -e    Export usage statistics to JSON file

This command displays:
- Total API usage cost in USD
- Token usage breakdown (input/output)
- Number of API requests made
- Session duration and current session stats
- Cost analysis and projections

For users with subscriptions:
- Shows subscription status instead of cost tracking
- Subscription includes Claude Code usage

Examples:
  /cost           # Show current usage and cost
  /cost --reset   # Reset all usage statistics
  /cost --export  # Export usage data to file

Note: Cost calculations are estimates based on standard API pricing and may not reflect actual billing.
    `.trim();
  }
};
