/**
 * /review 命令实现
 * @original: 原始文件L41875-41920中的Rh1对象
 */

import { githubService } from '../services/github-service.js';

/**
 * PR审查状态类型
 */
export const REVIEW_STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  CHANGES_REQUESTED: 'changes_requested',
  COMMENTED: 'commented'
};

/**
 * 审查优先级
 */
export const REVIEW_PRIORITY = {
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low'
};

/**
 * 代码质量检查项
 */
export const CODE_QUALITY_CHECKS = {
  CORRECTNESS: 'correctness',
  STYLE: 'style',
  PERFORMANCE: 'performance',
  SECURITY: 'security',
  TESTING: 'testing',
  DOCUMENTATION: 'documentation'
};

/**
 * 获取PR列表的提示文本
 * @param {string} toolName - 工具名称
 * @returns {string} 提示文本
 */
function getPRListPrompt(toolName) {
  return `Use ${toolName}("gh pr list") to show open pull requests in this repository.`;
}

/**
 * 获取PR详情的提示文本
 * @param {string} toolName - 工具名称
 * @param {string} prNumber - PR编号
 * @returns {string} 提示文本
 */
function getPRDetailsPrompt(toolName, prNumber) {
  return `Use ${toolName}("gh pr view ${prNumber}") to get detailed information about PR #${prNumber}.`;
}

/**
 * 获取PR差异的提示文本
 * @param {string} toolName - 工具名称
 * @param {string} prNumber - PR编号
 * @returns {string} 提示文本
 */
function getPRDiffPrompt(toolName, prNumber) {
  return `Use ${toolName}("gh pr diff ${prNumber}") to get the code changes in PR #${prNumber}.`;
}

/**
 * 生成代码审查提示文本
 * @param {string} args - 命令参数
 * @param {string} toolName - 工具名称
 * @returns {string} 完整的审查提示
 */
function generateReviewPrompt(args, toolName) {
  const prNumber = args?.trim();
  
  let prompt = `You are an expert code reviewer. Follow these steps:

`;

  if (!prNumber) {
    prompt += `1. ${getPRListPrompt(toolName)}
2. Ask the user to specify which PR they want to review
3. Once a PR number is provided, continue with the review process below

`;
  } else {
    prompt += `1. ${getPRDetailsPrompt(toolName, prNumber)}
2. ${getPRDiffPrompt(toolName, prNumber)}
3. Analyze the changes and provide a thorough code review

`;
  }

  prompt += `When reviewing the code, provide a comprehensive analysis that includes:

## Review Structure

### 📋 Overview
- Brief summary of what the PR accomplishes
- Main changes and their purpose
- Impact assessment (breaking changes, new features, bug fixes)

### 🔍 Code Quality Analysis
Evaluate each of these aspects:

**${CODE_QUALITY_CHECKS.CORRECTNESS}** - Code Correctness
- Logic accuracy and algorithmic soundness
- Edge case handling
- Error handling and validation
- Potential bugs or issues

**${CODE_QUALITY_CHECKS.STYLE}** - Code Style & Conventions
- Adherence to project coding standards
- Naming conventions and readability
- Code organization and structure
- Consistency with existing codebase

**${CODE_QUALITY_CHECKS.PERFORMANCE}** - Performance Implications
- Efficiency of algorithms and data structures
- Memory usage considerations
- Potential bottlenecks or optimizations
- Scalability concerns

**${CODE_QUALITY_CHECKS.SECURITY}** - Security Considerations
- Input validation and sanitization
- Authentication and authorization
- Data exposure risks
- Vulnerability assessments

**${CODE_QUALITY_CHECKS.TESTING}** - Test Coverage
- Adequacy of test cases
- Test quality and coverage
- Missing test scenarios
- Integration and unit test considerations

**${CODE_QUALITY_CHECKS.DOCUMENTATION}** - Documentation
- Code comments and inline documentation
- API documentation updates
- README or changelog updates
- Documentation clarity and completeness

### 💡 Specific Suggestions
Provide actionable feedback with:
- Line-specific comments where applicable
- Concrete improvement suggestions
- Alternative implementation approaches
- Best practice recommendations

### ⚠️ Risks & Concerns
Identify potential issues:
- Breaking changes and migration concerns
- Deployment risks
- Backward compatibility issues
- Dependencies and integration impacts

### ✅ Positive Aspects
Highlight what's done well:
- Good practices implemented
- Clean and maintainable code
- Effective solutions
- Thoughtful design decisions

## Review Guidelines

**Focus Areas:**
- Functionality and correctness
- Code maintainability and readability
- Performance and efficiency
- Security best practices
- Test coverage and quality
- Documentation completeness

**Review Tone:**
- Be constructive and helpful
- Provide specific, actionable feedback
- Balance criticism with positive observations
- Suggest improvements rather than just pointing out problems

**Format Requirements:**
- Use clear headings and bullet points
- Include code snippets when referencing specific issues
- Provide examples for suggested improvements
- Keep feedback concise but thorough

Remember to consider the context of the project, the complexity of the changes, and the experience level that might be appropriate for the feedback.`;

  return prompt;
}

/**
 * 验证GitHub CLI可用性
 * @returns {Promise<boolean>} GitHub CLI是否可用
 */
async function validateGitHubCLI() {
  try {
    const result = await githubService.checkGitHubCLI();
    return result.available;
  } catch (error) {
    return false;
  }
}

/**
 * 检查当前是否在Git仓库中
 * @returns {Promise<boolean>} 是否在Git仓库中
 */
async function validateGitRepository() {
  try {
    const result = await githubService.getCurrentRepository();
    return !!result.name;
  } catch (error) {
    return false;
  }
}

/**
 * 获取PR审查的前置条件检查结果
 * @returns {Promise<Object>} 检查结果
 */
async function getPrerequisiteChecks() {
  const checks = {
    githubCLI: await validateGitHubCLI(),
    gitRepository: await validateGitRepository(),
    errors: [],
    warnings: []
  };
  
  if (!checks.githubCLI) {
    checks.errors.push({
      title: 'GitHub CLI not available',
      message: 'GitHub CLI (gh) is required for PR review functionality.',
      instructions: [
        'Install GitHub CLI: https://cli.github.com/',
        'Authenticate with: gh auth login',
        'Verify installation with: gh --version'
      ]
    });
  }
  
  if (!checks.gitRepository) {
    checks.errors.push({
      title: 'Not in a Git repository',
      message: 'PR review requires being in a Git repository with GitHub remote.',
      instructions: [
        'Navigate to a Git repository directory',
        'Ensure the repository has a GitHub remote',
        'Verify with: git remote -v'
      ]
    });
  }
  
  return checks;
}

/**
 * 解析PR参数
 * @param {string} args - 命令参数
 * @returns {Object} 解析后的参数
 */
function parsePRArguments(args) {
  if (!args?.trim()) {
    return { prNumber: null, action: 'list' };
  }
  
  const trimmed = args.trim();
  
  // 检查是否是PR编号
  const prMatch = trimmed.match(/^#?(\d+)$/);
  if (prMatch) {
    return { 
      prNumber: prMatch[1], 
      action: 'review' 
    };
  }
  
  // 检查是否是PR URL
  const urlMatch = trimmed.match(/github\.com\/[^\/]+\/[^\/]+\/pull\/(\d+)/);
  if (urlMatch) {
    return { 
      prNumber: urlMatch[1], 
      action: 'review' 
    };
  }
  
  // 其他情况作为搜索查询
  return { 
    query: trimmed, 
    action: 'search' 
  };
}

/**
 * /review 命令定义
 * @original: 原始文件L41875-41920中的Rh1对象
 */
export const reviewCommand = {
  type: "prompt",
  name: "review",
  description: "Review a pull request",
  isEnabled: () => true,
  isHidden: false,
  progressMessage: "reviewing pull request",
  
  /**
   * 获取命令提示
   * @param {string} args - 命令参数
   * @param {Object} context - 执行上下文
   * @returns {Promise<Array>} 提示消息数组
   */
  async getPromptForCommand(args, context) {
    try {
      // 执行前置条件检查
      const checks = await getPrerequisiteChecks();
      
      if (checks.errors.length > 0) {
        const errorMessages = checks.errors.map(error => 
          `❌ ${error.title}: ${error.message}\n\nTo fix this:\n${error.instructions.map(inst => `• ${inst}`).join('\n')}`
        ).join('\n\n');
        
        return [{
          type: "text",
          text: `Cannot perform PR review due to the following issues:\n\n${errorMessages}\n\nPlease resolve these issues and try again.`
        }];
      }
      
      // 解析参数
      const parsedArgs = parsePRArguments(args);
      
      // 获取工具名称（用于生成工具调用指令）
      const toolName = context?.tools?.find(tool => tool.name === 'Bash')?.name || 'Bash';
      
      // 生成审查提示
      const reviewPrompt = generateReviewPrompt(parsedArgs.prNumber, toolName);
      
      // 添加上下文信息
      let contextInfo = '';
      if (checks.warnings.length > 0) {
        const warningMessages = checks.warnings.map(warning => 
          `⚠️ ${warning.title}: ${warning.message}`
        ).join('\n');
        contextInfo = `\nNote: ${warningMessages}\n\n`;
      }
      
      return [{
        type: "text",
        text: contextInfo + reviewPrompt
      }];
      
    } catch (error) {
      return [{
        type: "text",
        text: `Error setting up PR review: ${error.message}\n\nPlease ensure you have:\n• GitHub CLI installed and authenticated\n• Access to the repository\n• Proper permissions to view pull requests`
      }];
    }
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "review";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /review [pr-number|pr-url]

Review a GitHub pull request with comprehensive code analysis.

Examples:
  /review                    # List open PRs and select one to review
  /review 123               # Review PR #123
  /review #456              # Review PR #456 (with # prefix)
  /review https://github.com/owner/repo/pull/789  # Review PR from URL

Prerequisites:
- GitHub CLI (gh) must be installed and authenticated
- Must be in a Git repository with GitHub remote
- Appropriate permissions to view the repository and PRs

Review Process:
1. Fetches PR details and metadata
2. Analyzes code changes and diff
3. Provides comprehensive review covering:
   • Code correctness and logic
   • Style and conventions adherence
   • Performance implications
   • Security considerations
   • Test coverage assessment
   • Documentation completeness

Review Output Includes:
- Executive summary of changes
- Detailed code quality analysis
- Specific line-by-line feedback
- Improvement suggestions
- Risk assessment
- Positive aspects recognition

The review follows industry best practices and provides actionable, constructive feedback to help improve code quality and maintainability.

Note: This command requires network access to fetch PR data from GitHub.
    `.trim();
  }
};
