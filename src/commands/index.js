/**
 * CLI命令管理器 - 统一导出所有命令
 * @original: 原始文件L50464-50470中的ImB函数和命令注册逻辑
 */

// 导入已完整实现的命令
import { addDirCommand } from './add-dir.js';
import { bugCommand } from './bug.js';
import { clearCommand } from './clear.js';
import { compactCommand } from './compact.js';
import { configCommand } from './config.js';
import { costCommand } from './cost.js';
import { doctorCommand } from './doctor.js';
import { helpCommand } from './help.js';
import { loginCommand } from './login.js';
import { mcpCommand } from './mcp.js';
import { modelCommand } from './model.js';
import { permissionsCommand } from './permissions.js';
import { statusCommand } from './status.js';

// 导入模板中的剩余命令（待完整实现）
import {
  agentsCommand,
  bashesCommand,
  exportCommand,
  hooksCommand,
  ideCommand,
  initCommand,
  installGithubAppCommand,
  logoutCommand,
  memoryCommand,
  prCommentsCommand,
  releaseNotesCommand,
  resumeCommand,
  reviewCommand,
  securityReviewCommand,
  statuslineCommand,
  terminalSetupCommand,
  upgradeCommand,
  vimCommand
} from './_template.js';

/**
 * 所有内置命令的列表
 * @original: 原始文件L50464中的ImB函数
 */
export const builtInCommands = [
  addDirCommand,
  agentsCommand,
  bashesCommand,
  bugCommand,
  clearCommand,
  compactCommand,
  configCommand,
  costCommand,
  doctorCommand,
  exportCommand,
  helpCommand,
  hooksCommand,
  ideCommand,
  initCommand,
  installGithubAppCommand,
  loginCommand,
  logoutCommand,
  mcpCommand,
  memoryCommand,
  modelCommand,
  permissionsCommand,
  prCommentsCommand,
  releaseNotesCommand,
  resumeCommand,
  reviewCommand,
  securityReviewCommand,
  statusCommand,
  statuslineCommand,
  terminalSetupCommand,
  upgradeCommand,
  vimCommand
];

/**
 * 获取所有内置命令名称的集合
 * @original: 原始文件L50465中的HuB函数
 */
export const builtInCommandNames = new Set(builtInCommands.map(command => command.name));

/**
 * 命令管理器类
 */
export class CommandManager {
  constructor() {
    this.commands = new Map();
    this.aliases = new Map();
    this.customCommands = new Map();
    
    // 注册所有内置命令
    this.registerBuiltInCommands();
  }
  
  /**
   * 注册所有内置命令
   * @private
   */
  registerBuiltInCommands() {
    for (const command of builtInCommands) {
      this.registerCommand(command);
    }
  }
  
  /**
   * 注册单个命令
   * @param {Object} command - 命令对象
   */
  registerCommand(command) {
    if (!command.name) {
      throw new Error('Command must have a name');
    }
    
    // 注册主命令名
    this.commands.set(command.name, command);
    
    // 注册别名
    if (command.aliases && Array.isArray(command.aliases)) {
      for (const alias of command.aliases) {
        this.aliases.set(alias, command.name);
      }
    }
  }
  
  /**
   * 获取命令
   * @param {string} name - 命令名或别名
   * @returns {Object|null} 命令对象
   */
  getCommand(name) {
    // 先检查直接命令名
    if (this.commands.has(name)) {
      return this.commands.get(name);
    }
    
    // 再检查别名
    if (this.aliases.has(name)) {
      const commandName = this.aliases.get(name);
      return this.commands.get(commandName);
    }
    
    // 检查自定义命令
    if (this.customCommands.has(name)) {
      return this.customCommands.get(name);
    }
    
    return null;
  }
  
  /**
   * 获取所有可用命令
   * @param {boolean} includeHidden - 是否包含隐藏命令
   * @returns {Array} 命令列表
   */
  getAllCommands(includeHidden = false) {
    const commands = [
      ...Array.from(this.commands.values()),
      ...Array.from(this.customCommands.values())
    ];
    
    return commands.filter(command => {
      if (!includeHidden && command.isHidden) {
        return false;
      }
      
      // 检查命令是否启用
      if (typeof command.isEnabled === 'function') {
        return command.isEnabled();
      }
      
      return true;
    });
  }
  
  /**
   * 获取命令帮助信息
   * @param {string} commandName - 命令名
   * @returns {Object|null} 帮助信息
   */
  getCommandHelp(commandName) {
    const command = this.getCommand(commandName);
    if (!command) {
      return null;
    }
    
    return {
      name: command.name,
      description: typeof command.description === 'function' 
        ? command.description() 
        : command.description,
      argumentHint: command.argumentHint || '',
      aliases: command.aliases || [],
      type: command.type,
      isEnabled: typeof command.isEnabled === 'function' 
        ? command.isEnabled() 
        : true
    };
  }
  
  /**
   * 检查命令是否存在
   * @param {string} name - 命令名
   * @returns {boolean} 是否存在
   */
  hasCommand(name) {
    return this.getCommand(name) !== null;
  }
  
  /**
   * 注册自定义命令
   * @param {Object} command - 自定义命令对象
   */
  registerCustomCommand(command) {
    if (!command.name) {
      throw new Error('Custom command must have a name');
    }
    
    this.customCommands.set(command.name, command);
    
    // 注册别名
    if (command.aliases && Array.isArray(command.aliases)) {
      for (const alias of command.aliases) {
        this.aliases.set(alias, command.name);
      }
    }
  }
  
  /**
   * 移除自定义命令
   * @param {string} name - 命令名
   * @returns {boolean} 是否成功移除
   */
  removeCustomCommand(name) {
    const command = this.customCommands.get(name);
    if (!command) {
      return false;
    }
    
    // 移除别名
    if (command.aliases && Array.isArray(command.aliases)) {
      for (const alias of command.aliases) {
        this.aliases.delete(alias);
      }
    }
    
    return this.customCommands.delete(name);
  }
  
  /**
   * 获取命令统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const builtInCount = this.commands.size;
    const customCount = this.customCommands.size;
    const aliasCount = this.aliases.size;
    const enabledCommands = this.getAllCommands(false);
    
    return {
      total: builtInCount + customCount,
      builtIn: builtInCount,
      custom: customCount,
      aliases: aliasCount,
      enabled: enabledCommands.length,
      disabled: (builtInCount + customCount) - enabledCommands.length
    };
  }
  
  /**
   * 清除所有自定义命令
   */
  clearCustomCommands() {
    // 移除自定义命令的别名
    for (const command of this.customCommands.values()) {
      if (command.aliases && Array.isArray(command.aliases)) {
        for (const alias of command.aliases) {
          this.aliases.delete(alias);
        }
      }
    }
    
    this.customCommands.clear();
  }
}

// 创建默认命令管理器实例
export const commandManager = new CommandManager();

/**
 * 获取命令的简单函数
 * @param {string} name - 命令名
 * @returns {Object|null} 命令对象
 */
export function getCommand(name) {
  return commandManager.getCommand(name);
}

/**
 * 获取所有命令的简单函数
 * @param {boolean} includeHidden - 是否包含隐藏命令
 * @returns {Array} 命令列表
 */
export function getAllCommands(includeHidden = false) {
  return commandManager.getAllCommands(includeHidden);
}

/**
 * 注册自定义命令的简单函数
 * @param {Object} command - 命令对象
 */
export function registerCustomCommand(command) {
  return commandManager.registerCustomCommand(command);
}

/**
 * 检查命令是否存在的简单函数
 * @param {string} name - 命令名
 * @returns {boolean} 是否存在
 */
export function hasCommand(name) {
  return commandManager.hasCommand(name);
}

/**
 * 获取命令帮助信息的简单函数
 * @param {string} commandName - 命令名
 * @returns {Object|null} 帮助信息
 */
export function getCommandHelp(commandName) {
  return commandManager.getCommandHelp(commandName);
}
