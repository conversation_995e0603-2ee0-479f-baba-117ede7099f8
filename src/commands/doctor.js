/**
 * /doctor 命令实现
 * @original: 原始文件L36675-36691中的TH8对象和诊断逻辑
 */

import React from 'react';
import { execSync } from 'child_process';
import { existsSync, accessSync, constants } from 'fs';
import { resolve } from 'path';

/**
 * 诊断检查项目类型
 */
export const DIAGNOSTIC_TYPES = {
  SYSTEM: 'system',
  INSTALLATION: 'installation',
  CONFIGURATION: 'configuration',
  NETWORK: 'network',
  PERMISSIONS: 'permissions',
  DEPENDENCIES: 'dependencies'
};

/**
 * 诊断结果状态
 */
export const DIAGNOSTIC_STATUS = {
  PASS: 'pass',
  WARN: 'warn',
  FAIL: 'fail',
  INFO: 'info'
};

/**
 * 系统诊断检查
 * @returns {Array} 诊断结果数组
 */
function runSystemDiagnostics() {
  const diagnostics = [];
  
  // Node.js版本检查
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.match(/^v(\d+)/)[1]);
  
  diagnostics.push({
    type: DIAGNOSTIC_TYPES.SYSTEM,
    name: 'Node.js Version',
    status: majorVersion >= 18 ? DIAGNOSTIC_STATUS.PASS : DIAGNOSTIC_STATUS.FAIL,
    message: `Node.js ${nodeVersion}`,
    details: majorVersion >= 18 ? 'Supported version' : 'Requires Node.js 18 or higher',
    recommendation: majorVersion < 18 ? 'Please upgrade to Node.js 18 or higher' : null
  });
  
  // 平台检查
  diagnostics.push({
    type: DIAGNOSTIC_TYPES.SYSTEM,
    name: 'Platform',
    status: DIAGNOSTIC_STATUS.INFO,
    message: `${process.platform} ${process.arch}`,
    details: `Running on ${process.platform}`,
    recommendation: null
  });
  
  // 内存检查
  const memoryUsage = process.memoryUsage();
  const memoryMB = Math.round(memoryUsage.rss / 1024 / 1024);
  
  diagnostics.push({
    type: DIAGNOSTIC_TYPES.SYSTEM,
    name: 'Memory Usage',
    status: memoryMB < 500 ? DIAGNOSTIC_STATUS.PASS : memoryMB < 1000 ? DIAGNOSTIC_STATUS.WARN : DIAGNOSTIC_STATUS.FAIL,
    message: `${memoryMB} MB`,
    details: `RSS: ${memoryMB} MB, Heap: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB`,
    recommendation: memoryMB > 1000 ? 'High memory usage detected. Consider restarting Claude Code.' : null
  });
  
  return diagnostics;
}

/**
 * 安装诊断检查
 * @returns {Array} 诊断结果数组
 */
function runInstallationDiagnostics() {
  const diagnostics = [];
  
  // Claude Code版本检查
  const version = process.env.CLAUDE_CODE_VERSION || 'unknown';
  diagnostics.push({
    type: DIAGNOSTIC_TYPES.INSTALLATION,
    name: 'Claude Code Version',
    status: version !== 'unknown' ? DIAGNOSTIC_STATUS.PASS : DIAGNOSTIC_STATUS.WARN,
    message: version,
    details: version !== 'unknown' ? 'Version detected' : 'Version not detected',
    recommendation: version === 'unknown' ? 'Version information may be missing' : null
  });
  
  // 可执行文件检查
  try {
    const claudePath = process.argv[0];
    if (existsSync(claudePath)) {
      accessSync(claudePath, constants.X_OK);
      diagnostics.push({
        type: DIAGNOSTIC_TYPES.INSTALLATION,
        name: 'Executable',
        status: DIAGNOSTIC_STATUS.PASS,
        message: 'Accessible',
        details: `Located at: ${claudePath}`,
        recommendation: null
      });
    } else {
      diagnostics.push({
        type: DIAGNOSTIC_TYPES.INSTALLATION,
        name: 'Executable',
        status: DIAGNOSTIC_STATUS.FAIL,
        message: 'Not found',
        details: `Expected at: ${claudePath}`,
        recommendation: 'Reinstall Claude Code'
      });
    }
  } catch (error) {
    diagnostics.push({
      type: DIAGNOSTIC_TYPES.INSTALLATION,
      name: 'Executable',
      status: DIAGNOSTIC_STATUS.FAIL,
      message: 'Access error',
      details: error.message,
      recommendation: 'Check file permissions'
    });
  }
  
  return diagnostics;
}

/**
 * 配置诊断检查
 * @returns {Array} 诊断结果数组
 */
function runConfigurationDiagnostics() {
  const diagnostics = [];
  
  // 配置目录检查
  const configDir = resolve(process.env.HOME || process.env.USERPROFILE || '.', '.claude');
  
  if (existsSync(configDir)) {
    diagnostics.push({
      type: DIAGNOSTIC_TYPES.CONFIGURATION,
      name: 'Config Directory',
      status: DIAGNOSTIC_STATUS.PASS,
      message: 'Found',
      details: `Located at: ${configDir}`,
      recommendation: null
    });
    
    // 设置文件检查
    const settingsFile = resolve(configDir, 'settings.json');
    if (existsSync(settingsFile)) {
      try {
        const settings = require(settingsFile);
        diagnostics.push({
          type: DIAGNOSTIC_TYPES.CONFIGURATION,
          name: 'Settings File',
          status: DIAGNOSTIC_STATUS.PASS,
          message: 'Valid',
          details: `${Object.keys(settings).length} settings found`,
          recommendation: null
        });
      } catch (error) {
        diagnostics.push({
          type: DIAGNOSTIC_TYPES.CONFIGURATION,
          name: 'Settings File',
          status: DIAGNOSTIC_STATUS.FAIL,
          message: 'Invalid JSON',
          details: error.message,
          recommendation: 'Fix JSON syntax in settings file'
        });
      }
    } else {
      diagnostics.push({
        type: DIAGNOSTIC_TYPES.CONFIGURATION,
        name: 'Settings File',
        status: DIAGNOSTIC_STATUS.WARN,
        message: 'Not found',
        details: 'Using default settings',
        recommendation: 'Run /config to create settings file'
      });
    }
  } else {
    diagnostics.push({
      type: DIAGNOSTIC_TYPES.CONFIGURATION,
      name: 'Config Directory',
      status: DIAGNOSTIC_STATUS.WARN,
      message: 'Not found',
      details: 'Will be created on first use',
      recommendation: null
    });
  }
  
  return diagnostics;
}

/**
 * 网络诊断检查
 * @returns {Promise<Array>} 诊断结果数组
 */
async function runNetworkDiagnostics() {
  const diagnostics = [];
  
  // API连接检查
  try {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'HEAD',
      timeout: 5000
    });
    
    diagnostics.push({
      type: DIAGNOSTIC_TYPES.NETWORK,
      name: 'API Connectivity',
      status: response.ok ? DIAGNOSTIC_STATUS.PASS : DIAGNOSTIC_STATUS.WARN,
      message: response.ok ? 'Connected' : `HTTP ${response.status}`,
      details: `Response time: ${response.headers.get('x-response-time') || 'unknown'}`,
      recommendation: !response.ok ? 'Check network connection and API status' : null
    });
  } catch (error) {
    diagnostics.push({
      type: DIAGNOSTIC_TYPES.NETWORK,
      name: 'API Connectivity',
      status: DIAGNOSTIC_STATUS.FAIL,
      message: 'Connection failed',
      details: error.message,
      recommendation: 'Check internet connection and firewall settings'
    });
  }
  
  // DNS解析检查
  try {
    const dns = require('dns').promises;
    await dns.lookup('api.anthropic.com');
    
    diagnostics.push({
      type: DIAGNOSTIC_TYPES.NETWORK,
      name: 'DNS Resolution',
      status: DIAGNOSTIC_STATUS.PASS,
      message: 'Working',
      details: 'Can resolve api.anthropic.com',
      recommendation: null
    });
  } catch (error) {
    diagnostics.push({
      type: DIAGNOSTIC_TYPES.NETWORK,
      name: 'DNS Resolution',
      status: DIAGNOSTIC_STATUS.FAIL,
      message: 'Failed',
      details: error.message,
      recommendation: 'Check DNS settings'
    });
  }
  
  return diagnostics;
}

/**
 * 权限诊断检查
 * @returns {Array} 诊断结果数组
 */
function runPermissionDiagnostics() {
  const diagnostics = [];
  
  // 当前工作目录权限
  try {
    const cwd = process.cwd();
    accessSync(cwd, constants.R_OK | constants.W_OK);
    
    diagnostics.push({
      type: DIAGNOSTIC_TYPES.PERMISSIONS,
      name: 'Working Directory',
      status: DIAGNOSTIC_STATUS.PASS,
      message: 'Read/Write access',
      details: `Current directory: ${cwd}`,
      recommendation: null
    });
  } catch (error) {
    diagnostics.push({
      type: DIAGNOSTIC_TYPES.PERMISSIONS,
      name: 'Working Directory',
      status: DIAGNOSTIC_STATUS.FAIL,
      message: 'Access denied',
      details: error.message,
      recommendation: 'Check directory permissions'
    });
  }
  
  // 临时目录权限
  try {
    const tmpDir = require('os').tmpdir();
    accessSync(tmpDir, constants.R_OK | constants.W_OK);
    
    diagnostics.push({
      type: DIAGNOSTIC_TYPES.PERMISSIONS,
      name: 'Temp Directory',
      status: DIAGNOSTIC_STATUS.PASS,
      message: 'Accessible',
      details: `Temp directory: ${tmpDir}`,
      recommendation: null
    });
  } catch (error) {
    diagnostics.push({
      type: DIAGNOSTIC_TYPES.PERMISSIONS,
      name: 'Temp Directory',
      status: DIAGNOSTIC_STATUS.FAIL,
      message: 'Access denied',
      details: error.message,
      recommendation: 'Check temp directory permissions'
    });
  }
  
  return diagnostics;
}

/**
 * 依赖诊断检查
 * @returns {Array} 诊断结果数组
 */
function runDependencyDiagnostics() {
  const diagnostics = [];
  
  // 检查关键命令
  const commands = ['git', 'npm', 'node'];
  
  commands.forEach(cmd => {
    try {
      const version = execSync(`${cmd} --version`, { encoding: 'utf8', timeout: 5000 }).trim();
      diagnostics.push({
        type: DIAGNOSTIC_TYPES.DEPENDENCIES,
        name: cmd.toUpperCase(),
        status: DIAGNOSTIC_STATUS.PASS,
        message: 'Available',
        details: version,
        recommendation: null
      });
    } catch (error) {
      diagnostics.push({
        type: DIAGNOSTIC_TYPES.DEPENDENCIES,
        name: cmd.toUpperCase(),
        status: DIAGNOSTIC_STATUS.WARN,
        message: 'Not found',
        details: `Command '${cmd}' not found in PATH`,
        recommendation: `Install ${cmd} for full functionality`
      });
    }
  });
  
  return diagnostics;
}

/**
 * 诊断显示组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L36685-36688中的Pf1组件
 */
function DiagnosticDisplay({ onDone }) {
  const [diagnostics, setDiagnostics] = React.useState([]);
  const [isRunning, setIsRunning] = React.useState(true);
  const [progress, setProgress] = React.useState(0);
  
  React.useEffect(() => {
    runAllDiagnostics();
  }, []);
  
  const runAllDiagnostics = async () => {
    setIsRunning(true);
    setProgress(0);
    
    const allDiagnostics = [];
    
    // 运行各种诊断
    const diagnosticFunctions = [
      { name: 'System', fn: runSystemDiagnostics },
      { name: 'Installation', fn: runInstallationDiagnostics },
      { name: 'Configuration', fn: runConfigurationDiagnostics },
      { name: 'Network', fn: runNetworkDiagnostics },
      { name: 'Permissions', fn: runPermissionDiagnostics },
      { name: 'Dependencies', fn: runDependencyDiagnostics }
    ];
    
    for (let i = 0; i < diagnosticFunctions.length; i++) {
      const { name, fn } = diagnosticFunctions[i];
      setProgress(((i + 1) / diagnosticFunctions.length) * 100);
      
      try {
        const results = await fn();
        allDiagnostics.push(...results);
      } catch (error) {
        allDiagnostics.push({
          type: DIAGNOSTIC_TYPES.SYSTEM,
          name: `${name} Diagnostics`,
          status: DIAGNOSTIC_STATUS.FAIL,
          message: 'Error',
          details: error.message,
          recommendation: 'Check system configuration'
        });
      }
      
      setDiagnostics([...allDiagnostics]);
    }
    
    setIsRunning(false);
  };
  
  const getStatusIcon = (status) => {
    switch (status) {
      case DIAGNOSTIC_STATUS.PASS: return '✅';
      case DIAGNOSTIC_STATUS.WARN: return '⚠️';
      case DIAGNOSTIC_STATUS.FAIL: return '❌';
      case DIAGNOSTIC_STATUS.INFO: return 'ℹ️';
      default: return '❓';
    }
  };
  
  const getStatusColor = (status) => {
    switch (status) {
      case DIAGNOSTIC_STATUS.PASS: return '#00ff00';
      case DIAGNOSTIC_STATUS.WARN: return '#ffff00';
      case DIAGNOSTIC_STATUS.FAIL: return '#ff0000';
      case DIAGNOSTIC_STATUS.INFO: return '#00ffff';
      default: return '#ffffff';
    }
  };
  
  const groupedDiagnostics = diagnostics.reduce((groups, diagnostic) => {
    if (!groups[diagnostic.type]) {
      groups[diagnostic.type] = [];
    }
    groups[diagnostic.type].push(diagnostic);
    return groups;
  }, {});
  
  const handleKeyPress = React.useCallback((event) => {
    if (event.key === 'Escape') {
      onDone();
    } else if (event.key === 'r' || event.key === 'R') {
      runAllDiagnostics();
    }
  }, [onDone]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [handleKeyPress]);
  
  return React.createElement('div', {
    style: {
      padding: '20px',
      maxWidth: '800px',
      margin: '0 auto',
      fontFamily: 'monospace'
    }
  }, [
    // 标题
    React.createElement('h1', {
      key: 'title',
      style: { marginBottom: '20px', color: '#00ff00' }
    }, '🏥 Claude Code Doctor'),
    
    // 进度条
    isRunning && React.createElement('div', {
      key: 'progress',
      style: { marginBottom: '20px' }
    }, [
      React.createElement('div', {
        key: 'progress-text',
        style: { marginBottom: '5px', color: '#ffff00' }
      }, `Running diagnostics... ${Math.round(progress)}%`),
      React.createElement('div', {
        key: 'progress-bar',
        style: {
          width: '100%',
          height: '10px',
          backgroundColor: '#333',
          borderRadius: '5px',
          overflow: 'hidden'
        }
      }, React.createElement('div', {
        style: {
          width: `${progress}%`,
          height: '100%',
          backgroundColor: '#00ff00',
          transition: 'width 0.3s ease'
        }
      }))
    ]),
    
    // 诊断结果
    Object.keys(groupedDiagnostics).map(type => 
      React.createElement('div', {
        key: type,
        style: { marginBottom: '25px' }
      }, [
        React.createElement('h2', {
          key: 'type-title',
          style: { 
            color: '#ffff00', 
            marginBottom: '10px',
            textTransform: 'capitalize'
          }
        }, type.replace('_', ' ')),
        
        React.createElement('div', {
          key: 'type-diagnostics',
          style: { marginLeft: '10px' }
        }, groupedDiagnostics[type].map((diagnostic, index) => 
          React.createElement('div', {
            key: index,
            style: {
              marginBottom: '10px',
              padding: '10px',
              backgroundColor: '#0a0a0a',
              border: '1px solid #333',
              borderRadius: '4px'
            }
          }, [
            React.createElement('div', {
              key: 'header',
              style: { 
                display: 'flex',
                alignItems: 'center',
                marginBottom: '5px'
              }
            }, [
              React.createElement('span', {
                key: 'icon',
                style: { marginRight: '10px' }
              }, getStatusIcon(diagnostic.status)),
              React.createElement('span', {
                key: 'name',
                style: { 
                  fontWeight: 'bold',
                  color: getStatusColor(diagnostic.status)
                }
              }, diagnostic.name),
              React.createElement('span', {
                key: 'message',
                style: { 
                  marginLeft: '10px',
                  color: '#ffffff'
                }
              }, diagnostic.message)
            ]),
            
            diagnostic.details && React.createElement('div', {
              key: 'details',
              style: { 
                color: '#cccccc',
                fontSize: '12px',
                marginBottom: '5px'
              }
            }, diagnostic.details),
            
            diagnostic.recommendation && React.createElement('div', {
              key: 'recommendation',
              style: { 
                color: '#ff8800',
                fontSize: '12px',
                fontStyle: 'italic'
              }
            }, `💡 ${diagnostic.recommendation}`)
          ])
        ))
      ])
    ),
    
    // 底部控制
    !isRunning && React.createElement('div', {
      key: 'controls',
      style: {
        borderTop: '1px solid #333',
        paddingTop: '15px',
        textAlign: 'center',
        color: '#888888'
      }
    }, 'Press R to re-run diagnostics • Press Escape to close')
  ]);
}

/**
 * /doctor 命令定义
 * @original: 原始文件L36675-36691中的TH8对象
 */
export const doctorCommand = {
  name: "doctor",
  description: "Diagnose and verify your Claude Code installation and settings",
  isEnabled: () => !process.env.DISABLE_DOCTOR_COMMAND,
  isHidden: false,
  type: "local-jsx",
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "doctor";
  },
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @param {string} args - 命令参数
   * @returns {Promise<React.Element>} React组件
   */
  call(onDone) {
    const diagnosticComponent = React.createElement(DiagnosticDisplay, {
      onDone
    });
    
    return Promise.resolve(diagnosticComponent);
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /doctor

Diagnose and verify your Claude Code installation and settings.

This command performs comprehensive diagnostics including:

System Checks:
- Node.js version compatibility
- Platform and architecture
- Memory usage analysis

Installation Checks:
- Claude Code version detection
- Executable file accessibility
- Installation integrity

Configuration Checks:
- Config directory existence
- Settings file validation
- Configuration completeness

Network Checks:
- API connectivity testing
- DNS resolution verification
- Network latency measurement

Permission Checks:
- Working directory access
- Temp directory permissions
- File system capabilities

Dependency Checks:
- Required command availability (git, npm, node)
- Version compatibility
- PATH configuration

Interactive controls:
- Press R to re-run all diagnostics
- Press Escape to close the diagnostic display

The doctor command helps identify and resolve common issues with your Claude Code installation.
    `.trim();
  }
};
