/**
 * /resume 命令实现
 * @original: 原始文件L41854-41874中的L$8对象和L41806-41853中的N$8组件
 */

import React from 'react';
import { sessionService } from '../services/session-service.js';

/**
 * 对话状态类型
 */
export const CONVERSATION_STATUS = {
  ACTIVE: 'active',
  BOOKMARKED: 'bookmarked',
  SIDECHAIN: 'sidechain',
  ARCHIVED: 'archived'
};

/**
 * 格式化时间戳
 * @param {Date|string|number} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 * @original: 原始文件L41760-41763中的ix1函数调用
 */
function formatTimestamp(timestamp) {
  if (!timestamp) return '-';
  
  const date = new Date(timestamp);
  if (isNaN(date.getTime())) return '-';
  
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    // 今天 - 显示时间
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  } else if (diffDays === 1) {
    return 'Yesterday';
  } else if (diffDays < 7) {
    return `${diffDays}d ago`;
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return `${weeks}w ago`;
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30);
    return `${months}mo ago`;
  } else {
    const years = Math.floor(diffDays / 365);
    return `${years}y ago`;
  }
}

/**
 * 验证会话ID格式
 * @param {string} sessionId - 会话ID
 * @returns {boolean} 是否为有效的UUID格式
 * @original: 原始文件L5826-5829中的PK函数
 */
function isValidSessionId(sessionId) {
  if (typeof sessionId !== 'string') return false;
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(sessionId);
}

/**
 * 获取终端尺寸
 * @returns {Object} 终端尺寸信息
 */
function getTerminalSize() {
  return {
    columns: process.stdout.columns || 80,
    rows: process.stdout.rows || 24
  };
}

/**
 * 模拟获取对话历史列表
 * @returns {Promise<Array>} 对话历史数组
 * @original: 原始文件L6212-6218中的Ao函数
 */
async function getConversationHistory() {
  try {
    // 这里应该从实际的存储中获取对话历史
    // 为了演示，返回模拟数据
    const mockConversations = [
      {
        sessionId: '550e8400-e29b-41d4-a716-************',
        created: new Date(Date.now() - 86400000), // 1天前
        modified: new Date(Date.now() - 3600000), // 1小时前
        messageCount: 15,
        summary: 'Refactoring JavaScript CLI commands',
        firstPrompt: 'Help me refactor this JavaScript code',
        gitBranch: 'feature/refactor',
        isBookmarked: true,
        isSidechain: false,
        messages: [
          { sessionId: '550e8400-e29b-41d4-a716-************', type: 'user', content: 'Help me refactor this JavaScript code' }
        ]
      },
      {
        sessionId: '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
        created: new Date(Date.now() - 172800000), // 2天前
        modified: new Date(Date.now() - 7200000), // 2小时前
        messageCount: 8,
        summary: 'React component optimization',
        firstPrompt: 'How to optimize React components?',
        gitBranch: 'main',
        isBookmarked: false,
        isSidechain: false,
        messages: [
          { sessionId: '6ba7b810-9dad-11d1-80b4-00c04fd430c8', type: 'user', content: 'How to optimize React components?' }
        ]
      },
      {
        sessionId: '6ba7b811-9dad-11d1-80b4-00c04fd430c8',
        created: new Date(Date.now() - 259200000), // 3天前
        modified: new Date(Date.now() - 10800000), // 3小时前
        messageCount: 22,
        summary: 'Database schema design',
        firstPrompt: 'Design a database schema for user management',
        gitBranch: 'develop',
        isBookmarked: false,
        isSidechain: true, // 这个会被过滤掉
        messages: [
          { sessionId: '6ba7b811-9dad-11d1-80b4-00c04fd430c8', type: 'user', content: 'Design a database schema for user management' }
        ]
      }
    ];
    
    // 按修改时间排序，书签优先
    return mockConversations
      .sort((a, b) => {
        if (a.isBookmarked && !b.isBookmarked) return -1;
        if (!a.isBookmarked && b.isBookmarked) return 1;
        return b.modified.getTime() - a.modified.getTime();
      })
      .map((conv, index) => ({ ...conv, value: index }));
      
  } catch (error) {
    console.error('Error loading conversation history:', error);
    return [];
  }
}

/**
 * 根据会话ID获取会话数据
 * @param {string} sessionId - 会话ID
 * @returns {Object|null} 会话数据
 */
function getSessionById(sessionId) {
  if (!isValidSessionId(sessionId)) {
    return null;
  }
  
  // 这里应该从实际存储中获取会话数据
  // 为了演示，返回模拟数据
  return {
    sessionId,
    startTime: Date.now() - 86400000,
    lastInteractionTime: Date.now() - 3600000,
    cwd: process.cwd(),
    totalCostUSD: 0.15,
    totalAPIDuration: 2500,
    totalLinesAdded: 150,
    totalLinesRemoved: 75,
    modelUsage: {
      'claude-3-5-sonnet-20241022': {
        inputTokens: 12500,
        outputTokens: 3200,
        costUSD: 0.15
      }
    }
  };
}

/**
 * 对话列表组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L41743-41805中的pF1组件
 */
function ConversationList({ logs, maxHeight = Infinity, onCancel, onSelect }) {
  const { columns } = getTerminalSize();
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  
  if (logs.length === 0) return null;
  
  const visibleHeight = Math.min(maxHeight - 3, logs.length);
  const hiddenCount = Math.max(0, logs.length - visibleHeight);
  
  // 列宽定义
  const MODIFIED_WIDTH = 12;
  const CREATED_WIDTH = 12;
  const MESSAGE_COUNT_WIDTH = 10;
  const BRANCH_WIDTH = 15;
  
  const formattedOptions = logs.map(log => {
    const modified = formatTimestamp(log.modified).padEnd(MODIFIED_WIDTH);
    const created = formatTimestamp(log.created).padEnd(CREATED_WIDTH);
    const messageCount = `${log.messageCount}`.padStart(MESSAGE_COUNT_WIDTH);
    const branch = (log.gitBranch || '-').substring(0, BRANCH_WIDTH - 1).padEnd(BRANCH_WIDTH);
    const summary = log.summary || log.firstPrompt;
    const sidechainIndicator = log.isSidechain ? ' (sidechain)' : '';
    const bookmarkIndicator = log.isBookmarked ? '✻ ' : '';
    
    const fullLabel = `${bookmarkIndicator}${modified}${created}${messageCount} ${branch}${summary}${sidechainIndicator}`;
    
    return {
      label: fullLabel.length > columns - 2 ? 
        `${fullLabel.slice(0, columns - 5)}...` : 
        fullLabel,
      value: log.value.toString(),
      isBookmarked: log.isBookmarked
    };
  });
  
  const handleKeyPress = React.useCallback((event) => {
    switch (event.key) {
      case 'ArrowUp':
        setSelectedIndex(prev => Math.max(0, prev - 1));
        break;
      case 'ArrowDown':
        setSelectedIndex(prev => Math.min(formattedOptions.length - 1, prev + 1));
        break;
      case 'Enter':
        onSelect(parseInt(formattedOptions[selectedIndex].value, 10));
        break;
      case 'Escape':
        onCancel();
        break;
    }
  }, [selectedIndex, formattedOptions, onSelect, onCancel]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  return React.createElement('div', {
    style: {
      padding: '10px',
      border: '2px solid #8800ff',
      borderRadius: '8px',
      backgroundColor: '#000000',
      color: '#ffffff',
      fontFamily: 'monospace'
    }
  }, [
    // 标题
    React.createElement('h2', {
      key: 'title',
      style: { color: '#8800ff', marginBottom: '15px' }
    }, 'Resume Conversation'),
    
    // 列标题
    React.createElement('div', {
      key: 'header',
      style: {
        color: '#888888',
        fontSize: '12px',
        marginBottom: '10px',
        fontWeight: 'bold'
      }
    }, `${'Modified'.padEnd(MODIFIED_WIDTH)}${'Created'.padEnd(CREATED_WIDTH)}${'Messages'.padStart(MESSAGE_COUNT_WIDTH)} ${'Branch'.padEnd(BRANCH_WIDTH)}Summary`),
    
    // 对话列表
    React.createElement('div', {
      key: 'list',
      style: { marginBottom: '15px' }
    }, formattedOptions.slice(0, visibleHeight).map((option, index) => 
      React.createElement('div', {
        key: option.value,
        style: {
          padding: '5px',
          backgroundColor: index === selectedIndex ? '#333' : 'transparent',
          color: index === selectedIndex ? '#8800ff' : 
                 option.isBookmarked ? '#ffff00' : '#ffffff',
          cursor: 'pointer',
          borderRadius: '4px',
          fontSize: '12px',
          fontFamily: 'monospace'
        }
      }, `${index === selectedIndex ? '► ' : '  '}${option.label}`)
    )),
    
    // 隐藏项目提示
    hiddenCount > 0 && React.createElement('div', {
      key: 'hidden',
      style: {
        paddingLeft: '20px',
        color: '#888888',
        fontSize: '12px',
        marginBottom: '15px'
      }
    }, `and ${hiddenCount} more…`),
    
    // 帮助信息
    React.createElement('div', {
      key: 'help',
      style: {
        borderTop: '1px solid #333',
        paddingTop: '10px',
        color: '#888888',
        fontSize: '12px'
      }
    }, '↑/↓ to navigate • Enter to resume • Esc to cancel')
  ]);
}

/**
 * 对话恢复主组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L41806-41853中的N$8组件
 */
function ResumeConversationUI({ onDone, onResume }) {
  const [conversations, setConversations] = React.useState([]);
  const [loading, setLoading] = React.useState(true);
  const { rows } = getTerminalSize();
  
  React.useEffect(() => {
    async function loadConversations() {
      try {
        const conversationList = await getConversationHistory();
        if (conversationList.length === 0) {
          onDone('No conversations found to resume');
        } else {
          setConversations(conversationList);
        }
      } catch (error) {
        onDone('Failed to load conversations');
      } finally {
        setLoading(false);
      }
    }
    
    loadConversations();
  }, [onDone]);
  
  const handleSelect = React.useCallback(async (index) => {
    const selectedConversation = conversations[index];
    if (!selectedConversation) {
      onDone('Failed to load selected conversation');
      return;
    }
    
    const sessionId = isValidSessionId(
      selectedConversation.messages.find(msg => msg.sessionId)?.sessionId
    );
    
    if (!sessionId) {
      onDone('Failed to resume conversation');
      return;
    }
    
    const session = getSessionById(sessionId);
    if (!session) {
      onDone('Failed to resume conversation');
      return;
    }
    
    // 记录书签恢复事件
    if (selectedConversation.isBookmarked) {
      console.log('Resuming bookmarked conversation');
    }
    
    onResume(session, selectedConversation);
  }, [conversations, onDone, onResume]);
  
  const handleCancel = React.useCallback(() => {
    onDone();
  }, [onDone]);
  
  if (loading) {
    return React.createElement('div', {
      style: { 
        padding: '20px', 
        textAlign: 'center', 
        color: '#ffff00' 
      }
    }, '🔄 Loading conversations...');
  }
  
  // 过滤掉侧链对话
  const mainConversations = conversations.filter(conv => !conv.isSidechain);
  
  return React.createElement(ConversationList, {
    logs: mainConversations,
    maxHeight: rows - 2,
    onCancel: handleCancel,
    onSelect: handleSelect
  });
}

/**
 * /resume 命令定义
 * @original: 原始文件L41854-41874中的L$8对象
 */
export const resumeCommand = {
  type: "local-jsx",
  name: "resume",
  description: "Resume a conversation",
  isEnabled: () => true,
  isHidden: false,
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @returns {Promise<React.Element>} React组件
   */
  async call(onDone, context) {
    return React.createElement(ResumeConversationUI, {
      onDone,
      onResume: (session, conversation) => {
        // 调用上下文的恢复方法
        if (typeof context.resume === 'function') {
          context.resume(session, conversation);
        }
        
        // 完成命令，跳过消息显示
        onDone(undefined, {
          skipMessage: true
        });
      }
    });
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "resume";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /resume

Resume a previous conversation from your conversation history.

Features:
- Interactive conversation selection with keyboard navigation
- Displays conversation metadata (creation time, modification time, message count)
- Shows Git branch information when available
- Highlights bookmarked conversations with ✻ symbol
- Filters out sidechain conversations from main list
- Smart timestamp formatting (relative time for recent conversations)

Conversation List Columns:
- Modified: When the conversation was last updated
- Created: When the conversation was started
- Messages: Number of messages in the conversation
- Branch: Git branch name (if available)
- Summary: First prompt or conversation summary

Navigation:
- ↑/↓ arrows: Navigate between conversations
- Enter: Resume selected conversation
- Escape: Cancel and return to main interface

The resume command restores the complete conversation state including:
- All previous messages and responses
- Session metadata and statistics
- Tool usage history and context
- Working directory and environment state

Bookmarked conversations (marked with ✻) appear at the top of the list for quick access.

Note: Only conversations with valid session data can be resumed. Corrupted or incomplete conversation files will be filtered out automatically.
    `.trim();
  }
};
