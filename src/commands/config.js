/**
 * /config 命令实现
 * @original: 原始文件L35819-36258中的_vB组件和LH8对象
 */

import React from 'react';

/**
 * 配置项类型定义
 */
export const CONFIG_ITEM_TYPES = {
  BOOLEAN: 'boolean',
  ENUM: 'enum',
  MANAGED_ENUM: 'managedEnum',
  STRING: 'string',
  NUMBER: 'number'
};

/**
 * 主题选项
 */
export const THEME_OPTIONS = {
  'dark': 'Dark mode',
  'light': 'Light mode',
  'dark-daltonized': 'Dark mode (colorblind-friendly)',
  'light-daltonized': 'Light mode (colorblind-friendly)',
  'dark-ansi': 'Dark mode (ANSI colors only)',
  'light-ansi': 'Light mode (ANSI colors only)'
};

/**
 * 通知渠道选项
 */
export const NOTIFICATION_CHANNEL_OPTIONS = {
  'auto': 'Auto',
  'iterm2': 'iTerm2 (OSC 9)',
  'terminal_bell': 'Terminal Bell (\\a)',
  'kitty': 'Kitty (OSC 99)',
  'ghostty': 'Ghostty (OSC 777)',
  'iterm2_with_bell': 'iTerm2 w/ Bell',
  'notifications_disabled': 'Disabled'
};

/**
 * 获取默认配置
 * @returns {Object} 默认配置对象
 * @original: 原始文件L35824中的E0()函数调用
 */
function getDefaultConfig() {
  return {
    autoCompactEnabled: false,
    checkpointingEnabled: true,
    autoUpdates: true,
    theme: 'dark',
    preferredNotifChannel: 'auto',
    editorMode: 'normal',
    diffTool: 'auto',
    autoConnectIde: false,
    autoInstallIdeExtension: true,
    customApiKeyResponses: {
      approved: [],
      rejected: []
    }
  };
}

/**
 * 保存配置
 * @param {Object} config - 配置对象
 * @original: 原始文件L35910中的pA()函数调用
 */
function saveConfig(config) {
  try {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('claudeCodeConfig', JSON.stringify(config));
    }
    
    // 也保存到文件系统（如果在Node.js环境中）
    if (typeof process !== 'undefined' && process.env) {
      const fs = require('fs');
      const path = require('path');
      const os = require('os');
      
      const configDir = path.join(os.homedir(), '.claude');
      const configFile = path.join(configDir, 'config.json');
      
      try {
        if (!fs.existsSync(configDir)) {
          fs.mkdirSync(configDir, { recursive: true });
        }
        fs.writeFileSync(configFile, JSON.stringify(config, null, 2));
      } catch (error) {
        console.warn('Failed to save config to file:', error);
      }
    }
  } catch (error) {
    console.warn('Failed to save config:', error);
  }
}

/**
 * 加载配置
 * @returns {Object} 配置对象
 */
function loadConfig() {
  try {
    // 首先尝试从localStorage加载
    if (typeof localStorage !== 'undefined') {
      const stored = localStorage.getItem('claudeCodeConfig');
      if (stored) {
        return { ...getDefaultConfig(), ...JSON.parse(stored) };
      }
    }
    
    // 然后尝试从文件系统加载
    if (typeof process !== 'undefined' && process.env) {
      const fs = require('fs');
      const path = require('path');
      const os = require('os');
      
      const configFile = path.join(os.homedir(), '.claude', 'config.json');
      
      if (fs.existsSync(configFile)) {
        const fileContent = fs.readFileSync(configFile, 'utf8');
        return { ...getDefaultConfig(), ...JSON.parse(fileContent) };
      }
    }
  } catch (error) {
    console.warn('Failed to load config:', error);
  }
  
  return getDefaultConfig();
}

/**
 * 主题选择器组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L36135中的Lf1组件
 */
function ThemeSelector({ initialTheme, onThemeSelect, skipExitHandling }) {
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  const themes = Object.keys(THEME_OPTIONS);
  
  React.useEffect(() => {
    const currentIndex = themes.indexOf(initialTheme);
    if (currentIndex >= 0) {
      setSelectedIndex(currentIndex);
    }
  }, [initialTheme]);
  
  const handleKeyPress = React.useCallback((event) => {
    switch (event.key) {
      case 'ArrowUp':
        setSelectedIndex(prev => Math.max(0, prev - 1));
        break;
      case 'ArrowDown':
        setSelectedIndex(prev => Math.min(themes.length - 1, prev + 1));
        break;
      case 'Enter':
      case ' ':
        onThemeSelect(themes[selectedIndex]);
        break;
      case 'Escape':
        if (!skipExitHandling) {
          onThemeSelect(initialTheme);
        }
        break;
    }
  }, [selectedIndex, themes, onThemeSelect, initialTheme, skipExitHandling]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  return React.createElement('div', {
    style: {
      padding: '10px',
      border: '1px solid #333',
      borderRadius: '4px',
      backgroundColor: '#1a1a1a'
    }
  }, [
    React.createElement('h3', {
      key: 'title',
      style: { color: '#ffff00', marginBottom: '10px' }
    }, 'Select Theme'),
    
    React.createElement('div', {
      key: 'options',
      style: { marginBottom: '10px' }
    }, themes.map((theme, index) => 
      React.createElement('div', {
        key: theme,
        style: {
          padding: '5px',
          backgroundColor: index === selectedIndex ? '#333' : 'transparent',
          color: index === selectedIndex ? '#00ff00' : '#ffffff',
          cursor: 'pointer'
        }
      }, `${index === selectedIndex ? '► ' : '  '}${THEME_OPTIONS[theme]}`)
    )),
    
    React.createElement('div', {
      key: 'help',
      style: { color: '#888888', fontSize: '12px' }
    }, '↑/↓ to select • Enter/Space to confirm • Esc to cancel')
  ]);
}

/**
 * 模型选择器组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L36141中的Mf1组件
 */
function ModelSelector({ initial, onSelect }) {
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  const models = [
    'Default (recommended)',
    'claude-3-5-sonnet-20241022',
    'claude-3-5-haiku-20241022',
    'claude-3-opus-20240229'
  ];
  
  React.useEffect(() => {
    const currentIndex = models.indexOf(initial || 'Default (recommended)');
    if (currentIndex >= 0) {
      setSelectedIndex(currentIndex);
    }
  }, [initial]);
  
  const handleKeyPress = React.useCallback((event) => {
    switch (event.key) {
      case 'ArrowUp':
        setSelectedIndex(prev => Math.max(0, prev - 1));
        break;
      case 'ArrowDown':
        setSelectedIndex(prev => Math.min(models.length - 1, prev + 1));
        break;
      case 'Enter':
      case ' ':
        const selectedModel = models[selectedIndex];
        onSelect(selectedModel === 'Default (recommended)' ? null : selectedModel);
        break;
      case 'Escape':
        onSelect(initial);
        break;
    }
  }, [selectedIndex, models, onSelect, initial]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  return React.createElement('div', {
    style: {
      padding: '10px',
      border: '1px solid #333',
      borderRadius: '4px',
      backgroundColor: '#1a1a1a'
    }
  }, [
    React.createElement('h3', {
      key: 'title',
      style: { color: '#ffff00', marginBottom: '10px' }
    }, 'Select Model'),
    
    React.createElement('div', {
      key: 'options',
      style: { marginBottom: '10px' }
    }, models.map((model, index) => 
      React.createElement('div', {
        key: model,
        style: {
          padding: '5px',
          backgroundColor: index === selectedIndex ? '#333' : 'transparent',
          color: index === selectedIndex ? '#00ff00' : '#ffffff',
          cursor: 'pointer'
        }
      }, `${index === selectedIndex ? '► ' : '  '}${model}`)
    )),
    
    React.createElement('div', {
      key: 'help',
      style: { color: '#888888', fontSize: '12px' }
    }, '↑/↓ to select • Enter/Space to confirm • Esc to cancel')
  ]);
}

/**
 * 配置面板主组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L35819-36234中的_vB组件
 */
function ConfigPanel({ onClose, isConnectedToIde }) {
  const [config, setConfig] = React.useState(loadConfig());
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  const [activeDialog, setActiveDialog] = React.useState(null);
  const [pendingChanges, setPendingChanges] = React.useState({});
  
  // 创建配置项列表
  const configItems = React.useMemo(() => {
    const items = [
      {
        id: 'autoCompactEnabled',
        label: 'Auto-compact',
        value: config.autoCompactEnabled,
        type: CONFIG_ITEM_TYPES.BOOLEAN,
        onChange: (value) => {
          const newConfig = { ...config, autoCompactEnabled: value };
          setConfig(newConfig);
          saveConfig(newConfig);
        }
      },
      {
        id: 'checkpointingEnabled',
        label: 'Checkpointing',
        value: config.checkpointingEnabled,
        type: CONFIG_ITEM_TYPES.BOOLEAN,
        onChange: (value) => {
          const newConfig = { ...config, checkpointingEnabled: value };
          setConfig(newConfig);
          saveConfig(newConfig);
        }
      },
      {
        id: 'verbose',
        label: 'Verbose output',
        value: config.verbose || false,
        type: CONFIG_ITEM_TYPES.BOOLEAN,
        onChange: (value) => {
          const newConfig = { ...config, verbose: value };
          setConfig(newConfig);
          saveConfig(newConfig);
        }
      },
      {
        id: 'autoUpdates',
        label: 'Auto-updates',
        value: config.autoUpdates !== false,
        type: CONFIG_ITEM_TYPES.BOOLEAN,
        onChange: (value) => {
          const newConfig = { ...config, autoUpdates: value };
          setConfig(newConfig);
          saveConfig(newConfig);
        }
      },
      {
        id: 'theme',
        label: 'Theme',
        value: config.theme,
        type: CONFIG_ITEM_TYPES.MANAGED_ENUM,
        onChange: (value) => {
          const newConfig = { ...config, theme: value };
          setConfig(newConfig);
          saveConfig(newConfig);
        }
      },
      {
        id: 'notifChannel',
        label: 'Notifications',
        value: config.preferredNotifChannel,
        options: Object.keys(NOTIFICATION_CHANNEL_OPTIONS),
        type: CONFIG_ITEM_TYPES.ENUM,
        onChange: (value) => {
          const newConfig = { ...config, preferredNotifChannel: value };
          setConfig(newConfig);
          saveConfig(newConfig);
        }
      },
      {
        id: 'editorMode',
        label: 'Editor mode',
        value: config.editorMode === 'emacs' ? 'normal' : config.editorMode || 'normal',
        options: ['normal', 'vim'],
        type: CONFIG_ITEM_TYPES.ENUM,
        onChange: (value) => {
          const newConfig = { ...config, editorMode: value };
          setConfig(newConfig);
          saveConfig(newConfig);
        }
      }
    ];
    
    // 添加IDE相关配置项
    if (isConnectedToIde) {
      items.push({
        id: 'diffTool',
        label: 'Diff tool',
        value: config.diffTool ?? 'auto',
        options: ['terminal', 'auto'],
        type: CONFIG_ITEM_TYPES.ENUM,
        onChange: (value) => {
          const newConfig = { ...config, diffTool: value };
          setConfig(newConfig);
          saveConfig(newConfig);
        }
      });
    } else {
      items.push({
        id: 'autoConnectIde',
        label: 'Auto-connect to IDE',
        value: config.autoConnectIde ?? false,
        type: CONFIG_ITEM_TYPES.BOOLEAN,
        onChange: (value) => {
          const newConfig = { ...config, autoConnectIde: value };
          setConfig(newConfig);
          saveConfig(newConfig);
        }
      });
    }
    
    return items;
  }, [config, isConnectedToIde]);
  
  const handleKeyPress = React.useCallback((event) => {
    if (activeDialog) return;
    
    switch (event.key) {
      case 'Escape':
        // 生成变更摘要
        const changes = Object.entries(pendingChanges).map(([key, value]) => 
          `Set ${key} to ${value}`
        );
        
        if (changes.length > 0) {
          onClose(changes.join('\n'));
        } else {
          onClose();
        }
        break;
        
      case 'ArrowUp':
        setSelectedIndex(prev => Math.max(0, prev - 1));
        break;
        
      case 'ArrowDown':
        setSelectedIndex(prev => Math.min(configItems.length - 1, prev + 1));
        break;
        
      case 'Enter':
      case 'Tab':
      case ' ':
        const item = configItems[selectedIndex];
        if (!item || !item.onChange) return;
        
        if (item.type === CONFIG_ITEM_TYPES.BOOLEAN) {
          item.onChange(!item.value);
          setPendingChanges(prev => ({ ...prev, [item.label]: !item.value }));
        } else if (item.id === 'theme' && event.key === 'Enter') {
          setActiveDialog('theme');
        } else if (item.type === CONFIG_ITEM_TYPES.ENUM) {
          const currentIndex = item.options.indexOf(item.value);
          const nextIndex = (currentIndex + 1) % item.options.length;
          const nextValue = item.options[nextIndex];
          item.onChange(nextValue);
          setPendingChanges(prev => ({ ...prev, [item.label]: nextValue }));
        }
        break;
    }
  }, [activeDialog, selectedIndex, configItems, onClose, pendingChanges]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  // 渲染对话框
  if (activeDialog === 'theme') {
    return React.createElement(ThemeSelector, {
      initialTheme: config.theme,
      onThemeSelect: (theme) => {
        const newConfig = { ...config, theme };
        setConfig(newConfig);
        saveConfig(newConfig);
        setPendingChanges(prev => ({ ...prev, Theme: theme }));
        setActiveDialog(null);
      },
      skipExitHandling: true
    });
  }
  
  // 渲染主配置面板
  return React.createElement('div', {
    style: {
      padding: '10px',
      border: '1px solid #333',
      borderRadius: '4px',
      backgroundColor: '#000000',
      color: '#ffffff',
      fontFamily: 'monospace'
    }
  }, [
    // 标题
    React.createElement('div', {
      key: 'header',
      style: { marginBottom: '15px' }
    }, [
      React.createElement('h2', {
        key: 'title',
        style: { color: '#00ff00', marginBottom: '5px' }
      }, 'Settings'),
      React.createElement('p', {
        key: 'subtitle',
        style: { color: '#888888', fontSize: '14px' }
      }, 'Configure Claude Code preferences')
    ]),
    
    // 配置项列表
    React.createElement('div', {
      key: 'items',
      style: { marginBottom: '15px' }
    }, configItems.map((item, index) => {
      const isSelected = index === selectedIndex;
      
      return React.createElement('div', {
        key: item.id,
        style: {
          display: 'flex',
          padding: '5px',
          backgroundColor: isSelected ? '#333' : 'transparent',
          minHeight: '30px',
          alignItems: 'center'
        }
      }, [
        React.createElement('div', {
          key: 'label',
          style: {
            width: '300px',
            color: isSelected ? '#00ff00' : '#ffffff'
          }
        }, `${isSelected ? '► ' : '  '}${item.label}`),
        
        React.createElement('div', {
          key: 'value',
          style: {
            color: isSelected ? '#00ff00' : '#ffffff'
          }
        }, (() => {
          if (item.type === CONFIG_ITEM_TYPES.BOOLEAN) {
            return item.value.toString();
          } else if (item.id === 'theme') {
            return THEME_OPTIONS[item.value] || item.value;
          } else if (item.id === 'notifChannel') {
            return NOTIFICATION_CHANNEL_OPTIONS[item.value] || item.value;
          } else {
            return item.value.toString();
          }
        })())
      ]);
    })),
    
    // 帮助信息
    React.createElement('div', {
      key: 'help',
      style: {
        borderTop: '1px solid #333',
        paddingTop: '10px',
        color: '#888888',
        fontSize: '12px'
      }
    }, '↑/↓ to select • Enter/Tab/Space to change • Esc to close')
  ]);
}

/**
 * /config 命令定义
 * @original: 原始文件L36236-36258中的LH8对象
 */
export const configCommand = {
  aliases: ["theme"],
  type: "local-jsx",
  name: "config",
  description: "Open config panel",
  isEnabled: () => true,
  isHidden: false,
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @param {string} args - 命令参数
   * @returns {Promise<React.Element>} React组件
   */
  async call(onDone, context, args) {
    // 检查是否连接到IDE
    const isConnectedToIde = context.options?.mcpClients?.some(client => 
      client.connected && client.type === 'ide'
    ) || false;
    
    return React.createElement(ConfigPanel, {
      onClose: onDone,
      isConnectedToIde
    });
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "config";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /config

Open the configuration panel to manage Claude Code settings.

Available settings:
- Auto-compact: Automatically compress conversation history
- Checkpointing: Enable conversation checkpoints
- Verbose output: Show detailed operation information
- Auto-updates: Automatically update Claude Code
- Theme: Choose visual theme (dark, light, colorblind-friendly)
- Notifications: Configure notification method
- Editor mode: Choose between normal and vim editing modes
- Diff tool: Select diff visualization tool (when IDE connected)
- Auto-connect to IDE: Automatically connect to supported IDEs

Navigation:
- ↑/↓ arrows: Navigate between settings
- Enter/Tab/Space: Change setting value
- Escape: Save changes and close

The configuration panel provides an interactive interface for managing all Claude Code preferences. Changes are saved automatically and take effect immediately.
    `.trim();
  }
};
