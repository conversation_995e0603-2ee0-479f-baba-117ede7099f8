/**
 * /memory 命令实现
 * @original: 原始文件L37624-37705中的D$8对象和相关组件
 */

import React from 'react';
import { execSync } from 'child_process';
import { existsSync, mkdirSync, writeFileSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';

/**
 * 内存文件类型
 */
export const MEMORY_TYPES = {
  PROJECT: 'Project',
  LOCAL: 'Local', 
  USER: 'User'
};

/**
 * 内存文件配置
 */
export const MEMORY_CONFIG = {
  PROJECT_FILE: 'CLAUDE.md',
  LOCAL_FILE: 'CLAUDE.local.md',
  USER_FILE: 'CLAUDE.md',
  USER_DIR: '.claude'
};

/**
 * 获取内存文件路径
 * @param {string} memoryType - 内存类型
 * @returns {string} 文件路径
 */
function getMemoryFilePath(memoryType) {
  switch (memoryType) {
    case MEMORY_TYPES.PROJECT:
      return join(process.cwd(), MEMORY_CONFIG.PROJECT_FILE);
    case MEMORY_TYPES.LOCAL:
      return join(process.cwd(), MEMORY_CONFIG.LOCAL_FILE);
    case MEMORY_TYPES.USER:
      return join(homedir(), MEMORY_CONFIG.USER_DIR, MEMORY_CONFIG.USER_FILE);
    default:
      throw new Error(`Unknown memory type: ${memoryType}`);
  }
}

/**
 * 获取内存文件目录
 * @param {string} memoryType - 内存类型
 * @returns {string} 目录路径
 */
function getMemoryDirectory(memoryType) {
  switch (memoryType) {
    case MEMORY_TYPES.PROJECT:
    case MEMORY_TYPES.LOCAL:
      return process.cwd();
    case MEMORY_TYPES.USER:
      return join(homedir(), MEMORY_CONFIG.USER_DIR);
    default:
      throw new Error(`Unknown memory type: ${memoryType}`);
  }
}

/**
 * 检查是否在Git仓库中
 * @returns {boolean} 是否在Git仓库中
 */
function isInGitRepository() {
  try {
    execSync('git rev-parse --is-inside-work-tree', { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

/**
 * 获取内存类型的显示名称
 * @param {string} memoryType - 内存类型
 * @returns {string} 显示名称
 */
function getMemoryDisplayName(memoryType) {
  switch (memoryType) {
    case MEMORY_TYPES.PROJECT:
      return 'Project memory';
    case MEMORY_TYPES.LOCAL:
      return 'Project memory (local)';
    case MEMORY_TYPES.USER:
      return 'User memory';
    default:
      return memoryType;
  }
}

/**
 * 获取内存文件描述
 * @param {string} memoryType - 内存类型
 * @returns {string} 文件描述
 */
function getMemoryDescription(memoryType) {
  const isGitRepo = isInGitRepository();
  
  switch (memoryType) {
    case MEMORY_TYPES.PROJECT:
      return `${isGitRepo ? 'Checked in at' : 'Saved in'} ./CLAUDE.md`;
    case MEMORY_TYPES.LOCAL:
      return 'Gitignored in ./CLAUDE.local.md';
    case MEMORY_TYPES.USER:
      return 'Saved in ~/.claude/CLAUDE.md';
    default:
      return '';
  }
}

/**
 * 获取可用的内存类型选项
 * @returns {Array} 内存类型选项数组
 */
function getMemoryOptions() {
  const isGitRepo = isInGitRepository();
  
  const options = [
    {
      label: getMemoryDisplayName(MEMORY_TYPES.PROJECT),
      value: MEMORY_TYPES.PROJECT,
      description: getMemoryDescription(MEMORY_TYPES.PROJECT)
    }
  ];
  
  // 只有在Git仓库中才显示本地内存选项
  if (isGitRepo) {
    options.push({
      label: getMemoryDisplayName(MEMORY_TYPES.LOCAL),
      value: MEMORY_TYPES.LOCAL,
      description: getMemoryDescription(MEMORY_TYPES.LOCAL)
    });
  }
  
  options.push({
    label: getMemoryDisplayName(MEMORY_TYPES.USER),
    value: MEMORY_TYPES.USER,
    description: getMemoryDescription(MEMORY_TYPES.USER)
  });
  
  return options;
}

/**
 * 添加文件到Git忽略列表
 * @param {string} filePath - 文件路径
 * @returns {Promise<void>}
 */
async function addToGitIgnore(filePath) {
  try {
    const gitignorePath = join(process.cwd(), '.gitignore');
    const fileName = filePath.split('/').pop();
    
    // 检查.gitignore是否存在，如果不存在则创建
    if (!existsSync(gitignorePath)) {
      writeFileSync(gitignorePath, '', 'utf8');
    }
    
    // 检查文件是否已经在.gitignore中
    const fs = await import('fs');
    const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
    
    if (!gitignoreContent.includes(fileName)) {
      fs.appendFileSync(gitignorePath, `\n${fileName}\n`, 'utf8');
    }
  } catch (error) {
    console.warn(`Warning: Could not add ${filePath} to .gitignore:`, error.message);
  }
}

/**
 * 获取默认编辑器
 * @returns {Object} 编辑器信息
 */
function getDefaultEditor() {
  if (process.env.VISUAL) {
    return { name: '$VISUAL', command: process.env.VISUAL };
  } else if (process.env.EDITOR) {
    return { name: '$EDITOR', command: process.env.EDITOR };
  } else {
    return { name: 'default', command: 'default' };
  }
}

/**
 * 打开文件进行编辑
 * @param {string} filePath - 文件路径
 * @returns {Promise<void>}
 */
async function openFileForEditing(filePath) {
  const editor = getDefaultEditor();
  
  if (editor.name === 'default') {
    // 使用系统默认编辑器
    const platform = process.platform;
    let command;
    
    switch (platform) {
      case 'darwin':
        command = `open "${filePath}"`;
        break;
      case 'win32':
        command = `start "" "${filePath}"`;
        break;
      default:
        command = `xdg-open "${filePath}"`;
    }
    
    execSync(command, { stdio: 'inherit' });
  } else {
    // 使用环境变量指定的编辑器
    execSync(`${editor.command} "${filePath}"`, { stdio: 'inherit' });
  }
}

/**
 * 内存文件状态显示组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L37587-37623中的Eh1组件
 */
function MemoryFileStatus({ context }) {
  const [memoryFiles, setMemoryFiles] = React.useState([]);
  
  React.useEffect(() => {
    const files = [];
    
    // 检查项目内存文件
    const projectFile = getMemoryFilePath(MEMORY_TYPES.PROJECT);
    if (existsSync(projectFile)) {
      files.push({
        path: projectFile,
        type: MEMORY_TYPES.PROJECT,
        isNested: false
      });
    }
    
    // 检查本地内存文件
    if (isInGitRepository()) {
      const localFile = getMemoryFilePath(MEMORY_TYPES.LOCAL);
      if (existsSync(localFile)) {
        files.push({
          path: localFile,
          type: MEMORY_TYPES.LOCAL,
          isNested: false
        });
      }
    }
    
    // 检查用户内存文件
    const userFile = getMemoryFilePath(MEMORY_TYPES.USER);
    if (existsSync(userFile)) {
      files.push({
        path: userFile,
        type: MEMORY_TYPES.USER,
        isNested: false
      });
    }
    
    // 从上下文中获取已读取的内存文件
    if (context?.readFileState) {
      Array.from(context.readFileState.keys()).forEach(filePath => {
        const fileState = context.readFileState.get(filePath);
        if (fileState && filePath.endsWith('/CLAUDE.md') && 
            !files.some(f => f.path === filePath)) {
          files.push({
            path: filePath,
            type: MEMORY_TYPES.PROJECT,
            isNested: true
          });
        }
      });
    }
    
    setMemoryFiles(files);
  }, [context]);
  
  if (memoryFiles.length === 0) {
    return null;
  }
  
  return React.createElement('div', {
    style: { marginBottom: '10px' }
  }, memoryFiles.map((file, index) => {
    const fileName = file.path.split('/').pop();
    const prefix = file.isNested ? 'nested: ' : `${file.type.toLowerCase()}: `;
    
    return React.createElement('div', {
      key: index,
      style: { color: '#888888', fontSize: '12px' }
    }, `📄 ${prefix}${fileName}`);
  }));
}

/**
 * 内存文件详情组件
 * @param {Object} props - 组件属性
 */
function MemoryFileDetails({ memoryType }) {
  const filePath = getMemoryFilePath(memoryType);
  const fileExists = existsSync(filePath);
  
  if (!fileExists) {
    return React.createElement('div', {
      style: { color: '#888888', fontSize: '12px' }
    }, `Memory file does not exist yet. [Enter] to create ${memoryType}.`);
  }
  
  // 简单的内存条目计数（基于列表项标记）
  const fs = require('fs');
  const content = fs.readFileSync(filePath, 'utf8');
  const memoryCount = content.split('\n')
    .filter(line => {
      const trimmed = line.trim();
      return trimmed.startsWith('-') || trimmed.startsWith('*') || /^\s*\d+\./.test(trimmed);
    }).length;
  
  return React.createElement('div', {
    style: { color: '#00ff88', fontSize: '12px' }
  }, `${memoryCount} ${memoryCount === 1 ? 'memory' : 'memories'} in ${memoryType.toLowerCase()}`);
}

/**
 * 内存类型选择器组件
 * @param {Object} props - 组件属性
 * @original: 原始文件L37498-37576中的Hh1组件
 */
function MemoryTypeSelector({ onSelect, onCancel, title, renderDetails }) {
  const [selectedType, setSelectedType] = React.useState(MEMORY_TYPES.PROJECT);
  const options = getMemoryOptions();
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  
  const handleKeyPress = React.useCallback((event) => {
    switch (event.key) {
      case 'ArrowUp':
        setSelectedIndex(prev => {
          const newIndex = Math.max(0, prev - 1);
          setSelectedType(options[newIndex].value);
          return newIndex;
        });
        break;
      case 'ArrowDown':
        setSelectedIndex(prev => {
          const newIndex = Math.min(options.length - 1, prev + 1);
          setSelectedType(options[newIndex].value);
          return newIndex;
        });
        break;
      case 'Enter':
        onSelect(selectedType);
        break;
      case 'Escape':
        onCancel();
        break;
    }
  }, [selectedIndex, selectedType, options, onSelect, onCancel]);
  
  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);
  
  return React.createElement('div', {
    style: {
      padding: '15px',
      border: '2px solid #00ff88',
      borderRadius: '8px',
      backgroundColor: '#000000',
      color: '#ffffff',
      fontFamily: 'monospace'
    }
  }, [
    // 标题
    React.createElement('h2', {
      key: 'title',
      style: { color: '#00ff88', marginBottom: '15px' }
    }, title || 'Select memory to edit:'),
    
    // 选项列表
    React.createElement('div', {
      key: 'options',
      style: { marginBottom: '15px' }
    }, options.map((option, index) => 
      React.createElement('div', {
        key: option.value,
        style: {
          padding: '8px',
          backgroundColor: index === selectedIndex ? '#333' : 'transparent',
          color: index === selectedIndex ? '#00ff88' : '#ffffff',
          cursor: 'pointer',
          borderRadius: '4px',
          marginBottom: '5px'
        }
      }, [
        React.createElement('div', {
          key: 'label',
          style: { fontWeight: 'bold' }
        }, `${index === selectedIndex ? '► ' : '  '}${option.label}`),
        
        React.createElement('div', {
          key: 'description',
          style: { 
            fontSize: '12px', 
            color: '#888888',
            marginLeft: index === selectedIndex ? '20px' : '20px'
          }
        }, option.description)
      ])
    )),
    
    // 详情显示
    renderDetails && React.createElement('div', {
      key: 'details',
      style: { marginBottom: '15px' }
    }, renderDetails(selectedType)),
    
    // 帮助信息
    React.createElement('div', {
      key: 'help',
      style: {
        borderTop: '1px solid #333',
        paddingTop: '10px',
        color: '#888888',
        fontSize: '12px'
      }
    }, '↑/↓ to navigate • Enter to select • Esc to cancel')
  ]);
}

/**
 * /memory 命令定义
 * @original: 原始文件L37624-37705中的D$8对象
 */
export const memoryCommand = {
  type: "local-jsx",
  name: "memory",
  description: "Edit Claude memory files",
  isEnabled: () => true,
  isHidden: false,
  
  /**
   * 执行命令
   * @param {Function} onDone - 完成回调
   * @param {Object} context - 执行上下文
   * @returns {Promise<React.Element>} React组件
   */
  async call(onDone, context) {
    return React.createElement('div', {
      style: { padding: '10px' }
    }, [
      // 内存文件信息
      React.createElement('div', {
        key: 'info',
        style: { marginBottom: '20px' }
      }, [
        React.createElement('h2', {
          key: 'title',
          style: { color: '#00ff88', marginBottom: '10px' }
        }, 'Memory Files'),
        
        React.createElement(MemoryFileStatus, {
          key: 'status',
          context
        }),
        
        React.createElement('div', {
          key: 'learn-more',
          style: { 
            color: '#888888', 
            fontSize: '12px',
            marginTop: '10px'
          }
        }, [
          'Learn more: ',
          React.createElement('a', {
            key: 'link',
            href: 'https://docs.anthropic.com/en/docs/claude-code/memory',
            style: { color: '#00aaff' }
          }, 'https://docs.anthropic.com/en/docs/claude-code/memory')
        ])
      ]),
      
      // 内存类型选择器
      React.createElement(MemoryTypeSelector, {
        key: 'selector',
        title: 'Select memory to edit:',
        onSelect: async (memoryType) => {
          try {
            const filePath = getMemoryFilePath(memoryType);
            const directory = getMemoryDirectory(memoryType);
            
            // 确保目录存在
            if (!existsSync(directory)) {
              mkdirSync(directory, { recursive: true });
            }
            
            // 如果文件不存在，创建空文件
            if (!existsSync(filePath)) {
              writeFileSync(filePath, '', { encoding: 'utf8', flush: true });
              
              // 如果是本地文件，添加到.gitignore
              if (memoryType === MEMORY_TYPES.LOCAL) {
                await addToGitIgnore(filePath);
              }
            }
            
            // 打开文件进行编辑
            await openFileForEditing(filePath);
            
            // 获取编辑器信息
            const editor = getDefaultEditor();
            const editorInfo = editor.name !== 'default' ? 
              `Using ${editor.name}="${editor.command}".` : '';
            
            const editorTip = editorInfo ? 
              `> ${editorInfo} To change editor, set $EDITOR or $VISUAL environment variable.` :
              '> To use a different editor, set the $EDITOR or $VISUAL environment variable.';
            
            const displayName = getMemoryDisplayName(memoryType).toLowerCase();
            const shortPath = filePath.replace(process.cwd(), '.');
            
            onDone(`Opened ${displayName} at ${shortPath}\n\n${editorTip}`);
            
          } catch (error) {
            console.error('Error opening memory file:', error);
            onDone(`Error opening memory file: ${error.message}`);
          }
        },
        onCancel: () => {
          onDone('Cancelled memory editing');
        },
        renderDetails: (memoryType) => React.createElement(MemoryFileDetails, {
          memoryType
        })
      })
    ]);
  },
  
  /**
   * 获取用户友好的命令名
   * @returns {string} 命令名
   */
  userFacingName() {
    return "memory";
  },
  
  /**
   * 获取命令的详细帮助信息
   * @returns {string} 帮助信息
   */
  getDetailedHelp() {
    return `
Usage: /memory

Edit Claude memory files to store persistent information across conversations.

Memory Types:
• **Project Memory**: Stored in ./CLAUDE.md (version controlled)
• **Local Memory**: Stored in ./CLAUDE.local.md (gitignored, project-specific)
• **User Memory**: Stored in ~/.claude/CLAUDE.md (global, user-specific)

Features:
- Interactive memory type selection
- Automatic file creation if not exists
- Git integration (local files auto-added to .gitignore)
- Support for custom editors via $EDITOR or $VISUAL environment variables
- Memory count display for existing files

Memory File Format:
Memory files use Markdown format with list items:
- Use bullet points (-) or numbered lists (1.) for memories
- Each item represents a distinct memory or piece of information
- Supports full Markdown formatting for rich content

Editor Configuration:
- Set $EDITOR environment variable for preferred editor
- Set $VISUAL for visual editors (takes precedence over $EDITOR)
- Falls back to system default if no editor is configured

Examples:
  export EDITOR=nano     # Use nano as default editor
  export VISUAL=code     # Use VS Code as visual editor

The memory system helps Claude remember important context, preferences, and project-specific information across different conversations and sessions.
    `.trim();
  }
};
