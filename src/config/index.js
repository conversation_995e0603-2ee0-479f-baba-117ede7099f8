/**
 * 配置模块 - 统一管理所有配置信息
 * @original: 原始文件中的配置相关代码
 */

/**
 * API相关配置
 * @original: 原始文件中的API配置
 */
export const API_CONFIG = {
  // @original: 原始文件L2397-2412中的OAuth配置
  BASE_API_URL: process.env.BASE_API_URL || "https://api.anthropic.com",
  CONSOLE_AUTHORIZE_URL: process.env.CONSOLE_AUTHORIZE_URL || "https://console.anthropic.com/oauth/authorize",
  CLAUDE_AI_AUTHORIZE_URL: process.env.CLAUDE_AI_AUTHORIZE_URL || "https://claude.ai/oauth/authorize",
  TOKEN_URL: process.env.TOKEN_URL || "https://api.anthropic.com/v1/oauth/token",
  API_KEY_URL: process.env.API_KEY_URL || "https://api.anthropic.com/api/oauth/claude_cli/create_api_key",
  ROLES_URL: process.env.ROLES_URL || "https://api.anthropic.com/api/oauth/claude_cli/roles",
  CLIENT_ID: process.env.CLIENT_ID || "22422756-60c9-4084-8eb7-27705fd5cf9a",
  
  // @original: 原始文件中的区域配置
  CLOUD_ML_REGION: process.env.CLOUD_ML_REGION || "us-east5",
  
  // @original: 原始文件中的模型配置
  VERTEX_REGIONS: {
    CLAUDE_3_5_HAIKU: process.env.VERTEX_REGION_CLAUDE_3_5_HAIKU,
    CLAUDE_3_5_SONNET: process.env.VERTEX_REGION_CLAUDE_3_5_SONNET,
    CLAUDE_3_7_SONNET: process.env.VERTEX_REGION_CLAUDE_3_7_SONNET,
    CLAUDE_4_1_OPUS: process.env.VERTEX_REGION_CLAUDE_4_1_OPUS,
    CLAUDE_4_0_OPUS: process.env.VERTEX_REGION_CLAUDE_4_0_OPUS,
    CLAUDE_4_0_SONNET: process.env.VERTEX_REGION_CLAUDE_4_0_SONNET
  }
};

/**
 * 系统配置
 * @original: 原始文件中的系统相关配置
 */
export const SYSTEM_CONFIG = {
  // @original: 原始文件中的版本信息
  VERSION: "1.0.72",
  
  // @original: 原始文件中的调试配置
  DEBUG: process.env.DEBUG === "true" || process.env.NODE_ENV === "development",
  
  // @original: 原始文件中的环境配置
  NODE_ENV: process.env.NODE_ENV || "production",
  
  // @original: 原始文件中的文件路径配置
  TEMP_DIR: process.env.TEMP_DIR || require('os').tmpdir(),
  
  // @original: 原始文件中的权限配置
  DANGEROUSLY_SKIP_PERMISSIONS: process.env.DANGEROUSLY_SKIP_PERMISSIONS === "true",
  
  // @original: 原始文件中的MCP配置
  MCP_DEBUG: process.env.MCP_DEBUG === "true",
  STRICT_MCP_CONFIG: process.env.STRICT_MCP_CONFIG === "true",
  
  // @original: 原始文件中的IDE配置
  AUTO_CONNECT_IDE: process.env.CLAUDE_CODE_AUTO_CONNECT_IDE !== "false",
  
  // @original: 原始文件中的bash配置
  MAINTAIN_PROJECT_WORKING_DIR: process.env.CLAUDE_BASH_MAINTAIN_PROJECT_WORKING_DIR === "true"
};

/**
 * React DevTools配置
 * @original: 原始文件L289-323中的React DevTools过滤器配置
 */
export const REACT_DEVTOOLS_CONFIG = {
  COMPONENT_FILTERS: [
    {
      type: 1,
      value: 7,
      isEnabled: true
    },
    {
      type: 2,
      value: "InternalApp",
      isEnabled: true,
      isValid: true
    },
    {
      type: 2,
      value: "InternalAppContext",
      isEnabled: true,
      isValid: true
    },
    {
      type: 2,
      value: "InternalStdoutContext",
      isEnabled: true,
      isValid: true
    },
    {
      type: 2,
      value: "InternalStderrContext",
      isEnabled: true,
      isValid: true
    },
    {
      type: 2,
      value: "InternalStdinContext",
      isEnabled: true,
      isValid: true
    },
    {
      type: 2,
      value: "InternalFocusContext",
      isEnabled: true,
      isValid: true
    }
  ]
};

/**
 * 颜色和样式配置
 * @original: 原始文件中的颜色配置
 */
export const COLOR_CONFIG = {
  // @original: 原始文件L2413-2472中的颜色配置
  ANSI_COLORS: {
    reset: [0, 0],
    bold: [1, 22],
    dim: [2, 22],
    italic: [3, 23],
    underline: [4, 24],
    overline: [53, 55],
    inverse: [7, 27],
    hidden: [8, 28],
    strikethrough: [9, 29]
  }
};

/**
 * 获取模型对应的区域配置
 * @param {string} modelName - 模型名称
 * @returns {string} 区域名称
 * @original: 原始文件L2387-2394中的$o0函数
 */
export function getModelRegion(modelName) {
  if (!modelName) {
    return API_CONFIG.CLOUD_ML_REGION;
  }
  
  if (modelName.startsWith("claude-3-5-haiku")) {
    return API_CONFIG.VERTEX_REGIONS.CLAUDE_3_5_HAIKU || API_CONFIG.CLOUD_ML_REGION;
  }
  if (modelName.startsWith("claude-3-5-sonnet")) {
    return API_CONFIG.VERTEX_REGIONS.CLAUDE_3_5_SONNET || API_CONFIG.CLOUD_ML_REGION;
  }
  if (modelName.startsWith("claude-3-7-sonnet")) {
    return API_CONFIG.VERTEX_REGIONS.CLAUDE_3_7_SONNET || API_CONFIG.CLOUD_ML_REGION;
  }
  if (modelName.startsWith("claude-opus-4-1")) {
    return API_CONFIG.VERTEX_REGIONS.CLAUDE_4_1_OPUS || API_CONFIG.CLOUD_ML_REGION;
  }
  if (modelName.startsWith("claude-opus-4")) {
    return API_CONFIG.VERTEX_REGIONS.CLAUDE_4_0_OPUS || API_CONFIG.CLOUD_ML_REGION;
  }
  if (modelName.startsWith("claude-sonnet-4")) {
    return API_CONFIG.VERTEX_REGIONS.CLAUDE_4_0_SONNET || API_CONFIG.CLOUD_ML_REGION;
  }
  
  return API_CONFIG.CLOUD_ML_REGION;
}

/**
 * 检查是否为布尔值false的字符串表示
 * @param {string} value - 要检查的值
 * @returns {boolean} 是否为false
 * @original: 原始文件L2367-2371中的Uo0函数
 */
export function isFalsyString(value) {
  if (!value) return false;
  const normalizedValue = value.toLowerCase().trim();
  return ["0", "false", "no", "off"].includes(normalizedValue);
}

/**
 * 解析环境变量字符串为对象
 * @param {string[]} envArray - 环境变量数组
 * @returns {Object} 解析后的环境变量对象
 * @original: 原始文件L2372-2380中的wo0函数
 */
export function parseEnvironmentVariables(envArray) {
  const result = {};
  if (envArray) {
    for (const envVar of envArray) {
      const [key, ...valueParts] = envVar.split("=");
      if (!key || valueParts.length === 0) {
        throw new Error(`Invalid environment variable format: ${envVar}, environment variables should be added as: -e KEY1=value1 -e KEY2=value2`);
      }
      result[key] = valueParts.join("=");
    }
  }
  return result;
}
