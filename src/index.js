/**
 * Claude Code 主入口文件
 * @original: 原始文件L57220-57930中的KR8函数和应用程序初始化逻辑
 */

// 导入配置模块
import { API_CONFIG, SYSTEM_CONFIG } from './config/index.js';

// 导入工具函数
import * as utils from './utils/index.js';

// 导入服务层
import * as services from './services/index.js';

// 导入核心业务逻辑
import * as core from './core/index.js';

// 导入具体的服务和管理器
import { mcpService } from './services/mcp-service.js';
import { toolEngine } from './core/tool-engine.js';
import { commandProcessor } from './core/command-processor.js';
import { sdkStreamProcessor } from './core/sdk-stream.js';

// 导入第三方库（通过package.json管理）
import _ from 'lodash';
import axios from 'axios';

/**
 * 主应用程序类
 * @original: 原始文件L57220-57930中的主应用程序逻辑
 */
class ClaudeCodeApp {
  constructor() {
    this.config = SYSTEM_CONFIG;
    this.apiConfig = API_CONFIG;
    this.initialized = false;
    this.isRunning = false;
    this.shutdownHandlers = [];
  }

  /**
   * 初始化应用程序
   * @original: 原始文件L57220-57244中的初始化逻辑
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      // 设置环境变量
      if (!process.env.CLAUDE_CODE_ENTRYPOINT) {
        process.env.CLAUDE_CODE_ENTRYPOINT = 'cli';
      }

      // 设置进程标题
      process.title = 'claude';

      // 初始化配置
      await this._initializeConfig();

      // 初始化服务
      await this._initializeServices();

      // 初始化核心模块
      await this._initializeCoreModules();

      // 设置进程信号处理
      this._setupProcessHandlers();

      this.initialized = true;
      console.log('Claude Code应用程序初始化完成');
    } catch (error) {
      console.error('应用程序初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化配置
   * @private
   */
  async _initializeConfig() {
    // 检查Node.js版本
    const nodeVersion = process.version.match(/^v(\d+)\./)?.[1];
    if (!nodeVersion || parseInt(nodeVersion) < 18) {
      throw new Error('Claude Code requires Node.js version 18 or higher.');
    }

    // 初始化配置管理
    console.log('配置初始化完成');
  }

  /**
   * 初始化服务
   * @private
   */
  async _initializeServices() {
    try {
      // 初始化MCP服务
      await mcpService.initializeConnections();

      // 初始化其他服务
      console.log('服务初始化完成');
    } catch (error) {
      console.warn('服务初始化部分失败:', error.message);
    }
  }

  /**
   * 初始化核心模块
   * @private
   */
  async _initializeCoreModules() {
    try {
      // 注册内置工具
      await this._registerBuiltInTools();

      // 注册内置命令
      await this._registerBuiltInCommands();

      console.log('核心模块初始化完成');
    } catch (error) {
      console.warn('核心模块初始化部分失败:', error.message);
    }
  }

  /**
   * 注册内置工具
   * @private
   */
  async _registerBuiltInTools() {
    // 注册内置工具（如Bash、Edit、Write等）
    const builtInTools = [
      {
        name: 'Bash',
        type: 'system',
        description: 'Execute bash commands',
        handler: this.toolEngine.executeBashCommand.bind(this.toolEngine)
      },
      {
        name: 'Edit',
        type: 'file',
        description: 'Edit file contents',
        handler: this.editManager.editFile.bind(this.editManager)
      },
      {
        name: 'Read',
        type: 'file',
        description: 'Read file contents',
        handler: this.fileManager.readFile.bind(this.fileManager)
      },
      {
        name: 'Write',
        type: 'file',
        description: 'Write file contents',
        handler: this.fileManager.writeFile.bind(this.fileManager)
      },
      {
        name: 'Glob',
        type: 'file',
        description: 'Find files using glob patterns',
        handler: this.fileManager.globFiles.bind(this.fileManager)
      },
      {
        name: 'Grep',
        type: 'search',
        description: 'Search text in files',
        handler: this.fileManager.grepFiles.bind(this.fileManager)
      },
      {
        name: 'LS',
        type: 'file',
        description: 'List directory contents',
        handler: this.fileManager.listDirectory.bind(this.fileManager)
      },
      {
        name: 'Task',
        type: 'workflow',
        description: 'Create and manage tasks',
        handler: this.toolEngine.executeTask.bind(this.toolEngine)
      }
    ];

    // 注册所有内置工具
    for (const tool of builtInTools) {
      this.toolEngine.registerTool(tool);
    }
  }

  /**
   * 注册内置命令
   * @private
   */
  async _registerBuiltInCommands() {
    // 注册内置命令（如/help、/status等）
    // 从命令管理器中获取所有内置命令
    const { builtInCommands } = await import('./commands/index.js');

    // 注册所有内置命令到命令处理器
    for (const command of builtInCommands) {
      this.commandProcessor.registerCommand(command);
    }

    console.log(`Registered ${builtInCommands.length} built-in commands`);
  }

  /**
   * 设置进程信号处理
   * @private
   * @original: 原始文件L57246-57268中的HR8函数
   */
  _setupProcessHandlers() {
    const signals = ['SIGINT', 'SIGTERM', 'SIGHUP'];

    signals.forEach(signal => {
      process.on(signal, async () => {
        console.log(`\nReceived ${signal}, shutting down gracefully...`);
        await this.shutdown();
        process.exit(0);
      });
    });

    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      this.shutdown().then(() => process.exit(1));
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      this.shutdown().then(() => process.exit(1));
    });
  }

  /**
   * 启动应用程序
   * @original: 原始文件L57283-57924中的ER8函数
   */
  async start() {
    if (!this.initialized) {
      await this.initialize();
    }

    if (this.isRunning) {
      console.warn('应用程序已在运行中');
      return;
    }

    try {
      this.isRunning = true;

      // 处理特殊命令行参数
      await this._handleSpecialArgs();

      // 启动主CLI程序
      await this._startCLI();

      console.log('Claude Code应用程序已启动');
    } catch (error) {
      console.error('应用程序启动失败:', error);
      this.isRunning = false;
      throw error;
    }
  }

  /**
   * 处理特殊命令行参数
   * @private
   * @original: 原始文件L57221-57224中的ripgrep处理
   */
  async _handleSpecialArgs() {
    // 处理Ripgrep特殊命令
    if (process.argv[2] === '--ripgrep') {
      const ripgrepArgs = process.argv.slice(3);

      try {
        // 实现ripgrep执行逻辑
        const { spawn } = require('child_process');

        // 检查ripgrep是否可用
        const rg = spawn('rg', ['--version'], { stdio: 'pipe' });

        rg.on('error', () => {
          console.error('Error: ripgrep (rg) is not installed or not in PATH');
          process.exit(1);
        });

        rg.on('close', (code) => {
          if (code === 0) {
            // ripgrep可用，执行搜索
            const search = spawn('rg', ripgrepArgs, { stdio: 'inherit' });
            search.on('close', (searchCode) => {
              process.exit(searchCode);
            });
          } else {
            console.error('Error: Failed to verify ripgrep installation');
            process.exit(1);
          }
        });

      } catch (error) {
        console.error('Error executing ripgrep:', error.message);
        process.exit(1);
      }

      return; // 不要继续执行其他逻辑
    }
  }

  /**
   * 启动CLI程序
   * @private
   */
  async _startCLI() {
    // 实现CLI程序启动逻辑
    try {
      const { Command } = require('commander');
      const program = new Command();

      // 设置程序信息
      program
        .name('claude-code')
        .description('Claude Code - AI-powered development assistant')
        .version('1.0.0');

      // 添加全局选项
      program
        .option('-v, --verbose', 'Enable verbose output')
        .option('-q, --quiet', 'Suppress non-essential output')
        .option('--api-key <key>', 'Anthropic API key')
        .option('--model <model>', 'AI model to use')
        .option('--ide', 'Auto-connect to IDE');

      // 添加主要命令
      program
        .command('chat')
        .description('Start interactive chat session')
        .action(async () => {
          console.log('Starting interactive chat session...');
          await this._startInteractiveSession();
        });

      program
        .command('run <command>')
        .description('Run a specific command')
        .action(async (command) => {
          console.log(`Executing command: ${command}`);
          await this.commandProcessor.executeCommand(command);
        });

      // 解析命令行参数
      await program.parseAsync(process.argv);

    } catch (error) {
      console.error('Failed to start CLI:', error.message);
      process.exit(1);
    }
  }

  /**
   * 启动交互式会话
   * @private
   */
  async _startInteractiveSession() {
    console.log('🤖 Claude Code Interactive Session');
    console.log('Type /help for available commands, or start chatting!');
    console.log('Press Ctrl+C to exit.\n');

    // 这里应该启动实际的交互式会话
    // 目前只是一个占位符实现
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.on('data', (key) => {
      if (key.toString() === '\u0003') { // Ctrl+C
        console.log('\nGoodbye!');
        process.exit(0);
      }
    });
  }

  /**
   * 停止应用程序
   */
  async stop() {
    await this.shutdown();
  }

  /**
   * 优雅关闭应用程序
   * @original: 原始文件L57926-57928中的UR8函数
   */
  async shutdown() {
    if (!this.isRunning) {
      return;
    }

    console.log('正在关闭应用程序...');

    try {
      // 执行关闭处理器
      for (const handler of this.shutdownHandlers) {
        try {
          await handler();
        } catch (error) {
          console.error('关闭处理器执行失败:', error);
        }
      }

      // 清理MCP连接
      await mcpService.cleanup();

      // 取消所有活动的工具和命令
      toolEngine.cancelAllInProgress();
      commandProcessor.cancelAllActiveCommands();

      // 恢复光标显示
      const output = process.stderr.isTTY ? process.stderr :
                     process.stdout.isTTY ? process.stdout : undefined;
      output?.write('\x1B[?25h'); // 显示光标

      this.isRunning = false;
      console.log('应用程序已关闭');
    } catch (error) {
      console.error('应用程序关闭时发生错误:', error);
    }
  }

  /**
   * 添加关闭处理器
   * @param {Function} handler - 关闭处理器函数
   */
  addShutdownHandler(handler) {
    if (typeof handler === 'function') {
      this.shutdownHandlers.push(handler);
    }
  }

  /**
   * 获取应用程序状态
   * @returns {Object} 应用程序状态
   */
  getStatus() {
    return {
      initialized: this.initialized,
      running: this.isRunning,
      mcpStats: mcpService.getConnectionStats(),
      toolStats: toolEngine.getExecutionStats(),
      commandStats: commandProcessor.getExecutionStats(),
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      nodeVersion: process.version,
      platform: process.platform
    };
  }
}

// 创建应用程序实例
const app = new ClaudeCodeApp();

// 导出应用程序实例和主要模块
export {
  app as default,
  ClaudeCodeApp,
  utils,
  services,
  core
};

// 如果直接运行此文件，启动应用程序
if (import.meta.url === `file://${process.argv[1]}`) {
  app.start().catch(error => {
    console.error('应用程序启动失败:', error);
    process.exit(1);
  });
}
