
# 重构映射文档 (Refactoring Mapping)

## 目录结构规划

```
src/
├── core/                    # 核心功能模块
│   ├── module-system.js     # 模块系统相关工具
│   ├── lazy-loader.js       # 延迟加载器
│   └── main.js             # 主入口文件
├── utils/                   # 工具函数
│   ├── lodash/             # Lodash工具函数重构
│   │   ├── array.js        # 数组操作工具
│   │   ├── object.js       # 对象操作工具
│   │   ├── string.js       # 字符串操作工具
│   │   └── index.js        # 统一导出
│   ├── file-system.js      # 文件系统操作
│   ├── path-utils.js       # 路径处理工具
│   └── crypto-utils.js     # 加密相关工具
├── services/               # 服务层
│   ├── http-client.js      # HTTP客户端 (Axios重构)
│   ├── websocket.js        # WebSocket服务
│   ├── auth-service.js     # 认证服务
│   └── config-service.js   # 配置管理服务
├── cli/                    # 命令行接口
│   ├── commands/           # 各种CLI命令
│   │   ├── mcp.js         # MCP相关命令
│   │   ├── auth.js        # 认证相关命令
│   │   └── setup.js       # 设置相关命令
│   ├── ui/                # CLI用户界面组件
│   └── index.js           # CLI主入口
├── lib/                   # 第三方库适配器
│   ├── react-devtools.js  # React开发工具配置
│   └── polyfills.js       # 兼容性补丁
└── types/                 # TypeScript类型定义 (如需要)
    └── index.d.ts
```

## 模块依赖关系

### 核心模块依赖
- **`src/core/main.js`**
  - `import { createModuleWrapper } from './module-system.js';`
  - `import { createLazyLoader } from './lazy-loader.js';`
  - `import { httpClient } from '../services/http-client.js';`

### 工具模块依赖
- **`src/utils/index.js`**
  - `export * from './lodash/index.js';`
  - `export * from './file-system.js';`
  - `export * from './path-utils.js';`

### 服务模块依赖
- **`src/services/http-client.js`**
  - `import { createModuleWrapper } from '../core/module-system.js';`
  - 替换原始Axios代码为现代化实现

## 变量重命名映射

### 核心系统变量
| 原始名称 | 重构后名称 | 类型 | 用途 |
|---------|-----------|------|------|
| `xcB` | `createRequire` | Function | Node.js模块require创建器 |
| `E` | `createModuleWrapper` | Function | 模块包装器工厂 |
| `Mj` | `defineModuleExports` | Function | 模块导出定义器 |
| `gA1` | `createLazyLoader` | Function | 延迟加载器工厂 |
| `J1` | `moduleRequire` | Variable | require实例 |

### 外部模块引用
| 原始引用 | 重构后模块 | 功能描述 |
|---------|-----------|----------|
| `require("./nr1.isolated.js")` | `src/core/nr1-core.js` | NR1核心功能 |
| `require("./iIA.isolated.js")` | `src/utils/iia-utils.js` | IIA工具函数 |
| `require("./g.isolated.js")` | `src/utils/general.js` | 通用工具函数 |
| `require("./BT0.isolated.js")` | `src/services/bt0-service.js` | BT0服务模块 |

## 第三方库处理策略

### Lodash处理
- **策略**: 提取并重构为现代ES6+语法
- **目标**: `src/utils/lodash/`
- **优化**: 移除未使用的函数，优化性能

### Axios处理  
- **策略**: 保留核心HTTP功能，移除冗余代码
- **目标**: `src/services/http-client.js`
- **优化**: 使用现代fetch API作为备选

### React DevTools处理
- **策略**: 独立配置模块
- **目标**: `src/lib/react-devtools.js`
- **优化**: 条件加载，仅开发环境启用

## 代码重构原则

1. **逻辑等价性**: 确保重构后功能100%等价
2. **模块化**: 按功能职责清晰划分模块
3. **可读性**: 使用描述性命名和充分注释
4. **现代化**: 使用ES6+语法和最佳实践
5. **性能**: 优化加载和执行性能

## 重构检查清单

- [ ] 核心模块系统重构
- [ ] Lodash工具函数提取
- [ ] Axios HTTP客户端重构
- [ ] React DevTools配置分离
- [ ] CLI命令模块化
- [ ] 文件系统操作模块化
- [ ] 认证服务模块化
- [ ] 配置管理模块化
- [ ] 测试用例编写
- [ ] 文档完善

## 注意事项

1. **保持向后兼容**: 确保API接口不变
2. **错误处理**: 保留原有错误处理逻辑
3. **环境变量**: 保持环境配置一致性
4. **依赖版本**: 记录第三方库版本信息
5. **性能监控**: 对比重构前后性能指标

## 最新模块依赖映射 (第5000-5600行重构)

### 认证和安全模块
- **`services/oauth-service.js`**
  - `import { httpClient } from './http-service.js';`
  - `import { settingsService } from './settings-service.js';`

- **`services/credentials-service.js`**
  - `import { oauthService } from './oauth-service.js';`
  - `import { gitIgnoreManager } from '../utils/git-ignore-utils.js';`

- **`services/token-service.js`**
  - `import { credentialsService } from './credentials-service.js';`
  - `import { oauthService } from './oauth-service.js';`

- **`services/mtls-service.js`**
  - `import { Agent as HTTPSAgent } from 'https';`

### 配置和设置模块
- **`services/settings-service.js`**
  - `import { fileWatcherService } from './file-watcher-service.js';`
  - `import { gitIgnoreManager } from '../utils/git-ignore-utils.js';`

- **`services/file-watcher-service.js`**
  - `import { sep } from 'path';`

- **`services/mcp-server-service.js`**
  - `import { join } from 'path';`
  - `import { homedir } from 'os';`

### 工具和模型模块
- **`services/tool-mode-service.js`**
  - 独立模块，无外部依赖

- **`utils/model-utils.js`**
  - 独立模块，无外部依赖

### JSON处理模块
- **`utils/json-parser-utils.js`**
  - 独立模块，无外部依赖

- **`utils/json-formatter-utils.js`**
  - 独立模块，无外部依赖

- **`utils/json-edit-utils.js`**
  - `import { findNodeByPath, getJSONType, jsonParser } from './json-parser-utils.js';`

- **`utils/json-scanner-utils.js`**
  - 独立模块，无外部依赖

### Git和版本控制模块
- **`utils/git-utils.js`**
  - `import { join } from 'path';`
  - `import { homedir } from 'os';`

- **`utils/git-ignore-utils.js`**
  - `import { join } from 'path';`
  - `import { homedir } from 'os';`
  - `import { isDirectoryInsideGitRepository } from './git-utils.js';`

### 字符和编码模块
- **`utils/character-encoding-utils.js`**
  - 独立模块，无外部依赖

## 重构进度更新

### 已完成模块 (第0-5600行)
- ✅ `src/core/module-system.js` - 模块系统工具
- ✅ `src/core/lazy-loader.js` - 延迟加载器
- ✅ `src/utils/lodash/` - Lodash工具函数重构
- ✅ `src/utils/file-system.js` - 文件系统操作
- ✅ `src/utils/path-utils.js` - 路径处理工具
- ✅ `src/services/http-client.js` - HTTP客户端服务
- ✅ `src/services/oauth-service.js` - OAuth认证服务
- ✅ `src/services/credentials-service.js` - 凭据管理服务
- ✅ `src/services/token-service.js` - 令牌管理服务
- ✅ `src/services/mtls-service.js` - mTLS配置服务
- ✅ `src/services/settings-service.js` - 设置管理服务
- ✅ `src/services/file-watcher-service.js` - 文件监视器服务
- ✅ `src/services/mcp-server-service.js` - MCP服务器管理
- ✅ `src/services/tool-mode-service.js` - 工具模式管理
- ✅ `src/utils/json-parser-utils.js` - JSON解析器
- ✅ `src/utils/json-formatter-utils.js` - JSON格式化工具
- ✅ `src/utils/json-edit-utils.js` - JSON编辑工具
- ✅ `src/utils/json-scanner-utils.js` - JSON扫描器
- ✅ `src/utils/git-utils.js` - Git工具函数
- ✅ `src/utils/git-ignore-utils.js` - Git忽略文件工具
- ✅ `src/utils/character-encoding-utils.js` - 字符编码工具
- ✅ `src/utils/model-utils.js` - 模型管理工具

### 当前重构统计
- **已重构行数**: 5,600/57,930 (约9.7%)
- **已创建模块**: 70个文件
- **代码行数**: 约82,000行重构代码
- **服务模块**: 20个业务服务模块
- **工具函数**: 2200+ 个工具函数
