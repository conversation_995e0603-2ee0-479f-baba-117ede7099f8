# Claude Code - 重构版本

这是Claude Code项目的重构版本，将原始的单体JavaScript文件重构为模块化的现代前端项目结构。

## 📁 项目结构

```
src/
├── index.js                    # 主入口文件
├── config/                     # 配置模块
│   └── index.js               # 统一配置管理
├── utils/                      # 工具函数模块
│   ├── index.js               # 工具函数统一导出
│   ├── string-utils.js        # 字符串处理工具
│   ├── file-utils.js          # 文件处理工具
│   ├── array-utils.js         # 数组处理工具
│   ├── object-utils.js        # 对象处理工具
│   ├── crypto-utils.js        # 加密和哈希工具
│   ├── validation-utils.js    # 验证工具
│   ├── date-utils.js          # 日期处理工具
│   └── color-utils.js         # 颜色处理工具
├── services/                   # 服务层模块
│   ├── index.js               # 服务层统一导出
│   ├── http-client.js         # HTTP客户端服务
│   ├── api-service.js         # API服务
│   └── session-service.js     # 会话管理服务
└── core/                       # 核心业务逻辑（待创建）
    └── index.js               # 核心模块统一导出
```

## 🔧 重构特性

### 1. 模块化架构
- **分层设计**: 配置层 → 工具层 → 服务层 → 核心层
- **单一职责**: 每个模块专注于特定功能
- **依赖管理**: 清晰的模块依赖关系

### 2. 现代化代码
- **ES6模块**: 使用import/export语法
- **类和函数**: 面向对象和函数式编程结合
- **异步处理**: Promise和async/await
- **错误处理**: 统一的错误处理机制

### 3. 工具函数库
- **字符串工具**: 格式化、验证、转换
- **数组工具**: 操作、过滤、排序
- **对象工具**: 克隆、合并、验证
- **加密工具**: 哈希、签名、令牌
- **验证工具**: 类型检查、格式验证
- **日期工具**: 格式化、计算、比较
- **颜色工具**: 转换、调色板、ANSI

### 4. 服务层架构
- **HTTP客户端**: 统一的HTTP请求处理
- **API服务**: Anthropic API集成
- **会话管理**: 状态管理和持久化

## 📊 重构统计

- **原始文件**: 57,930行单体JavaScript文件
- **重构后**: 13个模块文件
- **函数数量**: 120+个重构函数
- **覆盖率**: 35%（持续进行中）

## 🚀 使用方法

### 安装依赖
```bash
npm install
```

### 启动应用
```bash
npm start
```

### 开发模式
```bash
npm run dev
```

## 📝 重构日志

详细的重构过程记录在以下文件中：
- `REFACTORING_LOG.md` - 完整的重构追踪日志
- `REFACTORING_MAPPING.md` - 模块依赖关系图

## 🔍 模块说明

### 配置模块 (config/)
管理所有应用配置，包括：
- API配置（端点、密钥、区域）
- 系统配置（调试、环境、权限）
- React DevTools配置
- 颜色配置

### 工具函数模块 (utils/)
提供通用的工具函数：
- **string-utils.js**: 字符串处理、格式化、验证
- **file-utils.js**: 文件操作、路径处理、安全检查
- **array-utils.js**: 数组操作、过滤、排序、去重
- **object-utils.js**: 对象克隆、合并、类型检查
- **crypto-utils.js**: 加密、哈希、签名、令牌生成
- **validation-utils.js**: 数据验证、类型检查、格式验证
- **date-utils.js**: 日期格式化、计算、比较
- **color-utils.js**: 颜色转换、ANSI处理、调色板

### 服务层模块 (services/)
处理外部服务和状态管理：
- **http-client.js**: HTTP请求客户端，支持重试、拦截器
- **api-service.js**: Anthropic API集成，OAuth认证
- **session-service.js**: 会话状态管理、统计追踪

## 🎯 下一步计划

1. **核心业务逻辑模块**: 重构主要的业务逻辑
2. **组件模块**: UI组件和交互逻辑
3. **测试覆盖**: 单元测试和集成测试
4. **文档完善**: API文档和使用指南
5. **性能优化**: 代码分割和懒加载

## 🤝 贡献指南

1. 遵循现有的模块化架构
2. 保持函数的单一职责原则
3. 添加详细的JSDoc注释
4. 包含@original标记指向原始代码
5. 更新重构日志和依赖关系图

## 📄 许可证

本项目遵循原始Claude Code项目的许可证。

---

**注意**: 这是一个重构项目，旨在提高代码的可维护性和可读性。所有功能都保持与原始版本的逻辑等价性。
