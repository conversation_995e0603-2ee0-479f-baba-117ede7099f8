# 项目结构概览

## 目录结构

```
src/
├── core/                             # 核心基础设施
│   ├── module-imports.js             # 模块导入管理和懒加载
│   └── lazy-loader.js                # 懒加载实现
│
├── data-structures/                  # 数据结构库
│   ├── linked-list.js                # 双向链表实现
│   ├── queue.js                      # 队列实现
│   ├── stack.js                      # 栈实现
│   ├── tree.js                       # 树结构实现
│   ├── graph.js                      # 图结构实现
│   └── hash-table.js                 # 哈希表实现
│
├── services/                         # 业务服务层
│   ├── http-client.js                # HTTP客户端服务
│   ├── ripgrep-service.js            # 文件搜索服务
│   ├── pdf-service.js                # PDF处理服务
│   ├── permission-service.js         # 权限管理服务
│   ├── workspace-service.js          # 工作空间管理服务
│   ├── mcp-config-service.js         # MCP配置管理服务
│   ├── file-cache-service.js         # 文件缓存服务
│   ├── file-read-service.js          # 文件读取服务
│   ├── file-permission-service.js    # 文件权限检查服务
│   └── file-operations-service.js    # 文件操作服务
│
└── utils/                            # 工具函数库
    ├── string-utils.js               # 字符串处理工具
    ├── array-utils.js                # 数组操作工具
    ├── object-utils.js               # 对象处理工具
    ├── color-utils.js                # 颜色处理工具
    ├── color-builder.js              # 颜色构建器
    ├── terminal-utils.js             # 终端操作工具
    ├── http-utils.js                 # HTTP工具函数
    ├── file-path-utils.js            # 文件路径工具
    ├── error-classes.js              # 错误类定义
    ├── validation-utils.js           # 验证工具函数
    ├── mcp-utils.js                  # MCP协议工具
    ├── windows-path-utils.js         # Windows路径工具
    ├── pattern-matching-utils.js     # 模式匹配工具
    └── abort-controller-polyfill.js  # AbortController兼容
```

## 模块功能说明

### 核心基础设施 (core/)

#### module-imports.js
- 统一的模块导入管理
- 懒加载机制实现
- 依赖关系管理
- 模块缓存优化

#### lazy-loader.js
- 懒加载工具函数
- 性能优化机制
- 内存使用优化

### 数据结构库 (data-structures/)

#### linked-list.js
- 双向链表完整实现
- 支持插入、删除、查找操作
- 迭代器支持
- 内存高效的节点管理

#### queue.js
- 队列数据结构
- FIFO操作支持
- 优先队列实现
- 批量操作支持

#### stack.js
- 栈数据结构
- LIFO操作支持
- 栈溢出保护
- 多栈管理

#### tree.js
- 通用树结构
- 二叉搜索树
- 树遍历算法
- 平衡树操作

#### graph.js
- 图数据结构
- 有向图和无向图
- 图遍历算法
- 最短路径算法

#### hash-table.js
- 哈希表实现
- 冲突解决机制
- 动态扩容
- 高性能哈希函数

### 业务服务层 (services/)

#### http-client.js
- 完整的HTTP客户端实现
- 支持GET、POST、PUT、DELETE等方法
- 请求/响应拦截器
- 错误处理和重试机制
- 超时控制和取消支持

#### ripgrep-service.js
- Ripgrep搜索引擎集成
- 文件搜索和统计
- macOS代码签名处理
- 跨平台可执行文件查找

#### pdf-service.js
- PDF文件处理和验证
- 文件大小限制检查
- Base64编码转换
- 批量PDF处理

#### permission-service.js
- 多源权限配置管理
- 工具权限检查逻辑
- 规则匹配算法
- 权限上下文管理

#### workspace-service.js
- 工作目录管理
- 路径解析和验证
- 命令搜索路径配置
- 跨平台路径处理

#### mcp-config-service.js
- MCP服务器配置管理
- 作用域验证和处理
- 传输类型验证
- HTTP头部解析

#### file-cache-service.js
- 基于修改时间的智能缓存
- 文件编码检测
- 缓存统计和管理
- 异步文件操作

#### file-read-service.js
- 文件读取权限控制
- 忽略模式处理
- 行数限制和格式化
- 配置管理和验证

#### file-permission-service.js
- 文件访问权限检查
- 基于规则的权限决策
- 工作目录安全验证
- 权限结果缓存

#### file-operations-service.js
- 文件搜索和内容读取
- 换行符检测和处理
- 截图路径解析
- 批量文件操作

### 工具函数库 (utils/)

#### string-utils.js
- 字符串处理和格式化
- 模板字符串支持
- 字符串验证和清理
- 编码转换工具

#### array-utils.js
- 数组操作和处理
- 数组去重和排序
- 数组分组和聚合
- 高性能数组算法

#### object-utils.js
- 对象深度操作
- 对象合并和克隆
- 属性路径访问
- 对象验证工具

#### color-utils.js
- 颜色格式转换
- ANSI颜色支持
- 256色和真彩色
- 颜色计算和混合

#### terminal-utils.js
- 终端操作和控制
- 文本样式和格式化
- 终端尺寸检测
- 跨平台终端支持

#### http-utils.js
- HTTP工具函数
- URL处理和验证
- 请求参数处理
- 响应数据解析

#### error-classes.js
- 自定义错误类定义
- 错误分类和处理
- 错误信息格式化
- 错误工厂模式

#### validation-utils.js
- 数据验证工具
- 类型检查和转换
- 格式验证器
- 验证规则组合

#### mcp-utils.js
- MCP协议工具函数
- 服务器管理工具
- 工具和资源过滤
- MCP名称解析

#### windows-path-utils.js
- Windows路径处理
- Cygwin路径转换
- Git Bash集成
- 跨平台路径工具

#### pattern-matching-utils.js
- 文件模式匹配
- 忽略模式处理
- 路径模式解析
- 模式编译和缓存

#### abort-controller-polyfill.js
- AbortController兼容实现
- 超时和组合信号
- 可中止Promise
- 环境兼容性处理

## 模块依赖关系

### 依赖层次
```
应用层 (services/)
    ↓
工具层 (utils/)
    ↓
数据结构层 (data-structures/)
    ↓
核心层 (core/)
```

### 关键依赖
- 所有服务模块依赖于工具函数库
- 工具函数库使用数据结构库
- 核心模块提供基础设施支持
- 跨模块通信通过标准接口

## 设计原则

### 1. 单一职责原则
每个模块都有明确的职责边界，避免功能重叠。

### 2. 开放封闭原则
模块对扩展开放，对修改封闭，支持功能扩展。

### 3. 依赖倒置原则
高层模块不依赖低层模块，都依赖于抽象接口。

### 4. 接口隔离原则
使用小而专一的接口，避免臃肿的接口设计。

### 5. 最少知识原则
模块之间保持最小的耦合度，减少相互依赖。

## 代码质量标准

### 命名规范
- 使用描述性的英文命名
- 函数名使用动词开头
- 类名使用名词，首字母大写
- 常量使用大写字母和下划线

### 注释规范
- 每个模块都有详细的功能说明
- 每个函数都有完整的JSDoc注释
- 复杂逻辑添加行内注释
- 使用中文注释提高可读性

### 错误处理
- 统一的错误处理机制
- 详细的错误信息和堆栈跟踪
- 分层的错误处理策略
- 错误恢复和降级处理

### 性能优化
- 懒加载和缓存机制
- 批量操作支持
- 内存使用优化
- 异步操作优化

这个项目结构展示了如何将大型单体代码重构为清晰、可维护的模块化架构，为后续开发和维护提供了坚实的基础。
