/**
 * 配置命令处理模块
 * @description Claude Code的配置管理CLI命令实现
 * @original 原始代码在57633-57685行
 */

/**
 * 创建配置命令组
 * @param {Object} program - Commander.js程序实例
 * @returns {Object} 配置命令组
 * @original let B = A.command("config").description("Manage configuration (eg. claude config set -g theme dark)").helpOption("-h, --help", "Display help for command");
 */
export function createConfigCommands(program) {
  const configCommand = program
    .command("config")
    .description("Manage configuration (eg. claude config set -g theme dark)")
    .helpOption("-h, --help", "Display help for command");

  // 获取配置值命令
  configCommand
    .command("get <key>")
    .description("Get a config value")
    .option("-g, --global", "Use global config")
    .helpOption("-h, --help", "Display help for command")
    .action(async (key, options) => {
      const { global: isGlobal } = options;
      
      try {
        // 初始化工作空间
        await initializeWorkspace(getCurrentWorkingDirectory(), "default", false, false, undefined);
        
        // 记录遥测数据
        logTelemetry("tengu_config_get", {
          key,
          global: isGlobal
        });
        
        // 获取配置值
        const value = getConfigValue(key, isGlobal ?? false);
        
        // 输出结果
        process.stdout.write(JSON.stringify(value) + '\n');
        process.exit(0);
      } catch (error) {
        console.error(`Error getting config value: ${error.message}`);
        process.exit(1);
      }
    });

  // 设置配置值命令
  configCommand
    .command("set <key> <value>")
    .description("Set a config value")
    .option("-g, --global", "Use global config")
    .helpOption("-h, --help", "Display help for command")
    .action(async (key, value, options) => {
      const { global: isGlobal } = options;
      
      try {
        // 初始化工作空间
        await initializeWorkspace(getCurrentWorkingDirectory(), "default", false, false, undefined);
        
        // 记录遥测数据
        logTelemetry("tengu_config_set", {
          key,
          global: isGlobal
        });
        
        // 设置配置值
        setConfigValue(key, value, isGlobal ?? false);
        
        // 输出结果
        process.stdout.write(`Set ${key} to ${value}\n`);
        process.exit(0);
      } catch (error) {
        console.error(`Error setting config value: ${error.message}`);
        process.exit(1);
      }
    });

  // 移除配置值命令
  configCommand
    .command("remove <key> [values...]")
    .alias("rm")
    .description("Remove a config value or items from a config array")
    .option("-g, --global", "Use global config")
    .helpOption("-h, --help", "Display help for command")
    .action(async (key, values, options) => {
      const { global: isGlobal } = options;
      
      try {
        // 初始化工作空间
        await initializeWorkspace(getCurrentWorkingDirectory(), "default", false, false, undefined);
        
        // 检查是否为数组配置且提供了要移除的值
        if (isConfigArray(key, isGlobal ?? false) && values && values.length > 0) {
          // 处理逗号分隔的值
          const processedValues = values
            .flatMap(value => value.includes(",") ? value.split(",") : value)
            .map(value => value.trim())
            .filter(value => value.length > 0);
          
          if (processedValues.length === 0) {
            console.error("Error: No valid values provided");
            process.exit(1);
          }
          
          // 记录遥测数据
          logTelemetry("tengu_config_remove", {
            key,
            global: isGlobal,
            count: values.length
          });
          
          // 从数组中移除指定值
          removeConfigArrayValues(key, processedValues, isGlobal ?? false, false);
          
          console.log(`Removed from ${key} in ${isGlobal ? "global" : "project"} config: ${processedValues.join(", ")}`);
        } else {
          // 记录遥测数据
          logTelemetry("tengu_config_delete", {
            key,
            global: isGlobal
          });
          
          // 完全删除配置键
          deleteConfigKey(key, isGlobal ?? false);
          
          process.stdout.write(JSON.stringify(`Removed ${key}`) + '\n');
        }
        
        process.exit(0);
      } catch (error) {
        console.error(`Error removing config value: ${error.message}`);
        process.exit(1);
      }
    });

  // 列出配置值命令
  configCommand
    .command("list")
    .alias("ls")
    .description("List all config values")
    .option("-g, --global", "Use global config", false)
    .helpOption("-h, --help", "Display help for command")
    .action(async (options) => {
      const { global: isGlobal } = options;
      
      try {
        // 初始化工作空间
        await initializeWorkspace(getCurrentWorkingDirectory(), "default", false, false, undefined);
        
        // 记录遥测数据
        logTelemetry("tengu_config_list", {
          global: isGlobal
        });
        
        // 获取所有配置值
        const allConfig = getAllConfigValues(isGlobal ?? false);
        
        // 输出结果
        process.stdout.write(JSON.stringify(allConfig, null, 2) + '\n');
        process.exit(0);
      } catch (error) {
        console.error(`Error listing config values: ${error.message}`);
        process.exit(1);
      }
    });

  // 添加配置数组项命令
  configCommand
    .command("add <key> <values...>")
    .description("Add items to a config array (space or comma separated)")
    .option("-g, --global", "Use global config")
    .helpOption("-h, --help", "Display help for command")
    .action(async (key, values, options) => {
      const { global: isGlobal } = options;
      
      try {
        // 初始化工作空间
        await initializeWorkspace(getCurrentWorkingDirectory(), "default", false, false, undefined);
        
        // 处理逗号分隔的值
        const processedValues = values
          .flatMap(value => value.includes(",") ? value.split(",") : value)
          .map(value => value.trim())
          .filter(value => value.length > 0);
        
        if (processedValues.length === 0) {
          console.error("Error: No valid values provided");
          process.exit(1);
        }
        
        // 记录遥测数据
        logTelemetry("tengu_config_add", {
          key,
          global: isGlobal,
          count: values.length
        });
        
        // 添加值到配置数组
        addConfigArrayValues(key, processedValues, isGlobal ?? false, false);
        
        console.log(`Added to ${key} in ${isGlobal ? "global" : "project"} config: ${processedValues.join(", ")}`);
        process.exit(0);
      } catch (error) {
        console.error(`Error adding config values: ${error.message}`);
        process.exit(1);
      }
    });

  return configCommand;
}

// 辅助函数 - 这些需要在其他模块中实现
function getCurrentWorkingDirectory() {
  return process.cwd();
}

async function initializeWorkspace(workingDir, mode, printMode, worktree, sessionId) {
  // @todo: 实现Ib()函数的逻辑
  console.log(`Initializing workspace: ${workingDir}`);
}

function logTelemetry(event, data) {
  // @todo: 实现C1()函数的逻辑
  console.log(`Telemetry: ${event}`, data);
}

function getConfigValue(key, isGlobal) {
  // @todo: 实现k$2()函数的逻辑
  console.log(`Getting config value: ${key} (global: ${isGlobal})`);
  return null;
}

function setConfigValue(key, value, isGlobal) {
  // @todo: 实现_$2()函数的逻辑
  console.log(`Setting config value: ${key} = ${value} (global: ${isGlobal})`);
}

function isConfigArray(key, isGlobal) {
  // @todo: 实现Qo()函数的逻辑
  // 检查指定的配置键是否为数组类型
  return false;
}

function removeConfigArrayValues(key, values, isGlobal, force) {
  // @todo: 实现T$2()函数的逻辑
  console.log(`Removing config array values: ${key} -= [${values.join(", ")}] (global: ${isGlobal})`);
}

function deleteConfigKey(key, isGlobal) {
  // @todo: 实现x$2()函数的逻辑
  console.log(`Deleting config key: ${key} (global: ${isGlobal})`);
}

function getAllConfigValues(isGlobal) {
  // @todo: 实现v$2()函数的逻辑
  console.log(`Getting all config values (global: ${isGlobal})`);
  return {};
}

function addConfigArrayValues(key, values, isGlobal, force) {
  // @todo: 实现aM1()函数的逻辑
  console.log(`Adding config array values: ${key} += [${values.join(", ")}] (global: ${isGlobal})`);
}
