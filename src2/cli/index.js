/**
 * Claude Code CLI主入口
 * @description Claude Code命令行工具的主要入口点
 * @original 原始代码在57283-57924行 (ER8函数)
 */

import { Command, Option } from 'commander';
import { createMcpCommands } from './commands/mcp.js';
import { createConfigCommands } from './commands/config.js';
import { createSystemCommands } from './commands/system.js';
import { handleMainCommand } from './handlers/main-command.js';

/**
 * 创建主CLI程序
 * @returns {Command} Commander.js程序实例
 * @original let A = new gdB(); A.name("claude").description("<PERSON> Code - starts an interactive session by default, use -p/--print for non-interactive output")...
 */
export function createCliProgram() {
  const program = new Command();

  // 主程序配置
  program
    .name("claude")
    .description("<PERSON> Code - starts an interactive session by default, use -p/--print for non-interactive output")
    .argument("[prompt]", "Your prompt", String)
    .helpOption("-h, --help", "Display help for command")
    .version("1.0.72", "-v, --version", "Output the version number");

  // 调试选项
  program
    .option("-d, --debug", "Enable debug mode", () => true)
    .addOption(new Option("-d2e, --debug-to-stderr", "Enable debug mode (to stderr)")
      .argParser(Boolean)
      .hideHelp())
    .option("--verbose", "Override verbose mode setting from config", () => true)
    .option("--mcp-debug", "[DEPRECATED. Use --debug instead] Enable MCP debug mode (shows MCP server errors)", () => true);

  // 输出选项
  program
    .option("-p, --print", "Print response and exit (useful for pipes)", () => true)
    .addOption(new Option("--output-format <format>", 'Output format (only works with --print): "text" (default), "json" (single result), or "stream-json" (realtime streaming)')
      .choices(["text", "json", "stream-json"]))
    .addOption(new Option("--input-format <format>", 'Input format (only works with --print): "text" (default), or "stream-json" (realtime streaming input)')
      .choices(["text", "stream-json"]));

  // 会话选项
  program
    .option("-c, --continue", "Continue the most recent conversation", () => true)
    .option("-r, --resume [sessionId]", "Resume a conversation - provide a session ID or interactively select a conversation to resume", value => value || true)
    .option("--session-id <uuid>", "Use a specific session ID for the conversation (must be a valid UUID)");

  // 模型选项
  program
    .option("--model <model>", "Model for the current session. Provide an alias for the latest model (e.g. 'sonnet' or 'opus') or a model's full name (e.g. 'claude-sonnet-4-20250514').")
    .option("--fallback-model <model>", "Enable automatic fallback to specified model when default model is overloaded (only works with --print)");

  // 系统提示选项
  program
    .addOption(new Option("--system-prompt <prompt>", "System prompt to use for the session  (only works with --print)")
      .argParser(String)
      .hideHelp())
    .addOption(new Option("--system-prompt-file <file>", "Read system prompt from a file (only works with --print)")
      .argParser(String)
      .hideHelp())
    .addOption(new Option("--append-system-prompt <prompt>", "Append a system prompt to the default system prompt")
      .argParser(String));

  // 权限和安全选项
  program
    .option("--dangerously-skip-permissions", "Bypass all permission checks. Recommended only for sandboxes with no internet access.", () => true)
    .addOption(new Option("--permission-mode <mode>", "Permission mode to use for the session")
      .argParser(String)
      .choices(getValidPermissionModes()))
    .addOption(new Option("--permission-prompt-tool <tool>", "MCP tool to use for permission prompts (only works with --print)")
      .argParser(String)
      .hideHelp());

  // 工具选项
  program
    .option("--allowedTools <tools...>", 'Comma or space-separated list of tool names to allow (e.g. "Bash(git:*) Edit")')
    .option("--disallowedTools <tools...>", 'Comma or space-separated list of tool names to deny (e.g. "Bash(git:*) Edit")');

  // MCP配置选项
  program
    .option("--mcp-config <file or string>", "Load MCP servers from a JSON file or string")
    .option("--strict-mcp-config", "Only use MCP servers from --mcp-config, ignoring all other MCP configurations", () => true);

  // 其他选项
  program
    .option("--settings <file-or-json>", "Path to a settings JSON file or a JSON string to load additional settings from")
    .option("--add-dir <directories...>", "Additional directories to allow tool access to")
    .option("--ide", "Automatically connect to IDE on startup if exactly one valid IDE is available", () => true)
    .addOption(new Option("--max-turns <turns>", "Maximum number of agentic turns in non-interactive mode. This will early exit the conversation after the specified number of turns. (only works with --print)")
      .argParser(Number)
      .hideHelp());

  // 隐藏选项
  program
    .addOption(new Option("--teleport [session]", "Resume a teleport session, optionally specify session ID")
      .hideHelp())
    .addOption(new Option("--remote <description>", "Create a remote session with the given description")
      .hideHelp());

  // 主命令处理
  program.action(handleMainCommand);

  // 添加子命令
  createConfigCommands(program);
  createMcpCommands(program);
  createSystemCommands(program);


  return program;
}

// 辅助函数 - 这些需要在其他模块中实现
function getValidPermissionModes() {
  // @todo: 实现_K1的逻辑
  return ["ask", "allow", "deny"];
}


