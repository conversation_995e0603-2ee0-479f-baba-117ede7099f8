/**
 * 模型选择服务
 * @description 重构自原始文件中的模型选择代码，对应第5634-5700行
 * @original 原始代码行 5634-5700
 */

/**
 * 获取小型快速模型
 * @returns {string} 小型快速模型名称
 * @original og函数
 */
export function getSmallFastModel() {
  return process.env.ANTHROPIC_SMALL_FAST_MODEL || getModelConfig().haiku35;
}

/**
 * 检查是否为Opus模型
 * @param {string} modelName - 模型名称
 * @returns {boolean} 是否为Opus模型
 * @original rr函数
 */
export function isOpusModel(modelName) {
  const modelConfig = getModelConfig();
  return modelName === modelConfig.opus40 || modelName === modelConfig.opus41;
}

/**
 * 获取主循环模型
 * @param {Object} options - 选项
 * @returns {string} 主循环模型名称
 * @original fM1函数
 */
export function getMainLoopModel(options) {
  const { permissionMode, mainLoopModel } = options;
  return mainLoopModel;
}

/**
 * 解析模型名称
 * @param {string} modelName - 模型名称
 * @returns {string} 解析后的模型名称
 * @original aw函数
 */
export function parseModelName(modelName) {
  if (modelName.includes("claude-opus-4-1")) {
    return "claude-opus-4-1";
  }
  
  if (modelName.includes("claude-opus-4")) {
    return "claude-opus-4";
  }
  
  const match = modelName.match(/(claude-(\d+-\d+-)?\w+)/);
  if (match && match[1]) {
    return match[1];
  }
  
  return modelName;
}

/**
 * 获取模型显示名称
 * @param {string} modelName - 模型名称
 * @returns {string} 显示名称
 * @original DP函数
 */
export function getModelDisplayName(modelName) {
  const modelConfig = getModelConfig();
  
  if (modelName === modelConfig.opus41) return "Opus 4.1";
  if (modelName === modelConfig.opus40) return "Opus 4";
  if (modelName === modelConfig.sonnet40) return "Sonnet 4";
  if (modelName === modelConfig.sonnet37) return "Sonnet 3.7";
  if (modelName === modelConfig.sonnet35) return "Sonnet 3.5";
  if (modelName === modelConfig.haiku35) return "Haiku 3.5";
  
  return modelName;
}

/**
 * 获取默认模型描述
 * @returns {string} 默认模型描述
 * @original hM1函数
 */
export function getDefaultModelDescription() {
  if (isEnterpriseVersion() && !isPaidUser()) {
    return getEnterpriseModelConfig().description;
  }
  
  const userConfig = getUserConfig();
  const fallbackThreshold = userConfig.fallbackAvailableWarningThreshold;
  
  if (fallbackThreshold === undefined) {
    return "Use Opus 4.1 or Sonnet 4 based on Max usage limits";
  }
  
  return `Opus 4.1 for up to ${(fallbackThreshold * 100).toFixed(0)}% of usage limits, then use Sonnet 4`;
}

/**
 * 获取推荐模型选项
 * @returns {Object} 推荐模型选项
 * @original rg函数
 */
export function getRecommendedModelOption() {
  if (isEnterpriseVersion() && !isPaidUser()) {
    return {
      value: null,
      label: "Sonnet",
      description: getEnterpriseModelConfig().description
    };
  }
  
  if (isPaidUser()) {
    return {
      value: null,
      label: "Default (recommended)",
      description: getDefaultModelDescription()
    };
  }
  
  const defaultModel = getDefaultModel({ forDisplay: true });
  const displayName = getModelDisplayName(defaultModel);
  
  return {
    value: null,
    label: "Default (recommended)",
    description: `Use the default model (currently ${displayName}) · $3/$15 per Mtok`
  };
}

/**
 * 模型选择服务类
 * @description 提供完整的模型选择和管理功能
 */
export class ModelSelectionService {
  constructor() {
    this.modelOptions = new Map();
    this.defaultModel = null;
    this.userPreferences = {};
  }

  /**
   * 获取可用模型选项
   * @returns {Array} 模型选项数组
   */
  getAvailableModels() {
    const models = [];
    
    // 添加推荐选项
    models.push(getRecommendedModelOption());
    
    // 添加Sonnet选项
    models.push({
      value: "sonnet",
      label: "Sonnet",
      description: "Sonnet 4 for daily use · $3/$15 per Mtok"
    });
    
    // 添加Opus选项（如果用户有权限）
    if (this.canUseOpus()) {
      models.push({
        value: "opus",
        label: "Opus",
        description: "Opus 4.1 for complex tasks · $15/$75 per Mtok"
      });
    }
    
    // 添加Haiku选项
    models.push({
      value: "haiku",
      label: "Haiku",
      description: "Haiku 3.5 for quick tasks · $0.25/$1.25 per Mtok"
    });
    
    return models;
  }

  /**
   * 根据任务类型推荐模型
   * @param {string} taskType - 任务类型
   * @param {Object} context - 上下文信息
   * @returns {string} 推荐的模型名称
   */
  recommendModel(taskType, context = {}) {
    const { complexity = "medium", urgency = "normal", budget = "normal" } = context;
    
    // 根据任务类型和上下文推荐模型
    switch (taskType) {
      case "coding":
      case "analysis":
        if (complexity === "high" && this.canUseOpus()) {
          return getModelConfig().opus41;
        }
        return getModelConfig().sonnet40;
      
      case "creative":
      case "writing":
        if (complexity === "high" && budget === "high" && this.canUseOpus()) {
          return getModelConfig().opus41;
        }
        return getModelConfig().sonnet40;
      
      case "quick":
      case "simple":
        return getModelConfig().haiku35;
      
      case "translation":
        return getModelConfig().sonnet35;
      
      default:
        return this.getDefaultModel();
    }
  }

  /**
   * 获取默认模型
   * @param {Object} options - 选项
   * @returns {string} 默认模型名称
   */
  getDefaultModel(options = {}) {
    if (this.defaultModel) {
      return this.defaultModel;
    }
    
    // 根据用户类型和设置确定默认模型
    if (isEnterpriseVersion() && !isPaidUser()) {
      return getModelConfig().sonnet40;
    }
    
    if (isPaidUser()) {
      return getModelConfig().opus41;
    }
    
    return getModelConfig().sonnet40;
  }

  /**
   * 设置默认模型
   * @param {string} modelName - 模型名称
   */
  setDefaultModel(modelName) {
    if (this.validateModel(modelName)) {
      this.defaultModel = modelName;
      this.saveUserPreferences();
    }
  }

  /**
   * 验证模型是否可用
   * @param {string} modelName - 模型名称
   * @returns {boolean} 是否可用
   */
  validateModel(modelName) {
    const modelConfig = getModelConfig();
    const availableModels = Object.values(modelConfig);
    
    return availableModels.includes(modelName);
  }

  /**
   * 检查用户是否可以使用Opus模型
   * @returns {boolean} 是否可以使用Opus
   * @private
   */
  canUseOpus() {
    return isPaidUser() || hasOpusAccess();
  }

  /**
   * 获取模型定价信息
   * @param {string} modelName - 模型名称
   * @returns {Object} 定价信息
   */
  getModelPricing(modelName) {
    const modelConfig = getModelConfig();
    
    const pricingMap = {
      [modelConfig.opus41]: { input: 15, output: 75 },
      [modelConfig.opus40]: { input: 15, output: 75 },
      [modelConfig.sonnet40]: { input: 3, output: 15 },
      [modelConfig.sonnet37]: { input: 3, output: 15 },
      [modelConfig.sonnet35]: { input: 3, output: 15 },
      [modelConfig.haiku35]: { input: 0.25, output: 1.25 }
    };
    
    return pricingMap[modelName] || { input: 0, output: 0 };
  }

  /**
   * 获取模型能力信息
   * @param {string} modelName - 模型名称
   * @returns {Object} 能力信息
   */
  getModelCapabilities(modelName) {
    const capabilities = {
      contextLength: 200000,
      multimodal: false,
      codeGeneration: true,
      reasoning: "medium",
      creativity: "medium",
      speed: "medium"
    };
    
    if (isOpusModel(modelName)) {
      capabilities.reasoning = "high";
      capabilities.creativity = "high";
      capabilities.speed = "slow";
      capabilities.multimodal = true;
    } else if (modelName.includes("sonnet")) {
      capabilities.reasoning = "high";
      capabilities.speed = "fast";
    } else if (modelName.includes("haiku")) {
      capabilities.reasoning = "medium";
      capabilities.speed = "very-fast";
      capabilities.contextLength = 200000;
    }
    
    return capabilities;
  }

  /**
   * 获取用户偏好
   * @returns {Object} 用户偏好
   */
  getUserPreferences() {
    return { ...this.userPreferences };
  }

  /**
   * 设置用户偏好
   * @param {Object} preferences - 用户偏好
   */
  setUserPreferences(preferences) {
    this.userPreferences = { ...this.userPreferences, ...preferences };
    this.saveUserPreferences();
  }

  /**
   * 保存用户偏好
   * @private
   */
  saveUserPreferences() {
    const userConfig = getUserConfig();
    userConfig.modelPreferences = {
      defaultModel: this.defaultModel,
      ...this.userPreferences
    };
    saveUserConfig(userConfig);
  }

  /**
   * 加载用户偏好
   * @private
   */
  loadUserPreferences() {
    const userConfig = getUserConfig();
    const preferences = userConfig.modelPreferences || {};
    
    this.defaultModel = preferences.defaultModel;
    this.userPreferences = preferences;
  }

  /**
   * 获取模型统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const availableModels = this.getAvailableModels();
    
    return {
      totalModels: availableModels.length,
      defaultModel: this.getDefaultModel(),
      canUseOpus: this.canUseOpus(),
      userType: isPaidUser() ? "paid" : "free",
      isEnterprise: isEnterpriseVersion()
    };
  }
}

// 辅助函数

/**
 * 获取模型配置
 * @returns {Object} 模型配置
 * @original EI函数的实现（推测）
 */
function getModelConfig() {
  return {
    opus41: "claude-3-opus-20240229",
    opus40: "claude-3-opus-20240229",
    sonnet40: "claude-3-5-sonnet-20241022",
    sonnet37: "claude-3-7-sonnet-20240307",
    sonnet35: "claude-3-5-sonnet-20241022",
    haiku35: "claude-3-5-haiku-20241022"
  };
}

/**
 * 获取企业模型配置
 * @returns {Object} 企业模型配置
 * @original gM1对象的实现（推测）
 */
function getEnterpriseModelConfig() {
  return {
    description: "Enterprise Sonnet model for business use"
  };
}

/**
 * 获取默认模型
 * @param {Object} options - 选项
 * @returns {string} 默认模型
 * @original r81函数的实现（推测）
 */
function getDefaultModel(options = {}) {
  const service = new ModelSelectionService();
  return service.getDefaultModel(options);
}

/**
 * 检查是否为企业版本
 * @returns {boolean} 是否为企业版本
 * @original F9函数的实现（推测）
 */
function isEnterpriseVersion() {
  return process.env.CLAUDE_ENTERPRISE === "true";
}

/**
 * 检查是否为付费用户
 * @returns {boolean} 是否为付费用户
 * @original mY函数的实现（推测）
 */
function isPaidUser() {
  return process.env.CLAUDE_PAID_USER === "true";
}

/**
 * 检查是否有Opus访问权限
 * @returns {boolean} 是否有Opus访问权限
 * @original 推测函数
 */
function hasOpusAccess() {
  return process.env.CLAUDE_OPUS_ACCESS === "true";
}

/**
 * 获取用户配置
 * @returns {Object} 用户配置
 * @original E0函数的实现（推测）
 */
function getUserConfig() {
  return {};
}

/**
 * 保存用户配置
 * @param {Object} config - 用户配置
 * @original pA函数的实现（推测）
 */
function saveUserConfig(config) {
  // @todo: 实现保存用户配置的逻辑
}

// 创建默认模型选择服务实例
export const modelSelectionService = new ModelSelectionService();

// 导出便捷函数
export const ModelSelection = {
  getAvailableModels: () => modelSelectionService.getAvailableModels(),
  recommendModel: (taskType, context) => modelSelectionService.recommendModel(taskType, context),
  getDefaultModel: (options) => modelSelectionService.getDefaultModel(options),
  setDefaultModel: (modelName) => modelSelectionService.setDefaultModel(modelName),
  getModelPricing: (modelName) => modelSelectionService.getModelPricing(modelName),
  getModelCapabilities: (modelName) => modelSelectionService.getModelCapabilities(modelName),
  getStats: () => modelSelectionService.getStats()
};
