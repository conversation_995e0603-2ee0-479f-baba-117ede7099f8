/**
 * Ripgrep搜索服务
 * @description 重构自原始文件中的Ripgrep集成代码，对应第2700-2785行
 * @original 原始代码行 2700-2785
 */

import { execFile } from "child_process";
import { join, resolve } from "path";
import { fileURLToPath } from "url";
import { createLazyLoader } from '../core/module-imports.js';

/**
 * 当前模块目录
 * @original v0Q变量
 */
const currentDir = fileURLToPath(import.meta.url);
const projectRoot = join(currentDir, "../");

/**
 * 检查是否在Bun环境中运行
 * @returns {boolean} 是否在Bun环境
 * @original function Kw()
 */
function isBunEnvironment() {
  return typeof Bun !== "undefined" && 
         !!Bun?.embeddedFiles && 
         Array.isArray(Bun?.embeddedFiles) && 
         (Bun?.embeddedFiles?.length ?? 0) > 0;
}

/**
 * 获取Ripgrep可执行文件路径
 * @returns {string} Ripgrep可执行文件路径
 * @original gC1变量和相关逻辑
 */
const getRipgrepPath = createLazyLoader(() => {
  // @todo: 实现y6A.findActualExecutable的逻辑
  const { cmd } = findActualExecutable("rg", []);
  
  if (cmd !== "rg" && !process.env.USE_BUILTIN_RIPGREP) {
    return cmd;
  } else {
    const vendorPath = resolve(projectRoot, "vendor", "ripgrep");
    
    if (process.platform === "win32") {
      return resolve(vendorPath, "x64-win32", "rg.exe");
    }
    
    return resolve(vendorPath, `${process.arch}-${process.platform}`, "rg");
  }
});

/**
 * 获取Ripgrep执行配置
 * @returns {Object} 包含路径和参数的配置对象
 * @original function Ts1()
 */
export function getRipgrepConfig() {
  return {
    rgPath: isBunEnvironment() ? process.execPath : getRipgrepPath(),
    rgArgs: isBunEnvironment() ? ["--ripgrep"] : []
  };
}

/**
 * 执行Ripgrep命令
 * @param {string[]} args - Ripgrep参数
 * @param {string} searchPath - 搜索路径
 * @param {AbortSignal} signal - 中止信号
 * @param {Function} callback - 回调函数
 * @returns {ChildProcess} 子进程对象
 * @original function f0Q(A, B, Q, D)
 */
function executeRipgrep(args, searchPath, signal, callback) {
  const { rgPath, rgArgs } = getRipgrepConfig();
  
  return execFile(rgPath, [...rgArgs, ...args, searchPath], {
    maxBuffer: 20000000,  // 20MB缓冲区
    signal: signal,
    timeout: 10000        // 10秒超时
  }, callback);
}

/**
 * 异步执行Ripgrep搜索
 * @param {string[]} args - Ripgrep参数
 * @param {string} searchPath - 搜索路径
 * @param {AbortSignal} signal - 中止信号
 * @returns {Promise<string[]>} 搜索结果行数组
 * @original async function PO(A, B, Q)
 */
export async function executeRipgrepSearch(args, searchPath, signal) {
  // 如果不在Bun环境中，先进行代码签名检查
  if (!isBunEnvironment()) {
    await ensureRipgrepSigned();
  }
  
  return new Promise(resolve => {
    executeRipgrep(args, searchPath, signal, (error, stdout, stderr) => {
      if (error) {
        // 处理不同的错误代码
        if (error.code !== 1 && error.code !== 2) {
          console.error("rg spawn failed: " + error);
          console.error(error);
          resolve([]);
        } else if (error.code === 2 && stdout && stdout.trim().length > 0) {
          // 错误代码2但有输出，返回结果
          resolve(stdout.trim().split('\n').filter(Boolean));
        } else {
          if (error.code === 2) {
            console.error("rg error (2) with no stdout: " + JSON.stringify(error) + stderr);
          }
          resolve([]);
        }
      } else {
        // 成功执行，返回结果
        resolve(stdout.trim().split('\n').filter(Boolean));
      }
    });
  });
}

/**
 * 搜索文件列表
 * @param {string} searchPath - 搜索路径
 * @param {AbortSignal} signal - 中止信号
 * @param {number} limit - 结果限制数量
 * @returns {Promise<string[]>} 文件路径数组
 * @original async function k6A(A, B, Q)
 */
export async function searchFiles(searchPath, signal, limit) {
  try {
    const results = await executeRipgrepSearch(["-l", "."], searchPath, signal);
    return results.slice(0, limit);
  } catch {
    return [];
  }
}

/**
 * 统计目录中的文件数量
 * @param {string} searchPath - 搜索路径
 * @param {AbortSignal} signal - 中止信号
 * @param {string[]} excludePatterns - 排除模式数组
 * @returns {Promise<number>} 文件数量（四舍五入到最近的10的幂）
 * @original uC1变量的实现
 */
export const countFiles = createLazyLoader(async (searchPath, signal, excludePatterns = []) => {
  try {
    const args = ["--files", "--hidden"];
    
    // 添加排除模式
    excludePatterns.forEach(pattern => {
      args.push("--glob", `!${pattern}`);
    });
    
    const results = await executeRipgrepSearch(args, searchPath, signal);
    const count = results.length;
    
    if (count === 0) return 0;
    
    // 四舍五入到最近的10的幂
    const magnitude = Math.floor(Math.log10(count));
    const base = Math.pow(10, magnitude);
    return Math.round(count / base) * base;
  } catch (error) {
    console.error(error instanceof Error ? error : new Error(String(error)));
    return 0;
  }
});

/**
 * 代码签名状态标志
 * @original j6A变量
 */
let isRipgrepSigned = false;

/**
 * 确保Ripgrep在macOS上已签名
 * @returns {Promise<void>}
 * @original async function h0Q()
 */
async function ensureRipgrepSigned() {
  // 只在macOS上执行，且只执行一次
  if (process.platform !== "darwin" || isRipgrepSigned) {
    return;
  }
  
  isRipgrepSigned = true;
  
  try {
    // 检查当前签名状态
    const codesignCheck = await executeCommand("codesign", ["-vv", "-d", getRipgrepPath()], {
      preserveOutputOnError: false
    });
    
    // 检查是否为linker-signed
    const isLinkerSigned = codesignCheck.stdout
      .split('\n')
      .find(line => line.includes("linker-signed"));
    
    if (!isLinkerSigned) {
      return;
    }
    
    // 重新签名
    const signResult = await executeCommand("codesign", [
      "--sign", "-", 
      "--force", 
      "--preserve-metadata=entitlements,requirements,flags,runtime", 
      getRipgrepPath()
    ]);
    
    if (signResult.code !== 0) {
      console.error(new Error(`Failed to sign ripgrep: ${signResult.stdout} ${signResult.stderr}`));
      return;
    }
    
    // 移除隔离属性
    const xattrResult = await executeCommand("xattr", [
      "-d", 
      "com.apple.quarantine", 
      getRipgrepPath()
    ]);
    
    if (xattrResult.code !== 0) {
      console.error(new Error(`Failed to remove quarantine: ${xattrResult.stdout} ${xattrResult.stderr}`));
    }
  } catch (error) {
    console.error(error);
  }
}

/**
 * Ripgrep服务类
 * @description 提供Ripgrep搜索功能的封装
 */
export class RipgrepService {
  constructor() {
    this.config = getRipgrepConfig();
  }

  /**
   * 搜索文本内容
   * @param {string} pattern - 搜索模式
   * @param {string} searchPath - 搜索路径
   * @param {Object} options - 搜索选项
   * @returns {Promise<string[]>} 搜索结果
   */
  async searchText(pattern, searchPath, options = {}) {
    const args = [];
    
    // 添加基本搜索参数
    if (options.ignoreCase) args.push("-i");
    if (options.wholeWord) args.push("-w");
    if (options.regex) args.push("-e");
    if (options.fixed) args.push("-F");
    if (options.lineNumber) args.push("-n");
    if (options.count) args.push("-c");
    if (options.filesWithMatches) args.push("-l");
    if (options.filesWithoutMatches) args.push("-L");
    
    // 添加文件类型过滤
    if (options.fileTypes) {
      options.fileTypes.forEach(type => {
        args.push("-t", type);
      });
    }
    
    // 添加排除模式
    if (options.exclude) {
      options.exclude.forEach(pattern => {
        args.push("--glob", `!${pattern}`);
      });
    }
    
    // 添加包含模式
    if (options.include) {
      options.include.forEach(pattern => {
        args.push("--glob", pattern);
      });
    }
    
    // 添加搜索模式
    args.push(pattern);
    
    return executeRipgrepSearch(args, searchPath, options.signal);
  }

  /**
   * 列出文件
   * @param {string} searchPath - 搜索路径
   * @param {Object} options - 选项
   * @returns {Promise<string[]>} 文件列表
   */
  async listFiles(searchPath, options = {}) {
    const args = ["--files"];
    
    if (options.hidden) args.push("--hidden");
    if (options.followLinks) args.push("--follow");
    
    // 添加排除模式
    if (options.exclude) {
      options.exclude.forEach(pattern => {
        args.push("--glob", `!${pattern}`);
      });
    }
    
    return executeRipgrepSearch(args, searchPath, options.signal);
  }

  /**
   * 获取文件统计信息
   * @param {string} searchPath - 搜索路径
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 统计信息
   */
  async getStats(searchPath, options = {}) {
    const files = await this.listFiles(searchPath, options);
    const approximateCount = await countFiles(searchPath, options.signal, options.exclude);
    
    return {
      fileCount: files.length,
      approximateCount,
      searchPath
    };
  }

  /**
   * 检查Ripgrep是否可用
   * @returns {Promise<boolean>} 是否可用
   */
  async isAvailable() {
    try {
      await executeRipgrepSearch(["--version"], ".", null);
      return true;
    } catch {
      return false;
    }
  }
}

// 辅助函数

/**
 * 查找实际可执行文件
 * @param {string} command - 命令名
 * @param {string[]} args - 参数
 * @returns {Object} 包含cmd的对象
 * @original y6A.findActualExecutable的实现（推测）
 */
function findActualExecutable(command, args) {
  // @todo: 实现实际的可执行文件查找逻辑
  // 这里返回一个简化的实现
  return { cmd: command };
}

/**
 * 执行命令
 * @param {string} command - 命令
 * @param {string[]} args - 参数
 * @param {Object} options - 选项
 * @returns {Promise<Object>} 执行结果
 * @original X2函数的实现（推测）
 */
async function executeCommand(command, args, options = {}) {
  return new Promise((resolve) => {
    execFile(command, args, options, (error, stdout, stderr) => {
      resolve({
        code: error ? error.code : 0,
        stdout: stdout || '',
        stderr: stderr || '',
        error
      });
    });
  });
}

// 创建默认服务实例
export const ripgrepService = new RipgrepService();
