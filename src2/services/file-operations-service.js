/**
 * 文件操作服务
 * @description 重构自原始文件中的文件操作代码，对应第3716-3800行
 * @original 原始代码行 3716-3800
 */

import { createAbortController } from '../utils/abort-controller-polyfill.js';
import { getIgnorePatternMap, buildPatternList } from '../utils/pattern-matching-utils.js';

/**
 * 搜索文件
 * @param {string} pattern - 搜索模式
 * @param {string} cwd - 当前工作目录
 * @param {Object} options - 搜索选项
 * @param {AbortSignal} signal - 中止信号
 * @param {Object} context - 上下文对象
 * @returns {Promise<Object>} 搜索结果
 * @original uIA函数
 */
export async function searchFiles(pattern, cwd, { limit, offset }, signal, context) {
  const ignorePatterns = buildPatternList(getIgnorePatternMap(context), cwd);
  
  // 使用glob搜索文件
  const files = (await globSearch([pattern], {
    cwd,
    nocase: true,
    nodir: true,
    signal,
    stat: true,
    withFileTypes: true,
    ignore: ignorePatterns
  })).sort((a, b) => (a.mtimeMs ?? 0) - (b.mtimeMs ?? 0));
  
  const hasMore = files.length > offset + limit;
  
  return {
    files: files.slice(offset, offset + limit).map(file => file.fullpath()),
    truncated: hasMore
  };
}

/**
 * 读取文件内容（分页）
 * @param {string} filePath - 文件路径
 * @param {number} offset - 起始行号
 * @param {number} limit - 行数限制
 * @returns {Object} 文件内容信息
 * @original mIA函数
 */
export function readFileContent(filePath, offset = 0, limit) {
  const fs = getFileSystem();
  const content = fs.readFileSync(filePath, { encoding: "utf8" });
  const lines = content.split(/\r?\n/);
  
  const selectedLines = limit !== undefined && lines.length - offset > limit ? 
    lines.slice(offset, offset + limit) : 
    lines.slice(offset);
  
  return {
    content: selectedLines.join('\n'),
    lineCount: selectedLines.length,
    totalLines: lines.length
  };
}

/**
 * 写入文件内容
 * @param {string} filePath - 文件路径
 * @param {string} content - 文件内容
 * @param {string} encoding - 文件编码
 * @param {string} lineEnding - 换行符类型
 * @original by函数
 */
export function writeFileContent(filePath, content, encoding, lineEnding) {
  let processedContent = content;
  
  if (lineEnding === "CRLF") {
    processedContent = content.split('\n').join('\r\n');
  }
  
  writeFileSync(filePath, processedContent, { encoding });
}

/**
 * 检测项目默认换行符
 * @returns {Promise<string>} 换行符类型 (LF/CRLF)
 * @original dIA函数
 */
export const detectProjectLineEnding = createLazyLoader(async () => {
  const abortController = createAbortController();
  
  // 设置1秒超时
  setTimeout(() => {
    abortController.abort();
  }, 1000);
  
  const files = await searchProjectFiles(getCurrentWorkingDirectory(), abortController.signal, 15);
  let crlfCount = 0;
  
  for (const file of files) {
    if (detectFileLineEnding(file) === "CRLF") {
      crlfCount++;
    }
  }
  
  return crlfCount > 3 ? "CRLF" : "LF";
});

/**
 * 检测文件换行符
 * @param {string} filePath - 文件路径
 * @param {string} encoding - 文件编码
 * @returns {string} 换行符类型 (LF/CRLF)
 * @original fO函数
 */
export function detectFileLineEnding(filePath, encoding = "utf8") {
  try {
    const fs = getFileSystem();
    const { resolvedPath } = resolveFilePath(fs, filePath);
    const { buffer, bytesRead } = fs.readSync(resolvedPath, { length: 4096 });
    const content = buffer.toString(encoding, 0, bytesRead);
    
    return analyzeLineEndings(content);
  } catch (error) {
    logError(error);
    return "LF";
  }
}

/**
 * 分析文本中的换行符
 * @param {string} content - 文本内容
 * @returns {string} 换行符类型 (LF/CRLF)
 * @original O5Q函数
 */
export function analyzeLineEndings(content) {
  let crlfCount = 0;
  let lfCount = 0;
  
  for (let i = 0; i < content.length; i++) {
    if (content[i] === '\n') {
      if (i > 0 && content[i - 1] === '\r') {
        crlfCount++;
      } else {
        lfCount++;
      }
    }
  }
  
  return crlfCount > lfCount ? "CRLF" : "LF";
}

/**
 * 解析截图文件路径
 * @param {string} filePath - 文件路径
 * @returns {string} 解析后的文件路径
 * @original Vh函数
 */
export function resolveScreenshotPath(filePath) {
  const resolvedPath = isAbsolutePath(filePath) ? filePath : joinPath(getCurrentWorkingDirectory(), filePath);
  const fs = getFileSystem();
  
  // 特殊字符处理
  const specialChar = String.fromCharCode(8239);
  const screenshotPattern = /^(.+)([ \u202F])(AM|PM)(\.png)$/;
  const basename = getBasename(resolvedPath);
  const match = basename.match(screenshotPattern);
  
  if (match) {
    // 如果文件存在，直接返回
    if (fs.existsSync(resolvedPath)) {
      return resolvedPath;
    }
    
    // 尝试替换特殊字符
    const [, prefix, separator, ampm, extension] = match;
    const alternatives = [
      // 尝试不同的分隔符
      `${prefix} ${ampm}${extension}`,
      `${prefix}${specialChar}${ampm}${extension}`,
      `${prefix}\u202F${ampm}${extension}`
    ];
    
    const directory = getDirname(resolvedPath);
    for (const alternative of alternatives) {
      const alternativePath = joinPath(directory, alternative);
      if (fs.existsSync(alternativePath)) {
        return alternativePath;
      }
    }
  }
  
  return resolvedPath;
}

/**
 * 文件操作服务类
 * @description 提供完整的文件操作功能
 */
export class FileOperationsService {
  constructor() {
    this.defaultEncoding = 'utf8';
    this.defaultLineEnding = 'LF';
    this.readCache = new Map();
    this.cacheTimeout = 30000; // 30秒缓存
  }

  /**
   * 搜索文件
   * @param {string} pattern - 搜索模式
   * @param {Object} options - 搜索选项
   * @returns {Promise<Object>} 搜索结果
   */
  async searchFiles(pattern, options = {}) {
    const {
      cwd = getCurrentWorkingDirectory(),
      limit = 100,
      offset = 0,
      signal,
      context = {}
    } = options;
    
    return searchFiles(pattern, cwd, { limit, offset }, signal, context);
  }

  /**
   * 读取文件内容
   * @param {string} filePath - 文件路径
   * @param {Object} options - 读取选项
   * @returns {Object} 文件内容信息
   */
  readFile(filePath, options = {}) {
    const {
      offset = 0,
      limit,
      useCache = true
    } = options;
    
    const cacheKey = `${filePath}:${offset}:${limit}`;
    
    if (useCache) {
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        return cached;
      }
    }
    
    const result = readFileContent(filePath, offset, limit);
    
    if (useCache) {
      this.setCache(cacheKey, result);
    }
    
    return result;
  }

  /**
   * 写入文件内容
   * @param {string} filePath - 文件路径
   * @param {string} content - 文件内容
   * @param {Object} options - 写入选项
   */
  writeFile(filePath, content, options = {}) {
    const {
      encoding = this.defaultEncoding,
      lineEnding = this.defaultLineEnding
    } = options;
    
    writeFileContent(filePath, content, encoding, lineEnding);
    
    // 清除相关缓存
    this.clearFileCache(filePath);
  }

  /**
   * 检测文件换行符
   * @param {string} filePath - 文件路径
   * @param {Object} options - 检测选项
   * @returns {string} 换行符类型
   */
  detectLineEnding(filePath, options = {}) {
    const { encoding = this.defaultEncoding } = options;
    return detectFileLineEnding(filePath, encoding);
  }

  /**
   * 获取项目默认换行符
   * @returns {Promise<string>} 换行符类型
   */
  async getProjectLineEnding() {
    return detectProjectLineEnding();
  }

  /**
   * 解析文件路径
   * @param {string} filePath - 文件路径
   * @returns {string} 解析后的路径
   */
  resolvePath(filePath) {
    return resolveScreenshotPath(filePath);
  }

  /**
   * 批量读取文件
   * @param {Array} filePaths - 文件路径数组
   * @param {Object} options - 读取选项
   * @returns {Promise<Array>} 读取结果数组
   */
  async batchReadFiles(filePaths, options = {}) {
    const results = [];
    
    for (const filePath of filePaths) {
      try {
        const content = this.readFile(filePath, options);
        results.push({
          filePath,
          success: true,
          content
        });
      } catch (error) {
        results.push({
          filePath,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * 批量写入文件
   * @param {Array} files - 文件信息数组
   * @param {Object} options - 写入选项
   * @returns {Array} 写入结果数组
   */
  batchWriteFiles(files, options = {}) {
    const results = [];
    
    for (const file of files) {
      try {
        this.writeFile(file.path, file.content, {
          ...options,
          ...file.options
        });
        results.push({
          filePath: file.path,
          success: true
        });
      } catch (error) {
        results.push({
          filePath: file.path,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * 从缓存获取结果
   * @param {string} key - 缓存键
   * @returns {Object|null} 缓存的结果或null
   * @private
   */
  getFromCache(key) {
    const cached = this.readCache.get(key);
    if (!cached) {
      return null;
    }
    
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      this.readCache.delete(key);
      return null;
    }
    
    return cached.result;
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {Object} result - 结果
   * @private
   */
  setCache(key, result) {
    this.readCache.set(key, {
      result,
      timestamp: Date.now()
    });
  }

  /**
   * 清除文件相关缓存
   * @param {string} filePath - 文件路径
   */
  clearFileCache(filePath) {
    for (const key of this.readCache.keys()) {
      if (key.startsWith(filePath + ':')) {
        this.readCache.delete(key);
      }
    }
  }

  /**
   * 清空所有缓存
   */
  clearAllCache() {
    this.readCache.clear();
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计信息
   */
  getCacheStats() {
    return {
      size: this.readCache.size,
      timeout: this.cacheTimeout,
      entries: Array.from(this.readCache.keys())
    };
  }

  /**
   * 设置默认编码
   * @param {string} encoding - 编码
   */
  setDefaultEncoding(encoding) {
    this.defaultEncoding = encoding;
  }

  /**
   * 设置默认换行符
   * @param {string} lineEnding - 换行符类型
   */
  setDefaultLineEnding(lineEnding) {
    this.defaultLineEnding = lineEnding;
  }
}

// 辅助函数

/**
 * 获取文件系统模块
 * @returns {Object} 文件系统模块
 * @original x1函数的实现（推测）
 */
function getFileSystem() {
  return require('fs');
}

/**
 * 同步写入文件
 * @param {string} filePath - 文件路径
 * @param {string} content - 文件内容
 * @param {Object} options - 写入选项
 * @original kN函数的实现（推测）
 */
function writeFileSync(filePath, content, options) {
  const fs = getFileSystem();
  fs.writeFileSync(filePath, content, options);
}

/**
 * 创建懒加载器
 * @param {Function} fn - 要懒加载的函数
 * @returns {Function} 懒加载函数
 * @original SA函数的实现（推测）
 */
function createLazyLoader(fn) {
  let cached = null;
  let promise = null;
  
  return async (...args) => {
    if (cached !== null) {
      return cached;
    }
    
    if (promise !== null) {
      return promise;
    }
    
    promise = fn(...args);
    cached = await promise;
    return cached;
  };
}

/**
 * 搜索项目文件
 * @param {string} cwd - 当前工作目录
 * @param {AbortSignal} signal - 中止信号
 * @param {number} limit - 限制数量
 * @returns {Promise<Array>} 文件列表
 * @original k6A函数的实现（推测）
 */
async function searchProjectFiles(cwd, signal, limit) {
  // @todo: 实现k6A函数的实际逻辑
  return [];
}

/**
 * Glob搜索
 * @param {Array} patterns - 搜索模式数组
 * @param {Object} options - 搜索选项
 * @returns {Promise<Array>} 搜索结果
 * @original DC1函数的实现（推测）
 */
async function globSearch(patterns, options) {
  // @todo: 实现DC1函数的实际逻辑
  return [];
}

/**
 * 解析文件路径
 * @param {Object} fs - 文件系统对象
 * @param {string} filePath - 文件路径
 * @returns {Object} 解析结果
 * @original WV函数的实现（推测）
 */
function resolveFilePath(fs, filePath) {
  return { resolvedPath: filePath };
}

/**
 * 记录错误
 * @param {Error} error - 错误对象
 * @original T1函数的实现（推测）
 */
function logError(error) {
  console.error('File operations error:', error);
}

/**
 * 检查是否为绝对路径
 * @param {string} path - 路径
 * @returns {boolean} 是否为绝对路径
 * @original T91函数的实现（推测）
 */
function isAbsolutePath(path) {
  return require('path').isAbsolute(path);
}

/**
 * 连接路径
 * @param {string} basePath - 基础路径
 * @param {string} relativePath - 相对路径
 * @returns {string} 连接后的路径
 * @original P91函数的实现（推测）
 */
function joinPath(basePath, relativePath) {
  return require('path').join(basePath, relativePath);
}

/**
 * 获取当前工作目录
 * @returns {string} 当前工作目录
 * @original a0函数的实现（推测）
 */
function getCurrentWorkingDirectory() {
  return process.cwd();
}

/**
 * 获取文件基础名
 * @param {string} filePath - 文件路径
 * @returns {string} 基础名
 * @original Ur1函数的实现（推测）
 */
function getBasename(filePath) {
  return require('path').basename(filePath);
}

/**
 * 获取目录名
 * @param {string} filePath - 文件路径
 * @returns {string} 目录名
 * @original getDirname的实现（推测）
 */
function getDirname(filePath) {
  return require('path').dirname(filePath);
}

/**
 * 替换制表符为空格
 * @param {string} content - 文本内容
 * @returns {string} 替换后的内容
 * @original vi函数
 */
export function replaceTabsWithSpaces(content) {
  return content.replace(/^\t+/gm, tabs => "  ".repeat(tabs.length));
}

/**
 * 解析文件路径（可选）
 * @param {string} filePath - 文件路径
 * @returns {string|undefined} 解析后的路径或undefined
 * @original Ch函数
 */
export function parseOptionalPath(filePath) {
  return filePath ? (isAbsolutePath(filePath) ? filePath : joinPath(getCurrentWorkingDirectory(), filePath)) : undefined;
}

/**
 * 获取路径信息
 * @param {string} filePath - 文件路径
 * @returns {Object} 路径信息对象
 * @original qr1函数
 */
export function getPathInfo(filePath) {
  const absolutePath = parseOptionalPath(filePath);
  const relativePath = absolutePath ? getRelativePath(getCurrentWorkingDirectory(), absolutePath) : undefined;

  return {
    absolutePath,
    relativePath
  };
}

/**
 * 获取显示路径
 * @param {string} filePath - 文件路径
 * @returns {string} 显示用的路径
 * @original TK1函数
 */
export function getDisplayPath(filePath) {
  const { relativePath } = getPathInfo(filePath);

  if (relativePath && !relativePath.startsWith("..")) {
    return relativePath;
  }

  const homeDir = getUserHomeDirectory();
  if (filePath.startsWith(homeDir + getPathSeparator())) {
    return "~" + filePath.slice(homeDir.length);
  }

  return filePath;
}

/**
 * 查找相似文件名
 * @param {string} filePath - 文件路径
 * @returns {string|undefined} 相似的文件名或undefined
 * @original PK1函数
 */
export function findSimilarFileName(filePath) {
  const fs = getFileSystem();

  try {
    const directory = getDirname(filePath);
    const targetBasename = getBasename(filePath, getExtname(filePath));

    if (!fs.existsSync(directory)) {
      return undefined;
    }

    const files = fs.readdirSync(directory, { withFileTypes: true });
    const similarFile = files.find(file => {
      const fileBasename = getBasename(file.name, getExtname(file.name));
      return fileBasename === targetBasename &&
             joinPath(directory, file.name) !== filePath;
    });

    return similarFile ? similarFile.name : undefined;
  } catch (error) {
    logError(error);
    return undefined;
  }
}

/**
 * 格式化代码内容（带行号）
 * @param {Object} options - 格式化选项
 * @returns {string} 格式化后的内容
 * @original fy函数
 */
export function formatCodeWithLineNumbers({ content, startLine }) {
  if (!content) return "";

  return content.split(/\r?\n/).map((line, index) => {
    const lineNumber = index + startLine;
    const lineNumberStr = String(lineNumber);

    if (lineNumberStr.length >= 6) {
      return `${lineNumberStr}→${line}`;
    }

    return `${lineNumberStr.padStart(6, " ")}→${line}`;
  }).join('\n');
}

/**
 * 检查目录是否为空
 * @param {string} directoryPath - 目录路径
 * @returns {boolean} 是否为空
 * @original cIA函数
 */
export function isDirectoryEmpty(directoryPath) {
  const fs = getFileSystem();

  if (!fs.existsSync(directoryPath)) {
    return true;
  }

  // 如果有isDirEmptySync方法，使用它
  if (typeof fs.isDirEmptySync === 'function') {
    return fs.isDirEmptySync(directoryPath);
  }

  // 否则手动检查
  try {
    const files = fs.readdirSync(directoryPath);
    return files.length === 0;
  } catch {
    return true;
  }
}

/**
 * 检查文件是否被忽略
 * @param {string} filePath - 文件路径
 * @param {string} basePath - 基础路径
 * @returns {boolean} 是否被忽略
 * @original Bz函数
 */
export function isFileIgnored(filePath, basePath = getCurrentWorkingDirectory()) {
  const projectConfig = getProjectConfig();

  if (!projectConfig.ignorePatterns || projectConfig.ignorePatterns.length === 0) {
    return false;
  }

  const absolutePath = isAbsolutePath(filePath) ? filePath : joinPath(basePath, filePath);
  const relativePath = getRelativePath(basePath, absolutePath);

  if (!relativePath) {
    return false;
  }

  // 创建忽略检查器
  const ignoreChecker = createIgnoreChecker(projectConfig.ignorePatterns);
  return ignoreChecker ? ignoreChecker.test(relativePath).ignored : false;
}

/**
 * 读取文件内容（使用缓存）
 * @param {string} filePath - 文件路径
 * @returns {string} 文件内容
 * @original Nr1函数
 */
export function readFileWithCache(filePath) {
  const { content } = globalFileCache.readFile(filePath);
  return content;
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 * @original wY函数
 */
export function formatFileSize(bytes) {
  const kb = bytes / 1024;

  if (kb < 1) {
    return `${bytes} bytes`;
  }

  if (kb < 1024) {
    return `${kb.toFixed(1).replace(/\.0$/, "")}KB`;
  }

  const mb = kb / 1024;
  if (mb < 1024) {
    return `${mb.toFixed(1).replace(/\.0$/, "")}MB`;
  }

  const gb = mb / 1024;
  return `${gb.toFixed(1).replace(/\.0$/, "")}GB`;
}

/**
 * 获取文件语言类型
 * @param {string} filePath - 文件路径
 * @returns {string} 语言类型名称
 * @original MY函数
 */
export function getFileLanguage(filePath) {
  const extension = getExtname(filePath);

  if (!extension) {
    return "unknown";
  }

  // @todo: 实现hIA.getLanguage的逻辑
  const languageMap = {
    '.js': 'JavaScript',
    '.ts': 'TypeScript',
    '.jsx': 'JavaScript React',
    '.tsx': 'TypeScript React',
    '.py': 'Python',
    '.java': 'Java',
    '.cpp': 'C++',
    '.c': 'C',
    '.cs': 'C#',
    '.php': 'PHP',
    '.rb': 'Ruby',
    '.go': 'Go',
    '.rs': 'Rust',
    '.swift': 'Swift',
    '.kt': 'Kotlin',
    '.scala': 'Scala',
    '.html': 'HTML',
    '.css': 'CSS',
    '.scss': 'SCSS',
    '.sass': 'Sass',
    '.less': 'Less',
    '.json': 'JSON',
    '.xml': 'XML',
    '.yaml': 'YAML',
    '.yml': 'YAML',
    '.md': 'Markdown',
    '.txt': 'Text',
    '.sh': 'Shell',
    '.bash': 'Bash',
    '.zsh': 'Zsh',
    '.fish': 'Fish',
    '.ps1': 'PowerShell',
    '.sql': 'SQL',
    '.r': 'R',
    '.m': 'Objective-C',
    '.mm': 'Objective-C++',
    '.pl': 'Perl',
    '.lua': 'Lua',
    '.vim': 'Vim Script',
    '.dockerfile': 'Dockerfile'
  };

  return languageMap[extension.toLowerCase()] ?? "unknown";
}

/**
 * 解析文件路径（支持多种格式）
 * @param {string} filePath - 文件路径
 * @param {string} basePath - 基础路径
 * @returns {string} 解析后的绝对路径
 * @original SK1函数
 */
export function resolveFilePath(filePath, basePath) {
  if (filePath.startsWith("~/")) {
    return joinPath(getUserHomeDirectory(), filePath.substring(2));
  } else if (isAbsolutePath(filePath)) {
    return filePath;
  } else {
    const relativePath = filePath.startsWith("./") ? filePath : `./${filePath}`;
    return joinPath(getDirname(basePath), relativePath);
  }
}

// 扩展FileOperationsService类
export class ExtendedFileOperationsService extends FileOperationsService {
  /**
   * 替换制表符为空格
   * @param {string} content - 文本内容
   * @returns {string} 替换后的内容
   */
  replaceTabsWithSpaces(content) {
    return replaceTabsWithSpaces(content);
  }

  /**
   * 获取路径信息
   * @param {string} filePath - 文件路径
   * @returns {Object} 路径信息对象
   */
  getPathInfo(filePath) {
    return getPathInfo(filePath);
  }

  /**
   * 获取显示路径
   * @param {string} filePath - 文件路径
   * @returns {string} 显示用的路径
   */
  getDisplayPath(filePath) {
    return getDisplayPath(filePath);
  }

  /**
   * 查找相似文件名
   * @param {string} filePath - 文件路径
   * @returns {string|undefined} 相似的文件名或undefined
   */
  findSimilarFileName(filePath) {
    return findSimilarFileName(filePath);
  }

  /**
   * 格式化代码内容（带行号）
   * @param {Object} options - 格式化选项
   * @returns {string} 格式化后的内容
   */
  formatCodeWithLineNumbers(options) {
    return formatCodeWithLineNumbers(options);
  }

  /**
   * 检查目录是否为空
   * @param {string} directoryPath - 目录路径
   * @returns {boolean} 是否为空
   */
  isDirectoryEmpty(directoryPath) {
    return isDirectoryEmpty(directoryPath);
  }

  /**
   * 检查文件是否被忽略
   * @param {string} filePath - 文件路径
   * @param {string} basePath - 基础路径
   * @returns {boolean} 是否被忽略
   */
  isFileIgnored(filePath, basePath) {
    return isFileIgnored(filePath, basePath);
  }

  /**
   * 读取文件内容（使用缓存）
   * @param {string} filePath - 文件路径
   * @returns {string} 文件内容
   */
  readFileWithCache(filePath) {
    return readFileWithCache(filePath);
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的大小
   */
  formatFileSize(bytes) {
    return formatFileSize(bytes);
  }

  /**
   * 获取文件语言类型
   * @param {string} filePath - 文件路径
   * @returns {string} 语言类型名称
   */
  getFileLanguage(filePath) {
    return getFileLanguage(filePath);
  }

  /**
   * 解析文件路径（支持多种格式）
   * @param {string} filePath - 文件路径
   * @param {string} basePath - 基础路径
   * @returns {string} 解析后的绝对路径
   */
  resolveFilePath(filePath, basePath) {
    return resolveFilePath(filePath, basePath);
  }
}

// 辅助函数

/**
 * 获取相对路径
 * @param {string} from - 起始路径
 * @param {string} to - 目标路径
 * @returns {string} 相对路径
 * @original bIA函数的实现（推测）
 */
function getRelativePath(from, to) {
  return require('path').relative(from, to);
}

/**
 * 获取文件扩展名
 * @param {string} filePath - 文件路径
 * @returns {string} 文件扩展名
 * @original wr1函数的实现（推测）
 */
function getExtname(filePath) {
  return require('path').extname(filePath);
}

/**
 * 获取项目配置
 * @returns {Object} 项目配置
 * @original t9函数的实现（推测）
 */
function getProjectConfig() {
  // @todo: 实现t9函数的实际逻辑
  return {
    ignorePatterns: []
  };
}

/**
 * 创建忽略检查器
 * @param {Array} patterns - 忽略模式数组
 * @returns {Object|null} 忽略检查器或null
 * @original fIA.default().add的实现（推测）
 */
function createIgnoreChecker(patterns) {
  if (!patterns || patterns.length === 0) {
    return null;
  }

  // @todo: 实现fIA.default().add的实际逻辑
  return {
    test: (path) => {
      for (const pattern of patterns) {
        if (matchPattern(path, pattern)) {
          return { ignored: true };
        }
      }
      return { ignored: false };
    }
  };
}

/**
 * 模式匹配
 * @param {string} path - 路径
 * @param {string} pattern - 模式
 * @returns {boolean} 是否匹配
 */
function matchPattern(path, pattern) {
  const regexPattern = pattern
    .replace(/\./g, '\\.')
    .replace(/\*/g, '.*')
    .replace(/\?/g, '.');

  const regex = new RegExp(`^${regexPattern}$`);
  return regex.test(path);
}

// 创建默认文件操作服务实例
export const fileOperationsService = new ExtendedFileOperationsService();
