/**
 * 工作空间管理服务
 * @description 重构自原始文件中的工作空间和文件路径管理代码，对应第3479-3500行
 * @original 原始代码行 3479-3500
 */

import { homedir } from "os";
import { sep, resolve, join, isAbsolute } from "path";

/**
 * 编辑工具名称
 * @original xO变量
 */
export const EDIT_TOOL_NAME = "Edit";

/**
 * 路径分隔符
 * @original vO变量
 */
export const PATH_SEPARATOR = sep;

/**
 * 权限源列表（完整版）
 * @original zw变量
 */
const PERMISSION_SOURCES_FULL = ["localSettings", "projectSettings", "userSettings", "cliArg", "command", "flagSettings"];

/**
 * 获取命令搜索路径
 * @returns {Array} 命令搜索路径数组
 * @original N5Q函数
 */
export function getCommandSearchPaths() {
  const paths = PERMISSION_SOURCES_FULL
    .map(source => getConfigPath(source))
    .filter(path => path !== undefined);
  
  const claudeCommandsPath = join(getUserHomeDirectory(), ".claude/commands");
  
  return [...paths, claudeCommandsPath];
}

/**
 * 解析工作目录路径
 * @param {string} path - 路径
 * @returns {string} 解析后的绝对路径
 * @original WZ函数
 */
export function resolveWorkingDirectory(path) {
  return isAbsolute(path) ? resolve(path) : resolve(getCurrentWorkingDirectory(), path);
}

/**
 * 获取工作目录集合
 * @param {Object} context - 上下文对象
 * @returns {Set} 工作目录集合
 * @original O91函数
 */
export function getWorkingDirectories(context) {
  return new Set([
    getUserHomeDirectory(),
    ...context.additionalWorkingDirectories.keys()
  ]);
}

/**
 * 检查路径是否在允许的工作目录内
 * @param {string} path - 要检查的路径
 * @param {Object} context - 上下文对象
 * @returns {boolean} 是否在允许的工作目录内
 * @original uW函数
 */
export function isPathInWorkingDirectories(path, context) {
  return Array.from(getWorkingDirectories(context)).some(workingDir => 
    isPathWithinDirectory(path, workingDir)
  );
}

/**
 * 检查路径是否在指定目录内
 * @param {string} path - 要检查的路径
 * @param {string} directory - 目录路径
 * @returns {boolean} 是否在目录内
 * @original ki函数
 */
export function isPathWithinDirectory(path, directory) {
  const resolvedPath = resolveWorkingDirectory(path);
  const resolvedDirectory = resolveWorkingDirectory(directory);
  
  // 规范化路径，处理macOS的特殊路径
  const normalizedPath = resolvedPath
    .replace(/^\/private\/var\//, "/var/")
    .replace(/^\/private\/tmp(\/|$)/, "/tmp$1");
  
  const normalizedDirectory = resolvedDirectory
    .replace(/^\/private\/var\//, "/var/")
    .replace(/^\/private\/tmp(\/|$)/, "/tmp$1");
  
  return normalizedPath.startsWith(normalizedDirectory + PATH_SEPARATOR) || 
         normalizedPath === normalizedDirectory;
}

/**
 * 工作空间管理服务类
 * @description 提供完整的工作空间管理功能
 */
export class WorkspaceService {
  constructor() {
    this.workingDirectories = new Set();
    this.additionalWorkingDirectories = new Map();
    this.commandSearchPaths = [];
    this.currentWorkingDirectory = process.cwd();
  }

  /**
   * 设置当前工作目录
   * @param {string} directory - 工作目录路径
   */
  setCurrentWorkingDirectory(directory) {
    this.currentWorkingDirectory = resolveWorkingDirectory(directory);
  }

  /**
   * 获取当前工作目录
   * @returns {string} 当前工作目录
   */
  getCurrentWorkingDirectory() {
    return this.currentWorkingDirectory;
  }

  /**
   * 添加额外的工作目录
   * @param {string} directory - 目录路径
   * @param {Object} metadata - 目录元数据
   */
  addWorkingDirectory(directory, metadata = {}) {
    const resolvedDirectory = resolveWorkingDirectory(directory);
    this.additionalWorkingDirectories.set(resolvedDirectory, {
      path: resolvedDirectory,
      added: new Date(),
      ...metadata
    });
  }

  /**
   * 移除工作目录
   * @param {string} directory - 目录路径
   * @returns {boolean} 是否移除成功
   */
  removeWorkingDirectory(directory) {
    const resolvedDirectory = resolveWorkingDirectory(directory);
    return this.additionalWorkingDirectories.delete(resolvedDirectory);
  }

  /**
   * 获取所有工作目录
   * @returns {Set} 工作目录集合
   */
  getAllWorkingDirectories() {
    return getWorkingDirectories({
      additionalWorkingDirectories: this.additionalWorkingDirectories
    });
  }

  /**
   * 检查路径是否被允许
   * @param {string} path - 要检查的路径
   * @returns {boolean} 是否被允许
   */
  isPathAllowed(path) {
    return isPathInWorkingDirectories(path, {
      additionalWorkingDirectories: this.additionalWorkingDirectories
    });
  }

  /**
   * 解析相对路径
   * @param {string} path - 路径
   * @returns {string} 解析后的绝对路径
   */
  resolvePath(path) {
    return resolveWorkingDirectory(path);
  }

  /**
   * 获取命令搜索路径
   * @returns {Array} 命令搜索路径数组
   */
  getCommandSearchPaths() {
    if (this.commandSearchPaths.length === 0) {
      this.commandSearchPaths = getCommandSearchPaths();
    }
    return [...this.commandSearchPaths];
  }

  /**
   * 添加命令搜索路径
   * @param {string} path - 搜索路径
   */
  addCommandSearchPath(path) {
    const resolvedPath = resolveWorkingDirectory(path);
    if (!this.commandSearchPaths.includes(resolvedPath)) {
      this.commandSearchPaths.push(resolvedPath);
    }
  }

  /**
   * 移除命令搜索路径
   * @param {string} path - 搜索路径
   * @returns {boolean} 是否移除成功
   */
  removeCommandSearchPath(path) {
    const resolvedPath = resolveWorkingDirectory(path);
    const index = this.commandSearchPaths.indexOf(resolvedPath);
    if (index > -1) {
      this.commandSearchPaths.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 检查目录是否存在
   * @param {string} directory - 目录路径
   * @returns {boolean} 是否存在
   */
  directoryExists(directory) {
    try {
      const fs = require('fs');
      const stats = fs.statSync(resolveWorkingDirectory(directory));
      return stats.isDirectory();
    } catch {
      return false;
    }
  }

  /**
   * 创建目录
   * @param {string} directory - 目录路径
   * @param {Object} options - 创建选项
   * @returns {boolean} 是否创建成功
   */
  createDirectory(directory, options = { recursive: true }) {
    try {
      const fs = require('fs');
      const resolvedDirectory = resolveWorkingDirectory(directory);
      fs.mkdirSync(resolvedDirectory, options);
      return true;
    } catch (error) {
      console.error('Failed to create directory:', error);
      return false;
    }
  }

  /**
   * 获取目录内容
   * @param {string} directory - 目录路径
   * @param {Object} options - 选项
   * @returns {Array} 目录内容数组
   */
  getDirectoryContents(directory, options = {}) {
    try {
      const fs = require('fs');
      const resolvedDirectory = resolveWorkingDirectory(directory);
      
      if (!this.isPathAllowed(resolvedDirectory)) {
        throw new Error(`Access denied to directory: ${directory}`);
      }
      
      const contents = fs.readdirSync(resolvedDirectory, { withFileTypes: true });
      
      return contents.map(item => ({
        name: item.name,
        path: join(resolvedDirectory, item.name),
        isDirectory: item.isDirectory(),
        isFile: item.isFile(),
        isSymbolicLink: item.isSymbolicLink()
      })).filter(item => {
        if (options.filesOnly && !item.isFile) return false;
        if (options.directoriesOnly && !item.isDirectory) return false;
        if (options.excludeHidden && item.name.startsWith('.')) return false;
        return true;
      });
    } catch (error) {
      console.error('Failed to read directory:', error);
      return [];
    }
  }

  /**
   * 获取工作空间统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      currentWorkingDirectory: this.currentWorkingDirectory,
      additionalWorkingDirectoriesCount: this.additionalWorkingDirectories.size,
      commandSearchPathsCount: this.commandSearchPaths.length,
      totalWorkingDirectories: this.getAllWorkingDirectories().size,
      workingDirectories: Array.from(this.getAllWorkingDirectories()),
      additionalDirectories: Array.from(this.additionalWorkingDirectories.entries()).map(([path, metadata]) => ({
        path,
        ...metadata
      }))
    };
  }

  /**
   * 清空额外的工作目录
   */
  clearAdditionalWorkingDirectories() {
    this.additionalWorkingDirectories.clear();
  }

  /**
   * 重置命令搜索路径
   */
  resetCommandSearchPaths() {
    this.commandSearchPaths = [];
  }

  /**
   * 导出工作空间配置
   * @returns {Object} 工作空间配置
   */
  exportConfig() {
    return {
      currentWorkingDirectory: this.currentWorkingDirectory,
      additionalWorkingDirectories: Object.fromEntries(this.additionalWorkingDirectories),
      commandSearchPaths: [...this.commandSearchPaths]
    };
  }

  /**
   * 导入工作空间配置
   * @param {Object} config - 工作空间配置
   */
  importConfig(config) {
    if (config.currentWorkingDirectory) {
      this.setCurrentWorkingDirectory(config.currentWorkingDirectory);
    }
    
    if (config.additionalWorkingDirectories) {
      this.additionalWorkingDirectories.clear();
      for (const [path, metadata] of Object.entries(config.additionalWorkingDirectories)) {
        this.additionalWorkingDirectories.set(path, metadata);
      }
    }
    
    if (config.commandSearchPaths) {
      this.commandSearchPaths = [...config.commandSearchPaths];
    }
  }
}

// 辅助函数

/**
 * 获取用户主目录
 * @returns {string} 用户主目录路径
 * @original x9函数的实现（推测）
 */
function getUserHomeDirectory() {
  return homedir();
}

/**
 * 获取当前工作目录
 * @returns {string} 当前工作目录
 * @original a0函数的实现（推测）
 */
function getCurrentWorkingDirectory() {
  return process.cwd();
}

/**
 * 连接路径
 * @param {string} basePath - 基础路径
 * @param {string} relativePath - 相对路径
 * @returns {string} 连接后的路径
 * @original qK1函数的实现（推测）
 */
function joinPath(basePath, relativePath) {
  return join(basePath, relativePath);
}

/**
 * 检查路径是否为绝对路径
 * @param {string} path - 路径
 * @returns {boolean} 是否为绝对路径
 * @original w5Q函数的实现（推测）
 */
function isAbsolutePath(path) {
  return isAbsolute(path);
}

/**
 * 获取配置路径
 * @param {string} source - 配置源
 * @returns {string|undefined} 配置路径或undefined
 * @original bO函数的实现（推测）
 */
function getConfigPath(source) {
  // @todo: 实现bO函数的实际逻辑
  switch (source) {
    case "localSettings":
      return join(getCurrentWorkingDirectory(), ".claude");
    case "userSettings":
      return join(getUserHomeDirectory(), ".claude");
    case "projectSettings":
      return join(getCurrentWorkingDirectory(), ".claude");
    default:
      return undefined;
  }
}

// 创建默认工作空间服务实例
export const workspaceService = new WorkspaceService();
