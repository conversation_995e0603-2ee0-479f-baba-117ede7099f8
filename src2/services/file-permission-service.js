/**
 * 文件权限检查服务
 * @description 重构自原始文件中的文件权限检查代码，对应第3608-3700行
 * @original 原始代码行 3608-3700
 */

import { relative, join } from "path";
import { homedir } from "os";
import { getToolContentPatterns, parsePatternPath, isPathWithinDirectory } from '../utils/pattern-matching-utils.js';
import { resolveWorkingDirectory, getCommandSearchPaths } from '../services/workspace-service.js';

/**
 * 检查文件路径权限
 * @param {string} filePath - 文件路径
 * @param {Object} context - 上下文对象
 * @param {string} operation - 操作类型 (read/edit)
 * @param {string} behavior - 行为类型 (allow/deny)
 * @returns {Object|null} 匹配的规则或null
 * @original NK1函数
 */
export function checkFilePathPermission(filePath, context, operation, behavior) {
  const resolvedPath = resolveWorkingDirectory(filePath);
  const patternMap = getToolContentPatterns(context, operation, behavior);
  
  for (const [root, patternRules] of patternMap.entries()) {
    // 创建忽略模式匹配器
    const ignoreChecker = createIgnoreChecker(Array.from(patternRules.keys()));
    
    let relativePath;
    if (getCurrentPlatform() === "windows") {
      // Windows路径处理
      const windowsResolvedPath = convertToCygwinPath(resolvedPath ?? getCurrentWorkingDirectory());
      const windowsRoot = convertToCygwinPath(root ?? getCurrentWorkingDirectory());
      relativePath = relative(windowsRoot, windowsResolvedPath);
    } else {
      relativePath = relative(root ?? getCurrentWorkingDirectory(), resolvedPath);
    }
    
    // 跳过父目录路径
    if (relativePath.startsWith(`..${getPathSeparator()}`)) {
      continue;
    }
    
    // 跳过空路径
    if (!relativePath) {
      continue;
    }
    
    // 测试路径是否匹配忽略模式
    const testResult = ignoreChecker.test(relativePath);
    if (testResult.ignored && testResult.rule) {
      return patternRules.get(testResult.rule.pattern) ?? null;
    }
  }
  
  return null;
}

/**
 * 检查读取权限
 * @param {Object} tool - 工具对象
 * @param {Object} input - 输入参数
 * @param {Object} context - 上下文对象
 * @returns {Object} 权限检查结果
 * @original vy函数
 */
export function checkReadPermission(tool, input, context) {
  // 检查工具是否有getPath方法
  if (typeof tool.getPath !== "function") {
    return {
      behavior: "ask",
      message: `Claude requested permissions to use ${tool.name}, but you haven't granted it yet.`
    };
  }
  
  const filePath = tool.getPath(input);
  
  // 先检查编辑权限（编辑权限包含读取权限）
  const editPermission = checkEditPermission(tool, input, context);
  if (editPermission.behavior === "allow") {
    return editPermission;
  }
  
  // 检查拒绝规则
  const denyRule = checkFilePathPermission(filePath, context, "read", "deny");
  if (denyRule) {
    return {
      behavior: "deny",
      message: `Permission to read ${filePath} has been denied.`,
      decisionReason: {
        type: "rule",
        rule: denyRule
      },
      ruleSuggestions: null
    };
  }
  
  // 检查是否在工作目录内
  if (isPathInWorkingDirectories(filePath, context)) {
    return {
      behavior: "allow",
      updatedInput: input,
      decisionReason: {
        type: "mode",
        mode: "default"
      }
    };
  }
  
  // 检查允许规则
  const allowRule = checkFilePathPermission(filePath, context, "read", "allow");
  if (allowRule) {
    return {
      behavior: "allow",
      updatedInput: input,
      decisionReason: {
        type: "rule",
        rule: allowRule
      }
    };
  }
  
  // 默认询问权限
  return {
    behavior: "ask",
    message: `Claude requested permissions to read from ${filePath}, but you haven't granted it yet.`
  };
}

/**
 * 检查编辑权限
 * @param {Object} tool - 工具对象
 * @param {Object} input - 输入参数
 * @param {Object} context - 上下文对象
 * @returns {Object} 权限检查结果
 * @original Xh函数
 */
export function checkEditPermission(tool, input, context) {
  // 检查工具是否有getPath方法
  if (typeof tool.getPath !== "function") {
    return {
      behavior: "ask",
      message: `Claude requested permissions to use ${tool.name}, but you haven't granted it yet.`
    };
  }
  
  const filePath = tool.getPath(input);
  
  // 检查拒绝规则
  const denyRule = checkFilePathPermission(filePath, context, "edit", "deny");
  if (denyRule) {
    return {
      behavior: "deny",
      message: `Permission to edit ${filePath} has been denied.`,
      decisionReason: {
        type: "rule",
        rule: denyRule
      },
      ruleSuggestions: null
    };
  }
  
  // 检查是否为Claude配置文件
  const claudeCommandsPath = join(getUserHomeDirectory(), ".claude/commands");
  const commandSearchPaths = getCommandSearchPaths();
  
  const isClaudeConfigFile = commandSearchPaths.some(searchPath => {
    if (searchPath !== claudeCommandsPath) {
      return filePath === searchPath;
    }
    return isPathWithinDirectory(filePath, searchPath);
  });
  
  if (isClaudeConfigFile) {
    return {
      behavior: "ask",
      message: `Claude requested permissions to use ${tool.name}, but you haven't granted it yet.`,
      decisionReason: {
        type: "other",
        reason: "Ask for permission to edit Claude Code settings files or slash commands"
      }
    };
  }
  
  // 检查接受编辑模式
  if (context.mode === "acceptEdits" && isPathInWorkingDirectories(filePath, context)) {
    return {
      behavior: "allow",
      updatedInput: input,
      decisionReason: {
        type: "mode",
        mode: "acceptEdits"
      }
    };
  }
  
  // 检查是否在工作目录内
  if (isPathInWorkingDirectories(filePath, context)) {
    return {
      behavior: "allow",
      updatedInput: input,
      decisionReason: {
        type: "mode",
        mode: "default"
      }
    };
  }
  
  // 检查允许规则
  const allowRule = checkFilePathPermission(filePath, context, "edit", "allow");
  if (allowRule) {
    return {
      behavior: "allow",
      updatedInput: input,
      decisionReason: {
        type: "rule",
        rule: allowRule
      }
    };
  }
  
  // 默认询问权限
  return {
    behavior: "ask",
    message: `Claude requested permissions to edit ${filePath}, but you haven't granted it yet.`
  };
}

/**
 * 文件权限检查服务类
 * @description 提供完整的文件权限检查功能
 */
export class FilePermissionService {
  constructor() {
    this.permissionCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
  }

  /**
   * 检查文件读取权限
   * @param {Object} tool - 工具对象
   * @param {Object} input - 输入参数
   * @param {Object} context - 上下文对象
   * @returns {Object} 权限检查结果
   */
  checkReadPermission(tool, input, context) {
    const cacheKey = this.createCacheKey('read', tool, input, context);
    const cached = this.getFromCache(cacheKey);
    
    if (cached) {
      return cached;
    }
    
    const result = checkReadPermission(tool, input, context);
    this.setCache(cacheKey, result);
    
    return result;
  }

  /**
   * 检查文件编辑权限
   * @param {Object} tool - 工具对象
   * @param {Object} input - 输入参数
   * @param {Object} context - 上下文对象
   * @returns {Object} 权限检查结果
   */
  checkEditPermission(tool, input, context) {
    const cacheKey = this.createCacheKey('edit', tool, input, context);
    const cached = this.getFromCache(cacheKey);
    
    if (cached) {
      return cached;
    }
    
    const result = checkEditPermission(tool, input, context);
    this.setCache(cacheKey, result);
    
    return result;
  }

  /**
   * 检查文件路径权限
   * @param {string} filePath - 文件路径
   * @param {Object} context - 上下文对象
   * @param {string} operation - 操作类型
   * @param {string} behavior - 行为类型
   * @returns {Object|null} 匹配的规则或null
   */
  checkFilePathPermission(filePath, context, operation, behavior) {
    return checkFilePathPermission(filePath, context, operation, behavior);
  }

  /**
   * 批量检查文件权限
   * @param {Array} files - 文件信息数组
   * @param {Object} context - 上下文对象
   * @param {string} operation - 操作类型
   * @returns {Array} 权限检查结果数组
   */
  batchCheckPermissions(files, context, operation) {
    return files.map(file => {
      const tool = { getPath: () => file.path, name: file.name || 'Unknown' };
      const input = file.input || {};
      
      switch (operation) {
        case 'read':
          return {
            file,
            permission: this.checkReadPermission(tool, input, context)
          };
        case 'edit':
          return {
            file,
            permission: this.checkEditPermission(tool, input, context)
          };
        default:
          return {
            file,
            permission: { behavior: 'ask', message: 'Unknown operation' }
          };
      }
    });
  }

  /**
   * 创建缓存键
   * @param {string} operation - 操作类型
   * @param {Object} tool - 工具对象
   * @param {Object} input - 输入参数
   * @param {Object} context - 上下文对象
   * @returns {string} 缓存键
   * @private
   */
  createCacheKey(operation, tool, input, context) {
    const filePath = typeof tool.getPath === 'function' ? tool.getPath(input) : 'unknown';
    const mode = context.mode || 'default';
    return `${operation}:${filePath}:${mode}:${tool.name}`;
  }

  /**
   * 从缓存获取结果
   * @param {string} key - 缓存键
   * @returns {Object|null} 缓存的结果或null
   * @private
   */
  getFromCache(key) {
    const cached = this.permissionCache.get(key);
    if (!cached) {
      return null;
    }
    
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      this.permissionCache.delete(key);
      return null;
    }
    
    return cached.result;
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {Object} result - 结果
   * @private
   */
  setCache(key, result) {
    this.permissionCache.set(key, {
      result,
      timestamp: Date.now()
    });
  }

  /**
   * 清空缓存
   */
  clearCache() {
    this.permissionCache.clear();
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计信息
   */
  getCacheStats() {
    return {
      size: this.permissionCache.size,
      timeout: this.cacheTimeout,
      entries: Array.from(this.permissionCache.keys())
    };
  }

  /**
   * 设置缓存超时时间
   * @param {number} timeout - 超时时间（毫秒）
   */
  setCacheTimeout(timeout) {
    this.cacheTimeout = timeout;
  }
}

// 辅助函数

/**
 * 创建忽略模式检查器
 * @param {Array} patterns - 模式数组
 * @returns {Object} 忽略检查器
 * @original _IA.default().add的实现（推测）
 */
function createIgnoreChecker(patterns) {
  // @todo: 实现_IA.default().add的实际逻辑
  return {
    test: (path) => {
      for (const pattern of patterns) {
        if (matchPattern(path, pattern)) {
          return {
            ignored: true,
            rule: { pattern }
          };
        }
      }
      return { ignored: false, rule: null };
    }
  };
}

/**
 * 模式匹配
 * @param {string} path - 路径
 * @param {string} pattern - 模式
 * @returns {boolean} 是否匹配
 */
function matchPattern(path, pattern) {
  const regexPattern = pattern
    .replace(/\./g, '\\.')
    .replace(/\*/g, '.*')
    .replace(/\?/g, '.');
  
  const regex = new RegExp(`^${regexPattern}$`);
  return regex.test(path);
}

/**
 * 获取当前平台
 * @returns {string} 平台名称
 * @original P9函数的实现（推测）
 */
function getCurrentPlatform() {
  return process.platform === 'win32' ? 'windows' : process.platform;
}

/**
 * 转换为Cygwin路径
 * @param {string} path - Windows路径
 * @returns {string} Cygwin路径
 * @original Xi函数的实现（推测）
 */
function convertToCygwinPath(path) {
  // @todo: 实现Xi函数的实际逻辑
  return path;
}

/**
 * 获取当前工作目录
 * @returns {string} 当前工作目录
 * @original a0函数的实现（推测）
 */
function getCurrentWorkingDirectory() {
  return process.cwd();
}

/**
 * 获取路径分隔符
 * @returns {string} 路径分隔符
 * @original vO变量的实现（推测）
 */
function getPathSeparator() {
  return require('path').sep;
}

/**
 * 获取用户主目录
 * @returns {string} 用户主目录
 * @original x9函数的实现（推测）
 */
function getUserHomeDirectory() {
  return homedir();
}

/**
 * 检查路径是否在工作目录内
 * @param {string} path - 路径
 * @param {Object} context - 上下文对象
 * @returns {boolean} 是否在工作目录内
 * @original uW函数的实现（推测）
 */
function isPathInWorkingDirectories(path, context) {
  // @todo: 实现uW函数的实际逻辑
  return true;
}

// 创建默认文件权限服务实例
export const filePermissionService = new FilePermissionService();
