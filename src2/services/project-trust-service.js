/**
 * 项目信任管理服务
 * @description 重构自原始文件中的项目信任管理代码，对应第6285-6300行
 * @original 原始代码行 6285-6300
 */

import { dirname } from "path";

/**
 * 项目配置字段列表
 * @original D51变量
 */
const PROJECT_CONFIG_FIELDS = [
  "allowedTools",
  "hasTrustDialogAccepted", 
  "hasCompletedProjectOnboarding",
  "ignorePatterns"
];

/**
 * 检查工具是否被允许
 * @param {string} toolName - 工具名称
 * @returns {boolean} 是否被允许
 * @original AG0函数
 */
export function isToolAllowed(toolName) {
  const allowedTools = getAllowedTools();
  return allowedTools.includes(toolName);
}

/**
 * 检查项目是否已接受信任对话框
 * @returns {boolean} 是否已接受信任对话框
 * @original R$2函数
 */
export function hasProjectAcceptedTrustDialog() {
  let currentPath = getCurrentWorkingDirectory();
  const userConfig = getUserConfig();
  
  while (true) {
    const projectConfig = userConfig.projects?.[currentPath];
    if (projectConfig?.hasTrustDialogAccepted) {
      return true;
    }
    
    const parentPath = dirname(currentPath);
    if (parentPath === currentPath) {
      break; // 到达根目录
    }
    currentPath = parentPath;
  }
  
  return false;
}

/**
 * 设置项目信任状态
 * @param {boolean} trusted - 是否信任
 * @returns {Promise<void>}
 * @original O$2函数
 */
export async function setProjectTrustStatus(trusted) {
  const currentPath = getCurrentWorkingDirectory();
  const userConfig = getUserConfig();
  
  if (!userConfig.projects) {
    userConfig.projects = {};
  }
  
  if (!userConfig.projects[currentPath]) {
    userConfig.projects[currentPath] = {};
  }
  
  userConfig.projects[currentPath].hasTrustDialogAccepted = trusted;
  
  await saveUserConfig(userConfig);
}

/**
 * 项目信任管理服务类
 * @description 提供完整的项目信任管理功能
 */
export class ProjectTrustService {
  constructor() {
    this.trustCache = new Map();
    this.configCache = new Map();
    this.cacheTimeout = 60000; // 1分钟缓存
  }

  /**
   * 检查项目是否被信任
   * @param {string} projectPath - 项目路径（可选，默认为当前目录）
   * @returns {boolean} 是否被信任
   */
  isProjectTrusted(projectPath = getCurrentWorkingDirectory()) {
    const cacheKey = `trust:${projectPath}`;
    const cached = this.getFromCache(this.trustCache, cacheKey);
    
    if (cached !== null) {
      return cached;
    }
    
    const trusted = this.checkProjectTrust(projectPath);
    this.setCache(this.trustCache, cacheKey, trusted);
    
    return trusted;
  }

  /**
   * 信任项目
   * @param {string} projectPath - 项目路径（可选）
   * @returns {Promise<void>}
   */
  async trustProject(projectPath = getCurrentWorkingDirectory()) {
    const userConfig = getUserConfig();
    
    if (!userConfig.projects) {
      userConfig.projects = {};
    }
    
    if (!userConfig.projects[projectPath]) {
      userConfig.projects[projectPath] = {};
    }
    
    userConfig.projects[projectPath].hasTrustDialogAccepted = true;
    
    await saveUserConfig(userConfig);
    
    // 清除缓存
    this.clearProjectCache(projectPath);
  }

  /**
   * 取消信任项目
   * @param {string} projectPath - 项目路径（可选）
   * @returns {Promise<void>}
   */
  async untrustProject(projectPath = getCurrentWorkingDirectory()) {
    const userConfig = getUserConfig();
    
    if (userConfig.projects?.[projectPath]) {
      userConfig.projects[projectPath].hasTrustDialogAccepted = false;
    }
    
    await saveUserConfig(userConfig);
    
    // 清除缓存
    this.clearProjectCache(projectPath);
  }

  /**
   * 获取项目配置
   * @param {string} projectPath - 项目路径（可选）
   * @returns {Object} 项目配置
   */
  getProjectConfig(projectPath = getCurrentWorkingDirectory()) {
    const cacheKey = `config:${projectPath}`;
    const cached = this.getFromCache(this.configCache, cacheKey);
    
    if (cached !== null) {
      return cached;
    }
    
    const userConfig = getUserConfig();
    const projectConfig = userConfig.projects?.[projectPath] || {};
    
    this.setCache(this.configCache, cacheKey, projectConfig);
    
    return projectConfig;
  }

  /**
   * 更新项目配置
   * @param {Object} config - 配置对象
   * @param {string} projectPath - 项目路径（可选）
   * @returns {Promise<void>}
   */
  async updateProjectConfig(config, projectPath = getCurrentWorkingDirectory()) {
    const userConfig = getUserConfig();
    
    if (!userConfig.projects) {
      userConfig.projects = {};
    }
    
    if (!userConfig.projects[projectPath]) {
      userConfig.projects[projectPath] = {};
    }
    
    // 只更新允许的字段
    for (const field of PROJECT_CONFIG_FIELDS) {
      if (config.hasOwnProperty(field)) {
        userConfig.projects[projectPath][field] = config[field];
      }
    }
    
    await saveUserConfig(userConfig);
    
    // 清除缓存
    this.clearProjectCache(projectPath);
  }

  /**
   * 获取允许的工具列表
   * @param {string} projectPath - 项目路径（可选）
   * @returns {Array} 允许的工具列表
   */
  getAllowedTools(projectPath = getCurrentWorkingDirectory()) {
    const projectConfig = this.getProjectConfig(projectPath);
    return projectConfig.allowedTools || [];
  }

  /**
   * 设置允许的工具
   * @param {Array} tools - 工具列表
   * @param {string} projectPath - 项目路径（可选）
   * @returns {Promise<void>}
   */
  async setAllowedTools(tools, projectPath = getCurrentWorkingDirectory()) {
    await this.updateProjectConfig({ allowedTools: tools }, projectPath);
  }

  /**
   * 添加允许的工具
   * @param {string} toolName - 工具名称
   * @param {string} projectPath - 项目路径（可选）
   * @returns {Promise<void>}
   */
  async addAllowedTool(toolName, projectPath = getCurrentWorkingDirectory()) {
    const allowedTools = this.getAllowedTools(projectPath);
    
    if (!allowedTools.includes(toolName)) {
      allowedTools.push(toolName);
      await this.setAllowedTools(allowedTools, projectPath);
    }
  }

  /**
   * 移除允许的工具
   * @param {string} toolName - 工具名称
   * @param {string} projectPath - 项目路径（可选）
   * @returns {Promise<void>}
   */
  async removeAllowedTool(toolName, projectPath = getCurrentWorkingDirectory()) {
    const allowedTools = this.getAllowedTools(projectPath);
    const index = allowedTools.indexOf(toolName);
    
    if (index !== -1) {
      allowedTools.splice(index, 1);
      await this.setAllowedTools(allowedTools, projectPath);
    }
  }

  /**
   * 检查是否完成了项目入门
   * @param {string} projectPath - 项目路径（可选）
   * @returns {boolean} 是否完成入门
   */
  hasCompletedOnboarding(projectPath = getCurrentWorkingDirectory()) {
    const projectConfig = this.getProjectConfig(projectPath);
    return projectConfig.hasCompletedProjectOnboarding || false;
  }

  /**
   * 设置项目入门完成状态
   * @param {boolean} completed - 是否完成
   * @param {string} projectPath - 项目路径（可选）
   * @returns {Promise<void>}
   */
  async setOnboardingCompleted(completed, projectPath = getCurrentWorkingDirectory()) {
    await this.updateProjectConfig({ hasCompletedProjectOnboarding: completed }, projectPath);
  }

  /**
   * 获取忽略模式
   * @param {string} projectPath - 项目路径（可选）
   * @returns {Array} 忽略模式列表
   */
  getIgnorePatterns(projectPath = getCurrentWorkingDirectory()) {
    const projectConfig = this.getProjectConfig(projectPath);
    return projectConfig.ignorePatterns || [];
  }

  /**
   * 设置忽略模式
   * @param {Array} patterns - 忽略模式列表
   * @param {string} projectPath - 项目路径（可选）
   * @returns {Promise<void>}
   */
  async setIgnorePatterns(patterns, projectPath = getCurrentWorkingDirectory()) {
    await this.updateProjectConfig({ ignorePatterns: patterns }, projectPath);
  }

  /**
   * 获取所有受信任的项目
   * @returns {Array} 受信任的项目路径列表
   */
  getTrustedProjects() {
    const userConfig = getUserConfig();
    const trustedProjects = [];
    
    if (userConfig.projects) {
      for (const [projectPath, config] of Object.entries(userConfig.projects)) {
        if (config.hasTrustDialogAccepted) {
          trustedProjects.push(projectPath);
        }
      }
    }
    
    return trustedProjects;
  }

  /**
   * 清除项目缓存
   * @param {string} projectPath - 项目路径
   * @private
   */
  clearProjectCache(projectPath) {
    this.trustCache.delete(`trust:${projectPath}`);
    this.configCache.delete(`config:${projectPath}`);
  }

  /**
   * 清除所有缓存
   */
  clearCache() {
    this.trustCache.clear();
    this.configCache.clear();
  }

  /**
   * 检查项目信任状态
   * @param {string} projectPath - 项目路径
   * @returns {boolean} 是否被信任
   * @private
   */
  checkProjectTrust(projectPath) {
    let currentPath = projectPath;
    const userConfig = getUserConfig();
    
    while (true) {
      const projectConfig = userConfig.projects?.[currentPath];
      if (projectConfig?.hasTrustDialogAccepted) {
        return true;
      }
      
      const parentPath = dirname(currentPath);
      if (parentPath === currentPath) {
        break; // 到达根目录
      }
      currentPath = parentPath;
    }
    
    return false;
  }

  /**
   * 从缓存获取数据
   * @param {Map} cache - 缓存对象
   * @param {string} key - 缓存键
   * @returns {*} 缓存数据或null
   * @private
   */
  getFromCache(cache, key) {
    const cached = cache.get(key);
    if (!cached) {
      return null;
    }
    
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      cache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  /**
   * 设置缓存
   * @param {Map} cache - 缓存对象
   * @param {string} key - 缓存键
   * @param {*} data - 数据
   * @private
   */
  setCache(cache, key, data) {
    cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * 获取服务状态
   * @returns {Object} 服务状态
   */
  getStatus() {
    return {
      trustCacheSize: this.trustCache.size,
      configCacheSize: this.configCache.size,
      cacheTimeout: this.cacheTimeout,
      trustedProjectsCount: this.getTrustedProjects().length
    };
  }
}

// 辅助函数

/**
 * 获取允许的工具列表
 * @returns {Array} 允许的工具列表
 * @original Q51变量的实现（推测）
 */
function getAllowedTools() {
  // @todo: 实现Q51变量的实际值
  return [];
}

/**
 * 获取当前工作目录
 * @returns {string} 当前工作目录
 * @original a0函数的实现（推测）
 */
function getCurrentWorkingDirectory() {
  return process.cwd();
}

/**
 * 获取用户配置
 * @returns {Object} 用户配置
 * @original U_函数的实现（推测）
 */
function getUserConfig() {
  // @todo: 实现U_函数的实际逻辑
  return { projects: {} };
}

/**
 * 保存用户配置
 * @param {Object} config - 用户配置
 * @returns {Promise<void>}
 * @original 保存用户配置的实现（推测）
 */
async function saveUserConfig(config) {
  // @todo: 实现保存用户配置的实际逻辑
}

// 创建默认项目信任服务实例
export const projectTrustService = new ProjectTrustService();

// 导出便捷函数
export const ProjectTrust = {
  isProjectTrusted: (path) => projectTrustService.isProjectTrusted(path),
  trustProject: (path) => projectTrustService.trustProject(path),
  untrustProject: (path) => projectTrustService.untrustProject(path),
  getProjectConfig: (path) => projectTrustService.getProjectConfig(path),
  updateProjectConfig: (config, path) => projectTrustService.updateProjectConfig(config, path),
  getAllowedTools: (path) => projectTrustService.getAllowedTools(path),
  setAllowedTools: (tools, path) => projectTrustService.setAllowedTools(tools, path),
  addAllowedTool: (tool, path) => projectTrustService.addAllowedTool(tool, path),
  removeAllowedTool: (tool, path) => projectTrustService.removeAllowedTool(tool, path),
  hasCompletedOnboarding: (path) => projectTrustService.hasCompletedOnboarding(path),
  setOnboardingCompleted: (completed, path) => projectTrustService.setOnboardingCompleted(completed, path),
  getTrustedProjects: () => projectTrustService.getTrustedProjects(),
  getStatus: () => projectTrustService.getStatus()
};
