/**
 * 代理配置服务
 * @description 重构自原始文件中的代理配置代码，对应第5609-5633行
 * @original 原始代码行 5609-5633
 */

/**
 * 获取Undici代理配置
 * @returns {Object} Undici代理配置
 * @original uw2函数
 */
export function getUndiciProxyConfig() {
  const proxyUrl = getProxyUrl();
  const undiciConfig = getUndiciAgentConfig();
  
  if (proxyUrl) {
    return {
      dispatcher: createProxyAgent(proxyUrl)
    };
  }
  
  return undiciConfig;
}

/**
 * 配置全局代理设置
 * @original mw2函数
 */
export function configureGlobalProxy() {
  const proxyUrl = getProxyUrl();
  const httpsAgent = createHTTPSAgent();
  const mtlsConfig = getMTLSConfig();
  
  if (proxyUrl) {
    // 禁用axios默认代理
    const axios = getAxiosInstance();
    axios.defaults.proxy = false;
    
    if (mtlsConfig) {
      // 使用带mTLS的代理代理
      axios.defaults.httpsAgent = new HttpsProxyAgent(proxyUrl, {
        cert: mtlsConfig.cert,
        key: mtlsConfig.key,
        passphrase: mtlsConfig.passphrase
      });
    } else {
      // 使用标准代理代理
      axios.defaults.httpsAgent = new HttpsProxyAgent(proxyUrl);
    }
    
    // 设置Undici全局调度器
    const undici = getUndiciModule();
    undici.setGlobalDispatcher(createProxyAgent(proxyUrl));
  } else if (httpsAgent) {
    // 使用mTLS HTTPS代理
    const axios = getAxiosInstance();
    axios.defaults.httpsAgent = httpsAgent;
    
    const undiciConfig = getUndiciAgentConfig();
    if (undiciConfig.dispatcher) {
      const undici = getUndiciModule();
      undici.setGlobalDispatcher(undiciConfig.dispatcher);
    }
  }
}

/**
 * 代理配置服务类
 * @description 提供完整的代理配置管理功能
 */
export class ProxyService {
  constructor() {
    this.proxyUrl = null;
    this.httpsAgent = null;
    this.proxyAgent = null;
    this.isConfigured = false;
  }

  /**
   * 初始化代理配置
   */
  initialize() {
    if (this.isConfigured) {
      return;
    }
    
    this.proxyUrl = this.detectProxyUrl();
    
    if (this.proxyUrl) {
      this.configureProxy();
    } else {
      this.configureMTLS();
    }
    
    this.isConfigured = true;
  }

  /**
   * 检测代理URL
   * @returns {string|null} 代理URL
   * @private
   */
  detectProxyUrl() {
    // 检查环境变量中的代理设置
    return process.env.HTTPS_PROXY || 
           process.env.https_proxy || 
           process.env.HTTP_PROXY || 
           process.env.http_proxy || 
           null;
  }

  /**
   * 配置代理
   * @private
   */
  configureProxy() {
    if (!this.proxyUrl) {
      return;
    }
    
    const mtlsConfig = this.getMTLSConfig();
    
    // 创建代理代理
    if (mtlsConfig) {
      this.proxyAgent = new HttpsProxyAgent(this.proxyUrl, {
        cert: mtlsConfig.cert,
        key: mtlsConfig.key,
        passphrase: mtlsConfig.passphrase
      });
    } else {
      this.proxyAgent = new HttpsProxyAgent(this.proxyUrl);
    }
    
    // 配置axios
    this.configureAxios();
    
    // 配置Undici
    this.configureUndici();
  }

  /**
   * 配置mTLS
   * @private
   */
  configureMTLS() {
    const { mtlsService } = require('./mtls-service.js');
    
    if (mtlsService.isEnabled()) {
      this.httpsAgent = mtlsService.getHTTPSAgent();
      this.configureAxios();
      this.configureUndici();
    }
  }

  /**
   * 配置Axios
   * @private
   */
  configureAxios() {
    const axios = this.getAxiosInstance();
    
    if (this.proxyUrl) {
      axios.defaults.proxy = false;
      axios.defaults.httpsAgent = this.proxyAgent;
    } else if (this.httpsAgent) {
      axios.defaults.httpsAgent = this.httpsAgent;
    }
  }

  /**
   * 配置Undici
   * @private
   */
  configureUndici() {
    const undici = this.getUndiciModule();
    
    if (this.proxyUrl) {
      const proxyAgent = this.createUndiciProxyAgent(this.proxyUrl);
      undici.setGlobalDispatcher(proxyAgent);
    } else {
      const { mtlsService } = require('./mtls-service.js');
      const undiciConfig = mtlsService.getUndiciConfig();
      
      if (undiciConfig.dispatcher) {
        undici.setGlobalDispatcher(undiciConfig.dispatcher);
      }
    }
  }

  /**
   * 创建Undici代理代理
   * @param {string} proxyUrl - 代理URL
   * @returns {Object} 代理代理
   * @private
   */
  createUndiciProxyAgent(proxyUrl) {
    const mtlsConfig = this.getMTLSConfig();
    
    const config = {
      uri: proxyUrl,
      pipelining: 1
    };
    
    if (mtlsConfig) {
      config.requestTls = {
        cert: mtlsConfig.cert,
        key: mtlsConfig.key,
        passphrase: mtlsConfig.passphrase
      };
    }
    
    const { ProxyAgent } = this.getUndiciModule();
    return new ProxyAgent(config);
  }

  /**
   * 获取代理状态
   * @returns {Object} 代理状态
   */
  getStatus() {
    return {
      isConfigured: this.isConfigured,
      hasProxy: !!this.proxyUrl,
      proxyUrl: this.proxyUrl,
      hasHTTPSAgent: !!this.httpsAgent,
      hasProxyAgent: !!this.proxyAgent,
      environmentVars: {
        HTTPS_PROXY: !!process.env.HTTPS_PROXY,
        https_proxy: !!process.env.https_proxy,
        HTTP_PROXY: !!process.env.HTTP_PROXY,
        http_proxy: !!process.env.http_proxy
      }
    };
  }

  /**
   * 创建HTTP配置
   * @param {Object} baseConfig - 基础配置
   * @returns {Object} HTTP配置
   */
  createHTTPConfig(baseConfig = {}) {
    if (this.proxyAgent) {
      return {
        ...baseConfig,
        httpsAgent: this.proxyAgent,
        proxy: false
      };
    }
    
    if (this.httpsAgent) {
      return {
        ...baseConfig,
        httpsAgent: this.httpsAgent
      };
    }
    
    return baseConfig;
  }

  /**
   * 创建Fetch配置
   * @param {Object} baseConfig - 基础配置
   * @returns {Object} Fetch配置
   */
  createFetchConfig(baseConfig = {}) {
    const undiciConfig = this.getUndiciProxyConfig();
    
    if (undiciConfig.dispatcher) {
      return {
        ...baseConfig,
        dispatcher: undiciConfig.dispatcher
      };
    }
    
    return baseConfig;
  }

  /**
   * 重置配置
   */
  reset() {
    this.proxyUrl = null;
    this.httpsAgent = null;
    this.proxyAgent = null;
    this.isConfigured = false;
  }

  /**
   * 重新配置
   */
  reconfigure() {
    this.reset();
    this.initialize();
  }

  /**
   * 获取mTLS配置
   * @returns {Object|null} mTLS配置
   * @private
   */
  getMTLSConfig() {
    const { mtlsService } = require('./mtls-service.js');
    return mtlsService.getConfig();
  }

  /**
   * 获取Axios实例
   * @returns {Object} Axios实例
   * @private
   */
  getAxiosInstance() {
    // @todo: 实现v9对象的实际逻辑
    return require('axios');
  }

  /**
   * 获取Undici模块
   * @returns {Object} Undici模块
   * @private
   */
  getUndiciModule() {
    // @todo: 实现_M1.default的实际逻辑
    return require('undici');
  }

  /**
   * 获取Undici代理配置
   * @returns {Object} Undici代理配置
   * @private
   */
  getUndiciProxyConfig() {
    return getUndiciProxyConfig();
  }
}

// 辅助函数

/**
 * 获取代理URL
 * @returns {string|null} 代理URL
 * @original a81函数的实现（推测）
 */
function getProxyUrl() {
  return process.env.HTTPS_PROXY || 
         process.env.https_proxy || 
         process.env.HTTP_PROXY || 
         process.env.http_proxy || 
         null;
}

/**
 * 获取Undici代理配置
 * @returns {Object} Undici代理配置
 * @original ag函数的实现（推测）
 */
function getUndiciAgentConfig() {
  const { mtlsService } = require('./mtls-service.js');
  return mtlsService.getUndiciConfig();
}

/**
 * 创建代理代理
 * @param {string} proxyUrl - 代理URL
 * @returns {Object} 代理代理
 * @original gw2函数的实现（推测）
 */
function createProxyAgent(proxyUrl) {
  const { createPoolAgent } = require('./mtls-service.js');
  return createPoolAgent(proxyUrl);
}

/**
 * 创建HTTPS代理
 * @returns {Object|null} HTTPS代理
 * @original xw2函数的实现（推测）
 */
function createHTTPSAgent() {
  const { mtlsService } = require('./mtls-service.js');
  return mtlsService.getHTTPSAgent();
}

/**
 * 获取mTLS配置
 * @returns {Object|null} mTLS配置
 * @original BP函数的实现（推测）
 */
function getMTLSConfig() {
  const { mtlsService } = require('./mtls-service.js');
  return mtlsService.getConfig();
}

/**
 * 获取Axios实例
 * @returns {Object} Axios实例
 * @original v9对象的实现（推测）
 */
function getAxiosInstance() {
  return require('axios');
}

/**
 * 获取Undici模块
 * @returns {Object} Undici模块
 * @original _M1.default的实现（推测）
 */
function getUndiciModule() {
  return require('undici');
}

/**
 * HTTPS代理代理类
 * @original kM1.default.HttpsProxyAgent的实现（推测）
 */
class HttpsProxyAgent {
  constructor(proxyUrl, options = {}) {
    this.proxyUrl = proxyUrl;
    this.options = options;
  }
}

// 创建默认代理服务实例
export const proxyService = new ProxyService();

// 导出便捷函数
export const Proxy = {
  initialize: () => proxyService.initialize(),
  getStatus: () => proxyService.getStatus(),
  createHTTPConfig: (config) => proxyService.createHTTPConfig(config),
  createFetchConfig: (config) => proxyService.createFetchConfig(config),
  reconfigure: () => proxyService.reconfigure()
};
