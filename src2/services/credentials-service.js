/**
 * 凭据管理服务
 * @description 重构自原始文件中的凭据管理代码，对应第5305-5400行
 * @original 原始代码行 5305-5400
 */

/**
 * 获取API密钥
 * @param {string} accessToken - 访问令牌
 * @returns {Promise<string|null>} API密钥或null
 * @original FWA函数
 */
export async function fetchAPIKey(accessToken) {
  try {
    const response = await makeHTTPRequest('post', getAPIConfig().API_KEY_URL, null, {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });
    
    const apiKey = response.data?.raw_key;
    
    if (apiKey) {
      // 存储API密钥
      storeAPIKey(apiKey);
      
      // 记录成功事件
      recordEvent("tengu_oauth_api_key", {
        status: "success",
        statusCode: response.status
      });
      
      return apiKey;
    }
    
    return null;
  } catch (error) {
    // 记录失败事件
    recordEvent("tengu_oauth_api_key", {
      status: "failure",
      error: error instanceof Error ? error.message : String(error)
    });
    
    throw error;
  }
}

/**
 * 检查令牌是否即将过期
 * @param {number} expiresAt - 过期时间戳
 * @returns {boolean} 是否即将过期
 * @original nK1函数
 */
export function isTokenExpiringSoon(expiresAt) {
  if (expiresAt === null) {
    return false;
  }
  
  const bufferTime = 300000; // 5分钟缓冲时间
  return Date.now() + bufferTime >= expiresAt;
}

/**
 * 获取订阅类型
 * @param {string} accessToken - 访问令牌
 * @returns {Promise<string|null>} 订阅类型
 * @original tr1函数
 */
export async function getSubscriptionType(accessToken) {
  try {
    const profile = await getOAuthProfile(accessToken);
    const organizationType = profile?.organization?.organization_type;
    
    switch (organizationType) {
      case "claude_max":
        return "max";
      case "claude_pro":
        return "pro";
      case "claude_enterprise":
        return "enterprise";
      case "claude_team":
        return "team";
      default:
        return null;
    }
  } catch {
    return null;
  }
}

/**
 * 获取组织UUID
 * @returns {Promise<string|null>} 组织UUID
 * @original aK1函数
 */
export async function getOrganizationUUID() {
  // 首先尝试从配置中获取
  const userConfig = getUserConfig();
  const organizationUuid = userConfig.oauthAccount?.organizationUuid;
  
  if (organizationUuid) {
    return organizationUuid;
  }
  
  // 如果配置中没有，尝试从OAuth令牌获取
  const tokenData = getStoredTokenData();
  const accessToken = tokenData?.accessToken;
  
  if (accessToken === undefined) {
    return null;
  }
  
  try {
    const profile = await getOAuthProfile(accessToken);
    const uuid = profile?.organization?.uuid;
    
    if (!uuid) {
      return null;
    }
    
    return uuid;
  } catch {
    return null;
  }
}

/**
 * 检查是否为凭据提供者错误
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否为凭据提供者错误
 * @original saA函数
 */
export function isCredentialsProviderError(error) {
  return error?.name === "CredentialsProviderError";
}

/**
 * 获取认证状态
 * @returns {Object} 认证状态
 * @original Fg函数
 */
export function getAuthenticationStatus() {
  // 检查环境变量中的认证令牌
  if (process.env.ANTHROPIC_AUTH_TOKEN) {
    return {
      source: "ANTHROPIC_AUTH_TOKEN",
      hasToken: true
    };
  }
  
  if (process.env.CLAUDE_CODE_OAUTH_TOKEN) {
    return {
      source: "CLAUDE_CODE_OAUTH_TOKEN",
      hasToken: true
    };
  }
  
  // 检查API密钥助手
  if (hasAPIKeyHelper()) {
    return {
      source: "apiKeyHelper",
      hasToken: true
    };
  }
  
  // 检查存储的OAuth令牌
  const tokenData = getStoredTokenData();
  if (hasValidScopes(tokenData?.scopes) && tokenData?.accessToken) {
    return {
      source: "claude.ai",
      hasToken: true
    };
  }
  
  return {
    source: "none",
    hasToken: false
  };
}

/**
 * 获取API密钥
 * @param {string} keyIdentifier - 密钥标识符
 * @returns {string} API密钥
 * @original RY函数
 */
export function getAPIKey(keyIdentifier) {
  const keyData = parseKeyIdentifier(keyIdentifier);
  return keyData.key;
}

/**
 * 清除API密钥助手缓存
 * @original eaA函数
 */
export function clearAPIKeyHelperCache() {
  if (hasAPIKeyHelper.cache) {
    hasAPIKeyHelper.cache.clear();
  }
}

/**
 * 清除令牌缓存
 * @original AsA函数
 */
export function clearTokenCache() {
  if (getStoredTokenData.cache) {
    getStoredTokenData.cache.clear();
  }
}

/**
 * 验证API密钥格式
 * @param {string} apiKey - API密钥
 * @returns {boolean} 是否有效
 * @original fB4函数
 */
export function validateAPIKeyFormat(apiKey) {
  return /^[a-zA-Z0-9-_]+$/.test(apiKey);
}

/**
 * 存储API密钥
 * @param {string} apiKey - API密钥
 * @throws {Error} 如果API密钥格式无效
 * @original IWA函数
 */
export function storeAPIKey(apiKey) {
  if (!validateAPIKeyFormat(apiKey)) {
    throw new Error("Invalid API key format. API key must contain only alphanumeric characters, dashes, and underscores.");
  }
  
  const userConfig = getUserConfig();
  
  // 清除现有的API密钥
  clearStoredAPIKey();
  
  // 在macOS上使用Keychain存储
  if (process.platform === "darwin") {
    try {
      const serviceName = getKeychainServiceName();
      executeCommand(`security add-generic-password -a $USER -s "${serviceName}" -w ${apiKey}`);
    } catch (error) {
      console.warn("Failed to store API key in Keychain:", error);
      // 降级到配置文件存储
      storeAPIKeyInConfig(apiKey, userConfig);
    }
  } else {
    // 在其他平台上存储到配置文件
    storeAPIKeyInConfig(apiKey, userConfig);
  }
}

/**
 * 凭据管理服务类
 * @description 提供完整的凭据管理功能
 */
export class CredentialsService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 300000; // 5分钟缓存
  }

  /**
   * 获取当前认证状态
   * @returns {Object} 认证状态
   */
  getAuthStatus() {
    return getAuthenticationStatus();
  }

  /**
   * 获取访问令牌
   * @param {boolean} refresh - 是否刷新令牌
   * @returns {Promise<string|null>} 访问令牌
   */
  async getAccessToken(refresh = false) {
    const authStatus = this.getAuthStatus();
    
    if (!authStatus.hasToken) {
      return null;
    }
    
    switch (authStatus.source) {
      case "ANTHROPIC_AUTH_TOKEN":
        return process.env.ANTHROPIC_AUTH_TOKEN;
      case "CLAUDE_CODE_OAUTH_TOKEN":
        return process.env.CLAUDE_CODE_OAUTH_TOKEN;
      case "claude.ai":
        const tokenData = getStoredTokenData();
        
        if (refresh && tokenData?.refreshToken) {
          try {
            const { refreshAccessToken } = require('./oauth-service.js');
            const newTokenData = await refreshAccessToken(tokenData.refreshToken);
            return newTokenData.accessToken;
          } catch (error) {
            console.error("Failed to refresh token:", error);
            return tokenData?.accessToken || null;
          }
        }
        
        return tokenData?.accessToken || null;
      default:
        return null;
    }
  }

  /**
   * 获取API密钥
   * @param {string} keyIdentifier - 密钥标识符（可选）
   * @returns {Promise<string|null>} API密钥
   */
  async getAPIKey(keyIdentifier) {
    if (keyIdentifier) {
      return getAPIKey(keyIdentifier);
    }
    
    // 尝试从各种来源获取API密钥
    const accessToken = await this.getAccessToken();
    
    if (accessToken) {
      try {
        return await fetchAPIKey(accessToken);
      } catch (error) {
        console.error("Failed to fetch API key:", error);
      }
    }
    
    // 尝试从存储中获取
    return this.getStoredAPIKey();
  }

  /**
   * 存储API密钥
   * @param {string} apiKey - API密钥
   * @returns {boolean} 是否存储成功
   */
  storeAPIKey(apiKey) {
    try {
      storeAPIKey(apiKey);
      return true;
    } catch (error) {
      console.error("Failed to store API key:", error);
      return false;
    }
  }

  /**
   * 清除所有凭据
   */
  clearCredentials() {
    clearStoredAPIKey();
    clearAPIKeyHelperCache();
    clearTokenCache();
    this.cache.clear();
  }

  /**
   * 验证凭据
   * @returns {Promise<boolean>} 凭据是否有效
   */
  async validateCredentials() {
    const accessToken = await this.getAccessToken();
    
    if (!accessToken) {
      return false;
    }
    
    try {
      const { oauthService } = require('./oauth-service.js');
      return await oauthService.validateToken(accessToken);
    } catch {
      return false;
    }
  }

  /**
   * 获取用户信息
   * @returns {Promise<Object|null>} 用户信息
   */
  async getUserInfo() {
    const accessToken = await this.getAccessToken();
    
    if (!accessToken) {
      return null;
    }
    
    try {
      const { getOAuthProfile } = require('./oauth-service.js');
      return await getOAuthProfile(accessToken);
    } catch (error) {
      console.error("Failed to get user info:", error);
      return null;
    }
  }

  /**
   * 获取组织信息
   * @returns {Promise<Object|null>} 组织信息
   */
  async getOrganizationInfo() {
    const userInfo = await this.getUserInfo();
    return userInfo?.organization || null;
  }

  /**
   * 检查令牌是否需要刷新
   * @returns {boolean} 是否需要刷新
   */
  needsTokenRefresh() {
    const tokenData = getStoredTokenData();
    
    if (!tokenData?.expiresAt) {
      return false;
    }
    
    return isTokenExpiringSoon(tokenData.expiresAt);
  }

  /**
   * 获取存储的API密钥
   * @returns {string|null} API密钥
   * @private
   */
  getStoredAPIKey() {
    // 首先尝试从Keychain获取（macOS）
    if (process.platform === "darwin") {
      try {
        const serviceName = getKeychainServiceName();
        const result = executeCommand(`security find-generic-password -a $USER -s "${serviceName}" -w`);
        return result.trim();
      } catch {
        // 降级到配置文件
      }
    }
    
    // 从配置文件获取
    const userConfig = getUserConfig();
    return userConfig.apiKey || null;
  }
}

// 辅助函数

/**
 * 发起HTTP请求
 * @param {string} method - HTTP方法
 * @param {string} url - 请求URL
 * @param {*} data - 请求数据
 * @param {Object} config - 请求配置
 * @returns {Promise<Object>} 响应对象
 */
async function makeHTTPRequest(method, url, data, config = {}) {
  const axios = require('axios');
  
  if (method === 'post') {
    return axios.post(url, data, config);
  }
  
  throw new Error(`Unsupported HTTP method: ${method}`);
}

/**
 * 获取API配置
 * @returns {Object} API配置
 */
function getAPIConfig() {
  return {
    API_KEY_URL: "https://api.anthropic.com/oauth/api-key"
  };
}

/**
 * 记录事件
 * @param {string} eventName - 事件名称
 * @param {Object} eventData - 事件数据
 */
function recordEvent(eventName, eventData) {
  console.log(`Event: ${eventName}`, eventData);
}

/**
 * 获取OAuth配置文件
 * @param {string} accessToken - 访问令牌
 * @returns {Promise<Object>} OAuth配置文件
 */
async function getOAuthProfile(accessToken) {
  const { getOAuthProfile } = require('./oauth-service.js');
  return getOAuthProfile(accessToken);
}

/**
 * 获取用户配置
 * @returns {Object} 用户配置
 */
function getUserConfig() {
  return { oauthAccount: {} };
}

/**
 * 获取存储的令牌数据
 * @returns {Object|null} 令牌数据
 */
function getStoredTokenData() {
  // @todo: 实现FD函数的实际逻辑
  return null;
}

/**
 * 检查是否有API密钥助手
 * @returns {boolean} 是否有API密钥助手
 */
function hasAPIKeyHelper() {
  // @todo: 实现Ig函数的实际逻辑
  return false;
}

/**
 * 检查作用域是否有效
 * @param {Array} scopes - 作用域数组
 * @returns {boolean} 是否有效
 */
function hasValidScopes(scopes) {
  // @todo: 实现hy函数的实际逻辑
  return Array.isArray(scopes) && scopes.length > 0;
}

/**
 * 解析密钥标识符
 * @param {string} keyIdentifier - 密钥标识符
 * @returns {Object} 解析结果
 */
function parseKeyIdentifier(keyIdentifier) {
  // @todo: 实现tJ函数的实际逻辑
  return { key: keyIdentifier };
}

/**
 * 清除存储的API密钥
 */
function clearStoredAPIKey() {
  // @todo: 实现DsA函数的实际逻辑
}

/**
 * 获取Keychain服务名称
 * @returns {string} 服务名称
 */
function getKeychainServiceName() {
  // @todo: 实现p91函数的实际逻辑
  return "claude-cli";
}

/**
 * 执行命令
 * @param {string} command - 命令
 * @returns {string} 命令输出
 */
function executeCommand(command) {
  // @todo: 实现GD函数的实际逻辑
  const { execSync } = require('child_process');
  return execSync(command, { encoding: 'utf8' });
}

/**
 * 在配置文件中存储API密钥
 * @param {string} apiKey - API密钥
 * @param {Object} userConfig - 用户配置
 */
function storeAPIKeyInConfig(apiKey, userConfig) {
  // @todo: 实现配置文件存储逻辑
  userConfig.apiKey = apiKey;
}

// 创建默认凭据服务实例
export const credentialsService = new CredentialsService();
