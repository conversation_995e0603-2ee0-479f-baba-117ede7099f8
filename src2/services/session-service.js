/**
 * 会话管理服务
 * @description 重构自原始文件中的会话管理代码，对应第5825-5900行
 * @original 原始代码行 5825-5900
 */

import { join } from "path";
import { basename } from "path";

/**
 * UUID验证正则表达式
 * @original dl4变量
 */
const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

/**
 * 验证UUID格式
 * @param {*} value - 要验证的值
 * @returns {string|null} 有效的UUID或null
 * @original PK函数
 */
export function validateUUID(value) {
  if (typeof value !== "string") {
    return null;
  }
  
  return UUID_REGEX.test(value) ? value : null;
}

/**
 * 获取项目目录路径
 * @returns {string} 项目目录路径
 * @original t81函数
 */
export function getProjectsDirectory() {
  return join(getDataDirectory(), "projects");
}

/**
 * 获取会话日志文件路径
 * @returns {string} 会话日志文件路径
 * @original lM1函数
 */
export function getSessionLogPath() {
  return getSessionPath(getSessionId());
}

/**
 * 获取指定会话的路径
 * @param {string} sessionId - 会话ID
 * @returns {string} 会话文件路径
 * @original Z$2函数
 */
export function getSessionPath(sessionId) {
  const projectDir = getProjectDirectory(getCurrentWorkingDirectory());
  return join(projectDir, `${sessionId}.jsonl`);
}

/**
 * 检查会话文件是否存在
 * @param {string} sessionId - 会话ID
 * @returns {boolean} 会话文件是否存在
 * @original G$2函数
 */
export function sessionExists(sessionId) {
  const projectDir = getProjectDirectory(getCurrentWorkingDirectory());
  const sessionPath = join(projectDir, `${sessionId}.jsonl`);
  const fs = getFileSystem();
  
  try {
    fs.statSync(sessionPath);
    return true;
  } catch {
    return false;
  }
}

/**
 * 获取环境类型
 * @returns {string} 环境类型
 * @original ll4函数
 */
export function getEnvironment() {
  return "production";
}

/**
 * 获取用户类型
 * @returns {string} 用户类型
 * @original F$2函数
 */
export function getUserType() {
  return "external";
}

/**
 * 获取项目目录
 * @param {string} workingDir - 工作目录
 * @returns {string} 项目目录路径
 * @original tr函数
 */
export function getProjectDirectory(workingDir) {
  return join(getProjectsDirectory(), workingDir.replace(/[^a-zA-Z0-9]/g, "-"));
}

/**
 * 会话管理器类
 * @description 提供完整的会话管理功能
 * @original I$2类
 */
export class SessionManager {
  constructor() {
    this.summaries = new Map();
    this.messages = new Map();
    this.checkpoints = new Map();
    this.bookmarks = new Map();
    this.didLoad = false;
    this.sessionFile = null;
  }

  /**
   * 插入消息链
   * @param {Array} messageChain - 消息链数组
   * @param {boolean} isSidechain - 是否为侧链
   * @returns {Promise<void>}
   * @original insertMessageChain方法
   */
  async insertMessageChain(messageChain, isSidechain = false) {
    let parentUuid = null;
    let gitBranch;
    
    try {
      gitBranch = await getCurrentGitBranch();
    } catch {
      gitBranch = undefined;
    }
    
    for (const message of messageChain) {
      const messageEntry = {
        parentUuid,
        isSidechain,
        userType: getUserType(),
        cwd: getCurrentWorkingDirectory(),
        sessionId: getSessionId(),
        version: getVersionInfo().VERSION,
        gitBranch,
        ...message
      };
      
      this.messages.set(message.uuid, messageEntry);
      await this.appendEntry(messageEntry);
      parentUuid = message.uuid;
    }
  }

  /**
   * 添加摘要
   * @param {string} uuid - UUID
   * @param {Object} summary - 摘要对象
   */
  addSummary(uuid, summary) {
    this.summaries.set(uuid, summary);
  }

  /**
   * 获取摘要
   * @param {string} uuid - UUID
   * @returns {Object|undefined} 摘要对象
   */
  getSummary(uuid) {
    return this.summaries.get(uuid);
  }

  /**
   * 添加检查点
   * @param {string} uuid - UUID
   * @param {Object} checkpoint - 检查点对象
   */
  addCheckpoint(uuid, checkpoint) {
    this.checkpoints.set(uuid, checkpoint);
  }

  /**
   * 获取检查点
   * @param {string} uuid - UUID
   * @returns {Object|undefined} 检查点对象
   */
  getCheckpoint(uuid) {
    return this.checkpoints.get(uuid);
  }

  /**
   * 添加书签
   * @param {string} uuid - UUID
   * @param {Object} bookmark - 书签对象
   */
  addBookmark(uuid, bookmark) {
    this.bookmarks.set(uuid, bookmark);
  }

  /**
   * 获取书签
   * @param {string} uuid - UUID
   * @returns {Object|undefined} 书签对象
   */
  getBookmark(uuid) {
    return this.bookmarks.get(uuid);
  }

  /**
   * 获取消息
   * @param {string} uuid - UUID
   * @returns {Object|undefined} 消息对象
   */
  getMessage(uuid) {
    return this.messages.get(uuid);
  }

  /**
   * 获取所有消息
   * @returns {Array} 消息数组
   */
  getAllMessages() {
    return Array.from(this.messages.values());
  }

  /**
   * 获取消息链
   * @param {string} startUuid - 起始UUID
   * @returns {Array} 消息链数组
   */
  getMessageChain(startUuid) {
    const chain = [];
    let currentUuid = startUuid;
    
    while (currentUuid) {
      const message = this.messages.get(currentUuid);
      if (!message) break;
      
      chain.unshift(message);
      currentUuid = message.parentUuid;
    }
    
    return chain;
  }

  /**
   * 清除会话数据
   */
  clear() {
    this.summaries.clear();
    this.messages.clear();
    this.checkpoints.clear();
    this.bookmarks.clear();
    this.didLoad = false;
    this.sessionFile = null;
  }

  /**
   * 加载会话数据
   * @param {string} sessionId - 会话ID
   * @returns {Promise<void>}
   */
  async loadSession(sessionId) {
    if (this.didLoad && this.sessionFile === sessionId) {
      return;
    }
    
    this.clear();
    
    const sessionPath = getSessionPath(sessionId);
    const fs = getFileSystem();
    
    try {
      if (!fs.existsSync(sessionPath)) {
        this.didLoad = true;
        this.sessionFile = sessionId;
        return;
      }
      
      const content = fs.readFileSync(sessionPath, 'utf8');
      const lines = content.trim().split('\n');
      
      for (const line of lines) {
        if (!line.trim()) continue;
        
        try {
          const entry = JSON.parse(line);
          this.processEntry(entry);
        } catch (error) {
          console.error('Failed to parse session entry:', error);
        }
      }
      
      this.didLoad = true;
      this.sessionFile = sessionId;
    } catch (error) {
      console.error('Failed to load session:', error);
      throw error;
    }
  }

  /**
   * 保存会话数据
   * @param {string} sessionId - 会话ID
   * @returns {Promise<void>}
   */
  async saveSession(sessionId) {
    const sessionPath = getSessionPath(sessionId);
    const fs = getFileSystem();
    
    // 确保目录存在
    const sessionDir = dirname(sessionPath);
    if (!fs.existsSync(sessionDir)) {
      fs.mkdirSync(sessionDir, { recursive: true });
    }
    
    const entries = [];
    
    // 收集所有条目
    for (const message of this.messages.values()) {
      entries.push(message);
    }
    
    for (const summary of this.summaries.values()) {
      entries.push({ type: 'summary', ...summary });
    }
    
    for (const checkpoint of this.checkpoints.values()) {
      entries.push({ type: 'checkpoint', ...checkpoint });
    }
    
    for (const bookmark of this.bookmarks.values()) {
      entries.push({ type: 'bookmark', ...bookmark });
    }
    
    // 按时间戳排序
    entries.sort((a, b) => (a.timestamp || 0) - (b.timestamp || 0));
    
    // 写入文件
    const content = entries.map(entry => JSON.stringify(entry)).join('\n');
    fs.writeFileSync(sessionPath, content, 'utf8');
  }

  /**
   * 获取会话统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      messagesCount: this.messages.size,
      summariesCount: this.summaries.size,
      checkpointsCount: this.checkpoints.size,
      bookmarksCount: this.bookmarks.size,
      didLoad: this.didLoad,
      sessionFile: this.sessionFile
    };
  }

  /**
   * 追加条目到会话文件
   * @param {Object} entry - 条目对象
   * @returns {Promise<void>}
   * @private
   */
  async appendEntry(entry) {
    if (!this.sessionFile) {
      return;
    }
    
    const sessionPath = getSessionPath(this.sessionFile);
    const fs = getFileSystem();
    
    // 确保目录存在
    const sessionDir = dirname(sessionPath);
    if (!fs.existsSync(sessionDir)) {
      fs.mkdirSync(sessionDir, { recursive: true });
    }
    
    const entryLine = JSON.stringify(entry) + '\n';
    fs.appendFileSync(sessionPath, entryLine, 'utf8');
  }

  /**
   * 插入检查点
   * @param {Object} checkpoint - 检查点对象
   * @returns {Promise<void>}
   * @original insertCheckpoint方法
   */
  async insertCheckpoint(checkpoint) {
    const sessionId = getSessionId();
    const checkpointEntry = {
      type: "checkpoint",
      sessionId,
      commit: checkpoint.commit,
      timestamp: checkpoint.timestamp.toISOString(),
      label: checkpoint.label,
      id: checkpoint.id
    };

    if (!this.checkpoints.has(sessionId)) {
      this.checkpoints.set(sessionId, []);
    }

    this.checkpoints.get(sessionId)?.push(checkpointEntry);
    await this.appendEntry(checkpointEntry);
  }

  /**
   * 插入书签
   * @param {string} sessionId - 会话ID
   * @param {boolean} isBookmarked - 是否书签
   * @returns {Promise<void>}
   * @original insertBookmark方法
   */
  async insertBookmark(sessionId, isBookmarked) {
    const bookmarkEntry = {
      type: "bookmark",
      sessionId,
      timestamp: new Date().toISOString(),
      isBookmarked
    };

    this.bookmarks.set(sessionId, bookmarkEntry);
    await this.appendEntry(bookmarkEntry);
  }

  /**
   * 获取所有转录
   * @returns {Promise<Array>} 转录数组
   * @original getAllTranscripts方法
   */
  async getAllTranscripts() {
    await this.loadAllSessions();

    const allMessages = [...this.messages.values()];
    const parentUuids = new Set(allMessages.map(msg => msg.parentUuid));

    // 找到根消息（没有父消息的消息）
    const rootMessages = allMessages.filter(msg => !parentUuids.has(msg.uuid));

    return rootMessages
      .map(msg => this.getTranscript(msg))
      .filter(transcript => transcript.length > 0);
  }

  /**
   * 获取转录
   * @param {Object} startMessage - 起始消息
   * @returns {Array} 转录数组
   * @original getTranscript方法
   */
  getTranscript(startMessage) {
    const transcript = [];
    let currentMessage = startMessage;

    while (currentMessage) {
      transcript.unshift(currentMessage);
      currentMessage = currentMessage.parentUuid ?
        this.messages.get(currentMessage.parentUuid) : undefined;
    }

    return transcript;
  }

  /**
   * 获取最后的日志
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Array|null>} 最后的日志或null
   * @original getLastLog方法
   */
  async getLastLog(sessionId) {
    const { messages } = await this.loadSessionData(sessionId);

    if (messages.size === 0) {
      return null;
    }

    // 按时间戳排序，获取最新的消息
    const sortedMessages = Array.from(messages.values())
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    const latestMessage = sortedMessages[0];
    if (!latestMessage) {
      return null;
    }

    // 构建消息链
    const messageChain = [];
    let currentMessage = latestMessage;

    while (currentMessage) {
      messageChain.unshift(currentMessage);
      currentMessage = currentMessage.parentUuid ?
        messages.get(currentMessage.parentUuid) : undefined;
    }

    return messageChain;
  }

  /**
   * 获取所有检查点
   * @param {Array} messageChain - 消息链
   * @returns {Map} 检查点映射
   * @original getAllCheckpoints方法
   */
  getAllCheckpoints(messageChain) {
    const checkpointMap = new Map();
    const sessionId = messageChain[messageChain.length - 1]?.sessionId;

    if (sessionId) {
      const sessionCheckpoints = this.checkpoints.get(sessionId) || [];

      for (const checkpoint of sessionCheckpoints) {
        if (checkpoint.id) {
          checkpointMap.set(checkpoint.id, checkpoint);
        }
      }
    }

    return checkpointMap;
  }

  /**
   * 加载所有会话
   * @returns {Promise<void>}
   * @original loadAllSessions方法
   */
  async loadAllSessions() {
    if (this.didLoad) {
      return;
    }

    const projectsDir = getProjectsDirectory();
    const fs = getFileSystem();

    try {
      if (!fs.existsSync(projectsDir)) {
        this.didLoad = true;
        return;
      }

      const projectDirs = fs.readdirSync(projectsDir);

      for (const projectDir of projectDirs) {
        const projectPath = join(projectsDir, projectDir);
        const stat = fs.statSync(projectPath);

        if (stat.isDirectory()) {
          const sessionFiles = fs.readdirSync(projectPath)
            .filter(file => file.endsWith('.jsonl'));

          for (const sessionFile of sessionFiles) {
            const sessionPath = join(projectPath, sessionFile);
            await this.loadSessionFile(sessionPath);
          }
        }
      }

      this.didLoad = true;
    } catch (error) {
      console.error('Failed to load all sessions:', error);
    }
  }

  /**
   * 加载会话文件
   * @param {string} sessionPath - 会话文件路径
   * @returns {Promise<void>}
   * @private
   */
  async loadSessionFile(sessionPath) {
    const fs = getFileSystem();

    try {
      const content = fs.readFileSync(sessionPath, 'utf8');
      const lines = content.trim().split('\n');

      for (const line of lines) {
        if (!line.trim()) continue;

        try {
          const entry = JSON.parse(line);
          this.processEntry(entry);
        } catch (error) {
          console.error('Failed to parse session entry:', error);
        }
      }
    } catch (error) {
      console.error(`Failed to load session file ${sessionPath}:`, error);
    }
  }

  /**
   * 加载会话数据
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Object>} 会话数据
   * @original pZ0函数
   */
  async loadSessionData(sessionId) {
    const messages = new Map();
    const summaries = new Map();
    const checkpoints = new Map();
    const bookmarks = new Map();

    const sessionPath = getSessionPath(sessionId);
    const fs = getFileSystem();

    try {
      if (!fs.existsSync(sessionPath)) {
        return { messages, summaries, checkpoints, bookmarks };
      }

      const content = fs.readFileSync(sessionPath, 'utf8');
      const lines = content.trim().split('\n');

      for (const line of lines) {
        if (!line.trim()) continue;

        try {
          const entry = JSON.parse(line);

          if (entry.uuid) {
            if (entry.type === 'summary') {
              summaries.set(entry.uuid, entry);
            } else if (entry.type === 'checkpoint') {
              checkpoints.set(entry.uuid, entry);
            } else if (entry.type === 'bookmark') {
              bookmarks.set(entry.uuid, entry);
            } else {
              messages.set(entry.uuid, entry);
            }
          }
        } catch (error) {
          console.error('Failed to parse session entry:', error);
        }
      }
    } catch (error) {
      console.error('Failed to load session data:', error);
    }

    return { messages, summaries, checkpoints, bookmarks };
  }

  /**
   * 处理条目
   * @param {Object} entry - 条目对象
   * @private
   */
  processEntry(entry) {
    if (entry.uuid) {
      if (entry.type === 'summary') {
        this.summaries.set(entry.uuid, entry);
      } else if (entry.type === 'checkpoint') {
        this.checkpoints.set(entry.uuid, entry);
      } else if (entry.type === 'bookmark') {
        this.bookmarks.set(entry.uuid, entry);
      } else {
        this.messages.set(entry.uuid, entry);
      }
    }
  }
}

// 辅助函数

/**
 * 获取数据目录
 * @returns {string} 数据目录路径
 * @original YQ函数的实现（推测）
 */
function getDataDirectory() {
  // @todo: 实现YQ函数的实际逻辑
  const { join } = require('path');
  const { homedir } = require('os');
  return join(homedir(), '.claude-code');
}

/**
 * 获取当前工作目录
 * @returns {string} 当前工作目录
 * @original a0函数的实现（推测）
 */
function getCurrentWorkingDirectory() {
  return process.cwd();
}

/**
 * 获取会话ID
 * @returns {string} 会话ID
 * @original B9函数的实现（推测）
 */
function getSessionId() {
  // @todo: 实现B9函数的实际逻辑
  return 'default-session';
}

/**
 * 获取当前Git分支
 * @returns {Promise<string>} Git分支名称
 * @original vK1函数的实现（推测）
 */
async function getCurrentGitBranch() {
  // @todo: 实现vK1函数的实际逻辑
  const { getCurrentBranchName } = require('../utils/git-utils.js');
  return getCurrentBranchName();
}

/**
 * 获取版本信息
 * @returns {Object} 版本信息
 * @original 版本信息对象的实现（推测）
 */
function getVersionInfo() {
  return {
    ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
    PACKAGE_URL: "@anthropic-ai/claude-code",
    README_URL: "https://docs.anthropic.com/s/claude-code",
    VERSION: "1.0.72"
  };
}

/**
 * 获取文件系统模块
 * @returns {Object} 文件系统模块
 * @original x1函数的实现（推测）
 */
function getFileSystem() {
  return require('fs');
}

/**
 * 获取目录名
 * @param {string} path - 文件路径
 * @returns {string} 目录名
 */
function dirname(path) {
  const { dirname } = require('path');
  return dirname(path);
}

// 创建默认会话管理器实例
let defaultSessionManager = null;

/**
 * 获取默认会话管理器
 * @returns {SessionManager} 会话管理器实例
 * @original mL函数
 */
export function getSessionManager() {
  if (!defaultSessionManager) {
    defaultSessionManager = new SessionManager();
  }
  return defaultSessionManager;
}

// 导出便捷函数
export const Session = {
  getManager: () => getSessionManager(),
  validateUUID: (value) => validateUUID(value),
  getProjectsDirectory: () => getProjectsDirectory(),
  getSessionPath: (sessionId) => getSessionPath(sessionId),
  sessionExists: (sessionId) => sessionExists(sessionId),
  getEnvironment: () => getEnvironment(),
  getUserType: () => getUserType()
};
