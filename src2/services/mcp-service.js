/**
 * MCP (Model Context Protocol) 服务管理
 * @description Claude Code的核心MCP服务器管理功能
 * @original 原始代码分布在多个位置，主要在3232行、4974行等
 */

/**
 * 验证并标准化配置作用域
 * @param {string} scope - 配置作用域 (local, user, project)
 * @returns {string} 标准化的作用域
 * @throws {Error} 无效作用域时抛出错误
 * @original function M91(A) { if (!A) return "local"; if (!Xr1.options.includes(A)) throw new Error(`Invalid scope: ${A}. Must be one of: ${Xr1.options.join(", ")}`); return A; }
 */
export function validateScope(scope) {
  if (!scope) return "local";
  
  const validScopes = ["local", "user", "project"];
  if (!validScopes.includes(scope)) {
    throw new Error(`Invalid scope: ${scope}. Must be one of: ${validScopes.join(", ")}`);
  }
  
  return scope;
}

/**
 * 验证并标准化传输类型
 * @param {string} transport - 传输类型 (stdio, sse, http)
 * @returns {string} 标准化的传输类型
 * @throws {Error} 无效传输类型时抛出错误
 * @original function PIA(A) { if (!A) return "stdio"; if (A !== "stdio" && A !== "sse" && A !== "http") throw new Error(`Invalid transport type: ${A}. Must be one of: stdio, sse, http`); return A; }
 */
export function validateTransportType(transport) {
  if (!transport) return "stdio";
  
  const validTransports = ["stdio", "sse", "http"];
  if (!validTransports.includes(transport)) {
    throw new Error(`Invalid transport type: ${transport}. Must be one of: ${validTransports.join(", ")}`);
  }
  
  return transport;
}

/**
 * 解析HTTP头部字符串数组
 * @param {string[]} headers - 头部字符串数组，格式为 "Header-Name: value"
 * @returns {Object} 解析后的头部对象
 * @throws {Error} 头部格式无效时抛出错误
 * @original function Hr1(A) { let B = {}; for (let Q of A) { let D = Q.indexOf(":"); if (D === -1) throw new Error(`Invalid header format: "${Q}". Expected format: "Header-Name: value"`); let Z = Q.substring(0, D).trim(), G = Q.substring(D + 1).trim(); B[Z] = G; } return B; }
 */
export function parseHeaders(headers) {
  const result = {};
  
  for (const header of headers) {
    const colonIndex = header.indexOf(":");
    if (colonIndex === -1) {
      throw new Error(`Invalid header format: "${header}". Expected format: "Header-Name: value"`);
    }
    
    const name = header.substring(0, colonIndex).trim();
    const value = header.substring(colonIndex + 1).trim();
    result[name] = value;
  }
  
  return result;
}

/**
 * 根据名称查找MCP服务器配置
 * @param {string} serverName - 服务器名称
 * @returns {Object|null} 服务器配置对象，未找到时返回null
 * @original function gi(A) { let { servers: B } = pZ("user"), { servers: Q } = pZ("project"), { servers: D } = pZ("local"); if (D[A]) return D[A]; if (Q[A]) return Q[A]; if (B[A]) return B[A]; return null; }
 */
export function findMcpServer(serverName) {
  // 注意：这里需要实现pZ函数来获取不同作用域的配置
  // 按优先级顺序查找：local -> project -> user
  
  const localConfig = getConfigByScope("local");
  const projectConfig = getConfigByScope("project");
  const userConfig = getConfigByScope("user");
  
  if (localConfig.servers[serverName]) {
    return { ...localConfig.servers[serverName], scope: "local" };
  }
  
  if (projectConfig.servers[serverName]) {
    return { ...projectConfig.servers[serverName], scope: "project" };
  }
  
  if (userConfig.servers[serverName]) {
    return { ...userConfig.servers[serverName], scope: "user" };
  }
  
  return null;
}

/**
 * 检查MCP服务器连接状态
 * @param {string} serverName - 服务器名称
 * @param {Object} serverConfig - 服务器配置
 * @returns {Promise<string>} 连接状态描述
 * @original async function ScB(A, B) { try { let Q = await k11(A, B); if (Q.type === "connected") return "✓ Connected"; else if (Q.type === "needs-auth") return "⚠ Needs authentication"; else return "✗ Failed to connect"; } catch (Q) { return "✗ Connection error"; } }
 */
export async function checkMcpServerStatus(serverName, serverConfig) {
  try {
    // 注意：这里需要实现k11函数来实际检查连接
    const connectionResult = await testMcpConnection(serverName, serverConfig);
    
    switch (connectionResult.type) {
      case "connected":
        return "✓ Connected";
      case "needs-auth":
        return "⚠ Needs authentication";
      default:
        return "✗ Failed to connect";
    }
  } catch (error) {
    return "✗ Connection error";
  }
}

/**
 * 获取作用域的友好显示名称
 * @param {string} scope - 配置作用域
 * @returns {string} 友好显示名称
 * @original function Yh(A) { switch (A) { case "local": return "Local config (private to you in this project)"; case "project": return "Project config (shared via .mcp.json)"; case "user": return "User config (available in all your projects)"; default: return A; } }
 */
export function getScopeDisplayName(scope) {
  switch (scope) {
    case "local":
      return "Local config (private to you in this project)";
    case "project":
      return "Project config (shared via .mcp.json)";
    case "user":
      return "User config (available in all your projects)";
    default:
      return scope;
  }
}

/**
 * 创建MCP服务器配置对象
 * @param {string} type - 服务器类型 (stdio, sse, http)
 * @param {Object} options - 配置选项
 * @returns {Object} 服务器配置对象
 */
export function createMcpServerConfig(type, options = {}) {
  const baseConfig = {
    type: validateTransportType(type)
  };
  
  switch (type) {
    case "stdio":
      return {
        ...baseConfig,
        command: options.command,
        args: options.args || [],
        env: options.env || {}
      };
      
    case "sse":
    case "http":
      return {
        ...baseConfig,
        url: options.url,
        headers: options.headers || {}
      };
      
    default:
      throw new Error(`Unsupported MCP server type: ${type}`);
  }
}

// 这些函数需要在其他模块中实现
function getConfigByScope(scope) {
  // @todo: 实现配置获取逻辑
  throw new Error("getConfigByScope not implemented");
}

async function testMcpConnection(serverName, serverConfig) {
  // @todo: 实现MCP连接测试逻辑
  throw new Error("testMcpConnection not implemented");
}
