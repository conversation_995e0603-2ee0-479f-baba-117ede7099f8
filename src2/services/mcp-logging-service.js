/**
 * MCP日志记录服务
 * @description 重构自原始文件中的MCP日志记录代码，对应第6236-6284行
 * @original 原始代码行 6236-6284
 */

import { join } from "path";

/**
 * 记录MCP服务器错误
 * @param {string} serverName - 服务器名称
 * @param {Error|string} error - 错误信息
 * @original YG函数
 */
export function logMCPServerError(serverName, error) {
  // 输出到控制台
  console.error(`MCP server "${serverName}" ${error}`);
  
  // 检查是否禁用日志记录
  const settingsConfig = getSettingsConfig();
  if (settingsConfig?.cleanupPeriodDays === 0) {
    return;
  }
  
  try {
    const logDir = getMCPLogsDirectory(serverName);
    const errorMessage = error instanceof Error ? error.stack || error.message : String(error);
    const timestamp = new Date().toISOString();
    const logFile = join(logDir, getCurrentDateString() + ".txt");
    
    // 确保日志目录存在
    const fs = getFileSystem();
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    // 初始化日志文件
    if (!fs.existsSync(logFile)) {
      fs.writeFileSync(logFile, "[]", {
        encoding: "utf8",
        flush: false
      });
    }
    
    // 创建日志条目
    const logEntry = {
      error: errorMessage,
      timestamp,
      sessionId: getSessionId(),
      cwd: getCurrentWorkingDirectory()
    };
    
    // 读取现有日志并添加新条目
    const existingLogs = readLogFile(logFile);
    existingLogs.push(logEntry);
    
    // 写入更新后的日志
    fs.writeFileSync(logFile, JSON.stringify(existingLogs, null, 2), {
      encoding: "utf8",
      flush: false
    });
  } catch (logError) {
    // 静默处理日志记录错误
    console.error('Failed to log MCP server error:', logError);
  }
}

/**
 * 记录MCP服务器调试信息
 * @param {string} serverName - 服务器名称
 * @param {string} debugMessage - 调试信息
 * @original YB函数
 */
export function logMCPServerDebug(serverName, debugMessage) {
  // 输出到控制台
  console.log(`MCP server "${serverName}": ${debugMessage}`);
  
  try {
    const logDir = getMCPLogsDirectory(serverName);
    const timestamp = new Date().toISOString();
    const logFile = join(logDir, getCurrentDateString() + ".txt");
    
    // 确保日志目录存在
    const fs = getFileSystem();
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    // 初始化日志文件
    if (!fs.existsSync(logFile)) {
      fs.writeFileSync(logFile, "[]", {
        encoding: "utf8",
        flush: false
      });
    }
    
    // 创建日志条目
    const logEntry = {
      debug: debugMessage,
      timestamp,
      sessionId: getSessionId(),
      cwd: getCurrentWorkingDirectory()
    };
    
    // 读取现有日志并添加新条目
    const existingLogs = readLogFile(logFile);
    existingLogs.push(logEntry);
    
    // 写入更新后的日志
    fs.writeFileSync(logFile, JSON.stringify(existingLogs, null, 2), {
      encoding: "utf8",
      flush: false
    });
  } catch (logError) {
    // 静默处理日志记录错误
    console.error('Failed to log MCP server debug:', logError);
  }
}

/**
 * MCP日志记录服务类
 * @description 提供完整的MCP日志记录功能
 */
export class MCPLoggingService {
  constructor() {
    this.logBuffer = new Map();
    this.flushInterval = 5000; // 5秒刷新间隔
    this.maxBufferSize = 100;
    this.flushTimer = null;
  }

  /**
   * 启动日志服务
   */
  start() {
    if (this.flushTimer) {
      return;
    }
    
    this.flushTimer = setInterval(() => {
      this.flushLogs();
    }, this.flushInterval);
  }

  /**
   * 停止日志服务
   */
  stop() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
    
    // 刷新剩余的日志
    this.flushLogs();
  }

  /**
   * 记录错误（带缓冲）
   * @param {string} serverName - 服务器名称
   * @param {Error|string} error - 错误信息
   */
  logError(serverName, error) {
    const logEntry = {
      type: 'error',
      serverName,
      message: error instanceof Error ? error.stack || error.message : String(error),
      timestamp: new Date().toISOString(),
      sessionId: getSessionId(),
      cwd: getCurrentWorkingDirectory()
    };
    
    this.addToBuffer(serverName, logEntry);
  }

  /**
   * 记录调试信息（带缓冲）
   * @param {string} serverName - 服务器名称
   * @param {string} debugMessage - 调试信息
   */
  logDebug(serverName, debugMessage) {
    const logEntry = {
      type: 'debug',
      serverName,
      message: debugMessage,
      timestamp: new Date().toISOString(),
      sessionId: getSessionId(),
      cwd: getCurrentWorkingDirectory()
    };
    
    this.addToBuffer(serverName, logEntry);
  }

  /**
   * 记录信息
   * @param {string} serverName - 服务器名称
   * @param {string} infoMessage - 信息内容
   */
  logInfo(serverName, infoMessage) {
    const logEntry = {
      type: 'info',
      serverName,
      message: infoMessage,
      timestamp: new Date().toISOString(),
      sessionId: getSessionId(),
      cwd: getCurrentWorkingDirectory()
    };
    
    this.addToBuffer(serverName, logEntry);
  }

  /**
   * 记录警告
   * @param {string} serverName - 服务器名称
   * @param {string} warningMessage - 警告信息
   */
  logWarning(serverName, warningMessage) {
    const logEntry = {
      type: 'warning',
      serverName,
      message: warningMessage,
      timestamp: new Date().toISOString(),
      sessionId: getSessionId(),
      cwd: getCurrentWorkingDirectory()
    };
    
    this.addToBuffer(serverName, logEntry);
  }

  /**
   * 读取服务器日志
   * @param {string} serverName - 服务器名称
   * @param {Object} options - 选项
   * @returns {Array} 日志条目数组
   */
  readLogs(serverName, options = {}) {
    const {
      date = getCurrentDateString(),
      limit = 100,
      type = null
    } = options;
    
    const logFile = join(getMCPLogsDirectory(serverName), date + ".txt");
    const logs = readLogFile(logFile);
    
    let filteredLogs = logs;
    
    // 按类型过滤
    if (type) {
      filteredLogs = logs.filter(log => log.type === type || 
        (type === 'error' && log.error) || 
        (type === 'debug' && log.debug));
    }
    
    // 限制数量
    if (limit > 0) {
      filteredLogs = filteredLogs.slice(-limit);
    }
    
    return filteredLogs;
  }

  /**
   * 清理旧日志
   * @param {string} serverName - 服务器名称
   * @param {number} daysToKeep - 保留天数
   */
  cleanupLogs(serverName, daysToKeep = 7) {
    const logDir = getMCPLogsDirectory(serverName);
    const fs = getFileSystem();
    
    try {
      if (!fs.existsSync(logDir)) {
        return;
      }
      
      const files = fs.readdirSync(logDir);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      
      for (const file of files) {
        if (!file.endsWith('.txt')) {
          continue;
        }
        
        const filePath = join(logDir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime < cutoffDate) {
          fs.unlinkSync(filePath);
        }
      }
    } catch (error) {
      console.error(`Failed to cleanup logs for ${serverName}:`, error);
    }
  }

  /**
   * 获取日志统计信息
   * @param {string} serverName - 服务器名称
   * @returns {Object} 统计信息
   */
  getLogStats(serverName) {
    const logDir = getMCPLogsDirectory(serverName);
    const fs = getFileSystem();
    
    const stats = {
      totalFiles: 0,
      totalSize: 0,
      oldestLog: null,
      newestLog: null,
      errorCount: 0,
      debugCount: 0
    };
    
    try {
      if (!fs.existsSync(logDir)) {
        return stats;
      }
      
      const files = fs.readdirSync(logDir);
      
      for (const file of files) {
        if (!file.endsWith('.txt')) {
          continue;
        }
        
        const filePath = join(logDir, file);
        const fileStats = fs.statSync(filePath);
        
        stats.totalFiles++;
        stats.totalSize += fileStats.size;
        
        if (!stats.oldestLog || fileStats.mtime < stats.oldestLog) {
          stats.oldestLog = fileStats.mtime;
        }
        
        if (!stats.newestLog || fileStats.mtime > stats.newestLog) {
          stats.newestLog = fileStats.mtime;
        }
        
        // 统计日志条目
        const logs = readLogFile(filePath);
        for (const log of logs) {
          if (log.error) {
            stats.errorCount++;
          } else if (log.debug) {
            stats.debugCount++;
          }
        }
      }
    } catch (error) {
      console.error(`Failed to get log stats for ${serverName}:`, error);
    }
    
    return stats;
  }

  /**
   * 添加到缓冲区
   * @param {string} serverName - 服务器名称
   * @param {Object} logEntry - 日志条目
   * @private
   */
  addToBuffer(serverName, logEntry) {
    if (!this.logBuffer.has(serverName)) {
      this.logBuffer.set(serverName, []);
    }
    
    const buffer = this.logBuffer.get(serverName);
    buffer.push(logEntry);
    
    // 如果缓冲区满了，立即刷新
    if (buffer.length >= this.maxBufferSize) {
      this.flushServerLogs(serverName);
    }
  }

  /**
   * 刷新所有日志
   * @private
   */
  flushLogs() {
    for (const serverName of this.logBuffer.keys()) {
      this.flushServerLogs(serverName);
    }
  }

  /**
   * 刷新特定服务器的日志
   * @param {string} serverName - 服务器名称
   * @private
   */
  flushServerLogs(serverName) {
    const buffer = this.logBuffer.get(serverName);
    
    if (!buffer || buffer.length === 0) {
      return;
    }
    
    try {
      const logDir = getMCPLogsDirectory(serverName);
      const logFile = join(logDir, getCurrentDateString() + ".txt");
      
      // 确保目录存在
      const fs = getFileSystem();
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
      
      // 读取现有日志
      const existingLogs = readLogFile(logFile);
      
      // 添加缓冲区中的日志
      existingLogs.push(...buffer);
      
      // 写入文件
      fs.writeFileSync(logFile, JSON.stringify(existingLogs, null, 2), {
        encoding: "utf8",
        flush: false
      });
      
      // 清空缓冲区
      this.logBuffer.set(serverName, []);
    } catch (error) {
      console.error(`Failed to flush logs for ${serverName}:`, error);
    }
  }
}

// 辅助函数

/**
 * 读取日志文件
 * @param {string} filePath - 文件路径
 * @returns {Array} 日志条目数组
 * @original sZ0函数
 */
function readLogFile(filePath) {
  const fs = getFileSystem();
  
  if (!fs.existsSync(filePath)) {
    return [];
  }
  
  try {
    const content = fs.readFileSync(filePath, { encoding: "utf8" });
    return JSON.parse(content);
  } catch {
    return [];
  }
}

/**
 * 获取MCP日志目录
 * @param {string} serverName - 服务器名称
 * @returns {string} 日志目录路径
 * @original _N.mcpLogs函数的实现（推测）
 */
function getMCPLogsDirectory(serverName) {
  // @todo: 实现_N.mcpLogs函数的实际逻辑
  const { join } = require('path');
  const { homedir } = require('os');
  return join(homedir(), '.claude-code', 'mcp-logs', serverName);
}

/**
 * 获取当前日期字符串
 * @returns {string} 日期字符串（YYYY-MM-DD格式）
 * @original aZ0变量的实现（推测）
 */
function getCurrentDateString() {
  const now = new Date();
  return now.toISOString().split('T')[0];
}

/**
 * 获取设置配置
 * @returns {Object} 设置配置
 * @original C9函数的实现（推测）
 */
function getSettingsConfig() {
  // @todo: 实现C9函数的实际逻辑
  return {};
}

/**
 * 获取会话ID
 * @returns {string} 会话ID
 * @original B9函数的实现（推测）
 */
function getSessionId() {
  // @todo: 实现B9函数的实际逻辑
  return 'default-session';
}

/**
 * 获取当前工作目录
 * @returns {string} 当前工作目录
 * @original x1().cwd函数的实现（推测）
 */
function getCurrentWorkingDirectory() {
  return process.cwd();
}

/**
 * 获取文件系统模块
 * @returns {Object} 文件系统模块
 * @original x1函数的实现（推测）
 */
function getFileSystem() {
  return require('fs');
}

// 创建默认MCP日志服务实例
export const mcpLoggingService = new MCPLoggingService();

// 导出便捷函数
export const MCPLogging = {
  logError: (serverName, error) => logMCPServerError(serverName, error),
  logDebug: (serverName, message) => logMCPServerDebug(serverName, message),
  logInfo: (serverName, message) => mcpLoggingService.logInfo(serverName, message),
  logWarning: (serverName, message) => mcpLoggingService.logWarning(serverName, message),
  readLogs: (serverName, options) => mcpLoggingService.readLogs(serverName, options),
  cleanupLogs: (serverName, daysToKeep) => mcpLoggingService.cleanupLogs(serverName, daysToKeep),
  getStats: (serverName) => mcpLoggingService.getLogStats(serverName),
  start: () => mcpLoggingService.start(),
  stop: () => mcpLoggingService.stop()
};
