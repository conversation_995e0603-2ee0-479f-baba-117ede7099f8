/**
 * 工具模式管理服务
 * @description 重构自原始文件中的工具模式管理代码，对应第4433-4500行
 * @original 原始代码行 4433-4500
 */

/**
 * 工具执行模式枚举
 * @description 定义各种工具执行模式
 * @original QYA函数相关的模式定义
 */
export const ToolExecutionMode = {
  DEFAULT: "default",
  PLAN: "plan",
  ACCEPT_EDITS: "acceptEdits",
  BYPASS_PERMISSIONS: "bypassPermissions"
};

/**
 * 工具模式配置
 * @original kr1对象
 */
export const TOOL_MODE_CONFIG = {
  filePatternTools: ["Read", "Write", "Edit", "Glob", "MultiEdit", "NotebookRead", "NotebookEdit"],
  bashPrefixTools: ["Bash"],
  customValidation: {
    WebSearch: (pattern) => {
      if (pattern.includes("*") || pattern.includes("?")) {
        return {
          valid: false,
          error: "WebSearch does not support wildcards",
          suggestion: "Use exact search terms without * or ?",
          examples: ["WebSearch(claude ai)", "WebSearch(typescript tutorial)"]
        };
      }
      return {
        valid: true
      };
    },
    WebFetch: (pattern) => {
      if (pattern.includes("://") || pattern.startsWith("http")) {
        return {
          valid: false,
          error: "WebFetch permissions use domain format, not URLs",
          suggestion: 'Use "domain:hostname" format',
          examples: ["WebFetch(domain:example.com)", "WebFetch(domain:github.com)"]
        };
      }
      if (!pattern.startsWith("domain:")) {
        return {
          valid: false,
          error: 'WebFetch permissions must use "domain:" prefix',
          suggestion: 'Use "domain:hostname" format',
          examples: ["WebFetch(domain:example.com)", "WebFetch(domain:*.google.com)"]
        };
      }
      return {
        valid: true
      };
    }
  }
};

/**
 * 验证工具执行模式
 * @param {string} mode - 模式字符串
 * @returns {string} 验证后的模式
 * @original QYA函数
 */
export function validateToolExecutionMode(mode) {
  switch (mode) {
    case "bypassPermissions":
      return ToolExecutionMode.BYPASS_PERMISSIONS;
    case "acceptEdits":
      return ToolExecutionMode.ACCEPT_EDITS;
    case "plan":
      return ToolExecutionMode.PLAN;
    case "default":
      return ToolExecutionMode.DEFAULT;
    default:
      return ToolExecutionMode.DEFAULT;
  }
}

/**
 * 获取工具模式显示名称
 * @param {string} mode - 工具模式
 * @returns {string} 显示名称
 * @original xK1函数
 */
export function getToolModeDisplayName(mode) {
  switch (mode) {
    case ToolExecutionMode.DEFAULT:
      return "Default";
    case ToolExecutionMode.PLAN:
      return "Plan Mode";
    case ToolExecutionMode.ACCEPT_EDITS:
      return "Accept Edits";
    case ToolExecutionMode.BYPASS_PERMISSIONS:
      return "Bypass Permissions";
    default:
      return "Default";
  }
}

/**
 * 检查是否为默认模式
 * @param {string} mode - 工具模式
 * @returns {boolean} 是否为默认模式
 * @original jr1函数
 */
export function isDefaultMode(mode) {
  return mode === ToolExecutionMode.DEFAULT || mode === undefined;
}

/**
 * 获取工具模式简短名称
 * @param {string} mode - 工具模式
 * @returns {string} 简短名称
 * @original DYA函数
 */
export function getToolModeShortName(mode) {
  switch (mode) {
    case ToolExecutionMode.DEFAULT:
      return "Default";
    case ToolExecutionMode.PLAN:
      return "Plan";
    case ToolExecutionMode.ACCEPT_EDITS:
      return "Accept";
    case ToolExecutionMode.BYPASS_PERMISSIONS:
      return "Bypass";
    default:
      return "Default";
  }
}

/**
 * 获取工具模式图标
 * @param {string} mode - 工具模式
 * @returns {string} 图标字符
 * @original yr1函数
 */
export function getToolModeIcon(mode) {
  switch (mode) {
    case ToolExecutionMode.DEFAULT:
      return "";
    case ToolExecutionMode.PLAN:
      return "⏸";
    case ToolExecutionMode.ACCEPT_EDITS:
      return "⏵⏵";
    case ToolExecutionMode.BYPASS_PERMISSIONS:
      return "";
    default:
      return "";
  }
}

/**
 * 获取工具模式样式类
 * @param {string} mode - 工具模式
 * @returns {string} 样式类名
 * @original _91函数
 */
export function getToolModeStyleClass(mode) {
  switch (mode) {
    case ToolExecutionMode.DEFAULT:
      return "text";
    case ToolExecutionMode.PLAN:
      return "planMode";
    case ToolExecutionMode.ACCEPT_EDITS:
      return "autoAccept";
    case ToolExecutionMode.BYPASS_PERMISSIONS:
      return "error";
    default:
      return "text";
  }
}

/**
 * 工具模式管理服务类
 * @description 提供完整的工具模式管理功能
 */
export class ToolModeService {
  constructor() {
    this.currentMode = ToolExecutionMode.DEFAULT;
    this.modeHistory = [];
    this.modeChangeListeners = [];
  }

  /**
   * 设置当前工具模式
   * @param {string} mode - 新的工具模式
   * @returns {boolean} 是否设置成功
   */
  setMode(mode) {
    const validatedMode = validateToolExecutionMode(mode);
    
    if (validatedMode !== this.currentMode) {
      const previousMode = this.currentMode;
      this.currentMode = validatedMode;
      
      // 记录模式历史
      this.modeHistory.push({
        mode: validatedMode,
        previousMode,
        timestamp: new Date(),
        source: 'manual'
      });
      
      // 通知监听器
      this.notifyModeChange(validatedMode, previousMode);
      
      return true;
    }
    
    return false;
  }

  /**
   * 获取当前工具模式
   * @returns {string} 当前工具模式
   */
  getCurrentMode() {
    return this.currentMode;
  }

  /**
   * 获取模式信息
   * @param {string} mode - 工具模式（可选，默认为当前模式）
   * @returns {Object} 模式信息
   */
  getModeInfo(mode = this.currentMode) {
    return {
      mode,
      displayName: getToolModeDisplayName(mode),
      shortName: getToolModeShortName(mode),
      icon: getToolModeIcon(mode),
      styleClass: getToolModeStyleClass(mode),
      isDefault: isDefaultMode(mode)
    };
  }

  /**
   * 检查工具是否支持文件模式
   * @param {string} toolName - 工具名称
   * @returns {boolean} 是否支持文件模式
   */
  isFilePatternTool(toolName) {
    return TOOL_MODE_CONFIG.filePatternTools.includes(toolName);
  }

  /**
   * 检查工具是否支持Bash前缀
   * @param {string} toolName - 工具名称
   * @returns {boolean} 是否支持Bash前缀
   */
  isBashPrefixTool(toolName) {
    return TOOL_MODE_CONFIG.bashPrefixTools.includes(toolName);
  }

  /**
   * 获取工具的执行配置
   * @param {string} toolName - 工具名称
   * @param {string} mode - 执行模式（可选）
   * @returns {Object} 执行配置
   */
  getToolExecutionConfig(toolName, mode = this.currentMode) {
    return {
      mode,
      toolName,
      supportsFilePattern: this.isFilePatternTool(toolName),
      supportsBashPrefix: this.isBashPrefixTool(toolName),
      requiresPermission: mode !== ToolExecutionMode.BYPASS_PERMISSIONS,
      autoAccept: mode === ToolExecutionMode.ACCEPT_EDITS,
      planMode: mode === ToolExecutionMode.PLAN
    };
  }

  /**
   * 添加模式变更监听器
   * @param {Function} listener - 监听器函数
   */
  addModeChangeListener(listener) {
    if (typeof listener === 'function') {
      this.modeChangeListeners.push(listener);
    }
  }

  /**
   * 移除模式变更监听器
   * @param {Function} listener - 监听器函数
   * @returns {boolean} 是否移除成功
   */
  removeModeChangeListener(listener) {
    const index = this.modeChangeListeners.indexOf(listener);
    if (index > -1) {
      this.modeChangeListeners.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 通知模式变更
   * @param {string} newMode - 新模式
   * @param {string} previousMode - 之前的模式
   * @private
   */
  notifyModeChange(newMode, previousMode) {
    const event = {
      newMode,
      previousMode,
      timestamp: new Date(),
      modeInfo: this.getModeInfo(newMode)
    };
    
    for (const listener of this.modeChangeListeners) {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in mode change listener:', error);
      }
    }
  }

  /**
   * 获取模式历史
   * @param {number} limit - 限制数量（可选）
   * @returns {Array} 模式历史数组
   */
  getModeHistory(limit) {
    if (limit && limit > 0) {
      return this.modeHistory.slice(-limit);
    }
    return [...this.modeHistory];
  }

  /**
   * 清空模式历史
   */
  clearModeHistory() {
    this.modeHistory = [];
  }

  /**
   * 重置到默认模式
   * @returns {boolean} 是否重置成功
   */
  resetToDefault() {
    return this.setMode(ToolExecutionMode.DEFAULT);
  }

  /**
   * 临时设置模式
   * @param {string} mode - 临时模式
   * @param {Function} callback - 回调函数
   * @returns {*} 回调函数的返回值
   */
  withTemporaryMode(mode, callback) {
    const originalMode = this.currentMode;
    
    try {
      this.setMode(mode);
      return callback();
    } finally {
      this.setMode(originalMode);
    }
  }

  /**
   * 获取所有可用模式
   * @returns {Array} 模式信息数组
   */
  getAllModes() {
    return Object.values(ToolExecutionMode).map(mode => this.getModeInfo(mode));
  }

  /**
   * 验证模式是否有效
   * @param {string} mode - 要验证的模式
   * @returns {boolean} 是否有效
   */
  isValidMode(mode) {
    return Object.values(ToolExecutionMode).includes(mode);
  }

  /**
   * 获取模式统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const modeUsage = {};
    
    for (const entry of this.modeHistory) {
      modeUsage[entry.mode] = (modeUsage[entry.mode] || 0) + 1;
    }
    
    return {
      currentMode: this.currentMode,
      totalModeChanges: this.modeHistory.length,
      modeUsage,
      listenersCount: this.modeChangeListeners.length,
      lastModeChange: this.modeHistory.length > 0 ? 
        this.modeHistory[this.modeHistory.length - 1].timestamp : null
    };
  }

  /**
   * 导出配置
   * @returns {Object} 配置对象
   */
  exportConfig() {
    return {
      currentMode: this.currentMode,
      modeHistory: [...this.modeHistory],
      toolModeConfig: { ...TOOL_MODE_CONFIG }
    };
  }

  /**
   * 导入配置
   * @param {Object} config - 配置对象
   */
  importConfig(config) {
    if (config.currentMode && this.isValidMode(config.currentMode)) {
      this.currentMode = config.currentMode;
    }
    
    if (Array.isArray(config.modeHistory)) {
      this.modeHistory = [...config.modeHistory];
    }
  }
}

  /**
   * 获取工具的自定义验证器
   * @param {string} toolName - 工具名称
   * @returns {Function|undefined} 验证器函数或undefined
   */
  getCustomValidator(toolName) {
    return TOOL_MODE_CONFIG.customValidation[toolName];
  }

  /**
   * 验证权限规则
   * @param {string} rule - 权限规则字符串
   * @returns {Object} 验证结果
   */
  validatePermissionRule(rule) {
    if (!rule || rule.trim() === "") {
      return {
        valid: false,
        error: "Permission rule cannot be empty"
      };
    }

    // 检查括号匹配
    const openParens = (rule.match(/\(/g) || []).length;
    const closeParens = (rule.match(/\)/g) || []).length;

    if (openParens !== closeParens) {
      return {
        valid: false,
        error: "Mismatched parentheses",
        suggestion: "Ensure all opening parentheses have matching closing parentheses"
      };
    }

    // 检查空括号
    if (rule.includes("()")) {
      const toolName = rule.substring(0, rule.indexOf("("));
      if (!toolName) {
        return {
          valid: false,
          error: "Empty parentheses with no tool name",
          suggestion: "Specify a tool name before the parentheses"
        };
      }
      return {
        valid: false,
        error: "Empty parentheses",
        suggestion: `Either specify a pattern or use just "${toolName}" without parentheses`,
        examples: [toolName, `${toolName}(some-pattern)`]
      };
    }

    // 解析规则
    const parsedRule = this.parsePermissionRule(rule);
    const mcpInfo = this.parseMCPToolName(parsedRule.toolName);

    // 检查MCP工具规则
    if (mcpInfo) {
      if (parsedRule.ruleContent !== undefined) {
        return {
          valid: false,
          error: "MCP rules do not support patterns",
          suggestion: `Use "${parsedRule.toolName}" without parentheses`,
          examples: [
            `mcp__${mcpInfo.serverName}`,
            mcpInfo.toolName ? `mcp__${mcpInfo.serverName}__${mcpInfo.toolName}` : undefined
          ].filter(Boolean)
        };
      }
      return {
        valid: true
      };
    }

    // 检查工具名称
    if (!parsedRule.toolName || parsedRule.toolName.length === 0) {
      return {
        valid: false,
        error: "Tool name cannot be empty"
      };
    }

    // 检查工具名称首字母大写
    if (parsedRule.toolName[0] !== parsedRule.toolName[0]?.toUpperCase()) {
      return {
        valid: false,
        error: "Tool names must start with uppercase",
        suggestion: `Use "${String(parsedRule.toolName).charAt(0).toUpperCase() + String(parsedRule.toolName).slice(1)}"`
      };
    }

    // 自定义验证
    const customValidator = this.getCustomValidator(parsedRule.toolName);
    if (customValidator && parsedRule.ruleContent !== undefined) {
      const validationResult = customValidator(parsedRule.ruleContent);
      if (!validationResult.valid) {
        return validationResult;
      }
    }

    // Bash工具特殊验证
    if (this.isBashPrefixTool(parsedRule.toolName) && parsedRule.ruleContent !== undefined) {
      const pattern = parsedRule.ruleContent;
      if (pattern.includes(":*") && !pattern.endsWith(":*")) {
        return {
          valid: false,
          error: "The :* pattern must be at the end",
          suggestion: "Move :* to the end for prefix matching",
          examples: ["Bash(npm run:*)", "Bash(git commit:*)"]
        };
      }
    }

    return {
      valid: true
    };
  }

  /**
   * 解析权限规则
   * @param {string} rule - 规则字符串
   * @returns {Object} 解析后的规则对象
   * @private
   */
  parsePermissionRule(rule) {
    const match = rule.match(/^([^(]+)\(([^)]+)\)$/);

    if (!match) {
      return {
        toolName: rule
      };
    }

    const [, toolName, ruleContent] = match;

    if (!toolName || !ruleContent) {
      return {
        toolName: rule
      };
    }

    return {
      toolName,
      ruleContent
    };
  }

  /**
   * 解析MCP工具名称
   * @param {string} toolName - 工具名称
   * @returns {Object|null} 解析结果或null
   * @private
   */
  parseMCPToolName(toolName) {
    const parts = toolName.split("__");
    const [prefix, serverName, ...toolNameParts] = parts;

    if (prefix !== "mcp" || !serverName) {
      return null;
    }

    const actualToolName = toolNameParts.length > 0 ? toolNameParts.join("__") : undefined;

    return {
      serverName,
      toolName: actualToolName
    };
  }
}

// 导出便捷函数
export const getCustomValidator = (toolName) => TOOL_MODE_CONFIG.customValidation[toolName];
export const validatePermissionRule = (rule) => new ToolModeService().validatePermissionRule(rule);

// 创建默认工具模式服务实例
export const toolModeService = new ToolModeService();
