/**
 * MCP配置管理服务
 * @description 重构自原始文件中的MCP配置管理代码，对应第3200-3300行
 * @original 原始代码行 3200-3300
 */

import { join } from "path";
import { existsSync } from "fs";

/**
 * MCP配置作用域选项
 * @original Xr1.options
 */
const MCP_SCOPE_OPTIONS = ["local", "project", "user"];

/**
 * MCP传输类型选项
 * @original 传输类型验证逻辑
 */
const MCP_TRANSPORT_TYPES = ["stdio", "sse", "http"];

/**
 * 权限源列表（扩展版）
 * @original SIA变量
 */
const PERMISSION_SOURCES_EXTENDED = ["localSettings", "projectSettings", "cliArg", "command"];

/**
 * 获取MCP配置文件路径
 * @param {string} scope - 配置作用域
 * @returns {string} 配置文件路径
 * @original 第3200-3218行的switch语句
 */
export function getMCPConfigPath(scope) {
  switch (scope) {
    case "user":
      {
        const userConfigPath = getUserConfigPath();
        const exists = existsSync(userConfigPath);
        return `${userConfigPath}${exists ? "" : " (file does not exist)"}`;
      }
    case "project":
      {
        const projectConfigPath = join(getCurrentWorkingDirectory(), ".mcp.json");
        const exists = existsSync(projectConfigPath);
        return `${projectConfigPath}${exists ? "" : " (file does not exist)"}`;
      }
    case "local":
      return `${getUserConfigPath()} [project: ${getCurrentWorkingDirectory()}]`;
    case "dynamic":
      return "Dynamically configured";
    default:
      return scope;
  }
}

/**
 * 获取MCP配置作用域描述
 * @param {string} scope - 配置作用域
 * @returns {string} 作用域描述
 * @original Yh函数
 */
export function getMCPScopeDescription(scope) {
  switch (scope) {
    case "local":
      return "Local config (private to you in this project)";
    case "project":
      return "Project config (shared via .mcp.json)";
    case "user":
      return "User config (available in all your projects)";
    default:
      return scope;
  }
}

/**
 * 验证MCP配置作用域
 * @param {string} scope - 配置作用域
 * @returns {string} 验证后的作用域
 * @throws {Error} 无效作用域错误
 * @original M91函数
 */
export function validateMCPScope(scope) {
  if (!scope) return "local";
  
  if (!MCP_SCOPE_OPTIONS.includes(scope)) {
    throw new Error(`Invalid scope: ${scope}. Must be one of: ${MCP_SCOPE_OPTIONS.join(", ")}`);
  }
  
  return scope;
}

/**
 * 验证MCP传输类型
 * @param {string} transportType - 传输类型
 * @returns {string} 验证后的传输类型
 * @throws {Error} 无效传输类型错误
 * @original PIA函数
 */
export function validateMCPTransportType(transportType) {
  if (!transportType) return "stdio";
  
  if (!MCP_TRANSPORT_TYPES.includes(transportType)) {
    throw new Error(`Invalid transport type: ${transportType}. Must be one of: ${MCP_TRANSPORT_TYPES.join(", ")}`);
  }
  
  return transportType;
}

/**
 * 解析HTTP头部
 * @param {Array} headers - 头部字符串数组
 * @returns {Object} 解析后的头部对象
 * @throws {Error} 头部格式错误
 * @original Hr1函数
 */
export function parseHTTPHeaders(headers) {
  const result = {};
  
  for (const header of headers) {
    const colonIndex = header.indexOf(":");
    if (colonIndex === -1) {
      throw new Error(`Invalid header format: "${header}". Expected format: "Header-Name: value"`);
    }
    
    const name = header.substring(0, colonIndex).trim();
    const value = header.substring(colonIndex + 1).trim();
    
    if (!name) {
      throw new Error(`Invalid header: "${header}". Header name cannot be empty.`);
    }
    
    result[name] = value;
  }
  
  return result;
}

/**
 * 获取MCP服务器状态
 * @param {string} serverName - 服务器名称
 * @returns {string} 服务器状态 (approved/rejected/pending)
 * @original EK1函数
 */
export function getMCPServerStatus(serverName) {
  const config = getGlobalConfig();
  
  if (config?.disabledMcpjsonServers?.includes(serverName)) {
    return "rejected";
  }
  
  if (config?.enabledMcpjsonServers?.includes(serverName) || config?.enableAllProjectMcpServers) {
    return "approved";
  }
  
  return "pending";
}

/**
 * 解析权限规则
 * @param {string} rule - 规则字符串
 * @returns {Object} 解析后的规则对象
 * @original Az函数
 */
export function parsePermissionRule(rule) {
  const match = rule.match(/^([^(]+)\(([^)]+)\)$/);
  
  if (!match) {
    return {
      toolName: rule
    };
  }
  
  const [, toolName, ruleContent] = match;
  
  if (!toolName || !ruleContent) {
    return {
      toolName: rule
    };
  }
  
  return {
    toolName,
    ruleContent
  };
}

/**
 * 格式化权限规则
 * @param {Object} rule - 规则对象
 * @returns {string} 格式化后的规则字符串
 * @original d5函数
 */
export function formatPermissionRule(rule) {
  return rule.ruleContent ? `${rule.toolName}(${rule.ruleContent})` : rule.toolName;
}

/**
 * 获取总是允许的规则
 * @param {Object} config - 配置对象
 * @returns {Array} 总是允许的规则数组
 * @original Wh函数
 */
export function getAlwaysAllowRules(config) {
  return PERMISSION_SOURCES_EXTENDED.flatMap(source => 
    (config.alwaysAllowRules[source] || []).map(rule => ({
      source,
      ruleBehavior: "allow",
      ruleValue: parsePermissionRule(rule)
    }))
  );
}

/**
 * 获取工具的允许规则内容
 * @param {Object} config - 配置对象
 * @param {string} toolName - 工具名称
 * @returns {Array} 允许规则内容数组
 * @original H5Q函数
 */
export function getToolAllowRules(config, toolName) {
  return getAlwaysAllowRules(config)
    .filter(rule => rule.ruleValue.toolName === toolName)
    .map(rule => {
      if (rule.ruleValue.ruleContent) {
        return rule.ruleValue.ruleContent;
      }
      return toolName;
    });
}

/**
 * 生成权限请求消息
 * @param {Object} config - 配置对象
 * @param {string} toolName - 工具名称
 * @returns {string} 权限请求消息
 * @original z5Q函数
 */
export function generatePermissionRequestMessage(config, toolName) {
  let message = `Claude requested permissions to use ${toolName}, but you haven't granted it yet.`;
  
  const allowedCommands = getToolAllowRules(config, toolName);
  if (allowedCommands.length > 0) {
    message += `\n\nAs a reminder, Claude can use these ${toolName} commands without approval: ${allowedCommands.join(", ")}`;
  }
  
  return message;
}

/**
 * MCP配置管理服务类
 * @description 提供完整的MCP配置管理功能
 */
export class MCPConfigService {
  constructor() {
    this.scopeOptions = MCP_SCOPE_OPTIONS;
    this.transportTypes = MCP_TRANSPORT_TYPES;
    this.permissionSources = PERMISSION_SOURCES_EXTENDED;
  }

  /**
   * 获取配置文件路径
   * @param {string} scope - 配置作用域
   * @returns {string} 配置文件路径
   */
  getConfigPath(scope) {
    return getMCPConfigPath(scope);
  }

  /**
   * 获取作用域描述
   * @param {string} scope - 配置作用域
   * @returns {string} 作用域描述
   */
  getScopeDescription(scope) {
    return getMCPScopeDescription(scope);
  }

  /**
   * 验证配置
   * @param {Object} config - 配置对象
   * @returns {Object} 验证结果
   */
  validateConfig(config) {
    const result = {
      valid: true,
      errors: [],
      warnings: []
    };

    if (!config || typeof config !== 'object') {
      result.valid = false;
      result.errors.push('Configuration must be an object');
      return result;
    }

    // 验证服务器配置
    if (config.servers) {
      if (typeof config.servers !== 'object') {
        result.valid = false;
        result.errors.push('servers must be an object');
      } else {
        for (const [serverName, serverConfig] of Object.entries(config.servers)) {
          const serverValidation = this.validateServerConfig(serverName, serverConfig);
          if (!serverValidation.valid) {
            result.valid = false;
            result.errors.push(...serverValidation.errors.map(err => `Server ${serverName}: ${err}`));
          }
          result.warnings.push(...serverValidation.warnings.map(warn => `Server ${serverName}: ${warn}`));
        }
      }
    }

    // 验证权限规则
    if (config.alwaysAllowRules) {
      if (typeof config.alwaysAllowRules !== 'object') {
        result.valid = false;
        result.errors.push('alwaysAllowRules must be an object');
      }
    }

    return result;
  }

  /**
   * 验证服务器配置
   * @param {string} serverName - 服务器名称
   * @param {Object} serverConfig - 服务器配置
   * @returns {Object} 验证结果
   */
  validateServerConfig(serverName, serverConfig) {
    const result = {
      valid: true,
      errors: [],
      warnings: []
    };

    if (!serverConfig || typeof serverConfig !== 'object') {
      result.valid = false;
      result.errors.push('Server configuration must be an object');
      return result;
    }

    // 验证命令
    if (!serverConfig.command) {
      result.valid = false;
      result.errors.push('command is required');
    } else if (typeof serverConfig.command !== 'string') {
      result.valid = false;
      result.errors.push('command must be a string');
    }

    // 验证参数
    if (serverConfig.args && !Array.isArray(serverConfig.args)) {
      result.valid = false;
      result.errors.push('args must be an array');
    }

    // 验证环境变量
    if (serverConfig.env && typeof serverConfig.env !== 'object') {
      result.valid = false;
      result.errors.push('env must be an object');
    }

    // 验证传输类型
    if (serverConfig.transport) {
      try {
        validateMCPTransportType(serverConfig.transport);
      } catch (error) {
        result.valid = false;
        result.errors.push(error.message);
      }
    }

    return result;
  }

  /**
   * 创建默认配置
   * @param {string} scope - 配置作用域
   * @returns {Object} 默认配置
   */
  createDefaultConfig(scope = "local") {
    return {
      mcpServers: {},
      alwaysAllowRules: {},
      scope: validateMCPScope(scope),
      version: "1.0.0",
      created: new Date().toISOString()
    };
  }

  /**
   * 合并配置
   * @param {Object} baseConfig - 基础配置
   * @param {Object} overrideConfig - 覆盖配置
   * @returns {Object} 合并后的配置
   */
  mergeConfigs(baseConfig, overrideConfig) {
    return {
      ...baseConfig,
      ...overrideConfig,
      mcpServers: {
        ...baseConfig.mcpServers,
        ...overrideConfig.mcpServers
      },
      alwaysAllowRules: {
        ...baseConfig.alwaysAllowRules,
        ...overrideConfig.alwaysAllowRules
      }
    };
  }

  /**
   * 获取服务器列表
   * @param {Object} config - 配置对象
   * @returns {Array} 服务器列表
   */
  getServerList(config) {
    if (!config || !config.mcpServers) {
      return [];
    }

    return Object.entries(config.mcpServers).map(([name, serverConfig]) => ({
      name,
      config: serverConfig,
      status: getMCPServerStatus(name)
    }));
  }

  /**
   * 添加服务器
   * @param {Object} config - 配置对象
   * @param {string} serverName - 服务器名称
   * @param {Object} serverConfig - 服务器配置
   * @returns {Object} 更新后的配置
   */
  addServer(config, serverName, serverConfig) {
    const validation = this.validateServerConfig(serverName, serverConfig);
    if (!validation.valid) {
      throw new Error(`Invalid server configuration: ${validation.errors.join(', ')}`);
    }

    return {
      ...config,
      mcpServers: {
        ...config.mcpServers,
        [serverName]: serverConfig
      }
    };
  }

  /**
   * 移除服务器
   * @param {Object} config - 配置对象
   * @param {string} serverName - 服务器名称
   * @returns {Object} 更新后的配置
   */
  removeServer(config, serverName) {
    const newConfig = { ...config };
    const newServers = { ...config.mcpServers };
    delete newServers[serverName];
    newConfig.mcpServers = newServers;
    return newConfig;
  }

  /**
   * 获取支持的作用域选项
   * @returns {Array} 作用域选项数组
   */
  getScopeOptions() {
    return [...this.scopeOptions];
  }

  /**
   * 获取支持的传输类型
   * @returns {Array} 传输类型数组
   */
  getTransportTypes() {
    return [...this.transportTypes];
  }

  /**
   * 获取权限源列表
   * @returns {Array} 权限源数组
   */
  getPermissionSources() {
    return [...this.permissionSources];
  }
}

// 辅助函数

/**
 * 获取用户配置路径
 * @returns {string} 用户配置路径
 * @original NY函数的实现（推测）
 */
function getUserConfigPath() {
  // @todo: 实现NY函数的实际逻辑
  return join(process.env.HOME || process.env.USERPROFILE || '', '.mcp.json');
}

/**
 * 获取当前工作目录
 * @returns {string} 当前工作目录
 * @original a0函数的实现（推测）
 */
function getCurrentWorkingDirectory() {
  // @todo: 实现a0函数的实际逻辑
  return process.cwd();
}

/**
 * 获取全局配置
 * @returns {Object} 全局配置
 * @original C9函数的实现（推测）
 */
function getGlobalConfig() {
  // @todo: 实现C9函数的实际逻辑
  return {
    disabledMcpjsonServers: [],
    enabledMcpjsonServers: [],
    enableAllProjectMcpServers: false
  };
}

// 创建默认MCP配置服务实例
export const mcpConfigService = new MCPConfigService();
