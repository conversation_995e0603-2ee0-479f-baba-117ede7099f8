/**
 * 错误追踪服务
 * @description 重构自原始文件中的错误追踪代码，对应第5808-5822行
 * @original 原始代码行 5808-5822
 */

/**
 * 初始化错误追踪
 * @original D$2函数
 */
export function initializeErrorTracking() {
  const sentry = getSentryModule();
  
  sentry.init({
    dsn: getErrorTrackingDSN(),
    environment: "external",
    release: getVersionInfo().VERSION,
    defaultIntegrations: false,
    tracesSampleRate: 1.0,
    tracePropagationTargets: ["localhost"]
  });
}

/**
 * 错误追踪服务类
 * @description 提供完整的错误追踪和监控功能
 */
export class ErrorTrackingService {
  constructor() {
    this.isInitialized = false;
    this.errorQueue = [];
    this.maxQueueSize = 100;
    this.flushInterval = 30000; // 30秒
    this.flushTimer = null;
  }

  /**
   * 初始化错误追踪服务
   * @param {Object} config - 配置选项
   */
  initialize(config = {}) {
    if (this.isInitialized) {
      return;
    }

    const {
      dsn = getErrorTrackingDSN(),
      environment = "external",
      release = getVersionInfo().VERSION,
      enableIntegrations = false,
      sampleRate = 1.0,
      enableLocalhost = true
    } = config;

    try {
      const sentry = getSentryModule();
      
      sentry.init({
        dsn,
        environment,
        release,
        defaultIntegrations: enableIntegrations,
        tracesSampleRate: sampleRate,
        tracePropagationTargets: enableLocalhost ? ["localhost"] : [],
        beforeSend: (event) => this.beforeSend(event),
        beforeSendTransaction: (transaction) => this.beforeSendTransaction(transaction)
      });

      this.isInitialized = true;
      this.startFlushTimer();
      
      console.log('Error tracking initialized successfully');
    } catch (error) {
      console.error('Failed to initialize error tracking:', error);
    }
  }

  /**
   * 捕获错误
   * @param {Error} error - 错误对象
   * @param {Object} context - 上下文信息
   */
  captureError(error, context = {}) {
    if (!this.isInitialized) {
      this.queueError(error, context);
      return;
    }

    try {
      const sentry = getSentryModule();
      
      sentry.withScope((scope) => {
        // 设置上下文信息
        if (context.user) {
          scope.setUser(context.user);
        }
        
        if (context.tags) {
          Object.entries(context.tags).forEach(([key, value]) => {
            scope.setTag(key, value);
          });
        }
        
        if (context.extra) {
          Object.entries(context.extra).forEach(([key, value]) => {
            scope.setExtra(key, value);
          });
        }
        
        if (context.level) {
          scope.setLevel(context.level);
        }
        
        // 设置指纹
        if (context.fingerprint) {
          scope.setFingerprint(context.fingerprint);
        }
        
        sentry.captureException(error);
      });
    } catch (captureError) {
      console.error('Failed to capture error:', captureError);
      this.queueError(error, context);
    }
  }

  /**
   * 捕获消息
   * @param {string} message - 消息内容
   * @param {string} level - 日志级别
   * @param {Object} context - 上下文信息
   */
  captureMessage(message, level = 'info', context = {}) {
    if (!this.isInitialized) {
      this.queueMessage(message, level, context);
      return;
    }

    try {
      const sentry = getSentryModule();
      
      sentry.withScope((scope) => {
        if (context.tags) {
          Object.entries(context.tags).forEach(([key, value]) => {
            scope.setTag(key, value);
          });
        }
        
        if (context.extra) {
          Object.entries(context.extra).forEach(([key, value]) => {
            scope.setExtra(key, value);
          });
        }
        
        scope.setLevel(level);
        sentry.captureMessage(message);
      });
    } catch (error) {
      console.error('Failed to capture message:', error);
      this.queueMessage(message, level, context);
    }
  }

  /**
   * 开始事务追踪
   * @param {string} name - 事务名称
   * @param {string} op - 操作类型
   * @param {Object} context - 上下文信息
   * @returns {Object} 事务对象
   */
  startTransaction(name, op = 'task', context = {}) {
    if (!this.isInitialized) {
      return this.createMockTransaction(name);
    }

    try {
      const sentry = getSentryModule();
      
      const transaction = sentry.startTransaction({
        name,
        op,
        ...context
      });
      
      return transaction;
    } catch (error) {
      console.error('Failed to start transaction:', error);
      return this.createMockTransaction(name);
    }
  }

  /**
   * 设置用户信息
   * @param {Object} user - 用户信息
   */
  setUser(user) {
    if (!this.isInitialized) {
      return;
    }

    try {
      const sentry = getSentryModule();
      sentry.setUser(user);
    } catch (error) {
      console.error('Failed to set user:', error);
    }
  }

  /**
   * 设置标签
   * @param {string} key - 标签键
   * @param {string} value - 标签值
   */
  setTag(key, value) {
    if (!this.isInitialized) {
      return;
    }

    try {
      const sentry = getSentryModule();
      sentry.setTag(key, value);
    } catch (error) {
      console.error('Failed to set tag:', error);
    }
  }

  /**
   * 设置额外信息
   * @param {string} key - 键
   * @param {*} value - 值
   */
  setExtra(key, value) {
    if (!this.isInitialized) {
      return;
    }

    try {
      const sentry = getSentryModule();
      sentry.setExtra(key, value);
    } catch (error) {
      console.error('Failed to set extra:', error);
    }
  }

  /**
   * 手动刷新错误队列
   */
  flush() {
    if (!this.isInitialized || this.errorQueue.length === 0) {
      return;
    }

    const queuedItems = [...this.errorQueue];
    this.errorQueue = [];

    for (const item of queuedItems) {
      if (item.type === 'error') {
        this.captureError(item.error, item.context);
      } else if (item.type === 'message') {
        this.captureMessage(item.message, item.level, item.context);
      }
    }
  }

  /**
   * 关闭错误追踪服务
   * @returns {Promise<void>}
   */
  async close() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }

    if (!this.isInitialized) {
      return;
    }

    try {
      const sentry = getSentryModule();
      await sentry.close(2000); // 2秒超时
    } catch (error) {
      console.error('Failed to close error tracking:', error);
    }
  }

  /**
   * 获取服务状态
   * @returns {Object} 服务状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      queuedErrors: this.errorQueue.length,
      maxQueueSize: this.maxQueueSize,
      flushInterval: this.flushInterval,
      hasFlushTimer: !!this.flushTimer
    };
  }

  /**
   * 错误发送前的处理
   * @param {Object} event - 错误事件
   * @returns {Object|null} 处理后的事件或null（丢弃）
   * @private
   */
  beforeSend(event) {
    // 过滤敏感信息
    if (event.exception) {
      event.exception.values?.forEach(exception => {
        if (exception.stacktrace?.frames) {
          exception.stacktrace.frames.forEach(frame => {
            // 移除敏感路径信息
            if (frame.filename) {
              frame.filename = frame.filename.replace(/\/Users\/<USER>\/]+/, '/Users/<USER>');
            }
          });
        }
      });
    }

    return event;
  }

  /**
   * 事务发送前的处理
   * @param {Object} transaction - 事务对象
   * @returns {Object|null} 处理后的事务或null（丢弃）
   * @private
   */
  beforeSendTransaction(transaction) {
    // 过滤低价值的事务
    if (transaction.transaction === 'heartbeat' || 
        transaction.transaction === 'ping') {
      return null;
    }

    return transaction;
  }

  /**
   * 将错误加入队列
   * @param {Error} error - 错误对象
   * @param {Object} context - 上下文信息
   * @private
   */
  queueError(error, context) {
    if (this.errorQueue.length >= this.maxQueueSize) {
      this.errorQueue.shift(); // 移除最旧的错误
    }

    this.errorQueue.push({
      type: 'error',
      error,
      context,
      timestamp: Date.now()
    });
  }

  /**
   * 将消息加入队列
   * @param {string} message - 消息内容
   * @param {string} level - 日志级别
   * @param {Object} context - 上下文信息
   * @private
   */
  queueMessage(message, level, context) {
    if (this.errorQueue.length >= this.maxQueueSize) {
      this.errorQueue.shift(); // 移除最旧的消息
    }

    this.errorQueue.push({
      type: 'message',
      message,
      level,
      context,
      timestamp: Date.now()
    });
  }

  /**
   * 创建模拟事务对象
   * @param {string} name - 事务名称
   * @returns {Object} 模拟事务对象
   * @private
   */
  createMockTransaction(name) {
    return {
      name,
      finish: () => {},
      setTag: () => {},
      setData: () => {},
      setStatus: () => {}
    };
  }

  /**
   * 启动刷新定时器
   * @private
   */
  startFlushTimer() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }
}

// 辅助函数

/**
 * 获取Sentry模块
 * @returns {Object} Sentry模块
 * @original uL对象的实现（推测）
 */
function getSentryModule() {
  // @todo: 实现uL对象的实际逻辑
  try {
    return require('@sentry/node');
  } catch {
    // 如果Sentry不可用，返回模拟对象
    return {
      init: () => {},
      captureException: () => {},
      captureMessage: () => {},
      startTransaction: () => ({ finish: () => {} }),
      withScope: (callback) => callback({ setUser: () => {}, setTag: () => {}, setExtra: () => {}, setLevel: () => {}, setFingerprint: () => {} }),
      setUser: () => {},
      setTag: () => {},
      setExtra: () => {},
      close: () => Promise.resolve()
    };
  }
}

/**
 * 获取错误追踪DSN
 * @returns {string} DSN字符串
 * @original rl0变量的实现（推测）
 */
function getErrorTrackingDSN() {
  // @todo: 实现rl0变量的实际值
  return process.env.SENTRY_DSN || '';
}

/**
 * 获取版本信息
 * @returns {Object} 版本信息
 * @original 版本信息对象的实现（推测）
 */
function getVersionInfo() {
  return {
    ISSUES_EXPLAINER: "report the issue at https://github.com/anthropics/claude-code/issues",
    PACKAGE_URL: "@anthropic-ai/claude-code",
    README_URL: "https://docs.anthropic.com/s/claude-code",
    VERSION: "1.0.72"
  };
}

// 创建默认错误追踪服务实例
export const errorTrackingService = new ErrorTrackingService();

// 导出便捷函数
export const ErrorTracking = {
  initialize: (config) => errorTrackingService.initialize(config),
  captureError: (error, context) => errorTrackingService.captureError(error, context),
  captureMessage: (message, level, context) => errorTrackingService.captureMessage(message, level, context),
  startTransaction: (name, op, context) => errorTrackingService.startTransaction(name, op, context),
  setUser: (user) => errorTrackingService.setUser(user),
  setTag: (key, value) => errorTrackingService.setTag(key, value),
  setExtra: (key, value) => errorTrackingService.setExtra(key, value),
  flush: () => errorTrackingService.flush(),
  close: () => errorTrackingService.close(),
  getStatus: () => errorTrackingService.getStatus()
};
