/**
 * React DevTools配置模块
 * @description 配置React开发工具和全局环境，对应原始文件第278-324行
 * @original 原始代码行 278-324
 */

import { createLazyLoader } from './module-imports.js';

/**
 * 全局环境引用
 * @description 对应原始文件第282-286行
 * @original var eR1; var JL2 = gA1(() => { tR1(); eR1 = global; eR1.WebSocket ||= S_; eR1.window ||= global; eR1.self ||= global; ... });
 */
let globalEnvironment;

/**
 * React DevTools组件过滤器配置
 * @description 定义哪些组件应该在React DevTools中被过滤掉
 * @original 第289-323行的__REACT_DEVTOOLS_COMPONENT_FILTERS__配置
 */
const REACT_DEVTOOLS_COMPONENT_FILTERS = [
  {
    type: 1,           // 过滤器类型：按元素类型过滤
    value: 7,          // 过滤值：7代表某种特定的元素类型
    isEnabled: true    // 启用此过滤器
  },
  {
    type: 2,           // 过滤器类型：按组件名称过滤
    value: "InternalApp",
    isEnabled: true,
    isValid: true
  },
  {
    type: 2,
    value: "InternalAppContext",
    isEnabled: true,
    isValid: true
  },
  {
    type: 2,
    value: "InternalStdoutContext",
    isEnabled: true,
    isValid: true
  },
  {
    type: 2,
    value: "InternalStderrContext", 
    isEnabled: true,
    isValid: true
  },
  {
    type: 2,
    value: "InternalStdinContext",
    isEnabled: true,
    isValid: true
  },
  {
    type: 2,
    value: "InternalFocusContext",
    isEnabled: true,
    isValid: true
  }
];

/**
 * 初始化全局环境和React DevTools
 * @description 设置全局对象和React DevTools配置
 * @original var JL2 = gA1(() => { ... });
 */
export const initializeGlobalEnvironment = createLazyLoader(() => {
  // 首先初始化模块依赖
  // @todo: 实现tR1()的逻辑
  console.log("Initializing global environment...");
  
  // 设置全局环境引用
  globalEnvironment = global;
  
  // 设置WebSocket全局引用（如果不存在）
  // @todo: 实现S_的逻辑（可能是WebSocket的polyfill或实现）
  if (!globalEnvironment.WebSocket) {
    // globalEnvironment.WebSocket = S_;
    console.log("WebSocket not available, would set polyfill");
  }
  
  // 设置window全局引用（Node.js环境中模拟浏览器环境）
  if (!globalEnvironment.window) {
    globalEnvironment.window = global;
  }
  
  // 设置self全局引用（Web Worker环境兼容）
  if (!globalEnvironment.self) {
    globalEnvironment.self = global;
  }
  
  // 配置React DevTools组件过滤器
  globalEnvironment.window.__REACT_DEVTOOLS_COMPONENT_FILTERS__ = REACT_DEVTOOLS_COMPONENT_FILTERS;
  
  console.log("Global environment initialized with React DevTools filters");
  
  return {
    globalEnvironment,
    filtersConfigured: true
  };
});

/**
 * React DevTools连接初始化
 * @description 连接到React DevTools，对应原始文件第330-334行
 * @original var VL2; var CL2 = gA1(() => { JL2(); VL2 = F1(XL2(), 1); VL2.default.connectToDevTools(); });
 */
let reactDevToolsConnection;

export const initializeReactDevTools = createLazyLoader(() => {
  // 首先确保全局环境已初始化
  initializeGlobalEnvironment();
  
  // @todo: 实现F1和XL2的逻辑
  console.log("Initializing React DevTools connection...");
  
  // 模拟React DevTools连接
  reactDevToolsConnection = {
    default: {
      connectToDevTools: () => {
        console.log("Connected to React DevTools");
        return true;
      }
    }
  };
  
  // 连接到React DevTools
  try {
    reactDevToolsConnection.default.connectToDevTools();
    console.log("React DevTools connection established");
  } catch (error) {
    console.error("Failed to connect to React DevTools:", error);
  }
  
  return {
    connection: reactDevToolsConnection,
    connected: true
  };
});

/**
 * 获取React DevTools过滤器配置
 * @returns {Array} 过滤器配置数组
 */
export function getReactDevToolsFilters() {
  return [...REACT_DEVTOOLS_COMPONENT_FILTERS];
}

/**
 * 添加React DevTools过滤器
 * @param {Object} filter - 过滤器配置对象
 */
export function addReactDevToolsFilter(filter) {
  if (!filter || typeof filter !== 'object') {
    throw new Error('Filter must be an object');
  }
  
  if (!filter.type || !filter.value) {
    throw new Error('Filter must have type and value properties');
  }
  
  REACT_DEVTOOLS_COMPONENT_FILTERS.push({
    type: filter.type,
    value: filter.value,
    isEnabled: filter.isEnabled !== false,
    isValid: filter.isValid !== false
  });
  
  // 更新全局配置
  if (globalEnvironment?.window) {
    globalEnvironment.window.__REACT_DEVTOOLS_COMPONENT_FILTERS__ = REACT_DEVTOOLS_COMPONENT_FILTERS;
  }
}

/**
 * 移除React DevTools过滤器
 * @param {string} componentName - 要移除过滤的组件名称
 */
export function removeReactDevToolsFilter(componentName) {
  const index = REACT_DEVTOOLS_COMPONENT_FILTERS.findIndex(
    filter => filter.value === componentName
  );
  
  if (index !== -1) {
    REACT_DEVTOOLS_COMPONENT_FILTERS.splice(index, 1);
    
    // 更新全局配置
    if (globalEnvironment?.window) {
      globalEnvironment.window.__REACT_DEVTOOLS_COMPONENT_FILTERS__ = REACT_DEVTOOLS_COMPONENT_FILTERS;
    }
  }
}

/**
 * 检查React DevTools是否已连接
 * @returns {boolean} 是否已连接
 */
export function isReactDevToolsConnected() {
  return reactDevToolsConnection !== undefined;
}

/**
 * 获取全局环境对象
 * @returns {Object} 全局环境对象
 */
export function getGlobalEnvironment() {
  if (!globalEnvironment) {
    initializeGlobalEnvironment();
  }
  return globalEnvironment;
}

/**
 * 设置全局变量
 * @param {string} name - 变量名
 * @param {*} value - 变量值
 */
export function setGlobalVariable(name, value) {
  const env = getGlobalEnvironment();
  if (env.window) {
    env.window[name] = value;
  }
  env[name] = value;
}

/**
 * 获取全局变量
 * @param {string} name - 变量名
 * @returns {*} 变量值
 */
export function getGlobalVariable(name) {
  const env = getGlobalEnvironment();
  return env.window?.[name] || env[name];
}

/**
 * 检查是否在浏览器环境中运行
 * @returns {boolean} 是否在浏览器环境
 */
export function isBrowserEnvironment() {
  return typeof window !== 'undefined' && typeof document !== 'undefined';
}

/**
 * 检查是否在Node.js环境中运行
 * @returns {boolean} 是否在Node.js环境
 */
export function isNodeEnvironment() {
  return typeof process !== 'undefined' && process.versions && process.versions.node;
}

/**
 * 获取环境信息
 * @returns {Object} 环境信息
 */
export function getEnvironmentInfo() {
  return {
    isBrowser: isBrowserEnvironment(),
    isNode: isNodeEnvironment(),
    hasWebSocket: typeof WebSocket !== 'undefined',
    hasReactDevTools: isReactDevToolsConnected(),
    globalVariables: Object.keys(getGlobalEnvironment())
  };
}
