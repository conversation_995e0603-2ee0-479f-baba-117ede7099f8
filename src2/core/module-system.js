/**
 * 模块系统核心工具
 * @description 提供模块包装、导出定义等核心功能
 * @original 原始代码行 141-154
 */

import { createRequire } from "node:module";

// @original: var xcB = createRequire
export const moduleRequire = createRequire(import.meta.url);

/**
 * 创建模块包装器
 * @description 用于包装模块工厂函数，实现延迟加载和缓存
 * @param {Function} moduleFactory - 模块工厂函数
 * @param {Object} cachedExports - 缓存的导出对象
 * @returns {Function} 包装后的模块加载函数
 * @original var E = (A, B) => () => (B || A((B = { exports: {} }).exports, B), B.exports);
 */
export function createModuleWrapper(moduleFactory, cachedExports) {
  return () => {
    if (!cachedExports) {
      cachedExports = { exports: {} };
      moduleFactory(cachedExports.exports, cachedExports);
    }
    return cachedExports.exports;
  };
}

/**
 * 定义模块导出
 * @description 为模块对象定义可枚举的导出属性
 * @param {Object} target - 目标对象
 * @param {Object} exports - 导出定义对象
 * @original var Mj = (A, B) => { for (var Q in B) Ou1(A, Q, { get: B[Q], enumerable: !0, configurable: !0, set: D => B[Q] = () => D }); };
 */
export function defineModuleExports(target, exports) {
  for (const key in exports) {
    Object.defineProperty(target, key, {
      get: exports[key],
      enumerable: true,
      configurable: true,
      set: value => exports[key] = () => value
    });
  }
}

/**
 * 获取Object.defineProperty函数的引用
 * @description 用于模块导出定义中的属性设置
 * @original Ou1 引用
 */
const Ou1 = Object.defineProperty;
