/**
 * 终端颜色和样式工具
 * @description 重构自原始文件中的终端颜色处理代码，对应第2413-2450行
 * @original 原始代码行 2413-2450
 */

/**
 * ANSI颜色代码生成器
 * @param {number} offset - 偏移量，默认为0
 * @returns {Function} 颜色代码生成函数
 * @original var To0 = (A = 0) => B => `\x1B[${B + A}m`;
 */
export const createColorCode = (offset = 0) => (code) => `\x1B[${code + offset}m`;

/**
 * ANSI 256色代码生成器
 * @param {number} offset - 偏移量，默认为0
 * @returns {Function} 256色代码生成函数
 * @original var Po0 = (A = 0) => B => `\x1B[${38 + A};5;${B}m`;
 */
export const create256ColorCode = (offset = 0) => (colorIndex) => `\x1B[${38 + offset};5;${colorIndex}m`;

/**
 * ANSI RGB颜色代码生成器
 * @param {number} offset - 偏移量，默认为0
 * @returns {Function} RGB颜色代码生成函数
 * @original var So0 = (A = 0) => (B, Q, D) => `\x1B[${38 + A};2;${B};${Q};${D}m`;
 */
export const createRGBColorCode = (offset = 0) => (r, g, b) => `\x1B[${38 + offset};2;${r};${g};${b}m`;

/**
 * ANSI样式和颜色定义
 * @description 完整的ANSI转义序列定义
 * @original QD对象的定义
 */
export const ANSI_STYLES = {
  modifier: {
    reset: [0, 0],
    bold: [1, 22],
    dim: [2, 22],
    italic: [3, 23],
    underline: [4, 24],
    overline: [53, 55],
    inverse: [7, 27],
    hidden: [8, 28],
    strikethrough: [9, 29]
  },
  color: {
    black: [30, 39],
    red: [31, 39],
    green: [32, 39],
    yellow: [33, 39],
    blue: [34, 39],
    magenta: [35, 39],
    cyan: [36, 39],
    white: [37, 39],
    blackBright: [90, 39],
    gray: [90, 39],
    grey: [90, 39],
    redBright: [91, 39],
    greenBright: [92, 39],
    yellowBright: [93, 39],
    blueBright: [94, 39],
    magentaBright: [95, 39],
    cyanBright: [96, 39],
    whiteBright: [97, 39]
  },
  bgColor: {
    bgBlack: [40, 49],
    bgRed: [41, 49],
    bgGreen: [42, 49],
    bgYellow: [43, 49],
    bgBlue: [44, 49],
    bgMagenta: [45, 49],
    bgCyan: [46, 49],
    bgWhite: [47, 49],
    bgBlackBright: [100, 49],
    bgGray: [100, 49],
    bgGrey: [100, 49],
    bgRedBright: [101, 49],
    bgGreenBright: [102, 49],
    bgYellowBright: [103, 49],
    bgBlueBright: [104, 49],
    bgMagentaBright: [105, 49],
    bgCyanBright: [106, 49],
    bgWhiteBright: [107, 49]
  }
};

/**
 * 终端颜色工具类
 * @description 提供终端颜色和样式的便捷方法
 */
export class TerminalColors {
  constructor() {
    this.enabled = this.supportsColor();
  }

  /**
   * 检查终端是否支持颜色
   * @returns {boolean} 是否支持颜色
   */
  supportsColor() {
    // 检查环境变量
    if (process.env.FORCE_COLOR) {
      return process.env.FORCE_COLOR !== '0';
    }
    
    if (process.env.NO_COLOR || process.env.NODE_DISABLE_COLORS) {
      return false;
    }
    
    // 检查终端类型
    if (process.stdout && !process.stdout.isTTY) {
      return false;
    }
    
    // 检查CI环境
    if (process.env.CI) {
      return ['TRAVIS', 'CIRCLECI', 'APPVEYOR', 'GITLAB_CI', 'GITHUB_ACTIONS'].some(
        ci => process.env[ci]
      );
    }
    
    // 检查终端程序
    const term = process.env.TERM;
    if (term === 'dumb') {
      return false;
    }
    
    return true;
  }

  /**
   * 应用样式到文本
   * @param {string} text - 要应用样式的文本
   * @param {string} styleName - 样式名称
   * @param {string} category - 样式类别 (modifier, color, bgColor)
   * @returns {string} 应用样式后的文本
   */
  applyStyle(text, styleName, category = 'color') {
    if (!this.enabled) {
      return text;
    }
    
    const style = ANSI_STYLES[category]?.[styleName];
    if (!style) {
      return text;
    }
    
    const [open, close] = style;
    return `\x1B[${open}m${text}\x1B[${close}m`;
  }

  /**
   * 重置所有样式
   * @param {string} text - 文本
   * @returns {string} 重置样式后的文本
   */
  reset(text) {
    return this.applyStyle(text, 'reset', 'modifier');
  }

  /**
   * 粗体文本
   * @param {string} text - 文本
   * @returns {string} 粗体文本
   */
  bold(text) {
    return this.applyStyle(text, 'bold', 'modifier');
  }

  /**
   * 暗淡文本
   * @param {string} text - 文本
   * @returns {string} 暗淡文本
   */
  dim(text) {
    return this.applyStyle(text, 'dim', 'modifier');
  }

  /**
   * 斜体文本
   * @param {string} text - 文本
   * @returns {string} 斜体文本
   */
  italic(text) {
    return this.applyStyle(text, 'italic', 'modifier');
  }

  /**
   * 下划线文本
   * @param {string} text - 文本
   * @returns {string} 下划线文本
   */
  underline(text) {
    return this.applyStyle(text, 'underline', 'modifier');
  }

  /**
   * 删除线文本
   * @param {string} text - 文本
   * @returns {string} 删除线文本
   */
  strikethrough(text) {
    return this.applyStyle(text, 'strikethrough', 'modifier');
  }

  /**
   * 反色文本
   * @param {string} text - 文本
   * @returns {string} 反色文本
   */
  inverse(text) {
    return this.applyStyle(text, 'inverse', 'modifier');
  }

  // 颜色方法
  black(text) { return this.applyStyle(text, 'black', 'color'); }
  red(text) { return this.applyStyle(text, 'red', 'color'); }
  green(text) { return this.applyStyle(text, 'green', 'color'); }
  yellow(text) { return this.applyStyle(text, 'yellow', 'color'); }
  blue(text) { return this.applyStyle(text, 'blue', 'color'); }
  magenta(text) { return this.applyStyle(text, 'magenta', 'color'); }
  cyan(text) { return this.applyStyle(text, 'cyan', 'color'); }
  white(text) { return this.applyStyle(text, 'white', 'color'); }
  gray(text) { return this.applyStyle(text, 'gray', 'color'); }
  grey(text) { return this.applyStyle(text, 'grey', 'color'); }

  // 亮色方法
  blackBright(text) { return this.applyStyle(text, 'blackBright', 'color'); }
  redBright(text) { return this.applyStyle(text, 'redBright', 'color'); }
  greenBright(text) { return this.applyStyle(text, 'greenBright', 'color'); }
  yellowBright(text) { return this.applyStyle(text, 'yellowBright', 'color'); }
  blueBright(text) { return this.applyStyle(text, 'blueBright', 'color'); }
  magentaBright(text) { return this.applyStyle(text, 'magentaBright', 'color'); }
  cyanBright(text) { return this.applyStyle(text, 'cyanBright', 'color'); }
  whiteBright(text) { return this.applyStyle(text, 'whiteBright', 'color'); }

  // 背景色方法
  bgBlack(text) { return this.applyStyle(text, 'bgBlack', 'bgColor'); }
  bgRed(text) { return this.applyStyle(text, 'bgRed', 'bgColor'); }
  bgGreen(text) { return this.applyStyle(text, 'bgGreen', 'bgColor'); }
  bgYellow(text) { return this.applyStyle(text, 'bgYellow', 'bgColor'); }
  bgBlue(text) { return this.applyStyle(text, 'bgBlue', 'bgColor'); }
  bgMagenta(text) { return this.applyStyle(text, 'bgMagenta', 'bgColor'); }
  bgCyan(text) { return this.applyStyle(text, 'bgCyan', 'bgColor'); }
  bgWhite(text) { return this.applyStyle(text, 'bgWhite', 'bgColor'); }

  /**
   * 使用256色索引着色
   * @param {string} text - 文本
   * @param {number} colorIndex - 颜色索引 (0-255)
   * @returns {string} 着色后的文本
   */
  color256(text, colorIndex) {
    if (!this.enabled) {
      return text;
    }
    
    const colorCode = create256ColorCode(0)(colorIndex);
    const resetCode = '\x1B[39m';
    return `${colorCode}${text}${resetCode}`;
  }

  /**
   * 使用256色索引设置背景色
   * @param {string} text - 文本
   * @param {number} colorIndex - 颜色索引 (0-255)
   * @returns {string} 设置背景色后的文本
   */
  bgColor256(text, colorIndex) {
    if (!this.enabled) {
      return text;
    }
    
    const colorCode = create256ColorCode(10)(colorIndex);
    const resetCode = '\x1B[49m';
    return `${colorCode}${text}${resetCode}`;
  }

  /**
   * 使用RGB值着色
   * @param {string} text - 文本
   * @param {number} r - 红色值 (0-255)
   * @param {number} g - 绿色值 (0-255)
   * @param {number} b - 蓝色值 (0-255)
   * @returns {string} 着色后的文本
   */
  rgb(text, r, g, b) {
    if (!this.enabled) {
      return text;
    }
    
    const colorCode = createRGBColorCode(0)(r, g, b);
    const resetCode = '\x1B[39m';
    return `${colorCode}${text}${resetCode}`;
  }

  /**
   * 使用RGB值设置背景色
   * @param {string} text - 文本
   * @param {number} r - 红色值 (0-255)
   * @param {number} g - 绿色值 (0-255)
   * @param {number} b - 蓝色值 (0-255)
   * @returns {string} 设置背景色后的文本
   */
  bgRgb(text, r, g, b) {
    if (!this.enabled) {
      return text;
    }
    
    const colorCode = createRGBColorCode(10)(r, g, b);
    const resetCode = '\x1B[49m';
    return `${colorCode}${text}${resetCode}`;
  }

  /**
   * 使用十六进制颜色值着色
   * @param {string} text - 文本
   * @param {string} hex - 十六进制颜色值 (如 '#FF0000' 或 'FF0000')
   * @returns {string} 着色后的文本
   */
  hex(text, hex) {
    const cleanHex = hex.replace('#', '');
    const r = parseInt(cleanHex.substr(0, 2), 16);
    const g = parseInt(cleanHex.substr(2, 2), 16);
    const b = parseInt(cleanHex.substr(4, 2), 16);
    
    return this.rgb(text, r, g, b);
  }

  /**
   * 使用十六进制颜色值设置背景色
   * @param {string} text - 文本
   * @param {string} hex - 十六进制颜色值 (如 '#FF0000' 或 'FF0000')
   * @returns {string} 设置背景色后的文本
   */
  bgHex(text, hex) {
    const cleanHex = hex.replace('#', '');
    const r = parseInt(cleanHex.substr(0, 2), 16);
    const g = parseInt(cleanHex.substr(2, 2), 16);
    const b = parseInt(cleanHex.substr(4, 2), 16);
    
    return this.bgRgb(text, r, g, b);
  }

  /**
   * 移除文本中的ANSI转义序列
   * @param {string} text - 包含ANSI转义序列的文本
   * @returns {string} 移除转义序列后的纯文本
   */
  stripColors(text) {
    return text.replace(/\x1B\[[0-9;]*m/g, '');
  }

  /**
   * 启用颜色输出
   */
  enable() {
    this.enabled = true;
  }

  /**
   * 禁用颜色输出
   */
  disable() {
    this.enabled = false;
  }
}

// 创建默认实例
export const colors = new TerminalColors();

/**
 * 颜色转换工具函数
 * @description 提供各种颜色格式之间的转换
 * @original 第2489-2541行的颜色转换函数
 */
export const ColorConverter = {
  /**
   * RGB转ANSI 256色
   * @param {number} r - 红色值 (0-255)
   * @param {number} g - 绿色值 (0-255)
   * @param {number} b - 蓝色值 (0-255)
   * @returns {number} ANSI 256色索引
   * @original rgbToAnsi256函数
   */
  rgbToAnsi256(r, g, b) {
    // 灰度处理
    if (r === g && g === b) {
      if (r < 8) return 16;
      if (r > 248) return 231;
      return Math.round((r - 8) / 247 * 24) + 232;
    }

    // 彩色处理
    return 16 + 36 * Math.round(r / 255 * 5) + 6 * Math.round(g / 255 * 5) + Math.round(b / 255 * 5);
  },

  /**
   * 十六进制转RGB
   * @param {string|number} hex - 十六进制颜色值
   * @returns {Array} RGB数组 [r, g, b]
   * @original hexToRgb函数
   */
  hexToRgb(hex) {
    const hexRegex = /[a-f\d]{6}|[a-f\d]{3}/i;
    const match = hexRegex.exec(hex.toString(16));

    if (!match) return [0, 0, 0];

    let [hexValue] = match;

    // 处理3位十六进制
    if (hexValue.length === 3) {
      hexValue = [...hexValue].map(char => char + char).join("");
    }

    const intValue = Number.parseInt(hexValue, 16);
    return [
      (intValue >> 16) & 255,
      (intValue >> 8) & 255,
      intValue & 255
    ];
  },

  /**
   * 十六进制转ANSI 256色
   * @param {string|number} hex - 十六进制颜色值
   * @returns {number} ANSI 256色索引
   * @original hexToAnsi256函数
   */
  hexToAnsi256(hex) {
    return this.rgbToAnsi256(...this.hexToRgb(hex));
  },

  /**
   * ANSI 256色转ANSI 16色
   * @param {number} ansi256 - ANSI 256色索引
   * @returns {number} ANSI 16色代码
   * @original ansi256ToAnsi函数
   */
  ansi256ToAnsi(ansi256) {
    if (ansi256 < 8) return 30 + ansi256;
    if (ansi256 < 16) return 90 + (ansi256 - 8);

    let r, g, b;

    if (ansi256 >= 232) {
      // 灰度色
      r = ((ansi256 - 232) * 10 + 8) / 255;
      g = r;
      b = r;
    } else {
      // 彩色
      ansi256 -= 16;
      const remainder = ansi256 % 36;
      r = Math.floor(ansi256 / 36) / 5;
      g = Math.floor(remainder / 6) / 5;
      b = (remainder % 6) / 5;
    }

    const max = Math.max(r, g, b) * 2;

    if (max === 0) return 30;

    let ansi = 30 + (Math.round(b) << 2 | Math.round(g) << 1 | Math.round(r));

    if (max === 2) ansi += 60;

    return ansi;
  },

  /**
   * RGB转ANSI 16色
   * @param {number} r - 红色值 (0-255)
   * @param {number} g - 绿色值 (0-255)
   * @param {number} b - 蓝色值 (0-255)
   * @returns {number} ANSI 16色代码
   * @original rgbToAnsi函数
   */
  rgbToAnsi(r, g, b) {
    return this.ansi256ToAnsi(this.rgbToAnsi256(r, g, b));
  },

  /**
   * 十六进制转ANSI 16色
   * @param {string|number} hex - 十六进制颜色值
   * @returns {number} ANSI 16色代码
   * @original hexToAnsi函数
   */
  hexToAnsi(hex) {
    return this.ansi256ToAnsi(this.hexToAnsi256(hex));
  }
};

/**
 * 颜色样式键列表
 * @original 第2469-2472行的键列表
 */
export const STYLE_KEYS = {
  modifiers: Object.keys(ANSI_STYLES.modifier),
  colors: Object.keys(ANSI_STYLES.color),
  bgColors: Object.keys(ANSI_STYLES.bgColor),
  allColors: [...Object.keys(ANSI_STYLES.color), ...Object.keys(ANSI_STYLES.bgColor)]
};

/**
 * 初始化颜色样式对象
 * @returns {Object} 初始化后的样式对象
 * @original a_9函数的实现
 */
export function initializeColorStyles() {
  const styles = JSON.parse(JSON.stringify(ANSI_STYLES)); // 深拷贝
  const codeMap = new Map();

  // 为每个样式创建open和close属性
  for (const [categoryName, category] of Object.entries(styles)) {
    for (const [styleName, codes] of Object.entries(category)) {
      const styleObj = {
        open: `\x1B[${codes[0]}m`,
        close: `\x1B[${codes[1]}m`
      };

      styles[styleName] = styleObj;
      category[styleName] = styleObj;
      codeMap.set(codes[0], codes[1]);
    }

    // 设置类别为不可枚举
    Object.defineProperty(styles, categoryName, {
      value: category,
      enumerable: false
    });
  }

  // 添加codes映射
  Object.defineProperty(styles, "codes", {
    value: codeMap,
    enumerable: false
  });

  // 设置通用关闭代码
  styles.color.close = "\x1B[39m";
  styles.bgColor.close = "\x1B[49m";

  // 添加ANSI生成器
  styles.color.ansi = createColorCode();
  styles.color.ansi256 = create256ColorCode();
  styles.color.ansi16m = createRGBColorCode();
  styles.bgColor.ansi = createColorCode(10);
  styles.bgColor.ansi256 = create256ColorCode(10);
  styles.bgColor.ansi16m = createRGBColorCode(10);

  // 添加颜色转换方法
  Object.defineProperties(styles, {
    rgbToAnsi256: {
      value: ColorConverter.rgbToAnsi256,
      enumerable: false
    },
    hexToRgb: {
      value: ColorConverter.hexToRgb,
      enumerable: false
    },
    hexToAnsi256: {
      value: ColorConverter.hexToAnsi256,
      enumerable: false
    },
    ansi256ToAnsi: {
      value: ColorConverter.ansi256ToAnsi,
      enumerable: false
    },
    rgbToAnsi: {
      value: ColorConverter.rgbToAnsi,
      enumerable: false
    },
    hexToAnsi: {
      value: ColorConverter.hexToAnsi,
      enumerable: false
    }
  });

  return styles;
}

/**
 * 初始化的颜色样式对象
 * @original s_9变量
 */
export const colorStyles = initializeColorStyles();

/**
 * 检查命令行参数中的颜色设置
 * @param {string} flag - 要检查的标志
 * @returns {boolean} 是否存在该标志
 * @original iH函数的实现（推测）
 */
function hasFlag(flag) {
  return process.argv.includes(`--${flag}`) || process.argv.includes(`--${flag}=true`);
}

/**
 * 颜色支持级别
 * @description 根据环境和命令行参数确定颜色支持级别
 * @original 第2545行的颜色级别设置
 */
export let colorLevel = 1; // 默认支持基本颜色

// 检查命令行参数
if (hasFlag("no-color") || hasFlag("no-colors") || hasFlag("color=false") || hasFlag("color=never")) {
  colorLevel = 0;
} else if (hasFlag("color") || hasFlag("colors") || hasFlag("color=true") || hasFlag("color=always")) {
  colorLevel = 1;
}

/**
 * 设置颜色支持级别
 * @param {number} level - 颜色支持级别 (0=无颜色, 1=基本颜色, 2=256色, 3=真彩色)
 */
export function setColorLevel(level) {
  colorLevel = level;
  colors.enabled = level > 0;
}

/**
 * 获取颜色支持级别
 * @returns {number} 颜色支持级别
 */
export function getColorLevel() {
  return colorLevel;
}

// 导出便捷函数
export const {
  reset, bold, dim, italic, underline, strikethrough, inverse,
  black, red, green, yellow, blue, magenta, cyan, white, gray, grey,
  blackBright, redBright, greenBright, yellowBright, blueBright,
  magentaBright, cyanBright, whiteBright,
  bgBlack, bgRed, bgGreen, bgYellow, bgBlue, bgMagenta, bgCyan, bgWhite,
  color256, bgColor256, rgb, bgRgb, hex, bgHex, stripColors
} = colors;
