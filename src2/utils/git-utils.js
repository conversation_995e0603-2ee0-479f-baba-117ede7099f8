/**
 * Git工具函数
 * @description 重构自原始文件中的Git相关代码，对应第4672-4700行
 * @original 原始代码行 4672-4700
 */

import { join } from "path";
import { homedir } from "os";

/**
 * 检查当前目录是否在Git仓库内
 * @returns {Promise<boolean>} 是否在Git仓库内
 * @original xN函数
 */
export const isInsideGitRepository = createLazyLoader(async () => {
  const { code } = await executeCommand("git", ["rev-parse", "--is-inside-work-tree"]);
  return code === 0;
});

/**
 * 检查指定目录是否在Git仓库内
 * @param {string} directory - 目录路径
 * @returns {Promise<boolean>} 是否在Git仓库内
 * @original WYA函数
 */
export async function isDirectoryInsideGitRepository(directory) {
  const { code } = await executeCommandInDirectory("git", ["rev-parse", "--is-inside-work-tree"], {
    preserveOutputOnError: false,
    cwd: directory
  });
  return code === 0;
}

/**
 * 获取当前Git提交的SHA
 * @returns {Promise<string>} 当前提交的SHA
 * @original _r1函数
 */
export async function getCurrentCommitSHA() {
  const { stdout } = await executeCommand("git", ["rev-parse", "HEAD"]);
  return stdout.trim();
}

/**
 * 获取当前Git分支名称
 * @returns {Promise<string>} 当前分支名称
 * @original vK1函数
 */
export async function getCurrentBranchName() {
  const { stdout } = await executeCommand("git", ["rev-parse", "--abbrev-ref", "HEAD"], {
    preserveOutputOnError: false
  });
  return stdout.trim();
}

/**
 * 获取默认分支名称
 * @returns {Promise<string>} 默认分支名称
 * @original JYA函数
 */
export async function getDefaultBranchName() {
  // 首先尝试获取origin/HEAD指向的分支
  const { stdout: headOutput, code: headCode } = await executeCommand("git", ["symbolic-ref", "refs/remotes/origin/HEAD"], {
    preserveOutputOnError: false
  });

  if (headCode === 0) {
    const match = headOutput.trim().match(/refs\/remotes\/origin\/(.+)/);
    if (match && match[1]) {
      return match[1];
    }
  }

  // 如果失败，查找远程分支中的main或master
  const { stdout: branchOutput, code: branchCode } = await executeCommand("git", ["branch", "-r"], {
    preserveOutputOnError: false
  });

  if (branchCode === 0) {
    const branches = branchOutput.trim().split('\n').map(branch => branch.trim());

    // 优先查找main，然后是master
    for (const defaultName of ["main", "master"]) {
      if (branches.some(branch => branch.includes(`origin/${defaultName}`))) {
        return defaultName;
      }
    }
  }

  // 默认返回main
  return "main";
}

/**
 * 获取远程仓库URL
 * @returns {Promise<string|null>} 远程仓库URL或null
 * @original x91函数
 */
export async function getRemoteOriginUrl() {
  const { stdout, code } = await executeCommand("git", ["remote", "get-url", "origin"], {
    preserveOutputOnError: false
  });

  return code === 0 ? stdout.trim() : null;
}

/**
 * 检查当前分支是否有上游分支
 * @returns {Promise<boolean>} 是否有上游分支
 * @original m5Q函数
 */
export async function hasUpstreamBranch() {
  const { code } = await executeCommand("git", ["rev-parse", "@{u}"], {
    preserveOutputOnError: false
  });

  return code === 0;
}

/**
 * 检查工作目录是否干净
 * @returns {Promise<boolean>} 工作目录是否干净
 * @original bK1函数
 */
export async function isWorkingDirectoryClean() {
  const { stdout } = await executeCommand("git", ["status", "--porcelain"], {
    preserveOutputOnError: false
  });

  return stdout.trim().length === 0;
}

/**
 * 获取文件变更状态
 * @returns {Promise<Object>} 文件变更状态
 * @original xr1函数
 */
export async function getFileChanges() {
  const { stdout } = await executeCommand("git", ["status", "--porcelain"], {
    preserveOutputOnError: false
  });

  const tracked = [];
  const untracked = [];

  stdout.trim().split('\n')
    .filter(line => line.length > 0)
    .forEach(line => {
      const status = line.substring(0, 2);
      const file = line.substring(2).trim();

      if (status === "??") {
        untracked.push(file);
      } else if (file) {
        tracked.push(file);
      }
    });

  return {
    tracked,
    untracked
  };
}

/**
 * 获取工作树数量
 * @returns {Promise<number>} 工作树数量
 * @original v91函数
 */
export async function getWorktreeCount() {
  try {
    const { stdout, code } = await executeCommand("git", ["worktree", "list"], {
      preserveOutputOnError: false
    });

    if (code !== 0) {
      return 0;
    }

    return stdout.trim().split('\n').length;
  } catch {
    return 0;
  }
}

/**
 * 创建自动stash
 * @param {string} message - stash消息（可选）
 * @returns {Promise<boolean>} 是否创建成功
 * @original XYA函数
 */
export async function createAutoStash(message) {
  try {
    const stashMessage = message || `Claude Code auto-stash - ${new Date().toISOString()}`;
    const { untracked } = await getFileChanges();

    // 如果有未跟踪的文件，先添加它们
    if (untracked.length > 0) {
      const { code: addCode } = await executeCommand("git", ["add", ...untracked], {
        preserveOutputOnError: false
      });

      if (addCode !== 0) {
        return false;
      }
    }

    // 创建stash
    const { code: stashCode } = await executeCommand("git", ["stash", "push", "--message", stashMessage], {
      preserveOutputOnError: false
    });

    return stashCode === 0;
  } catch {
    return false;
  }
}

/**
 * Git工具类
 * @description 提供完整的Git操作功能
 */
export class GitUtils {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 30000; // 30秒缓存
  }

  /**
   * 检查是否在Git仓库内
   * @param {string} directory - 目录路径（可选）
   * @returns {Promise<boolean>} 是否在Git仓库内
   */
  async isGitRepository(directory) {
    if (directory) {
      return isDirectoryInsideGitRepository(directory);
    }
    return isInsideGitRepository();
  }

  /**
   * 获取Git仓库根目录
   * @param {string} directory - 目录路径（可选）
   * @returns {Promise<string>} Git仓库根目录
   */
  async getRepositoryRoot(directory) {
    const cacheKey = `repo-root:${directory || process.cwd()}`;
    const cached = this.getFromCache(cacheKey);
    
    if (cached) {
      return cached;
    }

    try {
      const { stdout } = await executeCommandInDirectory("git", ["rev-parse", "--show-toplevel"], {
        cwd: directory
      });
      const root = stdout.trim();
      this.setCache(cacheKey, root);
      return root;
    } catch (error) {
      throw new Error(`Not a git repository: ${directory || process.cwd()}`);
    }
  }

  /**
   * 获取当前提交信息
   * @returns {Promise<Object>} 提交信息
   */
  async getCurrentCommitInfo() {
    const cacheKey = 'current-commit-info';
    const cached = this.getFromCache(cacheKey);
    
    if (cached) {
      return cached;
    }

    try {
      const [sha, branch, message] = await Promise.all([
        getCurrentCommitSHA(),
        getCurrentBranchName(),
        this.getCommitMessage('HEAD')
      ]);

      const info = {
        sha,
        shortSha: sha.substring(0, 7),
        branch,
        message: message.split('\n')[0], // 只取第一行
        fullMessage: message
      };

      this.setCache(cacheKey, info);
      return info;
    } catch (error) {
      throw new Error(`Failed to get commit info: ${error.message}`);
    }
  }

  /**
   * 获取提交消息
   * @param {string} commit - 提交SHA或引用
   * @returns {Promise<string>} 提交消息
   */
  async getCommitMessage(commit = 'HEAD') {
    const { stdout } = await executeCommand("git", ["log", "-1", "--pretty=format:%B", commit]);
    return stdout.trim();
  }

  /**
   * 获取文件状态
   * @param {string} filePath - 文件路径（可选）
   * @returns {Promise<Array>} 文件状态数组
   */
  async getFileStatus(filePath) {
    const args = ["status", "--porcelain"];
    if (filePath) {
      args.push(filePath);
    }

    const { stdout } = await executeCommand("git", args);
    
    return stdout.split('\n')
      .filter(line => line.trim())
      .map(line => {
        const status = line.substring(0, 2);
        const file = line.substring(3);
        
        return {
          file,
          status,
          staged: status[0] !== ' ' && status[0] !== '?',
          modified: status[1] !== ' ',
          untracked: status === '??',
          deleted: status.includes('D'),
          renamed: status.includes('R'),
          copied: status.includes('C')
        };
      });
  }

  /**
   * 检查文件是否被Git跟踪
   * @param {string} filePath - 文件路径
   * @returns {Promise<boolean>} 是否被跟踪
   */
  async isFileTracked(filePath) {
    try {
      const { code } = await executeCommand("git", ["ls-files", "--error-unmatch", filePath], {
        preserveOutputOnError: false
      });
      return code === 0;
    } catch {
      return false;
    }
  }

  /**
   * 获取远程仓库信息
   * @returns {Promise<Array>} 远程仓库数组
   */
  async getRemotes() {
    const { stdout } = await executeCommand("git", ["remote", "-v"]);
    
    const remotes = new Map();
    
    stdout.split('\n')
      .filter(line => line.trim())
      .forEach(line => {
        const [name, url, type] = line.split(/\s+/);
        if (!remotes.has(name)) {
          remotes.set(name, { name, url, fetch: false, push: false });
        }
        
        const remote = remotes.get(name);
        if (type === '(fetch)') {
          remote.fetch = true;
        } else if (type === '(push)') {
          remote.push = true;
        }
      });
    
    return Array.from(remotes.values());
  }

  /**
   * 获取分支列表
   * @param {boolean} includeRemote - 是否包含远程分支
   * @returns {Promise<Array>} 分支数组
   */
  async getBranches(includeRemote = false) {
    const args = ["branch"];
    if (includeRemote) {
      args.push("-a");
    }

    const { stdout } = await executeCommand("git", args);
    
    return stdout.split('\n')
      .filter(line => line.trim())
      .map(line => {
        const current = line.startsWith('*');
        const name = line.replace(/^\*?\s+/, '').replace(/\s+.*$/, '');
        const isRemote = name.startsWith('remotes/');
        
        return {
          name: isRemote ? name.replace('remotes/', '') : name,
          current,
          remote: isRemote
        };
      });
  }

  /**
   * 获取提交历史
   * @param {Object} options - 选项
   * @returns {Promise<Array>} 提交历史数组
   */
  async getCommitHistory(options = {}) {
    const {
      limit = 10,
      since,
      until,
      author,
      grep,
      filePath
    } = options;

    const args = ["log", `--max-count=${limit}`, "--pretty=format:%H|%h|%an|%ae|%ad|%s", "--date=iso"];
    
    if (since) args.push(`--since=${since}`);
    if (until) args.push(`--until=${until}`);
    if (author) args.push(`--author=${author}`);
    if (grep) args.push(`--grep=${grep}`);
    if (filePath) args.push("--", filePath);

    const { stdout } = await executeCommand("git", args);
    
    return stdout.split('\n')
      .filter(line => line.trim())
      .map(line => {
        const [sha, shortSha, authorName, authorEmail, date, subject] = line.split('|');
        return {
          sha,
          shortSha,
          author: {
            name: authorName,
            email: authorEmail
          },
          date: new Date(date),
          subject
        };
      });
  }

  /**
   * 从缓存获取结果
   * @param {string} key - 缓存键
   * @returns {*} 缓存的结果或null
   * @private
   */
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (!cached) {
      return null;
    }
    
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.result;
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {*} result - 结果
   * @private
   */
  setCache(key, result) {
    this.cache.set(key, {
      result,
      timestamp: Date.now()
    });
  }

  /**
   * 清空缓存
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * 获取Git配置
   * @param {string} key - 配置键
   * @returns {Promise<string>} 配置值
   */
  async getConfig(key) {
    try {
      const { stdout } = await executeCommand("git", ["config", "--get", key]);
      return stdout.trim();
    } catch {
      return null;
    }
  }

  /**
   * 设置Git配置
   * @param {string} key - 配置键
   * @param {string} value - 配置值
   * @param {boolean} global - 是否为全局配置
   * @returns {Promise<void>}
   */
  async setConfig(key, value, global = false) {
    const args = ["config"];
    if (global) {
      args.push("--global");
    }
    args.push(key, value);

    await executeCommand("git", args);
  }

  /**
   * 获取默认分支名称
   * @returns {Promise<string>} 默认分支名称
   */
  async getDefaultBranch() {
    return getDefaultBranchName();
  }

  /**
   * 获取远程仓库URL
   * @param {string} remote - 远程名称（默认为origin）
   * @returns {Promise<string|null>} 远程仓库URL
   */
  async getRemoteUrl(remote = 'origin') {
    if (remote === 'origin') {
      return getRemoteOriginUrl();
    }

    try {
      const { stdout, code } = await executeCommand("git", ["remote", "get-url", remote], {
        preserveOutputOnError: false
      });
      return code === 0 ? stdout.trim() : null;
    } catch {
      return null;
    }
  }

  /**
   * 检查工作目录状态
   * @returns {Promise<Object>} 工作目录状态
   */
  async getWorkingDirectoryStatus() {
    const [isClean, changes, hasUpstream] = await Promise.all([
      isWorkingDirectoryClean(),
      getFileChanges(),
      hasUpstreamBranch()
    ]);

    return {
      isClean,
      hasUpstream,
      trackedChanges: changes.tracked.length,
      untrackedFiles: changes.untracked.length,
      changes
    };
  }

  /**
   * 创建stash
   * @param {string} message - stash消息
   * @param {Object} options - 选项
   * @returns {Promise<boolean>} 是否创建成功
   */
  async stash(message, options = {}) {
    const {
      includeUntracked = true,
      keepIndex = false
    } = options;

    if (includeUntracked) {
      return createAutoStash(message);
    }

    try {
      const args = ["stash", "push"];

      if (keepIndex) {
        args.push("--keep-index");
      }

      if (message) {
        args.push("--message", message);
      }

      const { code } = await executeCommand("git", args, {
        preserveOutputOnError: false
      });

      return code === 0;
    } catch {
      return false;
    }
  }

  /**
   * 获取stash列表
   * @returns {Promise<Array>} stash列表
   */
  async getStashList() {
    try {
      const { stdout, code } = await executeCommand("git", ["stash", "list", "--pretty=format:%gd|%gs|%gD"], {
        preserveOutputOnError: false
      });

      if (code !== 0) {
        return [];
      }

      return stdout.split('\n')
        .filter(line => line.trim())
        .map(line => {
          const [ref, message, date] = line.split('|');
          return {
            ref: ref.trim(),
            message: message.trim(),
            date: date ? new Date(date.trim()) : null
          };
        });
    } catch {
      return [];
    }
  }

  /**
   * 应用stash
   * @param {string} stashRef - stash引用（可选）
   * @param {boolean} drop - 是否删除stash
   * @returns {Promise<boolean>} 是否应用成功
   */
  async applyStash(stashRef, drop = false) {
    try {
      const command = drop ? "pop" : "apply";
      const args = ["stash", command];

      if (stashRef) {
        args.push(stashRef);
      }

      const { code } = await executeCommand("git", args, {
        preserveOutputOnError: false
      });

      return code === 0;
    } catch {
      return false;
    }
  }

  /**
   * 获取工作树信息
   * @returns {Promise<Object>} 工作树信息
   */
  async getWorktreeInfo() {
    const count = await getWorktreeCount();

    if (count === 0) {
      return {
        count: 0,
        worktrees: []
      };
    }

    try {
      const { stdout } = await executeCommand("git", ["worktree", "list", "--porcelain"]);
      const worktrees = [];
      let current = {};

      stdout.split('\n').forEach(line => {
        if (line.startsWith('worktree ')) {
          if (current.path) {
            worktrees.push(current);
          }
          current = { path: line.substring(9) };
        } else if (line.startsWith('HEAD ')) {
          current.head = line.substring(5);
        } else if (line.startsWith('branch ')) {
          current.branch = line.substring(7);
        } else if (line === 'bare') {
          current.bare = true;
        } else if (line === 'detached') {
          current.detached = true;
        }
      });

      if (current.path) {
        worktrees.push(current);
      }

      return {
        count,
        worktrees
      };
    } catch {
      return {
        count,
        worktrees: []
      };
    }
  }

  /**
   * 检查是否有未提交的更改
   * @returns {Promise<boolean>} 是否有未提交的更改
   */
  async hasUncommittedChanges() {
    return !(await isWorkingDirectoryClean());
  }

  /**
   * 获取仓库统计信息
   * @returns {Promise<Object>} 仓库统计信息
   */
  async getRepositoryStats() {
    try {
      const [
        isRepo,
        currentBranch,
        defaultBranch,
        remoteUrl,
        isClean,
        changes,
        worktreeInfo,
        commitInfo
      ] = await Promise.all([
        this.isGitRepository(),
        getCurrentBranchName().catch(() => null),
        this.getDefaultBranch().catch(() => null),
        this.getRemoteUrl().catch(() => null),
        isWorkingDirectoryClean().catch(() => false),
        getFileChanges().catch(() => ({ tracked: [], untracked: [] })),
        this.getWorktreeInfo().catch(() => ({ count: 0, worktrees: [] })),
        this.getCurrentCommitInfo().catch(() => null)
      ]);

      return {
        isRepository: isRepo,
        currentBranch,
        defaultBranch,
        remoteUrl,
        isClean,
        trackedChanges: changes.tracked.length,
        untrackedFiles: changes.untracked.length,
        worktreeCount: worktreeInfo.count,
        currentCommit: commitInfo
      };
    } catch (error) {
      return {
        isRepository: false,
        error: error.message
      };
    }
  }
}

// 辅助函数

/**
 * 创建懒加载器
 * @param {Function} fn - 要懒加载的函数
 * @returns {Function} 懒加载函数
 * @original SA函数的实现（推测）
 */
function createLazyLoader(fn) {
  let cached = null;
  let promise = null;
  
  return async (...args) => {
    if (cached !== null) {
      return cached;
    }
    
    if (promise !== null) {
      return promise;
    }
    
    promise = fn(...args);
    cached = await promise;
    return cached;
  };
}

/**
 * 执行命令
 * @param {string} command - 命令
 * @param {Array} args - 参数
 * @param {Object} options - 选项
 * @returns {Promise<Object>} 执行结果
 * @original X2函数的实现（推测）
 */
async function executeCommand(command, args, options = {}) {
  // @todo: 实现X2函数的实际逻辑
  const { spawn } = require('child_process');
  
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: ['pipe', 'pipe', 'pipe'],
      ...options
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    child.on('close', (code) => {
      if (code === 0 || options.preserveOutputOnError === false) {
        resolve({ code, stdout, stderr });
      } else {
        reject(new Error(`Command failed with code ${code}: ${stderr}`));
      }
    });
    
    child.on('error', reject);
  });
}

/**
 * 在指定目录执行命令
 * @param {string} command - 命令
 * @param {Array} args - 参数
 * @param {Object} options - 选项
 * @returns {Promise<Object>} 执行结果
 * @original j5函数的实现（推测）
 */
async function executeCommandInDirectory(command, args, options = {}) {
  return executeCommand(command, args, options);
}

// 创建默认Git工具实例
export const gitUtils = new GitUtils();
