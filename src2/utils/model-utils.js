/**
 * 模型管理工具函数
 * @description 重构自原始文件中的模型相关代码，对应第5508-5534行
 * @original 原始代码行 5508-5534
 */

/**
 * 检查是否使用第三方提供商
 * @returns {boolean} 是否使用第三方提供商
 * @original ga函数
 */
export function isUsingThirdPartyProvider() {
  return !!(process.env.CLAUDE_CODE_USE_BEDROCK || process.env.CLAUDE_CODE_USE_VERTEX);
}

/**
 * 获取OpenTelemetry头部信息
 * @returns {Object} OpenTelemetry头部对象
 * @throws {Error} 如果获取头部信息失败
 * @original IsA函数
 */
export function getOpenTelemetryHeaders() {
  const settingsConfig = getSettingsConfig();
  const otelHeadersHelper = settingsConfig?.otelHeadersHelper;
  
  if (!otelHeadersHelper) {
    return {};
  }
  
  try {
    const result = executeCommand(otelHeadersHelper)?.toString().trim();
    
    if (!result) {
      throw new Error("otelHeadersHelper did not return a valid value");
    }
    
    const headers = JSON.parse(result);
    
    if (typeof headers !== "object" || headers === null || Array.isArray(headers)) {
      throw new Error("otelHeadersHelper must return a JSON object with string key-value pairs");
    }
    
    // 验证所有值都是字符串
    for (const [key, value] of Object.entries(headers)) {
      if (typeof value !== "string") {
        throw new Error(`otelHeadersHelper returned non-string value for key "${key}": ${typeof value}`);
      }
    }
    
    return headers;
  } catch (error) {
    const errorMessage = `Error getting OpenTelemetry headers from otelHeadersHelper (in settings): ${error instanceof Error ? error.message : String(error)}`;
    logError(new Error(errorMessage));
    throw error;
  }
}

/**
 * 解析模型显示名称
 * @param {string} modelName - 模型名称
 * @returns {string|undefined} 显示名称或undefined
 * @original YsA函数
 */
export function parseModelDisplayName(modelName) {
  const lowerName = modelName.toLowerCase();
  
  // 检查各种模型名称模式
  if (lowerName.includes("claude-sonnet-4")) {
    return "Sonnet 4";
  }
  
  if (lowerName.includes("claude-opus-4-1")) {
    return "Opus 4.1";
  }
  
  if (lowerName.includes("claude-opus-4")) {
    return "Opus 4";
  }
  
  if (lowerName.includes("claude-3-7-sonnet")) {
    return "Claude 3.7 Sonnet";
  }
  
  if (lowerName.includes("claude-3-5-sonnet")) {
    return "Claude 3.5 Sonnet";
  }
  
  if (lowerName.includes("claude-3-5-haiku")) {
    return "Claude 3.5 Haiku";
  }
  
  return undefined;
}

/**
 * 模型管理工具类
 * @description 提供完整的模型管理功能
 */
export class ModelUtils {
  constructor() {
    this.modelCache = new Map();
    this.displayNameCache = new Map();
  }

  /**
   * 获取模型显示名称
   * @param {string} modelName - 模型名称
   * @param {boolean} useCache - 是否使用缓存
   * @returns {string} 显示名称
   */
  getDisplayName(modelName, useCache = true) {
    if (useCache && this.displayNameCache.has(modelName)) {
      return this.displayNameCache.get(modelName);
    }
    
    const displayName = parseModelDisplayName(modelName) || modelName;
    
    if (useCache) {
      this.displayNameCache.set(modelName, displayName);
    }
    
    return displayName;
  }

  /**
   * 检查模型是否为Claude模型
   * @param {string} modelName - 模型名称
   * @returns {boolean} 是否为Claude模型
   */
  isClaudeModel(modelName) {
    return modelName.toLowerCase().includes("claude");
  }

  /**
   * 获取模型系列
   * @param {string} modelName - 模型名称
   * @returns {string} 模型系列
   */
  getModelFamily(modelName) {
    const lowerName = modelName.toLowerCase();
    
    if (lowerName.includes("sonnet")) {
      return "Sonnet";
    }
    
    if (lowerName.includes("opus")) {
      return "Opus";
    }
    
    if (lowerName.includes("haiku")) {
      return "Haiku";
    }
    
    return "Unknown";
  }

  /**
   * 获取模型版本
   * @param {string} modelName - 模型名称
   * @returns {string|null} 模型版本
   */
  getModelVersion(modelName) {
    const lowerName = modelName.toLowerCase();
    
    // 匹配版本号模式
    const versionPatterns = [
      /claude-(\d+(?:\.\d+)?)-/,  // claude-3.5-sonnet
      /claude-(\d+)-/,            // claude-3-sonnet
      /-(\d+(?:\.\d+)?)$/,        // sonnet-3.5
      /v(\d+(?:\.\d+)?)/          // v3.5
    ];
    
    for (const pattern of versionPatterns) {
      const match = lowerName.match(pattern);
      if (match) {
        return match[1];
      }
    }
    
    return null;
  }

  /**
   * 比较模型版本
   * @param {string} modelA - 模型A
   * @param {string} modelB - 模型B
   * @returns {number} 比较结果（-1, 0, 1）
   */
  compareModelVersions(modelA, modelB) {
    const versionA = this.getModelVersion(modelA);
    const versionB = this.getModelVersion(modelB);
    
    if (!versionA && !versionB) return 0;
    if (!versionA) return -1;
    if (!versionB) return 1;
    
    const parseVersion = (version) => {
      return version.split('.').map(num => parseInt(num, 10));
    };
    
    const partsA = parseVersion(versionA);
    const partsB = parseVersion(versionB);
    
    const maxLength = Math.max(partsA.length, partsB.length);
    
    for (let i = 0; i < maxLength; i++) {
      const partA = partsA[i] || 0;
      const partB = partsB[i] || 0;
      
      if (partA < partB) return -1;
      if (partA > partB) return 1;
    }
    
    return 0;
  }

  /**
   * 获取推荐模型
   * @param {Array} availableModels - 可用模型列表
   * @param {string} task - 任务类型
   * @returns {string|null} 推荐模型
   */
  getRecommendedModel(availableModels, task = "general") {
    const claudeModels = availableModels.filter(model => this.isClaudeModel(model));
    
    if (claudeModels.length === 0) {
      return availableModels[0] || null;
    }
    
    // 根据任务类型推荐模型
    switch (task) {
      case "coding":
      case "analysis":
        // 优先推荐Sonnet系列
        return claudeModels.find(model => model.toLowerCase().includes("sonnet")) || claudeModels[0];
      
      case "creative":
      case "writing":
        // 优先推荐Opus系列
        return claudeModels.find(model => model.toLowerCase().includes("opus")) || claudeModels[0];
      
      case "quick":
      case "simple":
        // 优先推荐Haiku系列
        return claudeModels.find(model => model.toLowerCase().includes("haiku")) || claudeModels[0];
      
      default:
        // 默认推荐最新的Sonnet
        const sonnetModels = claudeModels.filter(model => model.toLowerCase().includes("sonnet"));
        if (sonnetModels.length > 0) {
          return sonnetModels.sort((a, b) => this.compareModelVersions(b, a))[0];
        }
        return claudeModels[0];
    }
  }

  /**
   * 获取模型信息
   * @param {string} modelName - 模型名称
   * @returns {Object} 模型信息
   */
  getModelInfo(modelName) {
    return {
      name: modelName,
      displayName: this.getDisplayName(modelName),
      family: this.getModelFamily(modelName),
      version: this.getModelVersion(modelName),
      isClaudeModel: this.isClaudeModel(modelName)
    };
  }

  /**
   * 验证模型名称
   * @param {string} modelName - 模型名称
   * @returns {boolean} 是否有效
   */
  validateModelName(modelName) {
    if (!modelName || typeof modelName !== 'string') {
      return false;
    }
    
    // 基本的模型名称验证
    const validPattern = /^[a-zA-Z0-9._-]+$/;
    return validPattern.test(modelName);
  }

  /**
   * 获取模型能力
   * @param {string} modelName - 模型名称
   * @returns {Object} 模型能力
   */
  getModelCapabilities(modelName) {
    const family = this.getModelFamily(modelName);
    const version = this.getModelVersion(modelName);
    
    // 基于模型系列和版本推断能力
    const capabilities = {
      textGeneration: true,
      codeGeneration: false,
      imageAnalysis: false,
      longContext: false,
      multimodal: false
    };
    
    if (family === "Sonnet") {
      capabilities.codeGeneration = true;
      capabilities.longContext = true;
    }
    
    if (family === "Opus") {
      capabilities.codeGeneration = true;
      capabilities.imageAnalysis = true;
      capabilities.multimodal = true;
      capabilities.longContext = true;
    }
    
    // 版本3.5及以上支持更多功能
    if (version && parseFloat(version) >= 3.5) {
      capabilities.imageAnalysis = true;
      capabilities.multimodal = true;
    }
    
    return capabilities;
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.modelCache.clear();
    this.displayNameCache.clear();
  }

  /**
   * 获取缓存统计
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    return {
      modelCacheSize: this.modelCache.size,
      displayNameCacheSize: this.displayNameCache.size
    };
  }
}

// 辅助函数

/**
 * 获取设置配置
 * @returns {Object} 设置配置
 */
function getSettingsConfig() {
  // @todo: 实现C9函数的实际逻辑
  return {};
}

/**
 * 执行命令
 * @param {string} command - 命令
 * @returns {string} 命令输出
 */
function executeCommand(command) {
  // @todo: 实现GD函数的实际逻辑
  const { execSync } = require('child_process');
  return execSync(command, { encoding: 'utf8' });
}

/**
 * 记录错误
 * @param {Error} error - 错误对象
 */
function logError(error) {
  console.error('Model utils error:', error);
}

// 创建默认模型工具实例
export const modelUtils = new ModelUtils();

// 导出便捷函数
export const Model = {
  getDisplayName: (name) => modelUtils.getDisplayName(name),
  isClaudeModel: (name) => modelUtils.isClaudeModel(name),
  getFamily: (name) => modelUtils.getModelFamily(name),
  getVersion: (name) => modelUtils.getModelVersion(name),
  getInfo: (name) => modelUtils.getModelInfo(name),
  getCapabilities: (name) => modelUtils.getModelCapabilities(name),
  validate: (name) => modelUtils.validateModelName(name),
  recommend: (models, task) => modelUtils.getRecommendedModel(models, task)
};
