/**
 * 会话工具函数
 * @description 重构自原始文件中的会话工具代码，对应第6065-6100行
 * @original 原始代码行 6065-6100
 */

/**
 * 插入消息链并返回最后一个UUID
 * @param {Array} messageChain - 消息链数组
 * @returns {Promise<string|null>} 最后一个消息的UUID
 * @original pM1函数
 */
export async function insertMessageChain(messageChain) {
  const processedChain = processMessageChain(messageChain);
  await getSessionManager().insertMessageChain(processedChain);
  return processedChain[processedChain.length - 1]?.uuid || null;
}

/**
 * 插入侧链消息
 * @param {Array} messageChain - 消息链数组
 * @returns {Promise<void>}
 * @original Y$2函数
 */
export async function insertSidechainMessages(messageChain) {
  const processedChain = processMessageChain(messageChain);
  await getSessionManager().insertMessageChain(processedChain, true);
}

/**
 * 插入检查点
 * @param {Object} checkpoint - 检查点对象
 * @returns {Promise<void>}
 * @original cZ0函数
 */
export async function insertCheckpoint(checkpoint) {
  await getSessionManager().insertCheckpoint(checkpoint);
}

/**
 * 初始化会话文件
 * @returns {Promise<void>}
 * @original W$2函数
 */
export async function initializeSessionFile() {
  const sessionManager = getSessionManager();
  sessionManager.sessionFile = getSessionLogPath();
}

/**
 * 获取消息链的提示文本
 * @param {Array} messageChain - 消息链数组
 * @returns {string} 提示文本
 * @original pl4函数
 */
export function getPromptFromMessageChain(messageChain) {
  const userMessage = messageChain.find(msg => msg.type === "user");
  
  if (!userMessage || userMessage.type !== "user") {
    return "No prompt";
  }
  
  const content = userMessage.message?.content;
  let promptText = "";
  
  if (typeof content === "string") {
    promptText = content;
  } else if (Array.isArray(content)) {
    const textContent = content.find(item => item.type === "text");
    promptText = textContent?.text || "No prompt";
  } else {
    promptText = "No prompt";
  }
  
  // 清理和截断文本
  promptText = promptText.replace(/\n/g, " ").trim();
  
  if (promptText.length > 45) {
    promptText = promptText.slice(0, 45) + "...";
  }
  
  return promptText;
}

/**
 * 清理消息链（移除内部字段）
 * @param {Array} messageChain - 消息链数组
 * @returns {Array} 清理后的消息链
 * @original il4函数
 */
export function cleanMessageChain(messageChain) {
  return messageChain.map(message => {
    const { isSidechain, parentUuid, ...cleanedMessage } = message;
    return cleanedMessage;
  });
}

/**
 * 创建会话摘要
 * @param {Array} messageChain - 消息链数组
 * @param {number} tokenCount - 令牌数量
 * @param {string} summary - 摘要文本
 * @param {Array} checkpoints - 检查点数组
 * @param {boolean} isBookmarked - 是否书签
 * @returns {Object} 会话摘要对象
 * @original J$2函数
 */
export function createSessionSummary(messageChain, tokenCount = 0, summary, checkpoints, isBookmarked) {
  const lastMessage = messageChain[messageChain.length - 1];
  const firstMessage = messageChain[0];

  if (!lastMessage || !firstMessage) {
    throw new Error("Message chain must have at least one message");
  }

  const firstPrompt = getPromptFromMessageChain(messageChain);
  const createdDate = new Date(firstMessage.timestamp);
  const modifiedDate = new Date(lastMessage.timestamp);

  const processedCheckpoints = checkpoints?.map(checkpoint => ({
    id: checkpoint.id ?? "unavailable",
    commit: checkpoint.commit,
    timestamp: new Date(checkpoint.timestamp),
    label: checkpoint.label
  }));

  return {
    date: lastMessage.timestamp,
    messages: cleanMessageChain(messageChain),
    fullPath: "n/a",
    value: tokenCount,
    created: createdDate,
    modified: modifiedDate,
    firstPrompt,
    messageCount: messageChain.length,
    isSidechain: firstMessage.isSidechain,
    leafUuid: lastMessage.uuid,
    summary,
    checkpoints: processedCheckpoints,
    gitBranch: lastMessage.gitBranch,
    isBookmarked
  };
}

/**
 * 获取所有会话转录
 * @returns {Promise<Array>} 会话转录数组
 * @original X$2函数
 */
export async function getAllSessionTranscripts() {
  const sessionManager = getSessionManager();
  const allTranscripts = await sessionManager.getAllTranscripts();
  const summaries = sessionManager.summaries;

  return allTranscripts.map((transcript, index) => {
    const lastMessage = transcript[transcript.length - 1];
    const summary = lastMessage ? summaries.get(lastMessage.uuid) : undefined;
    const checkpoints = lastMessage ? sessionManager.getAllCheckpoints(transcript) : undefined;
    const sessionId = lastMessage?.sessionId;
    const isBookmarked = sessionId ? sessionManager.getBookmarkStatus(sessionId) : false;

    return createSessionSummary(transcript, index, summary, checkpoints, isBookmarked);
  }).sort((a, b) => {
    return b.modified.getTime() - a.modified.getTime();
  });
}

/**
 * 添加会话摘要
 * @param {string} leafUuid - 叶子UUID
 * @param {string} summaryText - 摘要文本
 * @returns {Promise<void>}
 * @original V$2函数
 */
export async function addSessionSummary(leafUuid, summaryText) {
  await getSessionManager().appendEntry({
    type: "summary",
    summary: summaryText,
    leafUuid
  });
}

/**
 * 从文件加载会话数据
 * @param {string} filePath - 文件路径
 * @returns {Promise<Object>} 会话数据
 * @original lZ0函数
 */
export async function loadSessionDataFromFile(filePath) {
  const messages = new Map();
  const summaries = new Map();
  const checkpoints = new Map();

  try {
    const entries = await readSessionFile(filePath);

    for (const entry of entries) {
      if (entry.type === "user" || entry.type === "assistant" ||
          entry.type === "attachment" || entry.type === "system") {
        messages.set(entry.uuid, entry);
      } else if (entry.type === "summary" && entry.leafUuid) {
        summaries.set(entry.leafUuid, entry.summary);
      } else if (entry.type === "checkpoint") {
        const checkpointId = entry.id ?? entry.commit;
        if (checkpointId) {
          checkpoints.set(checkpointId, entry);
        }
      }
    }
  } catch (error) {
    console.error('Failed to load session data from file:', error);
  }

  return {
    messages,
    summaries,
    checkpoints
  };
}

/**
 * 加载会话数据（按会话ID）
 * @param {string} sessionId - 会话ID
 * @returns {Promise<Object>} 会话数据
 * @original pZ0函数
 */
export async function loadSessionData(sessionId) {
  const sessionPath = getSessionPath(sessionId);
  return loadSessionDataFromFile(sessionPath);
}

/**
 * 获取会话信息（懒加载）
 * @param {string} sessionId - 会话ID
 * @returns {Promise<Object>} 会话信息
 * @original nl4函数
 */
export const getSessionInfo = createLazyLoader(async (sessionId) => {
  const { messages, checkpoints } = await loadSessionData(sessionId);

  return {
    messageSet: new Set(messages.keys()),
    checkpointSet: new Set(checkpoints.keys())
  };
}, sessionId => sessionId);

/**
 * 获取最后的会话日志
 * @param {string} sessionId - 会话ID
 * @returns {Promise<Object|null>} 会话日志或null
 * @original C$2函数
 */
export async function getLastSessionLog(sessionId) {
  const sessionManager = getSessionManager();
  const lastLog = await sessionManager.getLastLog(sessionId);

  if (lastLog !== null && lastLog !== undefined) {
    const lastMessage = lastLog[lastLog.length - 1];
    const { summaries, checkpoints } = await loadSessionData(sessionId);
    const summary = lastMessage ? summaries.get(lastMessage.uuid) : undefined;

    return createSessionSummary(lastLog, 0, summary, Array.from(checkpoints.values()));
  }

  return null;
}

/**
 * 过滤消息链
 * @param {Array} messageChain - 消息链数组
 * @returns {Array} 过滤后的消息链
 * @original K$2函数
 */
export function filterMessageChain(messageChain) {
  return messageChain.filter(message => {
    // 过滤进度消息
    if (message.type === "progress") {
      return false;
    }

    // 过滤非内部用户的附件消息
    if (message.type === "attachment" && getUserType() !== "ant") {
      return false;
    }

    return true;
  });
}

/**
 * 获取所有会话ID
 * @returns {Array} 会话ID数组
 * @original H$2函数
 */
export function getAllSessionIds() {
  return [...getInternalSessionIds()];
}

/**
 * 会话工具类
 * @description 提供完整的会话工具功能
 */
export class SessionUtils {
  constructor() {
    this.messageProcessors = new Map();
    this.summaryGenerators = new Map();
  }

  /**
   * 注册消息处理器
   * @param {string} messageType - 消息类型
   * @param {Function} processor - 处理器函数
   */
  registerMessageProcessor(messageType, processor) {
    this.messageProcessors.set(messageType, processor);
  }

  /**
   * 注册摘要生成器
   * @param {string} summaryType - 摘要类型
   * @param {Function} generator - 生成器函数
   */
  registerSummaryGenerator(summaryType, generator) {
    this.summaryGenerators.set(summaryType, generator);
  }

  /**
   * 处理消息
   * @param {Object} message - 消息对象
   * @returns {Object} 处理后的消息
   */
  processMessage(message) {
    const processor = this.messageProcessors.get(message.type);
    
    if (processor) {
      return processor(message);
    }
    
    // 默认处理：添加时间戳和UUID（如果缺失）
    const processedMessage = { ...message };
    
    if (!processedMessage.timestamp) {
      processedMessage.timestamp = new Date().toISOString();
    }
    
    if (!processedMessage.uuid) {
      processedMessage.uuid = generateUUID();
    }
    
    return processedMessage;
  }

  /**
   * 批量处理消息链
   * @param {Array} messageChain - 消息链数组
   * @returns {Array} 处理后的消息链
   */
  processMessageChain(messageChain) {
    return messageChain.map(message => this.processMessage(message));
  }

  /**
   * 生成会话摘要
   * @param {Array} messageChain - 消息链数组
   * @param {Object} options - 选项
   * @returns {Object} 会话摘要
   */
  generateSummary(messageChain, options = {}) {
    const {
      summaryType = "default",
      tokenCount = 0,
      model,
      subscriptionType
    } = options;
    
    const generator = this.summaryGenerators.get(summaryType);
    
    if (generator) {
      return generator(messageChain, options);
    }
    
    // 默认摘要生成
    return createSessionSummary(messageChain, tokenCount, undefined, model, subscriptionType);
  }

  /**
   * 验证消息链
   * @param {Array} messageChain - 消息链数组
   * @returns {Object} 验证结果
   */
  validateMessageChain(messageChain) {
    const errors = [];
    const warnings = [];
    
    if (!Array.isArray(messageChain)) {
      errors.push("Message chain must be an array");
      return { valid: false, errors, warnings };
    }
    
    if (messageChain.length === 0) {
      errors.push("Message chain cannot be empty");
      return { valid: false, errors, warnings };
    }
    
    for (let i = 0; i < messageChain.length; i++) {
      const message = messageChain[i];
      
      if (!message.uuid) {
        warnings.push(`Message at index ${i} is missing UUID`);
      }
      
      if (!message.type) {
        errors.push(`Message at index ${i} is missing type`);
      }
      
      if (!message.timestamp) {
        warnings.push(`Message at index ${i} is missing timestamp`);
      }
      
      // 验证父子关系
      if (i > 0 && message.parentUuid !== messageChain[i - 1].uuid) {
        warnings.push(`Message at index ${i} has incorrect parent UUID`);
      }
    }
    
    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 修复消息链
   * @param {Array} messageChain - 消息链数组
   * @returns {Array} 修复后的消息链
   */
  repairMessageChain(messageChain) {
    const repairedChain = [];
    let previousUuid = null;
    
    for (const message of messageChain) {
      const repairedMessage = { ...message };
      
      // 修复UUID
      if (!repairedMessage.uuid) {
        repairedMessage.uuid = generateUUID();
      }
      
      // 修复时间戳
      if (!repairedMessage.timestamp) {
        repairedMessage.timestamp = new Date().toISOString();
      }
      
      // 修复父UUID
      if (previousUuid) {
        repairedMessage.parentUuid = previousUuid;
      }
      
      repairedChain.push(repairedMessage);
      previousUuid = repairedMessage.uuid;
    }
    
    return repairedChain;
  }

  /**
   * 获取消息链统计信息
   * @param {Array} messageChain - 消息链数组
   * @returns {Object} 统计信息
   */
  getMessageChainStats(messageChain) {
    const stats = {
      totalMessages: messageChain.length,
      messageTypes: {},
      totalTokens: 0,
      timeSpan: null,
      hasAttachments: false
    };
    
    let earliestTime = null;
    let latestTime = null;
    
    for (const message of messageChain) {
      // 统计消息类型
      const type = message.type || 'unknown';
      stats.messageTypes[type] = (stats.messageTypes[type] || 0) + 1;
      
      // 统计令牌数
      if (message.tokenCount) {
        stats.totalTokens += message.tokenCount;
      }
      
      // 统计时间跨度
      if (message.timestamp) {
        const messageTime = new Date(message.timestamp);
        
        if (!earliestTime || messageTime < earliestTime) {
          earliestTime = messageTime;
        }
        
        if (!latestTime || messageTime > latestTime) {
          latestTime = messageTime;
        }
      }
      
      // 检查附件
      if (message.attachments && message.attachments.length > 0) {
        stats.hasAttachments = true;
      }
    }
    
    if (earliestTime && latestTime) {
      stats.timeSpan = {
        start: earliestTime.toISOString(),
        end: latestTime.toISOString(),
        duration: latestTime.getTime() - earliestTime.getTime()
      };
    }
    
    return stats;
  }
}

// 辅助函数

/**
 * 处理消息链
 * @param {Array} messageChain - 消息链数组
 * @returns {Array} 处理后的消息链
 * @original K$2函数的实现（推测）
 */
function processMessageChain(messageChain) {
  const utils = new SessionUtils();
  return utils.processMessageChain(messageChain);
}

/**
 * 获取会话管理器
 * @returns {Object} 会话管理器
 * @original mL函数的实现（推测）
 */
function getSessionManager() {
  const { getSessionManager } = require('../services/session-service.js');
  return getSessionManager();
}

/**
 * 获取会话日志路径
 * @returns {string} 会话日志路径
 * @original lM1函数的实现（推测）
 */
function getSessionLogPath() {
  const { getSessionLogPath } = require('../services/session-service.js');
  return getSessionLogPath();
}

/**
 * 获取会话ID
 * @returns {string} 会话ID
 * @original B9函数的实现（推测）
 */
function getSessionId() {
  // @todo: 实现B9函数的实际逻辑
  return 'default-session';
}

/**
 * 获取用户类型
 * @returns {string} 用户类型
 * @original F$2函数的实现（推测）
 */
function getUserType() {
  return "external";
}

/**
 * 获取环境类型
 * @returns {string} 环境类型
 * @original ll4函数的实现（推测）
 */
function getEnvironment() {
  return "production";
}

/**
 * 生成UUID
 * @returns {string} UUID字符串
 * @original 推测函数
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 创建懒加载器
 * @param {Function} fn - 要懒加载的函数
 * @param {Function} keyGenerator - 键生成函数
 * @returns {Function} 懒加载函数
 * @original SA函数的实现（推测）
 */
function createLazyLoader(fn, keyGenerator) {
  const cache = new Map();

  const loader = async (...args) => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);

    if (cache.has(key)) {
      return cache.get(key);
    }

    const result = await fn(...args);
    cache.set(key, result);

    return result;
  };

  loader.cache = {
    clear: () => cache.clear()
  };

  return loader;
}

/**
 * 读取会话文件
 * @param {string} filePath - 文件路径
 * @returns {Promise<Array>} 会话条目数组
 * @original Sr1函数的实现（推测）
 */
async function readSessionFile(filePath) {
  const fs = require('fs');

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.trim().split('\n');
    const entries = [];

    for (const line of lines) {
      if (!line.trim()) continue;

      try {
        const entry = JSON.parse(line);
        entries.push(entry);
      } catch (error) {
        console.error('Failed to parse session entry:', error);
      }
    }

    return entries;
  } catch (error) {
    console.error('Failed to read session file:', error);
    return [];
  }
}

/**
 * 获取会话路径
 * @param {string} sessionId - 会话ID
 * @returns {string} 会话文件路径
 * @original 推测函数
 */
function getSessionPath(sessionId) {
  const { getSessionPath } = require('../services/session-service.js');
  return getSessionPath(sessionId);
}

/**
 * 获取内部会话ID集合
 * @returns {Set} 内部会话ID集合
 * @original iM1变量的实现（推测）
 */
function getInternalSessionIds() {
  // @todo: 实现iM1变量的实际值
  return new Set(['default-session']);
}

// 创建默认会话工具实例
export const sessionUtils = new SessionUtils();

// 导出便捷函数
export const SessionUtil = {
  insertMessageChain: (chain) => insertMessageChain(chain),
  insertSidechainMessages: (chain) => insertSidechainMessages(chain),
  insertCheckpoint: (checkpoint) => insertCheckpoint(checkpoint),
  initializeSessionFile: () => initializeSessionFile(),
  getPromptFromMessageChain: (chain) => getPromptFromMessageChain(chain),
  cleanMessageChain: (chain) => cleanMessageChain(chain),
  createSessionSummary: (chain, tokenCount, summary, model, subscriptionType) => 
    createSessionSummary(chain, tokenCount, summary, model, subscriptionType),
  validateMessageChain: (chain) => sessionUtils.validateMessageChain(chain),
  repairMessageChain: (chain) => sessionUtils.repairMessageChain(chain),
  getMessageChainStats: (chain) => sessionUtils.getMessageChainStats(chain)
};
