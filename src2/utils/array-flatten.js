/**
 * 数组扁平化工具函数
 * @description 重构自原始文件中的数组扁平化函数，对应第1532-1558行
 * @original 原始代码行 1532-1558
 */

/**
 * Symbol.isConcatSpreadable的引用
 * @original var jP0 = AI ? AI.isConcatSpreadable : void 0;
 */
const isConcatSpreadable = Symbol ? Symbol.isConcatSpreadable : undefined;

/**
 * 检查值是否可以被扁平化
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否可以被扁平化
 * @original function FnB(A) { return x8(A) || eR(A) || !!(jP0 && A && A[jP0]); }
 */
function isFlattenable(value) {
  return Array.isArray(value) || 
         isArguments(value) || 
         !!(isConcatSpreadable && value && value[isConcatSpreadable]);
}

/**
 * 检查值是否为arguments对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为arguments对象
 * @original eR(A) - 需要实现这个函数的逻辑
 */
function isArguments(value) {
  return Object.prototype.toString.call(value) === '[object Arguments]';
}

/**
 * 将数组元素添加到结果数组中
 * @param {Array} array - 源数组
 * @param {Array} result - 结果数组
 * @original gc(Z, I) - 需要实现这个函数的逻辑
 */
function arrayPush(array, result) {
  let index = -1;
  const length = array.length;
  const offset = result.length;
  
  result.length += length;
  while (++index < length) {
    result[offset + index] = array[index];
  }
  
  return result;
}

/**
 * 基础扁平化函数
 * @param {Array} array - 要扁平化的数组
 * @param {number} depth - 扁平化深度
 * @param {Function} predicate - 判断函数
 * @param {boolean} isStrict - 是否严格模式
 * @param {Array} result - 结果数组
 * @returns {Array} 扁平化后的数组
 * @original function kP0(A, B, Q, D, Z) { var G = -1, F = A.length; Q || (Q = yP0), Z || (Z = []); while (++G < F) { var I = A[G]; if (B > 0 && Q(I)) { if (B > 1) kP0(I, B - 1, Q, D, Z);else gc(Z, I); } else if (!D) Z[Z.length] = I; } return Z; }
 */
export function baseFlatten(array, depth, predicate, isStrict, result) {
  let index = -1;
  const length = array.length;
  
  predicate || (predicate = isFlattenable);
  result || (result = []);
  
  while (++index < length) {
    const value = array[index];
    if (depth > 0 && predicate(value)) {
      if (depth > 1) {
        // 递归扁平化
        baseFlatten(value, depth - 1, predicate, isStrict, result);
      } else {
        // 直接添加数组元素
        arrayPush(value, result);
      }
    } else if (!isStrict) {
      // 非严格模式下添加非数组元素
      result[result.length] = value;
    }
  }
  
  return result;
}

/**
 * 扁平化数组一层
 * @param {Array} array - 要扁平化的数组
 * @returns {Array} 扁平化后的数组
 * @original function InB(A) { var B = A == null ? 0 : A.length; return B ? _P0(A, 1) : []; }
 */
export function flatten(array) {
  const length = array == null ? 0 : array.length;
  return length ? baseFlatten(array, 1) : [];
}

/**
 * 深度扁平化数组
 * @param {Array} array - 要扁平化的数组
 * @returns {Array} 完全扁平化后的数组
 */
export function flattenDeep(array) {
  const length = array == null ? 0 : array.length;
  return length ? baseFlatten(array, Infinity) : [];
}

/**
 * 按指定深度扁平化数组
 * @param {Array} array - 要扁平化的数组
 * @param {number} depth - 扁平化深度，默认为1
 * @returns {Array} 扁平化后的数组
 */
export function flattenDepth(array, depth = 1) {
  const length = array == null ? 0 : array.length;
  if (!length || depth < 1) {
    return [];
  }
  return baseFlatten(array, depth);
}

/**
 * 扁平化数组并映射每个元素
 * @param {Array} array - 要处理的数组
 * @param {Function} iteratee - 映射函数
 * @returns {Array} 扁平化并映射后的数组
 */
export function flatMap(array, iteratee) {
  const length = array == null ? 0 : array.length;
  if (!length) {
    return [];
  }
  
  const mapped = [];
  let index = -1;
  
  while (++index < length) {
    const result = iteratee(array[index], index, array);
    if (Array.isArray(result)) {
      arrayPush(result, mapped);
    } else {
      mapped[mapped.length] = result;
    }
  }
  
  return mapped;
}

/**
 * 深度扁平化数组并映射每个元素
 * @param {Array} array - 要处理的数组
 * @param {Function} iteratee - 映射函数
 * @returns {Array} 深度扁平化并映射后的数组
 */
export function flatMapDeep(array, iteratee) {
  const length = array == null ? 0 : array.length;
  if (!length) {
    return [];
  }
  
  const mapped = [];
  let index = -1;
  
  while (++index < length) {
    const result = iteratee(array[index], index, array);
    baseFlatten([result], Infinity, undefined, false, mapped);
  }
  
  return mapped;
}

/**
 * 按指定深度扁平化数组并映射每个元素
 * @param {Array} array - 要处理的数组
 * @param {Function} iteratee - 映射函数
 * @param {number} depth - 扁平化深度，默认为1
 * @returns {Array} 扁平化并映射后的数组
 */
export function flatMapDepth(array, iteratee, depth = 1) {
  const length = array == null ? 0 : array.length;
  if (!length) {
    return [];
  }
  
  const mapped = [];
  let index = -1;
  
  while (++index < length) {
    const result = iteratee(array[index], index, array);
    baseFlatten([result], depth, undefined, false, mapped);
  }
  
  return mapped;
}

/**
 * 压缩多个数组为一个数组
 * @param {...Array} arrays - 要压缩的数组
 * @returns {Array} 压缩后的数组
 */
export function concat(...arrays) {
  const result = [];
  
  for (const array of arrays) {
    if (Array.isArray(array)) {
      arrayPush(array, result);
    } else {
      result[result.length] = array;
    }
  }
  
  return result;
}

/**
 * 移除数组中的假值元素
 * @param {Array} array - 要处理的数组
 * @returns {Array} 移除假值后的数组
 */
export function compact(array) {
  let resIndex = 0;
  const result = [];
  
  if (array == null) {
    return result;
  }
  
  for (const value of array) {
    if (value) {
      result[resIndex++] = value;
    }
  }
  
  return result;
}

/**
 * 创建一个排除指定值的新数组
 * @param {Array} array - 要处理的数组
 * @param {...*} values - 要排除的值
 * @returns {Array} 排除指定值后的数组
 */
export function without(array, ...values) {
  if (array == null) {
    return [];
  }
  
  const result = [];
  let resIndex = 0;
  
  for (const item of array) {
    if (!values.includes(item)) {
      result[resIndex++] = item;
    }
  }
  
  return result;
}

/**
 * 创建一个去重后的数组
 * @param {Array} array - 要处理的数组
 * @returns {Array} 去重后的数组
 */
export function uniq(array) {
  if (array == null || !array.length) {
    return [];
  }
  
  const result = [];
  const seen = new Set();
  
  for (const value of array) {
    if (!seen.has(value)) {
      seen.add(value);
      result.push(value);
    }
  }
  
  return result;
}

/**
 * 使用迭代函数创建去重数组
 * @param {Array} array - 要处理的数组
 * @param {Function} iteratee - 迭代函数
 * @returns {Array} 去重后的数组
 */
export function uniqBy(array, iteratee) {
  if (array == null || !array.length) {
    return [];
  }
  
  const result = [];
  const seen = new Set();
  
  for (const value of array) {
    const computed = iteratee(value);
    if (!seen.has(computed)) {
      seen.add(computed);
      result.push(value);
    }
  }
  
  return result;
}
