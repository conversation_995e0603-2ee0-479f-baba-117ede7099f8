/**
 * 随机采样和洗牌工具函数
 * @description 重构自原始文件中的随机采样函数，对应第1885-1950行
 * @original 原始代码行 1885-1950
 */

import { values } from './collection-operations.js';
import { negate } from './collection-operations.js';
import { clamp } from './number-processing.js';
import { toInteger } from './number-processing.js';

/**
 * Math对象的引用
 * @original var { floor: XoB, random: VoB } = Math;
 */
const { floor, random } = Math;

/**
 * 生成指定范围内的随机整数
 * @param {number} lower - 下界
 * @param {number} upper - 上界
 * @returns {number} 随机整数
 * @original function CoB(A, B) { return A + XoB(VoB() * (B - A + 1)); }
 */
export function randomInt(lower, upper) {
  return lower + floor(random() * (upper - lower + 1));
}

/**
 * 拒绝集合中满足条件的元素
 * @param {Array|Object} collection - 集合
 * @param {Function} predicate - 判断函数
 * @returns {Array} 拒绝后的数组
 * @original function KoB(A, B) { var Q = x8(A) ? _Y1 : Ij0; return Q(A, Xj0(nq(B, 3))); }
 */
export function reject(collection, predicate) {
  const func = Array.isArray(collection) ? arrayFilter : filter;
  return func(collection, negate(iteratee(predicate, 3)));
}

/**
 * 从数组中随机选择一个元素
 * @param {Array} array - 数组
 * @returns {*} 随机选择的元素
 * @original function HoB(A) { var B = A.length; return B ? A[FW1(0, B - 1)] : void 0; }
 */
export function sample(array) {
  const length = array.length;
  return length ? array[randomInt(0, length - 1)] : undefined;
}

/**
 * 从对象的值中随机选择一个
 * @param {Object} object - 对象
 * @returns {*} 随机选择的值
 * @original function zoB(A) { return YW1(QW1(A)); }
 */
function sampleObject(object) {
  return sample(values(object));
}

/**
 * 从集合中随机选择一个元素
 * @param {Array|Object} collection - 集合
 * @returns {*} 随机选择的元素
 * @original function EoB(A) { var B = x8(A) ? YW1 : zj0; return B(A); }
 */
export function sampleSize(collection) {
  const func = Array.isArray(collection) ? sample : sampleObject;
  return func(collection);
}

/**
 * 洗牌数组（原地修改）
 * @param {Array} array - 要洗牌的数组
 * @param {number} size - 洗牌的大小
 * @returns {Array} 洗牌后的数组
 * @original function UoB(A, B) { var Q = -1, D = A.length, Z = D - 1; B = B === void 0 ? D : B; while (++Q < B) { var G = FW1(Q, Z), F = A[G]; A[G] = A[Q], A[Q] = F; } return A.length = B, A; }
 */
export function baseShuffle(array, size) {
  let index = -1;
  const length = array.length;
  const lastIndex = length - 1;
  
  size = size === undefined ? length : size;
  while (++index < size) {
    const rand = randomInt(index, lastIndex);
    const value = array[rand];
    
    array[rand] = array[index];
    array[index] = value;
  }
  
  array.length = size;
  return array;
}

/**
 * 从数组中随机采样指定数量的元素
 * @param {Array} array - 源数组
 * @param {number} n - 采样数量
 * @returns {Array} 采样结果
 * @original function woB(A, B) { return WW1(Pc(A), jY1(B, 0, A.length)); }
 */
export function sampleSizeArray(array, n) {
  return baseShuffle(copyArray(array), clamp(n, 0, array.length));
}

/**
 * 从对象的值中随机采样指定数量的元素
 * @param {Object} object - 源对象
 * @param {number} n - 采样数量
 * @returns {Array} 采样结果
 * @original function $oB(A, B) { var Q = QW1(A); return WW1(Q, jY1(B, 0, Q.length)); }
 */
export function sampleSizeObject(object, n) {
  const props = values(object);
  return baseShuffle(props, clamp(n, 0, props.length));
}

/**
 * 从集合中随机采样指定数量的元素
 * @param {Array|Object} collection - 集合
 * @param {number} n - 采样数量，默认为1
 * @returns {Array} 采样结果
 * @original function qoB(A, B, Q) { if (Q ? $Y1(A, B, Q) : B === void 0) B = 1;else B = yT0(B); var D = x8(A) ? Ej0 : Uj0; return D(A, B); }
 */
export function sampleSizeCollection(collection, n, guard) {
  if ((guard ? isIterateeCall(collection, n, guard) : n === undefined)) {
    n = 1;
  } else {
    n = toInteger(n);
  }
  
  const func = Array.isArray(collection) ? sampleSizeArray : sampleSizeObject;
  return func(collection, n);
}

/**
 * 洗牌数组（返回新数组）
 * @param {Array} array - 要洗牌的数组
 * @returns {Array} 洗牌后的新数组
 */
export function shuffle(array) {
  return baseShuffle(copyArray(array));
}

/**
 * 洗牌对象的值（返回新数组）
 * @param {Object} object - 要洗牌的对象
 * @returns {Array} 洗牌后的值数组
 */
export function shuffleObject(object) {
  return baseShuffle(values(object));
}

/**
 * 洗牌集合
 * @param {Array|Object} collection - 集合
 * @returns {Array} 洗牌后的数组
 */
export function shuffleCollection(collection) {
  const func = Array.isArray(collection) ? shuffle : shuffleObject;
  return func(collection);
}

/**
 * 生成指定范围内的随机数
 * @param {number} lower - 下界，默认为0
 * @param {number} upper - 上界，默认为1
 * @param {boolean} floating - 是否返回浮点数
 * @returns {number} 随机数
 */
export function randomNumber(lower = 0, upper = 1, floating) {
  if (floating === undefined) {
    if (typeof upper === 'boolean') {
      floating = upper;
      upper = lower;
      lower = 0;
    } else if (typeof lower === 'boolean') {
      floating = lower;
      lower = 0;
    }
  }
  
  if (lower === undefined) {
    lower = 0;
  }
  if (upper === undefined) {
    upper = 1;
  }
  
  if (lower > upper) {
    const temp = lower;
    lower = upper;
    upper = temp;
  }
  
  if (floating || lower % 1 || upper % 1) {
    const rand = random();
    const randLength = String(rand).length - 1;
    return Math.min(lower + (rand * (upper - lower + parseFloat('1e-' + randLength))), upper);
  }
  
  return randomInt(lower, upper);
}

/**
 * 从数组中随机移除并返回一个元素
 * @param {Array} array - 数组
 * @returns {*} 移除的元素
 */
export function pullSample(array) {
  if (!(array && array.length)) {
    return undefined;
  }
  
  const index = randomInt(0, array.length - 1);
  return array.splice(index, 1)[0];
}

/**
 * 从数组中随机移除并返回多个元素
 * @param {Array} array - 数组
 * @param {number} n - 要移除的元素数量
 * @returns {Array} 移除的元素数组
 */
export function pullSampleSize(array, n = 1) {
  const length = array == null ? 0 : array.length;
  if (!length) {
    return [];
  }
  
  n = clamp(toInteger(n), 0, length);
  const result = [];
  
  while (n-- > 0) {
    const sample = pullSample(array);
    if (sample !== undefined) {
      result.push(sample);
    }
  }
  
  return result;
}

// 辅助函数

/**
 * 复制数组
 * @param {Array} source - 源数组
 * @returns {Array} 复制的数组
 */
function copyArray(source) {
  let index = -1;
  const length = source.length;
  const result = Array(length);
  
  while (++index < length) {
    result[index] = source[index];
  }
  return result;
}

/**
 * 数组过滤函数
 * @param {Array} array - 数组
 * @param {Function} predicate - 判断函数
 * @returns {Array} 过滤后的数组
 */
function arrayFilter(array, predicate) {
  let index = -1;
  let resIndex = 0;
  const length = array == null ? 0 : array.length;
  const result = [];
  
  while (++index < length) {
    const value = array[index];
    if (predicate(value, index, array)) {
      result[resIndex++] = value;
    }
  }
  
  return result;
}

/**
 * 集合过滤函数
 * @param {Object} object - 对象
 * @param {Function} predicate - 判断函数
 * @returns {Array} 过滤后的数组
 */
function filter(object, predicate) {
  const result = [];
  if (object == null) {
    return result;
  }
  
  for (const key in object) {
    if (Object.prototype.hasOwnProperty.call(object, key)) {
      const value = object[key];
      if (predicate(value, key, object)) {
        result.push(value);
      }
    }
  }
  
  return result;
}

/**
 * 创建迭代函数
 * @param {*} func - 函数或其他值
 * @param {number} arity - 参数数量
 * @returns {Function} 迭代函数
 */
function iteratee(func, arity) {
  if (typeof func === 'function') {
    return func;
  }
  if (func == null) {
    return identity;
  }
  if (typeof func === 'object') {
    return Array.isArray(func) ? matchesProperty(func[0], func[1]) : matches(func);
  }
  return property(func);
}

/**
 * 恒等函数
 * @param {*} value - 值
 * @returns {*} 原值
 */
function identity(value) {
  return value;
}

/**
 * 创建属性匹配函数
 * @param {string} path - 属性路径
 * @param {*} srcValue - 要匹配的值
 * @returns {Function} 匹配函数
 */
function matchesProperty(path, srcValue) {
  return function(object) {
    return get(object, path) === srcValue;
  };
}

/**
 * 创建对象匹配函数
 * @param {Object} source - 源对象
 * @returns {Function} 匹配函数
 */
function matches(source) {
  return function(object) {
    for (const key in source) {
      if (Object.prototype.hasOwnProperty.call(source, key)) {
        if (object[key] !== source[key]) {
          return false;
        }
      }
    }
    return true;
  };
}

/**
 * 创建属性获取函数
 * @param {string} path - 属性路径
 * @returns {Function} 属性获取函数
 */
function property(path) {
  return function(object) {
    return get(object, path);
  };
}

/**
 * 获取对象属性值
 * @param {Object} object - 对象
 * @param {string} path - 属性路径
 * @returns {*} 属性值
 */
function get(object, path) {
  if (object == null) {
    return undefined;
  }
  
  const keys = Array.isArray(path) ? path : path.split('.');
  let result = object;
  
  for (const key of keys) {
    if (result == null) {
      return undefined;
    }
    result = result[key];
  }
  
  return result;
}

/**
 * 检查是否为迭代调用
 * @param {*} value - 值
 * @param {*} index - 索引
 * @param {*} object - 对象
 * @returns {boolean} 是否为迭代调用
 */
function isIterateeCall(value, index, object) {
  if (!isObject(object)) {
    return false;
  }
  const type = typeof index;
  if (type == 'number'
        ? (isArrayLike(object) && isIndex(index, object.length))
        : (type == 'string' && index in object)
      ) {
    return eq(object[index], value);
  }
  return false;
}

/**
 * 检查值是否为对象
 * @param {*} value - 值
 * @returns {boolean} 是否为对象
 */
function isObject(value) {
  const type = typeof value;
  return value != null && (type == 'object' || type == 'function');
}

/**
 * 检查值是否类似数组
 * @param {*} value - 值
 * @returns {boolean} 是否类似数组
 */
function isArrayLike(value) {
  return value != null && typeof value != 'function' && isLength(value.length);
}

/**
 * 检查值是否为有效长度
 * @param {*} value - 值
 * @returns {boolean} 是否为有效长度
 */
function isLength(value) {
  return typeof value == 'number' &&
    value > -1 && value % 1 == 0 && value <= Number.MAX_SAFE_INTEGER;
}

/**
 * 检查值是否为有效索引
 * @param {*} value - 值
 * @param {number} length - 长度
 * @returns {boolean} 是否为有效索引
 */
function isIndex(value, length) {
  const type = typeof value;
  length = length == null ? Number.MAX_SAFE_INTEGER : length;
  
  return !!length &&
    (type == 'number' || (type != 'symbol' && /^(?:0|[1-9]\d*)$/.test(value))) &&
    (value > -1 && value % 1 == 0 && value < length);
}

/**
 * 执行SameValueZero比较
 * @param {*} value - 值
 * @param {*} other - 另一个值
 * @returns {boolean} 是否相等
 */
function eq(value, other) {
  return value === other || (value !== value && other !== other);
}
