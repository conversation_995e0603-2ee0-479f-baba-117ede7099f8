/**
 * Set Cache实现
 * @description 重构自原始文件中的Set Cache实现，对应第1698-1708行
 * @original 原始代码行 1698-1708
 */

import MapCache from './map-cache.js';

/**
 * Set Cache的undefined占位符
 * @original var vsB = "__lodash_hash_undefined__";
 */
const SET_CACHE_UNDEFINED = "__lodash_hash_undefined__";

/**
 * Set Cache构造函数
 * @param {Array} values - 初始值数组
 * @original function lY1(A) { ... } (需要从上下文推断)
 */
export function SetCache(values) {
  let index = -1;
  const length = values == null ? 0 : values.length;
  
  this.__data__ = new MapCache();
  while (++index < length) {
    this.add(values[index]);
  }
}

/**
 * 向Set Cache中添加值
 * @param {*} value - 要添加的值
 * @returns {Object} Set Cache实例
 * @original function bsB(A) { return this.__data__.set(A, vsB), this; }
 */
function setCacheAdd(value) {
  this.__data__.set(value, SET_CACHE_UNDEFINED);
  return this;
}

/**
 * 检查Set Cache中是否存在指定值
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否存在该值
 * @original function fsB(A) { return this.__data__.has(A); }
 */
function setCacheHas(value) {
  return this.__data__.has(value);
}

// 设置SetCache原型方法
SetCache.prototype.add = SetCache.prototype.push = setCacheAdd;
SetCache.prototype.has = setCacheHas;

/**
 * 创建Set Cache实例
 * @param {Array} values - 初始值数组
 * @returns {SetCache} Set Cache实例
 */
export function createSetCache(values) {
  return new SetCache(values);
}

/**
 * 检查值是否为Set Cache实例
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为Set Cache实例
 */
export function isSetCache(value) {
  return value instanceof SetCache;
}

/**
 * Set Cache工具函数
 */
export const SetCacheUtils = {
  /**
   * 将数组转换为Set Cache
   * @param {Array} array - 要转换的数组
   * @returns {SetCache} Set Cache实例
   */
  fromArray(array) {
    return new SetCache(array);
  },

  /**
   * 将Set Cache转换为数组
   * @param {SetCache} setCache - Set Cache实例
   * @returns {Array} 值数组
   */
  toArray(setCache) {
    if (!isSetCache(setCache)) {
      throw new TypeError('Expected a SetCache instance');
    }
    
    const result = [];
    const data = setCache.__data__;
    
    // 遍历MapCache获取所有键（值存储为键）
    if (data && typeof data.keys === 'function') {
      const keys = data.keys();
      for (const key of keys) {
        result.push(key);
      }
    } else if (data && data.__data__) {
      // 手动遍历MapCache的内部结构
      const mapData = data.__data__;
      
      // 遍历hash存储
      if (mapData.hash && mapData.hash.__data__) {
        const hashData = mapData.hash.__data__;
        for (const key in hashData) {
          if (Object.prototype.hasOwnProperty.call(hashData, key)) {
            result.push(key);
          }
        }
      }
      
      // 遍历string存储
      if (mapData.string && mapData.string.__data__) {
        const stringData = mapData.string.__data__;
        for (const key in stringData) {
          if (Object.prototype.hasOwnProperty.call(stringData, key)) {
            result.push(key);
          }
        }
      }
      
      // 遍历map存储
      if (mapData.map && mapData.map.__data__) {
        const mapEntries = mapData.map.__data__;
        for (let i = 0; i < mapEntries.length; i++) {
          result.push(mapEntries[i][0]);
        }
      }
    }
    
    return result;
  },

  /**
   * 获取Set Cache的大小
   * @param {SetCache} setCache - Set Cache实例
   * @returns {number} 大小
   */
  size(setCache) {
    if (!isSetCache(setCache)) {
      throw new TypeError('Expected a SetCache instance');
    }
    
    return setCache.__data__.size || 0;
  },

  /**
   * 清空Set Cache
   * @param {SetCache} setCache - Set Cache实例
   * @returns {SetCache} 清空后的Set Cache实例
   */
  clear(setCache) {
    if (!isSetCache(setCache)) {
      throw new TypeError('Expected a SetCache instance');
    }
    
    setCache.__data__.clear();
    return setCache;
  },

  /**
   * 从Set Cache中删除值
   * @param {SetCache} setCache - Set Cache实例
   * @param {*} value - 要删除的值
   * @returns {boolean} 是否删除成功
   */
  delete(setCache, value) {
    if (!isSetCache(setCache)) {
      throw new TypeError('Expected a SetCache instance');
    }
    
    return setCache.__data__.delete(value);
  },

  /**
   * 遍历Set Cache
   * @param {SetCache} setCache - Set Cache实例
   * @param {Function} iteratee - 遍历函数
   */
  forEach(setCache, iteratee) {
    if (!isSetCache(setCache)) {
      throw new TypeError('Expected a SetCache instance');
    }
    
    const values = this.toArray(setCache);
    for (let i = 0; i < values.length; i++) {
      const value = values[i];
      if (iteratee(value, value, setCache) === false) {
        break;
      }
    }
  },

  /**
   * 检查Set Cache是否为空
   * @param {SetCache} setCache - Set Cache实例
   * @returns {boolean} 是否为空
   */
  isEmpty(setCache) {
    if (!isSetCache(setCache)) {
      throw new TypeError('Expected a SetCache instance');
    }
    
    return this.size(setCache) === 0;
  },

  /**
   * 克隆Set Cache
   * @param {SetCache} setCache - 要克隆的Set Cache实例
   * @returns {SetCache} 克隆的Set Cache实例
   */
  clone(setCache) {
    if (!isSetCache(setCache)) {
      throw new TypeError('Expected a SetCache instance');
    }
    
    return new SetCache(this.toArray(setCache));
  },

  /**
   * 合并多个Set Cache
   * @param {...SetCache} setCaches - 要合并的Set Cache实例
   * @returns {SetCache} 合并后的Set Cache实例
   */
  union(...setCaches) {
    const result = new SetCache();
    
    for (const setCache of setCaches) {
      if (isSetCache(setCache)) {
        const values = this.toArray(setCache);
        for (const value of values) {
          result.add(value);
        }
      }
    }
    
    return result;
  },

  /**
   * 获取多个Set Cache的交集
   * @param {SetCache} setCache1 - 第一个Set Cache
   * @param {SetCache} setCache2 - 第二个Set Cache
   * @returns {SetCache} 交集Set Cache
   */
  intersection(setCache1, setCache2) {
    if (!isSetCache(setCache1) || !isSetCache(setCache2)) {
      throw new TypeError('Expected SetCache instances');
    }
    
    const result = new SetCache();
    const values1 = this.toArray(setCache1);
    
    for (const value of values1) {
      if (setCache2.has(value)) {
        result.add(value);
      }
    }
    
    return result;
  },

  /**
   * 获取两个Set Cache的差集
   * @param {SetCache} setCache1 - 第一个Set Cache
   * @param {SetCache} setCache2 - 第二个Set Cache
   * @returns {SetCache} 差集Set Cache
   */
  difference(setCache1, setCache2) {
    if (!isSetCache(setCache1) || !isSetCache(setCache2)) {
      throw new TypeError('Expected SetCache instances');
    }
    
    const result = new SetCache();
    const values1 = this.toArray(setCache1);
    
    for (const value of values1) {
      if (!setCache2.has(value)) {
        result.add(value);
      }
    }
    
    return result;
  }
};

// 导出SetCache构造函数作为默认导出
export default SetCache;
