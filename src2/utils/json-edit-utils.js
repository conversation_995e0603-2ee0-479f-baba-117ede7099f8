/**
 * JSON编辑工具函数
 * @description 重构自原始文件中的JSON编辑代码，对应第4196-4300行
 * @original 原始代码行 4196-4300
 */

import { findNodeByPath, getJSONType, jsonParser } from './json-parser-utils.js';

/**
 * JSON编辑操作
 * @param {string} text - JSON文本
 * @param {Array} path - 路径数组
 * @param {*} value - 新值（undefined表示删除）
 * @param {Object} options - 编辑选项
 * @returns {Array} 编辑数组
 * @original nIA函数
 */
export function editJSON(text, path, value, options = {}) {
  const pathCopy = path.slice();
  const root = jsonParser.parseTree(text, []);
  let targetNode = undefined;
  let parentNode = undefined;

  // 查找目标节点和父节点
  while (pathCopy.length > 0) {
    const segment = pathCopy.pop();
    parentNode = findNodeByPath(root, pathCopy);
    
    if (parentNode === undefined && value !== undefined) {
      // 如果父节点不存在且要插入值，创建中间结构
      if (typeof segment === "string") {
        value = { [segment]: value };
      } else {
        value = [value];
      }
    } else {
      break;
    }
  }

  if (!parentNode) {
    // 根节点操作
    if (value === undefined) {
      throw new Error("Cannot delete in empty document");
    }
    
    return applyEdit(text, {
      offset: root ? root.offset : 0,
      length: root ? root.length : 0,
      content: JSON.stringify(value)
    }, options);
  }

  const lastSegment = pathCopy[pathCopy.length - 1] || path[path.length - 1];

  if (parentNode.type === "object" && typeof lastSegment === "string" && Array.isArray(parentNode.children)) {
    return handleObjectEdit(text, parentNode, lastSegment, value, options);
  } else if (parentNode.type === "array" && typeof lastSegment === "number" && Array.isArray(parentNode.children)) {
    return handleArrayEdit(text, parentNode, lastSegment, value, options);
  }

  return [];
}

/**
 * 处理对象编辑
 * @param {string} text - JSON文本
 * @param {Object} objectNode - 对象节点
 * @param {string} key - 属性键
 * @param {*} value - 新值
 * @param {Object} options - 选项
 * @returns {Array} 编辑数组
 * @private
 */
function handleObjectEdit(text, objectNode, key, value, options) {
  const existingProperty = findNodeByPath(objectNode, [key]);

  if (existingProperty !== undefined) {
    // 属性已存在
    if (value === undefined) {
      // 删除属性
      return deleteObjectProperty(text, objectNode, existingProperty, options);
    } else {
      // 更新属性值
      return updatePropertyValue(text, existingProperty, value, options);
    }
  } else {
    // 属性不存在
    if (value === undefined) {
      return []; // 删除不存在的属性，无操作
    }
    
    // 插入新属性
    return insertObjectProperty(text, objectNode, key, value, options);
  }
}

/**
 * 处理数组编辑
 * @param {string} text - JSON文本
 * @param {Object} arrayNode - 数组节点
 * @param {number} index - 数组索引
 * @param {*} value - 新值
 * @param {Object} options - 选项
 * @returns {Array} 编辑数组
 * @private
 */
function handleArrayEdit(text, arrayNode, index, value, options) {
  if (index === -1) {
    // 追加到数组末尾
    return appendToArray(text, arrayNode, value, options);
  } else if (value === undefined && arrayNode.children.length >= 0) {
    // 删除数组元素
    return deleteArrayElement(text, arrayNode, index, options);
  } else if (value !== undefined) {
    // 更新或插入数组元素
    if (index < arrayNode.children.length) {
      return updateArrayElement(text, arrayNode, index, value, options);
    } else {
      return insertArrayElement(text, arrayNode, index, value, options);
    }
  }

  return [];
}

/**
 * 删除对象属性
 * @param {string} text - JSON文本
 * @param {Object} objectNode - 对象节点
 * @param {Object} propertyNode - 属性节点
 * @param {Object} options - 选项
 * @returns {Array} 编辑数组
 * @private
 */
function deleteObjectProperty(text, objectNode, propertyNode, options) {
  if (!propertyNode.parent) {
    throw new Error("Malformed AST");
  }

  const propertyIndex = objectNode.children.indexOf(propertyNode.parent);
  let startOffset, endOffset;

  if (propertyIndex > 0) {
    // 不是第一个属性，删除前面的逗号
    const previousProperty = objectNode.children[propertyIndex - 1];
    startOffset = previousProperty.offset + previousProperty.length;
    endOffset = propertyNode.parent.offset + propertyNode.parent.length;
  } else {
    // 是第一个属性
    startOffset = objectNode.offset + 1;
    endOffset = propertyNode.parent.offset + propertyNode.parent.length;
    
    if (objectNode.children.length > 1) {
      // 如果还有其他属性，删除后面的逗号
      endOffset = objectNode.children[1].offset;
    }
  }

  return applyEdit(text, {
    offset: startOffset,
    length: endOffset - startOffset,
    content: ""
  }, options);
}

/**
 * 更新属性值
 * @param {string} text - JSON文本
 * @param {Object} propertyNode - 属性节点
 * @param {*} value - 新值
 * @param {Object} options - 选项
 * @returns {Array} 编辑数组
 * @private
 */
function updatePropertyValue(text, propertyNode, value, options) {
  return applyEdit(text, {
    offset: propertyNode.offset,
    length: propertyNode.length,
    content: JSON.stringify(value)
  }, options);
}

/**
 * 插入对象属性
 * @param {string} text - JSON文本
 * @param {Object} objectNode - 对象节点
 * @param {string} key - 属性键
 * @param {*} value - 属性值
 * @param {Object} options - 选项
 * @returns {Array} 编辑数组
 * @private
 */
function insertObjectProperty(text, objectNode, key, value, options) {
  const propertyContent = `${JSON.stringify(key)}: ${JSON.stringify(value)}`;
  
  // 确定插入位置
  const insertionIndex = options.getInsertionIndex ? 
    options.getInsertionIndex(objectNode.children.map(child => child.children[0].value)) : 
    objectNode.children.length;

  let edit;
  if (insertionIndex > 0) {
    // 插入到现有属性之后
    const previousProperty = objectNode.children[insertionIndex - 1];
    edit = {
      offset: previousProperty.offset + previousProperty.length,
      length: 0,
      content: "," + propertyContent
    };
  } else if (objectNode.children.length === 0) {
    // 空对象
    edit = {
      offset: objectNode.offset + 1,
      length: 0,
      content: propertyContent
    };
  } else {
    // 插入到第一个位置
    edit = {
      offset: objectNode.offset + 1,
      length: 0,
      content: propertyContent + ","
    };
  }

  return applyEdit(text, edit, options);
}

/**
 * 追加到数组末尾
 * @param {string} text - JSON文本
 * @param {Object} arrayNode - 数组节点
 * @param {*} value - 新值
 * @param {Object} options - 选项
 * @returns {Array} 编辑数组
 * @private
 */
function appendToArray(text, arrayNode, value, options) {
  const elementContent = JSON.stringify(value);
  let edit;

  if (arrayNode.children.length === 0) {
    // 空数组
    edit = {
      offset: arrayNode.offset + 1,
      length: 0,
      content: elementContent
    };
  } else {
    // 追加到最后一个元素之后
    const lastElement = arrayNode.children[arrayNode.children.length - 1];
    edit = {
      offset: lastElement.offset + lastElement.length,
      length: 0,
      content: "," + elementContent
    };
  }

  return applyEdit(text, edit, options);
}

/**
 * 删除数组元素
 * @param {string} text - JSON文本
 * @param {Object} arrayNode - 数组节点
 * @param {number} index - 索引
 * @param {Object} options - 选项
 * @returns {Array} 编辑数组
 * @private
 */
function deleteArrayElement(text, arrayNode, index, options) {
  const elementToDelete = arrayNode.children[index];
  let edit;

  if (arrayNode.children.length === 1) {
    // 删除唯一元素
    edit = {
      offset: arrayNode.offset + 1,
      length: arrayNode.length - 2,
      content: ""
    };
  } else if (arrayNode.children.length - 1 === index) {
    // 删除最后一个元素
    const previousElement = arrayNode.children[index - 1];
    const startOffset = previousElement.offset + previousElement.length;
    const endOffset = arrayNode.offset + arrayNode.length;
    
    edit = {
      offset: startOffset,
      length: endOffset - 2 - startOffset,
      content: ""
    };
  } else {
    // 删除中间元素
    edit = {
      offset: elementToDelete.offset,
      length: arrayNode.children[index + 1].offset - elementToDelete.offset,
      content: ""
    };
  }

  return applyEdit(text, edit, options);
}

/**
 * 更新数组元素
 * @param {string} text - JSON文本
 * @param {Object} arrayNode - 数组节点
 * @param {number} index - 索引
 * @param {*} value - 新值
 * @param {Object} options - 选项
 * @returns {Array} 编辑数组
 * @private
 */
function updateArrayElement(text, arrayNode, index, value, options) {
  const elementToUpdate = arrayNode.children[index];
  
  return applyEdit(text, {
    offset: elementToUpdate.offset,
    length: elementToUpdate.length,
    content: JSON.stringify(value)
  }, options);
}

/**
 * 插入数组元素
 * @param {string} text - JSON文本
 * @param {Object} arrayNode - 数组节点
 * @param {number} index - 索引
 * @param {*} value - 新值
 * @param {Object} options - 选项
 * @returns {Array} 编辑数组
 * @private
 */
function insertArrayElement(text, arrayNode, index, value, options) {
  // 简化实现：追加到数组末尾
  return appendToArray(text, arrayNode, value, options);
}

/**
 * 应用编辑
 * @param {string} text - 原始文本
 * @param {Object} edit - 编辑对象
 * @param {Object} options - 选项
 * @returns {Array} 编辑数组
 * @original Kh函数
 */
function applyEdit(text, edit, options) {
  // @todo: 实现Kh函数的实际逻辑
  // 这里返回简化的编辑数组
  return [edit];
}

/**
 * JSON编辑工具类
 * @description 提供高级JSON编辑功能
 */
export class JSONEditUtils {
  /**
   * 设置JSON值
   * @param {string} text - JSON文本
   * @param {Array} path - 路径数组
   * @param {*} value - 新值
   * @param {Object} options - 选项
   * @returns {Array} 编辑数组
   */
  static setValue(text, path, value, options = {}) {
    return editJSON(text, path, value, options);
  }

  /**
   * 删除JSON值
   * @param {string} text - JSON文本
   * @param {Array} path - 路径数组
   * @param {Object} options - 选项
   * @returns {Array} 编辑数组
   */
  static deleteValue(text, path, options = {}) {
    return editJSON(text, path, undefined, options);
  }

  /**
   * 插入JSON值
   * @param {string} text - JSON文本
   * @param {Array} path - 路径数组
   * @param {*} value - 新值
   * @param {Object} options - 选项
   * @returns {Array} 编辑数组
   */
  static insertValue(text, path, value, options = {}) {
    return editJSON(text, path, value, options);
  }

  /**
   * 应用编辑到文本
   * @param {string} text - 原始文本
   * @param {Array} edits - 编辑数组
   * @returns {string} 编辑后的文本
   */
  static applyEdits(text, edits) {
    // 按偏移量倒序排序，从后往前应用编辑
    const sortedEdits = edits.slice().sort((a, b) => b.offset - a.offset);
    
    let result = text;
    for (const edit of sortedEdits) {
      result = result.substring(0, edit.offset) + 
               edit.content + 
               result.substring(edit.offset + edit.length);
    }
    
    return result;
  }

  /**
   * 批量编辑JSON
   * @param {string} text - JSON文本
   * @param {Array} operations - 操作数组
   * @param {Object} options - 选项
   * @returns {string} 编辑后的文本
   */
  static batchEdit(text, operations, options = {}) {
    let allEdits = [];
    
    for (const operation of operations) {
      const { type, path, value } = operation;
      let edits = [];
      
      switch (type) {
        case 'set':
          edits = this.setValue(text, path, value, options);
          break;
        case 'delete':
          edits = this.deleteValue(text, path, options);
          break;
        case 'insert':
          edits = this.insertValue(text, path, value, options);
          break;
      }
      
      allEdits.push(...edits);
    }
    
    return this.applyEdits(text, allEdits);
  }
}

// 导出便捷函数
export { editJSON as edit };
export const JSONEdit = JSONEditUtils;
