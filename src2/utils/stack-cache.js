/**
 * Stack Cache实现
 * @description 重构自原始文件中的Stack Cache实现，对应第1643-1676行
 * @original 原始代码行 1643-1676
 */

import ListCache from './list-cache.js';
import MapCache from './map-cache.js';

/**
 * Stack Cache的大小阈值
 * @original var rnB = 200;
 */
const LARGE_ARRAY_SIZE = 200;

/**
 * Stack Cache构造函数
 * @param {Array} entries - 初始键值对数组
 * @original function mc(A) { ... } (需要从上下文推断)
 */
export function Stack(entries) {
  const data = this.__data__ = new ListCache(entries);
  this.size = data.size;
}

/**
 * 清空Stack Cache
 * @original function inB() { this.__data__ = new jj(), this.size = 0; }
 */
function stackClear() {
  this.__data__ = new ListCache();
  this.size = 0;
}

/**
 * 删除Stack Cache中的键值对
 * @param {*} key - 要删除的键
 * @returns {boolean} 是否删除成功
 * @original function nnB(A) { var B = this.__data__, Q = B.delete(A); return this.size = B.size, Q; }
 */
function stackDelete(key) {
  const data = this.__data__;
  const result = data.delete(key);
  
  this.size = data.size;
  return result;
}

/**
 * 获取Stack Cache中指定键的值
 * @param {*} key - 要获取的键
 * @returns {*} 键对应的值
 * @original function anB(A) { return this.__data__.get(A); }
 */
function stackGet(key) {
  return this.__data__.get(key);
}

/**
 * 检查Stack Cache中是否存在指定键
 * @param {*} key - 要检查的键
 * @returns {boolean} 是否存在该键
 * @original function snB(A) { return this.__data__.has(A); }
 */
function stackHas(key) {
  return this.__data__.has(key);
}

/**
 * 设置Stack Cache中的键值对
 * @param {*} key - 要设置的键
 * @param {*} value - 要设置的值
 * @returns {Object} Stack Cache实例
 * @original function onB(A, B) { var Q = this.__data__; if (Q instanceof jj) { var D = Q.__data__; if (!yj || D.length < rnB - 1) return D.push([A, B]), this.size = ++Q.size, this; Q = this.__data__ = new eb(D); } return Q.set(A, B), this.size = Q.size, this; }
 */
function stackSet(key, value) {
  let data = this.__data__;
  
  if (data instanceof ListCache) {
    const pairs = data.__data__;
    
    // 如果没有原生Map或者数据量还不够大，继续使用ListCache
    if (!Map || (pairs.length < LARGE_ARRAY_SIZE - 1)) {
      pairs.push([key, value]);
      this.size = ++data.size;
      return this;
    }
    
    // 数据量大时，升级为MapCache
    data = this.__data__ = new MapCache(pairs);
  }
  
  data.set(key, value);
  this.size = data.size;
  return this;
}

// 设置Stack原型方法
Stack.prototype.clear = stackClear;
Stack.prototype.delete = stackDelete;
Stack.prototype.get = stackGet;
Stack.prototype.has = stackHas;
Stack.prototype.set = stackSet;

/**
 * 创建Stack Cache实例
 * @param {Array} entries - 初始键值对数组
 * @returns {Stack} Stack Cache实例
 */
export function createStack(entries) {
  return new Stack(entries);
}

/**
 * 检查值是否为Stack Cache实例
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为Stack Cache实例
 */
export function isStack(value) {
  return value instanceof Stack;
}

/**
 * Stack Cache工具函数
 */
export const StackUtils = {
  /**
   * 将对象转换为Stack Cache
   * @param {Object} object - 要转换的对象
   * @returns {Stack} Stack Cache实例
   */
  fromObject(object) {
    const entries = [];
    for (const key in object) {
      if (Object.prototype.hasOwnProperty.call(object, key)) {
        entries.push([key, object[key]]);
      }
    }
    return new Stack(entries);
  },

  /**
   * 将Stack Cache转换为普通对象
   * @param {Stack} stack - Stack Cache实例
   * @returns {Object} 普通对象
   */
  toObject(stack) {
    if (!isStack(stack)) {
      throw new TypeError('Expected a Stack instance');
    }
    
    const result = {};
    const data = stack.__data__;
    
    if (data instanceof ListCache) {
      // ListCache的情况
      const pairs = data.__data__;
      for (let i = 0; i < pairs.length; i++) {
        const [key, value] = pairs[i];
        result[key] = value;
      }
    } else if (data instanceof MapCache) {
      // MapCache的情况
      const keys = data.__data__.hash ? Object.keys(data.__data__.hash.__data__) : [];
      keys.forEach(key => {
        result[key] = data.get(key);
      });
    }
    
    return result;
  },

  /**
   * 获取Stack Cache的所有键
   * @param {Stack} stack - Stack Cache实例
   * @returns {Array} 键数组
   */
  keys(stack) {
    if (!isStack(stack)) {
      throw new TypeError('Expected a Stack instance');
    }
    
    const keys = [];
    const data = stack.__data__;
    
    if (data instanceof ListCache) {
      const pairs = data.__data__;
      for (let i = 0; i < pairs.length; i++) {
        keys.push(pairs[i][0]);
      }
    } else if (data instanceof MapCache) {
      // 使用MapCache的keys方法
      return data.keys ? data.keys() : [];
    }
    
    return keys;
  },

  /**
   * 获取Stack Cache的所有值
   * @param {Stack} stack - Stack Cache实例
   * @returns {Array} 值数组
   */
  values(stack) {
    if (!isStack(stack)) {
      throw new TypeError('Expected a Stack instance');
    }
    
    const values = [];
    const data = stack.__data__;
    
    if (data instanceof ListCache) {
      const pairs = data.__data__;
      for (let i = 0; i < pairs.length; i++) {
        values.push(pairs[i][1]);
      }
    } else if (data instanceof MapCache) {
      // 使用MapCache的values方法
      return data.values ? data.values() : [];
    }
    
    return values;
  },

  /**
   * 获取Stack Cache的所有键值对
   * @param {Stack} stack - Stack Cache实例
   * @returns {Array} 键值对数组
   */
  entries(stack) {
    if (!isStack(stack)) {
      throw new TypeError('Expected a Stack instance');
    }
    
    const data = stack.__data__;
    
    if (data instanceof ListCache) {
      return [...data.__data__];
    } else if (data instanceof MapCache) {
      // 使用MapCache的entries方法
      return data.entries ? data.entries() : [];
    }
    
    return [];
  },

  /**
   * 遍历Stack Cache
   * @param {Stack} stack - Stack Cache实例
   * @param {Function} iteratee - 遍历函数
   */
  forEach(stack, iteratee) {
    if (!isStack(stack)) {
      throw new TypeError('Expected a Stack instance');
    }
    
    const data = stack.__data__;
    
    if (data instanceof ListCache) {
      const pairs = data.__data__;
      for (let i = 0; i < pairs.length; i++) {
        const [key, value] = pairs[i];
        if (iteratee(value, key, stack) === false) {
          break;
        }
      }
    } else if (data instanceof MapCache) {
      // 遍历MapCache
      const entries = this.entries(stack);
      for (const [key, value] of entries) {
        if (iteratee(value, key, stack) === false) {
          break;
        }
      }
    }
  },

  /**
   * 克隆Stack Cache
   * @param {Stack} stack - 要克隆的Stack Cache实例
   * @returns {Stack} 克隆的Stack Cache实例
   */
  clone(stack) {
    if (!isStack(stack)) {
      throw new TypeError('Expected a Stack instance');
    }
    
    return new Stack(this.entries(stack));
  },

  /**
   * 检查Stack Cache是否为空
   * @param {Stack} stack - Stack Cache实例
   * @returns {boolean} 是否为空
   */
  isEmpty(stack) {
    if (!isStack(stack)) {
      throw new TypeError('Expected a Stack instance');
    }
    
    return stack.size === 0;
  },

  /**
   * 获取Stack Cache的大小
   * @param {Stack} stack - Stack Cache实例
   * @returns {number} 大小
   */
  size(stack) {
    if (!isStack(stack)) {
      throw new TypeError('Expected a Stack instance');
    }
    
    return stack.size;
  },

  /**
   * 清空Stack Cache
   * @param {Stack} stack - Stack Cache实例
   * @returns {Stack} 清空后的Stack Cache实例
   */
  clear(stack) {
    if (!isStack(stack)) {
      throw new TypeError('Expected a Stack instance');
    }
    
    stack.clear();
    return stack;
  }
};

// 导出Stack构造函数作为默认导出
export default Stack;
