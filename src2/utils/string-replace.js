/**
 * 字符串替换工具函数
 * @description 重构自原始文件中的字符串替换函数，对应第2546-2600行
 * @original 原始代码行 2546-2600
 */

/**
 * 字符串替换函数（支持多次替换）
 * @param {string} str - 源字符串
 * @param {string} searchValue - 要搜索的字符串
 * @param {string} replaceValue - 替换字符串
 * @returns {string} 替换后的字符串
 * @original function _o0(A, B, Q) { ... }
 */
export function replaceAll(str, searchValue, replaceValue) {
  let index = str.indexOf(searchValue);
  if (index === -1) return str;
  
  const searchLength = searchValue.length;
  let replaceCount = 0;
  let result = str;
  
  while (index !== -1) {
    result = result.slice(0, index) + replaceValue + result.slice(index + searchLength);
    replaceCount++;
    
    // 从替换后的位置继续搜索
    index = result.indexOf(searchValue, index + replaceValue.length);
  }
  
  return result;
}

/**
 * 字符串替换函数（限制替换次数）
 * @param {string} str - 源字符串
 * @param {string} searchValue - 要搜索的字符串
 * @param {string} replaceValue - 替换字符串
 * @param {number} maxReplacements - 最大替换次数
 * @returns {string} 替换后的字符串
 */
export function replaceN(str, searchValue, replaceValue, maxReplacements) {
  if (maxReplacements <= 0) return str;
  
  let index = str.indexOf(searchValue);
  if (index === -1) return str;
  
  const searchLength = searchValue.length;
  let replaceCount = 0;
  let result = str;
  
  while (index !== -1 && replaceCount < maxReplacements) {
    result = result.slice(0, index) + replaceValue + result.slice(index + searchLength);
    replaceCount++;
    
    // 从替换后的位置继续搜索
    index = result.indexOf(searchValue, index + replaceValue.length);
  }
  
  return result;
}

/**
 * 字符串替换函数（只替换第一个匹配项）
 * @param {string} str - 源字符串
 * @param {string} searchValue - 要搜索的字符串
 * @param {string} replaceValue - 替换字符串
 * @returns {string} 替换后的字符串
 */
export function replaceFirst(str, searchValue, replaceValue) {
  return replaceN(str, searchValue, replaceValue, 1);
}

/**
 * 字符串替换函数（从右向左替换）
 * @param {string} str - 源字符串
 * @param {string} searchValue - 要搜索的字符串
 * @param {string} replaceValue - 替换字符串
 * @param {number} maxReplacements - 最大替换次数，默认为1
 * @returns {string} 替换后的字符串
 */
export function replaceFromRight(str, searchValue, replaceValue, maxReplacements = 1) {
  if (maxReplacements <= 0) return str;
  
  let index = str.lastIndexOf(searchValue);
  if (index === -1) return str;
  
  const searchLength = searchValue.length;
  let replaceCount = 0;
  let result = str;
  
  while (index !== -1 && replaceCount < maxReplacements) {
    result = result.slice(0, index) + replaceValue + result.slice(index + searchLength);
    replaceCount++;
    
    // 从当前位置向左继续搜索
    index = result.lastIndexOf(searchValue, index - 1);
  }
  
  return result;
}

/**
 * 多模式字符串替换
 * @param {string} str - 源字符串
 * @param {Object} replacements - 替换映射对象 {searchValue: replaceValue}
 * @returns {string} 替换后的字符串
 */
export function replaceMultiple(str, replacements) {
  let result = str;
  
  for (const [searchValue, replaceValue] of Object.entries(replacements)) {
    result = replaceAll(result, searchValue, replaceValue);
  }
  
  return result;
}

/**
 * 使用正则表达式替换
 * @param {string} str - 源字符串
 * @param {RegExp|string} pattern - 正则表达式或字符串模式
 * @param {string|Function} replacement - 替换字符串或替换函数
 * @returns {string} 替换后的字符串
 */
export function replaceRegex(str, pattern, replacement) {
  if (typeof pattern === 'string') {
    pattern = new RegExp(pattern, 'g');
  }
  
  return str.replace(pattern, replacement);
}

/**
 * 大小写不敏感的字符串替换
 * @param {string} str - 源字符串
 * @param {string} searchValue - 要搜索的字符串
 * @param {string} replaceValue - 替换字符串
 * @returns {string} 替换后的字符串
 */
export function replaceIgnoreCase(str, searchValue, replaceValue) {
  const regex = new RegExp(escapeRegExp(searchValue), 'gi');
  return str.replace(regex, replaceValue);
}

/**
 * 转义正则表达式特殊字符
 * @param {string} str - 要转义的字符串
 * @returns {string} 转义后的字符串
 */
export function escapeRegExp(str) {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * 字符串模板替换
 * @param {string} template - 模板字符串，使用 {{key}} 作为占位符
 * @param {Object} values - 替换值对象
 * @returns {string} 替换后的字符串
 */
export function replaceTemplate(template, values) {
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return values.hasOwnProperty(key) ? values[key] : match;
  });
}

/**
 * 字符串模板替换（支持嵌套属性）
 * @param {string} template - 模板字符串，使用 {{key.subkey}} 作为占位符
 * @param {Object} values - 替换值对象
 * @returns {string} 替换后的字符串
 */
export function replaceNestedTemplate(template, values) {
  return template.replace(/\{\{([\w.]+)\}\}/g, (match, path) => {
    const keys = path.split('.');
    let value = values;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return match; // 如果路径不存在，保留原始占位符
      }
    }
    
    return value;
  });
}

/**
 * 条件字符串替换
 * @param {string} str - 源字符串
 * @param {string} searchValue - 要搜索的字符串
 * @param {string} replaceValue - 替换字符串
 * @param {Function} condition - 条件函数，接收匹配的字符串和索引
 * @returns {string} 替换后的字符串
 */
export function replaceConditional(str, searchValue, replaceValue, condition) {
  let index = 0;
  let result = str;
  let offset = 0;
  
  while ((index = str.indexOf(searchValue, index)) !== -1) {
    if (condition(searchValue, index)) {
      const actualIndex = index + offset;
      result = result.slice(0, actualIndex) + replaceValue + result.slice(actualIndex + searchValue.length);
      offset += replaceValue.length - searchValue.length;
    }
    index += searchValue.length;
  }
  
  return result;
}

/**
 * 字符串插值替换（支持表达式）
 * @param {string} template - 模板字符串，使用 ${expression} 作为占位符
 * @param {Object} context - 上下文对象
 * @returns {string} 替换后的字符串
 */
export function interpolate(template, context) {
  return template.replace(/\$\{([^}]+)\}/g, (match, expression) => {
    try {
      // 创建一个安全的求值函数
      const func = new Function(...Object.keys(context), `return ${expression}`);
      return func(...Object.values(context));
    } catch (error) {
      console.warn(`Failed to evaluate expression: ${expression}`, error);
      return match;
    }
  });
}

/**
 * 批量字符串替换工具
 */
export class StringReplacer {
  constructor() {
    this.replacements = new Map();
  }

  /**
   * 添加替换规则
   * @param {string} searchValue - 搜索值
   * @param {string} replaceValue - 替换值
   * @returns {StringReplacer} 链式调用
   */
  add(searchValue, replaceValue) {
    this.replacements.set(searchValue, replaceValue);
    return this;
  }

  /**
   * 批量添加替换规则
   * @param {Object} replacements - 替换规则对象
   * @returns {StringReplacer} 链式调用
   */
  addMultiple(replacements) {
    for (const [searchValue, replaceValue] of Object.entries(replacements)) {
      this.replacements.set(searchValue, replaceValue);
    }
    return this;
  }

  /**
   * 移除替换规则
   * @param {string} searchValue - 搜索值
   * @returns {StringReplacer} 链式调用
   */
  remove(searchValue) {
    this.replacements.delete(searchValue);
    return this;
  }

  /**
   * 清空所有替换规则
   * @returns {StringReplacer} 链式调用
   */
  clear() {
    this.replacements.clear();
    return this;
  }

  /**
   * 执行替换
   * @param {string} str - 源字符串
   * @returns {string} 替换后的字符串
   */
  replace(str) {
    let result = str;
    
    for (const [searchValue, replaceValue] of this.replacements) {
      result = replaceAll(result, searchValue, replaceValue);
    }
    
    return result;
  }

  /**
   * 获取所有替换规则
   * @returns {Object} 替换规则对象
   */
  getRules() {
    return Object.fromEntries(this.replacements);
  }

  /**
   * 获取替换规则数量
   * @returns {number} 规则数量
   */
  size() {
    return this.replacements.size;
  }
}

/**
 * 创建字符串替换器实例
 * @param {Object} initialReplacements - 初始替换规则
 * @returns {StringReplacer} 字符串替换器实例
 */
export function createReplacer(initialReplacements = {}) {
  const replacer = new StringReplacer();
  if (Object.keys(initialReplacements).length > 0) {
    replacer.addMultiple(initialReplacements);
  }
  return replacer;
}
