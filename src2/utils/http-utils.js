/**
 * HTTP工具函数
 * @description 重构自原始文件中的HTTP相关函数，对应第2176-2250行
 * @original 原始代码行 2176-2250
 */

/**
 * 进程信号列表
 * @description 支持的进程信号列表
 * @original Zf数组的定义
 */
export const PROCESS_SIGNALS = ["SIGHUP", "SIGINT", "SIGTERM"];

// 根据平台添加额外的信号
if (process.platform !== "win32") {
  PROCESS_SIGNALS.push(
    "SIGALRM", "SIGABRT", "SIGVTALRM", "SIGXCPU", 
    "SIGXFSZ", "SIGUSR2", "SIGTRAP", "SIGSYS", 
    "SIGQUIT", "SIGIOT"
  );
}

if (process.platform === "linux") {
  PROCESS_SIGNALS.push("SIGIO", "SIGPOLL", "SIGPWR", "SIGSTKFLT");
}

/**
 * Axios错误代码列表
 * @description Axios支持的错误代码
 * @original 第2199行的错误代码数组
 */
export const AXIOS_ERROR_CODES = [
  "ERR_BAD_OPTION_VALUE",
  "ERR_BAD_OPTION", 
  "ECONNABORTED",
  "ETIMEDOUT",
  "ERR_NETWORK",
  "ERR_FR_TOO_MANY_REDIRECTS",
  "ERR_DEPRECATED",
  "ERR_BAD_RESPONSE",
  "ERR_BAD_REQUEST",
  "ERR_CANCELED",
  "ERR_NOT_SUPPORT",
  "ERR_INVALID_URL"
];

/**
 * 自定义错误类
 * @description 扩展Error类，添加额外的属性
 * @original vl类的实现
 */
export class CustomError extends Error {
  constructor(message, config, code, request, response) {
    super(message);
    this.name = 'CustomError';
    this.config = config;
    this.code = code;
    this.request = request;
    this.response = response;
    this.isAxiosError = true;
    this.status = response && response.status ? response.status : null;
  }

  /**
   * 转换为JSON对象
   * @returns {Object} JSON表示
   * @original toJSON方法的实现
   */
  toJSON() {
    return {
      message: this.message,
      name: this.name,
      description: this.description,
      number: this.number,
      fileName: this.fileName,
      lineNumber: this.lineNumber,
      columnNumber: this.columnNumber,
      stack: this.stack,
      config: this.toJSONObject(this.config),
      code: this.code,
      status: this.status
    };
  }

  /**
   * 将对象转换为JSON对象
   * @param {Object} obj - 要转换的对象
   * @returns {Object} JSON对象
   */
  toJSONObject(obj) {
    const stack = {};
    const visit = (source, i) => {
      if (source === null) return null;
      if (typeof source !== 'object') return source;
      if (stack[i]) return {};
      
      let cloned = {};
      if (Array.isArray(source)) {
        cloned = [];
      }
      
      stack[i] = cloned;
      
      for (const key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          cloned[key] = visit(source[key], i + 1);
        }
      }
      
      delete stack[i];
      return cloned;
    };
    
    return visit(obj, 0);
  }
}

/**
 * URL编码函数
 * @param {string} str - 要编码的字符串
 * @returns {string} 编码后的字符串
 * @original Rn0函数的实现
 */
export function encode(str) {
  const replacements = {
    "!": "%21",
    "'": "%27",
    "(": "%28",
    ")": "%29",
    "~": "%7E",
    "%20": "+",
    "%00": "\x00"
  };
  
  return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function(match) {
    return replacements[match];
  });
}

/**
 * URL搜索参数构建器
 * @description 用于构建URL查询字符串的类
 * @original On0类的实现
 */
export class URLSearchParams {
  constructor() {
    this._pairs = [];
  }

  /**
   * 添加键值对
   * @param {string} name - 参数名
   * @param {string} value - 参数值
   * @original append方法的实现
   */
  append(name, value) {
    this._pairs.push([name, value]);
  }

  /**
   * 转换为查询字符串
   * @param {Function} encoder - 编码函数
   * @returns {string} 查询字符串
   * @original toString方法的实现
   */
  toString(encoder) {
    const defaultEncoder = encoder ? function(name) {
      return encoder.call(this, name, encode);
    } : encode;
    
    return this._pairs.map(function(pair) {
      return defaultEncoder(pair[0]) + "=" + defaultEncoder(pair[1]);
    }, "").join("&");
  }

  /**
   * 设置键值对（如果存在则替换）
   * @param {string} name - 参数名
   * @param {string} value - 参数值
   */
  set(name, value) {
    // 先删除所有同名的参数
    this._pairs = this._pairs.filter(pair => pair[0] !== name);
    // 添加新的参数
    this.append(name, value);
  }

  /**
   * 获取参数值
   * @param {string} name - 参数名
   * @returns {string|null} 参数值
   */
  get(name) {
    const pair = this._pairs.find(pair => pair[0] === name);
    return pair ? pair[1] : null;
  }

  /**
   * 获取所有同名参数的值
   * @param {string} name - 参数名
   * @returns {Array} 参数值数组
   */
  getAll(name) {
    return this._pairs.filter(pair => pair[0] === name).map(pair => pair[1]);
  }

  /**
   * 检查是否存在指定参数
   * @param {string} name - 参数名
   * @returns {boolean} 是否存在
   */
  has(name) {
    return this._pairs.some(pair => pair[0] === name);
  }

  /**
   * 删除指定参数
   * @param {string} name - 参数名
   */
  delete(name) {
    this._pairs = this._pairs.filter(pair => pair[0] !== name);
  }

  /**
   * 遍历所有参数
   * @param {Function} callback - 回调函数
   */
  forEach(callback) {
    this._pairs.forEach(pair => {
      callback(pair[1], pair[0], this);
    });
  }

  /**
   * 获取所有参数名
   * @returns {Iterator} 参数名迭代器
   */
  keys() {
    return this._pairs.map(pair => pair[0])[Symbol.iterator]();
  }

  /**
   * 获取所有参数值
   * @returns {Iterator} 参数值迭代器
   */
  values() {
    return this._pairs.map(pair => pair[1])[Symbol.iterator]();
  }

  /**
   * 获取所有键值对
   * @returns {Iterator} 键值对迭代器
   */
  entries() {
    return this._pairs[Symbol.iterator]();
  }
}

/**
 * 浏览器环境检测
 * @description 检测当前运行环境的浏览器特性
 * @original 第2241-2247行的环境检测
 */
export const BrowserEnv = {
  /**
   * 是否为浏览器环境
   * @original Fp1变量
   */
  hasBrowserEnv: typeof window !== "undefined" && typeof document !== "undefined",

  /**
   * Navigator对象
   * @original Gp1变量
   */
  navigator: typeof navigator === "object" && navigator || undefined,

  /**
   * 是否为标准浏览器环境
   * @original MP9变量
   */
  hasStandardBrowserEnv: false,

  /**
   * 是否为标准浏览器Web Worker环境
   * @original RP9变量
   */
  hasStandardBrowserWebWorkerEnv: false,

  /**
   * 当前页面的origin
   * @original OP9变量
   */
  origin: "http://localhost"
};

// 初始化环境检测
BrowserEnv.hasStandardBrowserEnv = BrowserEnv.hasBrowserEnv && 
  (!BrowserEnv.navigator || ["ReactNative", "NativeScript", "NS"].indexOf(BrowserEnv.navigator.product) < 0);

BrowserEnv.hasStandardBrowserWebWorkerEnv = (() => {
  return typeof WorkerGlobalScope !== "undefined" && 
         self instanceof WorkerGlobalScope && 
         typeof self.importScripts === "function";
})();

BrowserEnv.origin = BrowserEnv.hasBrowserEnv && window.location.href || "http://localhost";

/**
 * HTTP方法列表
 * @description 支持的HTTP方法
 */
export const HTTP_METHODS = ["delete", "get", "head", "post", "put", "patch"];

/**
 * 创建HTTP头部对象
 * @returns {Object} HTTP头部对象
 */
export function createHeaders() {
  const headers = {};
  HTTP_METHODS.forEach(method => {
    headers[method] = {};
  });
  return headers;
}

/**
 * 检查是否为Axios错误
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否为Axios错误
 */
export function isAxiosError(error) {
  return error && error.isAxiosError === true;
}

/**
 * 创建错误对象
 * @param {string} message - 错误消息
 * @param {Object} config - 配置对象
 * @param {string} code - 错误代码
 * @param {Object} request - 请求对象
 * @param {Object} response - 响应对象
 * @returns {CustomError} 错误对象
 */
export function createError(message, config, code, request, response) {
  return new CustomError(message, config, code, request, response);
}

/**
 * 检查是否为网络错误
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否为网络错误
 */
export function isNetworkError(error) {
  return isAxiosError(error) && 
         (error.code === 'ERR_NETWORK' || error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT');
}

/**
 * 检查是否为超时错误
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否为超时错误
 */
export function isTimeoutError(error) {
  return isAxiosError(error) && error.code === 'ETIMEDOUT';
}

/**
 * 检查是否为取消错误
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否为取消错误
 */
export function isCancelError(error) {
  return isAxiosError(error) && error.code === 'ERR_CANCELED';
}

/**
 * 格式化URL
 * @param {string} baseURL - 基础URL
 * @param {string} url - 相对URL
 * @returns {string} 完整URL
 */
export function buildURL(baseURL, url) {
  if (!baseURL) return url;
  if (!url) return baseURL;
  
  // 如果url已经是完整URL，直接返回
  if (/^https?:\/\//.test(url)) {
    return url;
  }
  
  // 确保baseURL以/结尾，url不以/开头
  const normalizedBase = baseURL.replace(/\/+$/, '');
  const normalizedUrl = url.replace(/^\/+/, '');
  
  return `${normalizedBase}/${normalizedUrl}`;
}
