/**
 * 验证工具函数
 * @description 提供各种数据验证功能
 */

/**
 * 验证UUID格式
 * @param {string} uuid - 要验证的UUID字符串
 * @returns {string|null} 有效的UUID或null
 * @original let s1 = PK(N); if (!s1) process.stderr.write(A0.red(`Error: Invalid session ID. Must be a valid UUID.`)), process.exit(1);
 */
export function validateUuid(uuid) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid) ? uuid : null;
}

/**
 * 检查会话ID是否正在使用
 * @param {string} sessionId - 会话ID
 * @returns {boolean} 是否正在使用
 * @original if (G$2(s1)) process.stderr.write(A0.red(`Error: Session ID ${s1} is already in use.`)), process.exit(1);
 */
export function isSessionIdInUse(sessionId) {
  // @todo: 实现G$2()函数的逻辑
  // 这里需要检查当前活跃的会话列表
  return false;
}

/**
 * 验证服务器名称格式
 * @param {string} serverName - 服务器名称
 * @returns {boolean} 是否有效
 * @original if (A.match(/[^a-zA-Z0-9_-]/)) throw new Error(`Invalid name ${A}. Names can only contain letters, numbers, hyphens, and underscores.`);
 */
export function validateServerName(serverName) {
  return !serverName.match(/[^a-zA-Z0-9_-]/);
}

/**
 * 验证服务器名称并抛出错误
 * @param {string} serverName - 服务器名称
 * @throws {Error} 名称无效时抛出错误
 */
export function validateServerNameOrThrow(serverName) {
  if (!validateServerName(serverName)) {
    throw new Error(`Invalid name ${serverName}. Names can only contain letters, numbers, hyphens, and underscores.`);
  }
}

/**
 * 验证传输类型
 * @param {string} transport - 传输类型
 * @returns {boolean} 是否有效
 */
export function validateTransportType(transport) {
  const validTransports = ["stdio", "sse", "http"];
  return validTransports.includes(transport);
}

/**
 * 验证配置作用域
 * @param {string} scope - 配置作用域
 * @returns {boolean} 是否有效
 */
export function validateScope(scope) {
  const validScopes = ["local", "user", "project"];
  return validScopes.includes(scope);
}

/**
 * 验证权限模式
 * @param {string} permissionMode - 权限模式
 * @returns {boolean} 是否有效
 */
export function validatePermissionMode(permissionMode) {
  const validModes = ["ask", "allow", "deny"];
  return validModes.includes(permissionMode);
}

/**
 * 验证输出格式
 * @param {string} outputFormat - 输出格式
 * @returns {boolean} 是否有效
 */
export function validateOutputFormat(outputFormat) {
  const validFormats = ["text", "json", "stream-json"];
  return validFormats.includes(outputFormat);
}

/**
 * 验证输入格式
 * @param {string} inputFormat - 输入格式
 * @returns {boolean} 是否有效
 */
export function validateInputFormat(inputFormat) {
  const validFormats = ["text", "stream-json"];
  return validFormats.includes(inputFormat);
}

/**
 * 验证模型名称格式
 * @param {string} modelName - 模型名称
 * @returns {boolean} 是否有效
 */
export function validateModelName(modelName) {
  if (!modelName || typeof modelName !== 'string') {
    return false;
  }
  
  // 允许别名（如 'sonnet', 'opus'）或完整模型名称
  const aliasPattern = /^(sonnet|opus|haiku)$/i;
  const fullNamePattern = /^claude-[a-z]+-\d+-\d{8}$/i;
  
  return aliasPattern.test(modelName) || fullNamePattern.test(modelName);
}

/**
 * 验证环境变量格式
 * @param {string} envVar - 环境变量字符串 (格式: KEY=value)
 * @returns {boolean} 是否有效
 */
export function validateEnvironmentVariable(envVar) {
  if (!envVar || typeof envVar !== 'string') {
    return false;
  }
  
  const parts = envVar.split('=');
  return parts.length >= 2 && parts[0].trim().length > 0;
}

/**
 * 验证HTTP头部格式
 * @param {string} header - HTTP头部字符串 (格式: "Header-Name: value")
 * @returns {boolean} 是否有效
 */
export function validateHttpHeader(header) {
  if (!header || typeof header !== 'string') {
    return false;
  }
  
  const colonIndex = header.indexOf(':');
  return colonIndex > 0 && colonIndex < header.length - 1;
}

/**
 * 验证URL格式
 * @param {string} url - URL字符串
 * @returns {boolean} 是否有效
 */
export function validateUrl(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * 验证文件路径格式
 * @param {string} filePath - 文件路径
 * @returns {boolean} 是否有效
 */
export function validateFilePath(filePath) {
  if (!filePath || typeof filePath !== 'string') {
    return false;
  }
  
  // 基本路径验证，排除明显无效的字符
  const invalidChars = /[<>"|?*\x00-\x1f]/;
  return !invalidChars.test(filePath);
}

/**
 * 验证目录路径格式
 * @param {string} dirPath - 目录路径
 * @returns {boolean} 是否有效
 */
export function validateDirectoryPath(dirPath) {
  return validateFilePath(dirPath);
}

/**
 * 验证JSON字符串
 * @param {string} jsonString - JSON字符串
 * @returns {boolean} 是否有效
 */
export function validateJsonString(jsonString) {
  try {
    JSON.parse(jsonString);
    return true;
  } catch {
    return false;
  }
}

/**
 * 验证端口号
 * @param {number|string} port - 端口号
 * @returns {boolean} 是否有效
 */
export function validatePort(port) {
  const portNum = typeof port === 'string' ? parseInt(port, 10) : port;
  return Number.isInteger(portNum) && portNum >= 1 && portNum <= 65535;
}

/**
 * 验证工具名称格式
 * @param {string} toolName - 工具名称
 * @returns {boolean} 是否有效
 */
export function validateToolName(toolName) {
  if (!toolName || typeof toolName !== 'string') {
    return false;
  }
  
  // 工具名称可以包含字母、数字、下划线、连字符和括号
  const validPattern = /^[a-zA-Z0-9_\-():\*\s]+$/;
  return validPattern.test(toolName);
}
