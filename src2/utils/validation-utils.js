/**
 * 验证工具函数
 * @description 重构自原始文件中的验证相关代码，对应第3091-3142行
 * @original 原始代码行 3091-3142
 */

/**
 * 通用工具函数命名空间
 * @description 提供各种通用工具函数
 * @original W6命名空间
 */
export const Utils = {
  /**
   * 断言相等（空实现）
   * @param {*} value - 要断言的值
   * @original assertEqual方法
   */
  assertEqual: (value) => {},

  /**
   * 断言类型（空实现）
   * @param {*} value - 要断言的值
   * @original assertIs方法
   */
  assertIs: (value) => {},

  /**
   * 断言永不到达（抛出错误）
   * @param {*} value - 不应该存在的值
   * @throws {Error} 总是抛出错误
   * @original assertNever方法
   */
  assertNever: (value) => {
    throw new Error(`Unexpected value: ${value}`);
  },

  /**
   * 将数组转换为枚举对象
   * @param {Array} array - 要转换的数组
   * @returns {Object} 枚举对象
   * @original arrayToEnum方法
   */
  arrayToEnum: (array) => {
    const enumObject = {};
    for (const item of array) {
      enumObject[item] = item;
    }
    return enumObject;
  },

  /**
   * 获取有效的枚举值
   * @param {Object} enumObject - 枚举对象
   * @returns {Array} 有效枚举值数组
   * @original getValidEnumValues方法
   */
  getValidEnumValues: (enumObject) => {
    const keys = Utils.objectKeys(enumObject).filter(key => 
      typeof enumObject[enumObject[key]] !== "number"
    );
    const validEnum = {};
    for (const key of keys) {
      validEnum[key] = enumObject[key];
    }
    return Utils.objectValues(validEnum);
  },

  /**
   * 获取对象的所有值
   * @param {Object} object - 对象
   * @returns {Array} 值数组
   * @original objectValues方法
   */
  objectValues: (object) => {
    return Utils.objectKeys(object).map(key => object[key]);
  },

  /**
   * 获取对象的所有键
   * @param {Object} object - 对象
   * @returns {Array} 键数组
   * @original objectKeys方法
   */
  objectKeys: typeof Object.keys === "function" ? 
    (object) => Object.keys(object) : 
    (object) => {
      const keys = [];
      for (const key in object) {
        if (Object.prototype.hasOwnProperty.call(object, key)) {
          keys.push(key);
        }
      }
      return keys;
    },

  /**
   * 在数组中查找满足条件的第一个元素
   * @param {Array} array - 要搜索的数组
   * @param {Function} predicate - 判断函数
   * @returns {*} 找到的元素或undefined
   * @original find方法
   */
  find: (array, predicate) => {
    for (const item of array) {
      if (predicate(item)) {
        return item;
      }
    }
    return undefined;
  },

  /**
   * 检查值是否为整数
   * @param {*} value - 要检查的值
   * @returns {boolean} 是否为整数
   * @original isInteger方法
   */
  isInteger: typeof Number.isInteger === "function" ? 
    (value) => Number.isInteger(value) : 
    (value) => typeof value === "number" && Number.isFinite(value) && Math.floor(value) === value,

  /**
   * 连接值为字符串
   * @param {Array} values - 值数组
   * @param {string} separator - 分隔符，默认为 " | "
   * @returns {string} 连接后的字符串
   * @original joinValues方法
   */
  joinValues: (values, separator = " | ") => {
    return values.map(value => 
      typeof value === "string" ? `'${value}'` : value
    ).join(separator);
  },

  /**
   * JSON字符串化替换器
   * @param {string} key - 键
   * @param {*} value - 值
   * @returns {*} 处理后的值
   * @original jsonStringifyReplacer方法
   */
  jsonStringifyReplacer: (key, value) => {
    if (typeof value === "bigint") {
      return value.toString();
    }
    return value;
  }
};

/**
 * 形状合并工具
 * @description 用于合并对象形状
 * @original Yr1命名空间
 */
export const ShapeUtils = {
  /**
   * 合并两个形状对象
   * @param {Object} shape1 - 第一个形状
   * @param {Object} shape2 - 第二个形状
   * @returns {Object} 合并后的形状
   * @original mergeShapes方法
   */
  mergeShapes: (shape1, shape2) => {
    return {
      ...shape1,
      ...shape2
    };
  }
};

/**
 * 错误处理工具
 * @description 用于处理错误对象
 * @original V9命名空间
 */
export const ErrorUtils = {
  /**
   * 将错误转换为对象
   * @param {string|Error|Object} error - 错误
   * @returns {Object} 错误对象
   * @original errToObj方法
   */
  errToObj: (error) => {
    if (typeof error === "string") {
      return { message: error };
    }
    return error || {};
  },

  /**
   * 将错误转换为字符串
   * @param {string|Error|Object} error - 错误
   * @returns {string} 错误字符串
   * @original toString方法
   */
  toString: (error) => {
    if (typeof error === "string") {
      return error;
    }
    return error?.message || '';
  }
};

/**
 * Zod类型枚举
 * @description Zod验证库的所有类型名称
 * @original eA命名空间
 */
export const ZodTypes = {
  ZodString: "ZodString",
  ZodNumber: "ZodNumber", 
  ZodNaN: "ZodNaN",
  ZodBigInt: "ZodBigInt",
  ZodBoolean: "ZodBoolean",
  ZodDate: "ZodDate",
  ZodSymbol: "ZodSymbol",
  ZodUndefined: "ZodUndefined",
  ZodNull: "ZodNull",
  ZodAny: "ZodAny",
  ZodUnknown: "ZodUnknown",
  ZodNever: "ZodNever",
  ZodVoid: "ZodVoid",
  ZodArray: "ZodArray",
  ZodObject: "ZodObject",
  ZodUnion: "ZodUnion",
  ZodDiscriminatedUnion: "ZodDiscriminatedUnion",
  ZodIntersection: "ZodIntersection",
  ZodTuple: "ZodTuple",
  ZodRecord: "ZodRecord",
  ZodMap: "ZodMap",
  ZodSet: "ZodSet",
  ZodFunction: "ZodFunction",
  ZodLazy: "ZodLazy",
  ZodLiteral: "ZodLiteral",
  ZodEnum: "ZodEnum",
  ZodEffects: "ZodEffects",
  ZodNativeEnum: "ZodNativeEnum",
  ZodOptional: "ZodOptional",
  ZodNullable: "ZodNullable",
  ZodDefault: "ZodDefault",
  ZodCatch: "ZodCatch",
  ZodPromise: "ZodPromise",
  ZodBranded: "ZodBranded",
  ZodPipeline: "ZodPipeline",
  ZodReadonly: "ZodReadonly"
};

/**
 * 验证工具类
 * @description 提供各种验证功能
 */
export class ValidationUtils {
  /**
   * 验证对象是否符合指定形状
   * @param {Object} object - 要验证的对象
   * @param {Object} shape - 期望的形状
   * @returns {Object} 验证结果
   */
  static validateShape(object, shape) {
    const result = {
      valid: true,
      errors: [],
      warnings: []
    };

    if (!object || typeof object !== 'object') {
      result.valid = false;
      result.errors.push('Value must be an object');
      return result;
    }

    // 检查必需的属性
    for (const key in shape) {
      if (!(key in object)) {
        result.valid = false;
        result.errors.push(`Missing required property: ${key}`);
      }
    }

    // 检查额外的属性
    for (const key in object) {
      if (!(key in shape)) {
        result.warnings.push(`Unexpected property: ${key}`);
      }
    }

    return result;
  }

  /**
   * 验证数组中的所有元素
   * @param {Array} array - 要验证的数组
   * @param {Function} validator - 验证函数
   * @returns {Object} 验证结果
   */
  static validateArray(array, validator) {
    const result = {
      valid: true,
      errors: [],
      validItems: [],
      invalidItems: []
    };

    if (!Array.isArray(array)) {
      result.valid = false;
      result.errors.push('Value must be an array');
      return result;
    }

    array.forEach((item, index) => {
      try {
        const isValid = validator(item);
        if (isValid) {
          result.validItems.push({ index, item });
        } else {
          result.invalidItems.push({ index, item, error: 'Validation failed' });
          result.valid = false;
        }
      } catch (error) {
        result.invalidItems.push({ index, item, error: error.message });
        result.valid = false;
      }
    });

    return result;
  }

  /**
   * 验证枚举值
   * @param {*} value - 要验证的值
   * @param {Array|Object} enumValues - 枚举值
   * @returns {boolean} 是否为有效的枚举值
   */
  static validateEnum(value, enumValues) {
    if (Array.isArray(enumValues)) {
      return enumValues.includes(value);
    }
    
    if (typeof enumValues === 'object') {
      return Object.values(enumValues).includes(value);
    }
    
    return false;
  }

  /**
   * 验证类型
   * @param {*} value - 要验证的值
   * @param {string} expectedType - 期望的类型
   * @returns {boolean} 是否为期望的类型
   */
  static validateType(value, expectedType) {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'integer':
        return Utils.isInteger(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return value !== null && typeof value === 'object' && !Array.isArray(value);
      case 'null':
        return value === null;
      case 'undefined':
        return value === undefined;
      case 'function':
        return typeof value === 'function';
      case 'symbol':
        return typeof value === 'symbol';
      case 'bigint':
        return typeof value === 'bigint';
      case 'date':
        return value instanceof Date;
      default:
        return false;
    }
  }

  /**
   * 创建类型验证器
   * @param {string} type - 类型名称
   * @returns {Function} 验证器函数
   */
  static createTypeValidator(type) {
    return (value) => this.validateType(value, type);
  }

  /**
   * 创建范围验证器
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @returns {Function} 验证器函数
   */
  static createRangeValidator(min, max) {
    return (value) => {
      if (typeof value !== 'number') {
        return false;
      }
      return value >= min && value <= max;
    };
  }

  /**
   * 创建长度验证器
   * @param {number} minLength - 最小长度
   * @param {number} maxLength - 最大长度
   * @returns {Function} 验证器函数
   */
  static createLengthValidator(minLength, maxLength) {
    return (value) => {
      if (typeof value !== 'string' && !Array.isArray(value)) {
        return false;
      }
      const length = value.length;
      return length >= minLength && length <= maxLength;
    };
  }

  /**
   * 创建正则表达式验证器
   * @param {RegExp} pattern - 正则表达式模式
   * @returns {Function} 验证器函数
   */
  static createRegexValidator(pattern) {
    return (value) => {
      if (typeof value !== 'string') {
        return false;
      }
      return pattern.test(value);
    };
  }

  /**
   * 组合多个验证器
   * @param {...Function} validators - 验证器函数数组
   * @returns {Function} 组合验证器函数
   */
  static combineValidators(...validators) {
    return (value) => {
      for (const validator of validators) {
        if (!validator(value)) {
          return false;
        }
      }
      return true;
    };
  }

  /**
   * 创建可选验证器
   * @param {Function} validator - 基础验证器
   * @returns {Function} 可选验证器函数
   */
  static createOptionalValidator(validator) {
    return (value) => {
      if (value === undefined || value === null) {
        return true;
      }
      return validator(value);
    };
  }
}
