/**
 * MCP (Model Context Protocol) 工具函数
 * @description 重构自原始文件中的MCP相关代码，对应第3145-3200行
 * @original 原始代码行 3145-3200
 */

import { join } from "path";

/**
 * 清理名称，移除非法字符
 * @param {string} name - 要清理的名称
 * @returns {string} 清理后的名称
 * @original CK1函数
 */
export function sanitizeName(name) {
  return name.replace(/[^a-zA-Z0-9_-]/g, "_");
}

/**
 * 过滤指定服务器的工具
 * @param {Array} tools - 工具数组
 * @param {string} serverName - 服务器名称
 * @returns {Array} 过滤后的工具数组
 * @original L91函数
 */
export function filterToolsByServer(tools, serverName) {
  const prefix = `mcp__${serverName}__`;
  return tools.filter(tool => tool.name?.startsWith(prefix));
}

/**
 * 过滤指定服务器的资源
 * @param {Array} resources - 资源数组
 * @param {string} serverName - 服务器名称
 * @returns {Array} 过滤后的资源数组
 * @original KK1函数
 */
export function filterResourcesByServer(resources, serverName) {
  const prefix = `mcp__${serverName}__`;
  return resources.filter(resource => resource.name?.startsWith(prefix));
}

/**
 * 排除指定服务器的工具
 * @param {Array} tools - 工具数组
 * @param {string} serverName - 服务器名称
 * @returns {Array} 排除后的工具数组
 * @original MIA函数
 */
export function excludeToolsByServer(tools, serverName) {
  const prefix = `mcp__${serverName}__`;
  return tools.filter(tool => !tool.name?.startsWith(prefix));
}

/**
 * 排除指定服务器的资源
 * @param {Array} resources - 资源数组
 * @param {string} serverName - 服务器名称
 * @returns {Array} 排除后的资源数组
 * @original RIA函数
 */
export function excludeResourcesByServer(resources, serverName) {
  const prefix = `mcp__${serverName}__`;
  return resources.filter(resource => !resource.name?.startsWith(prefix));
}

/**
 * 从对象中删除指定键
 * @param {Object} object - 源对象
 * @param {string} key - 要删除的键
 * @returns {Object} 删除键后的新对象
 * @original OIA函数
 */
export function omitKey(object, key) {
  const result = { ...object };
  delete result[key];
  return result;
}

/**
 * 创建MCP前缀
 * @param {string} serverName - 服务器名称
 * @returns {string} MCP前缀
 * @original TIA函数
 */
export function createMCPPrefix(serverName) {
  return `mcp__${serverName}__`;
}

/**
 * 检查工具是否为MCP工具
 * @param {Object} tool - 工具对象
 * @returns {boolean} 是否为MCP工具
 * @original Kr1函数
 */
export function isMCPTool(tool) {
  return tool.name?.startsWith("mcp__") || tool.isMcp === true;
}

/**
 * 解析MCP工具名称
 * @param {string} toolName - 工具名称
 * @returns {Object|null} 解析结果或null
 * @original xy函数
 */
export function parseMCPToolName(toolName) {
  const parts = toolName.split("__");
  const [prefix, serverName, ...toolNameParts] = parts;
  
  if (prefix !== "mcp" || !serverName) {
    return null;
  }
  
  const actualToolName = toolNameParts.length > 0 ? toolNameParts.join("__") : undefined;
  
  return {
    serverName,
    toolName: actualToolName
  };
}

/**
 * 移除MCP前缀
 * @param {string} name - 带前缀的名称
 * @param {string} serverName - 服务器名称
 * @returns {string} 移除前缀后的名称
 * @original HK1函数
 */
export function removeMCPPrefix(name, serverName) {
  const prefix = `mcp__${sanitizeName(serverName)}__`;
  return name.replace(prefix, "");
}

/**
 * 清理MCP描述
 * @param {string} description - 原始描述
 * @returns {string} 清理后的描述
 * @original zK1函数
 */
export function cleanMCPDescription(description) {
  // 移除末尾的 (MCP) 标记
  let cleaned = description.replace(/\s*\(MCP\)\s*$/, "");
  cleaned = cleaned.trim();
  
  // 如果包含 " - "，提取后面的部分
  const dashIndex = cleaned.indexOf(" - ");
  if (dashIndex !== -1) {
    return cleaned.substring(dashIndex + 3).trim();
  }
  
  return cleaned;
}

/**
 * MCP工具管理器类
 * @description 提供MCP工具的完整管理功能
 */
export class MCPToolManager {
  constructor() {
    this.servers = new Map();
    this.tools = new Map();
    this.resources = new Map();
  }

  /**
   * 注册MCP服务器
   * @param {string} serverName - 服务器名称
   * @param {Object} serverConfig - 服务器配置
   */
  registerServer(serverName, serverConfig) {
    this.servers.set(serverName, {
      name: serverName,
      config: serverConfig,
      tools: new Set(),
      resources: new Set(),
      registered: new Date()
    });
  }

  /**
   * 注销MCP服务器
   * @param {string} serverName - 服务器名称
   * @returns {boolean} 是否注销成功
   */
  unregisterServer(serverName) {
    const server = this.servers.get(serverName);
    if (!server) {
      return false;
    }

    // 移除服务器的所有工具和资源
    for (const toolName of server.tools) {
      this.tools.delete(toolName);
    }
    for (const resourceName of server.resources) {
      this.resources.delete(resourceName);
    }

    this.servers.delete(serverName);
    return true;
  }

  /**
   * 注册工具到服务器
   * @param {string} serverName - 服务器名称
   * @param {Object} tool - 工具对象
   * @returns {string} 注册后的工具名称
   */
  registerTool(serverName, tool) {
    const server = this.servers.get(serverName);
    if (!server) {
      throw new Error(`Server ${serverName} not found`);
    }

    const mcpToolName = `mcp__${sanitizeName(serverName)}__${tool.name}`;
    const mcpTool = {
      ...tool,
      name: mcpToolName,
      originalName: tool.name,
      serverName,
      isMcp: true,
      description: tool.description ? `${tool.description} (MCP)` : `${tool.name} (MCP)`
    };

    this.tools.set(mcpToolName, mcpTool);
    server.tools.add(mcpToolName);

    return mcpToolName;
  }

  /**
   * 注册资源到服务器
   * @param {string} serverName - 服务器名称
   * @param {Object} resource - 资源对象
   * @returns {string} 注册后的资源名称
   */
  registerResource(serverName, resource) {
    const server = this.servers.get(serverName);
    if (!server) {
      throw new Error(`Server ${serverName} not found`);
    }

    const mcpResourceName = `mcp__${sanitizeName(serverName)}__${resource.name}`;
    const mcpResource = {
      ...resource,
      name: mcpResourceName,
      originalName: resource.name,
      serverName,
      isMcp: true
    };

    this.resources.set(mcpResourceName, mcpResource);
    server.resources.add(mcpResourceName);

    return mcpResourceName;
  }

  /**
   * 获取服务器的所有工具
   * @param {string} serverName - 服务器名称
   * @returns {Array} 工具数组
   */
  getServerTools(serverName) {
    const allTools = Array.from(this.tools.values());
    return filterToolsByServer(allTools, serverName);
  }

  /**
   * 获取服务器的所有资源
   * @param {string} serverName - 服务器名称
   * @returns {Array} 资源数组
   */
  getServerResources(serverName) {
    const allResources = Array.from(this.resources.values());
    return filterResourcesByServer(allResources, serverName);
  }

  /**
   * 获取所有MCP工具
   * @returns {Array} MCP工具数组
   */
  getAllMCPTools() {
    return Array.from(this.tools.values());
  }

  /**
   * 获取所有非MCP工具
   * @param {Array} allTools - 所有工具数组
   * @returns {Array} 非MCP工具数组
   */
  getNonMCPTools(allTools) {
    return allTools.filter(tool => !isMCPTool(tool));
  }

  /**
   * 获取工具信息
   * @param {string} toolName - 工具名称
   * @returns {Object|null} 工具信息或null
   */
  getToolInfo(toolName) {
    const tool = this.tools.get(toolName);
    if (!tool) {
      return null;
    }

    const parsed = parseMCPToolName(toolName);
    return {
      ...tool,
      parsed,
      server: this.servers.get(parsed?.serverName)
    };
  }

  /**
   * 检查服务器是否存在
   * @param {string} serverName - 服务器名称
   * @returns {boolean} 是否存在
   */
  hasServer(serverName) {
    return this.servers.has(serverName);
  }

  /**
   * 获取服务器信息
   * @param {string} serverName - 服务器名称
   * @returns {Object|null} 服务器信息或null
   */
  getServerInfo(serverName) {
    return this.servers.get(serverName) || null;
  }

  /**
   * 获取所有服务器名称
   * @returns {Array} 服务器名称数组
   */
  getServerNames() {
    return Array.from(this.servers.keys());
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      serverCount: this.servers.size,
      toolCount: this.tools.size,
      resourceCount: this.resources.size,
      servers: Array.from(this.servers.entries()).map(([name, server]) => ({
        name,
        toolCount: server.tools.size,
        resourceCount: server.resources.size,
        registered: server.registered
      }))
    };
  }

  /**
   * 清空所有数据
   */
  clear() {
    this.servers.clear();
    this.tools.clear();
    this.resources.clear();
  }

  /**
   * 导出配置
   * @returns {Object} 配置对象
   */
  exportConfig() {
    return {
      servers: Object.fromEntries(
        Array.from(this.servers.entries()).map(([name, server]) => [
          name,
          {
            config: server.config,
            registered: server.registered
          }
        ])
      ),
      tools: Object.fromEntries(this.tools),
      resources: Object.fromEntries(this.resources)
    };
  }

  /**
   * 导入配置
   * @param {Object} config - 配置对象
   */
  importConfig(config) {
    this.clear();

    // 导入服务器
    if (config.servers) {
      for (const [name, serverData] of Object.entries(config.servers)) {
        this.servers.set(name, {
          name,
          config: serverData.config,
          tools: new Set(),
          resources: new Set(),
          registered: new Date(serverData.registered)
        });
      }
    }

    // 导入工具
    if (config.tools) {
      for (const [name, tool] of Object.entries(config.tools)) {
        this.tools.set(name, tool);
        if (tool.serverName && this.servers.has(tool.serverName)) {
          this.servers.get(tool.serverName).tools.add(name);
        }
      }
    }

    // 导入资源
    if (config.resources) {
      for (const [name, resource] of Object.entries(config.resources)) {
        this.resources.set(name, resource);
        if (resource.serverName && this.servers.has(resource.serverName)) {
          this.servers.get(resource.serverName).resources.add(name);
        }
      }
    }
  }
}

// 创建默认MCP工具管理器实例
export const mcpToolManager = new MCPToolManager();
