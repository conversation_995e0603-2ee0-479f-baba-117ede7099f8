/**
 * 数值处理工具函数
 * @description 重构自原始文件中的数值处理函数，对应第1326-1360行
 * @original 原始代码行 1326-1360
 */

import { trimString } from './string-processing.js';

/**
 * 数值常量
 */
const NaN_VALUE = NaN;
const POSITIVE_INFINITY = 1 / 0;
const MAX_SAFE_INTEGER = 179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;

/**
 * 数值格式正则表达式
 */
const HEX_REGEX = /^[-+]0x[0-9a-f]+$/i;      // 十六进制
const BINARY_REGEX = /^0b[01]+$/i;           // 二进制
const OCTAL_REGEX = /^0o[0-7]+$/i;           // 八进制

/**
 * 检查值是否为Symbol类型
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为Symbol
 * @original Rj(A) - 需要实现这个函数的逻辑
 */
function isSymbol(value) {
  return typeof value === 'symbol' || 
         (typeof value === 'object' && value != null && Object.prototype.toString.call(value) === '[object Symbol]');
}

/**
 * 检查值是否为对象类型
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为对象
 * @original T3(A) - 需要实现这个函数的逻辑
 */
function isObject(value) {
  const type = typeof value;
  return value != null && (type === 'object' || type === 'function');
}

/**
 * 转换值为数字
 * @param {*} value - 要转换的值
 * @returns {number} 转换后的数字
 * @original function WlB(A) { ... }
 */
export function toNumber(value) {
  // 如果已经是数字，直接返回
  if (typeof value === "number") {
    return value;
  }
  
  // 如果是Symbol，返回NaN
  if (isSymbol(value)) {
    return NaN_VALUE;
  }
  
  // 如果是对象，尝试调用valueOf方法
  if (isObject(value)) {
    const primitive = typeof value.valueOf === "function" ? value.valueOf() : value;
    value = isObject(primitive) ? primitive + "" : primitive;
  }
  
  // 如果不是字符串，进行类型转换
  if (typeof value !== "string") {
    return value === 0 ? value : +value;
  }
  
  // 去除首尾空白
  value = trimString(value);
  
  // 检查二进制格式
  const isBinary = BINARY_REGEX.test(value);
  if (isBinary || OCTAL_REGEX.test(value)) {
    return parseInt(value.slice(2), isBinary ? 2 : 8);
  }
  
  // 检查十六进制格式
  if (HEX_REGEX.test(value)) {
    return NaN_VALUE;
  }
  
  // 默认转换
  return +value;
}

/**
 * 转换值为有限数字
 * @param {*} value - 要转换的值
 * @returns {number} 转换后的有限数字
 * @original function XlB(A) { ... }
 */
export function toFinite(value) {
  if (!value) {
    return value === 0 ? value : 0;
  }
  
  value = toNumber(value);
  
  // 处理无穷大
  if (value === POSITIVE_INFINITY || value === -POSITIVE_INFINITY) {
    const sign = value < 0 ? -1 : 1;
    return sign * MAX_SAFE_INTEGER;
  }
  
  // 处理NaN
  return value === value ? value : 0;
}

/**
 * 转换值为整数
 * @param {*} value - 要转换的值
 * @returns {number} 转换后的整数
 * @original function VlB(A) { ... }
 */
export function toInteger(value) {
  const result = toFinite(value);
  const remainder = result % 1;
  
  return result === result ? (remainder ? result - remainder : result) : 0;
}

/**
 * 空函数
 * @returns {undefined}
 * @original function flB() {}
 */
export function noop() {
  // 空函数，不执行任何操作
}

/**
 * 检查数字是否为NaN
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为NaN
 * @original function slB(A) { return A !== A; }
 */
export function isNaN(value) {
  return value !== value;
}

/**
 * 安全的数字转换
 * @param {*} value - 要转换的值
 * @param {number} defaultValue - 默认值
 * @returns {number} 转换后的数字或默认值
 */
export function safeToNumber(value, defaultValue = 0) {
  try {
    const result = toNumber(value);
    return isNaN(result) ? defaultValue : result;
  } catch {
    return defaultValue;
  }
}

/**
 * 安全的整数转换
 * @param {*} value - 要转换的值
 * @param {number} defaultValue - 默认值
 * @returns {number} 转换后的整数或默认值
 */
export function safeToInteger(value, defaultValue = 0) {
  try {
    const result = toInteger(value);
    return isNaN(result) ? defaultValue : result;
  } catch {
    return defaultValue;
  }
}

/**
 * 检查值是否为有效数字
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为有效数字
 */
export function isValidNumber(value) {
  const num = toNumber(value);
  return !isNaN(num) && isFinite(num);
}

/**
 * 将数字限制在指定范围内
 * @param {number} value - 数值
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 限制后的数值
 */
export function clamp(value, min, max) {
  const num = toNumber(value);
  const minNum = toNumber(min);
  const maxNum = toNumber(max);
  
  if (isNaN(num)) return minNum;
  if (num < minNum) return minNum;
  if (num > maxNum) return maxNum;
  return num;
}

/**
 * 检查数字是否在指定范围内
 * @param {number} value - 数值
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {boolean} 是否在范围内
 */
export function inRange(value, min, max) {
  const num = toNumber(value);
  const minNum = toNumber(min);
  const maxNum = toNumber(max);
  
  return !isNaN(num) && num >= minNum && num <= maxNum;
}

/**
 * 生成指定范围内的随机数
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 随机数
 */
export function randomInRange(min = 0, max = 1) {
  const minNum = toNumber(min);
  const maxNum = toNumber(max);
  
  return Math.random() * (maxNum - minNum) + minNum;
}

/**
 * 生成指定范围内的随机整数
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 随机整数
 */
export function randomIntInRange(min = 0, max = 100) {
  const minInt = toInteger(min);
  const maxInt = toInteger(max);
  
  return Math.floor(Math.random() * (maxInt - minInt + 1)) + minInt;
}
