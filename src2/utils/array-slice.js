/**
 * 数组切片工具函数
 * @description 重构自原始文件中的数组切片函数，对应第1559-1574行
 * @original 原始代码行 1559-1574
 */

/**
 * 数组切片函数
 * @param {Array} array - 要切片的数组
 * @param {number} start - 开始索引
 * @param {number} end - 结束索引
 * @returns {Array} 切片后的数组
 * @original function znB(A, B, Q) { var D = -1, Z = A.length; if (B < 0) B = -B > Z ? 0 : Z + B; if (Q = Q > Z ? Z : Q, Q < 0) Q += Z; Z = B > Q ? 0 : Q - B >>> 0, B >>>= 0; var G = Array(Z); while (++D < Z) G[D] = A[D + B]; return G; }
 */
export function baseSlice(array, start, end) {
  let index = -1;
  let length = array.length;
  
  // 处理负数开始索引
  if (start < 0) {
    start = -start > length ? 0 : (length + start);
  }
  
  // 处理结束索引
  end = end > length ? length : end;
  if (end < 0) {
    end += length;
  }
  
  // 计算切片长度
  length = start > end ? 0 : ((end - start) >>> 0);
  start >>>= 0;
  
  // 创建结果数组
  const result = Array(length);
  while (++index < length) {
    result[index] = array[index + start];
  }
  
  return result;
}

/**
 * 基础切片函数（带条件判断）
 * @param {Array} array - 要切片的数组
 * @param {number} start - 开始索引
 * @param {number} end - 结束索引
 * @returns {Array} 切片后的数组
 * @original function EnB(A, B, Q) { var D = A.length; return Q = Q === void 0 ? D : Q, !B && Q >= D ? A : PY1(A, B, Q); }
 */
export function castSlice(array, start, end) {
  const length = array.length;
  end = end === undefined ? length : end;
  
  // 如果不需要切片，直接返回原数组
  return (!start && end >= length) ? array : baseSlice(array, start, end);
}

/**
 * 安全的数组切片函数
 * @param {Array} array - 要切片的数组
 * @param {number} start - 开始索引，默认为0
 * @param {number} end - 结束索引，默认为数组长度
 * @returns {Array} 切片后的数组
 */
export function slice(array, start = 0, end) {
  if (array == null) {
    return [];
  }
  
  const length = array.length;
  if (!length) {
    return [];
  }
  
  end = end === undefined ? length : end;
  return baseSlice(array, start, end);
}

/**
 * 获取数组的第一个元素
 * @param {Array} array - 数组
 * @returns {*} 第一个元素
 */
export function head(array) {
  return (array && array.length) ? array[0] : undefined;
}

/**
 * 获取数组的最后一个元素
 * @param {Array} array - 数组
 * @returns {*} 最后一个元素
 */
export function last(array) {
  const length = array == null ? 0 : array.length;
  return length ? array[length - 1] : undefined;
}

/**
 * 获取数组除了第一个元素之外的所有元素
 * @param {Array} array - 数组
 * @returns {Array} 除第一个元素外的数组
 */
export function tail(array) {
  const length = array == null ? 0 : array.length;
  return length > 1 ? baseSlice(array, 1, length) : [];
}

/**
 * 获取数组除了最后一个元素之外的所有元素
 * @param {Array} array - 数组
 * @returns {Array} 除最后一个元素外的数组
 */
export function initial(array) {
  const length = array == null ? 0 : array.length;
  return length ? baseSlice(array, 0, -1) : [];
}

/**
 * 获取数组的前n个元素
 * @param {Array} array - 数组
 * @param {number} n - 要获取的元素数量，默认为1
 * @returns {Array} 前n个元素组成的数组
 */
export function take(array, n = 1) {
  if (!(array && array.length)) {
    return [];
  }
  
  return baseSlice(array, 0, n < 0 ? 0 : n);
}

/**
 * 获取数组的后n个元素
 * @param {Array} array - 数组
 * @param {number} n - 要获取的元素数量，默认为1
 * @returns {Array} 后n个元素组成的数组
 */
export function takeRight(array, n = 1) {
  const length = array == null ? 0 : array.length;
  if (!length) {
    return [];
  }
  
  n = length - n;
  return baseSlice(array, n < 0 ? 0 : n, length);
}

/**
 * 从数组开头开始，获取满足条件的元素
 * @param {Array} array - 数组
 * @param {Function} predicate - 判断函数
 * @returns {Array} 满足条件的元素组成的数组
 */
export function takeWhile(array, predicate) {
  if (!(array && array.length)) {
    return [];
  }
  
  let index = 0;
  const length = array.length;
  
  while (index < length && predicate(array[index], index, array)) {
    index++;
  }
  
  return baseSlice(array, 0, index);
}

/**
 * 从数组末尾开始，获取满足条件的元素
 * @param {Array} array - 数组
 * @param {Function} predicate - 判断函数
 * @returns {Array} 满足条件的元素组成的数组
 */
export function takeRightWhile(array, predicate) {
  const length = array == null ? 0 : array.length;
  if (!length) {
    return [];
  }
  
  let index = length;
  
  while (index-- && predicate(array[index], index, array)) {
    // 继续循环
  }
  
  return baseSlice(array, index + 1, length);
}

/**
 * 移除数组开头的n个元素
 * @param {Array} array - 数组
 * @param {number} n - 要移除的元素数量，默认为1
 * @returns {Array} 移除元素后的数组
 */
export function drop(array, n = 1) {
  const length = array == null ? 0 : array.length;
  return length ? baseSlice(array, n < 0 ? 0 : n, length) : [];
}

/**
 * 移除数组末尾的n个元素
 * @param {Array} array - 数组
 * @param {number} n - 要移除的元素数量，默认为1
 * @returns {Array} 移除元素后的数组
 */
export function dropRight(array, n = 1) {
  const length = array == null ? 0 : array.length;
  return length ? baseSlice(array, 0, n < 0 ? length : -n) : [];
}

/**
 * 从数组开头开始，移除满足条件的元素
 * @param {Array} array - 数组
 * @param {Function} predicate - 判断函数
 * @returns {Array} 移除元素后的数组
 */
export function dropWhile(array, predicate) {
  if (!(array && array.length)) {
    return [];
  }
  
  let index = 0;
  const length = array.length;
  
  while (index < length && predicate(array[index], index, array)) {
    index++;
  }
  
  return baseSlice(array, index, length);
}

/**
 * 从数组末尾开始，移除满足条件的元素
 * @param {Array} array - 数组
 * @param {Function} predicate - 判断函数
 * @returns {Array} 移除元素后的数组
 */
export function dropRightWhile(array, predicate) {
  const length = array == null ? 0 : array.length;
  if (!length) {
    return [];
  }
  
  let index = length;
  
  while (index-- && predicate(array[index], index, array)) {
    // 继续循环
  }
  
  return baseSlice(array, 0, index + 1);
}

/**
 * 将数组分割成指定大小的块
 * @param {Array} array - 要分割的数组
 * @param {number} size - 每块的大小，默认为1
 * @returns {Array} 分割后的二维数组
 */
export function chunk(array, size = 1) {
  size = Math.max(Math.floor(size), 0);
  const length = array == null ? 0 : array.length;
  
  if (!length || size < 1) {
    return [];
  }
  
  let index = 0;
  let resIndex = 0;
  const result = Array(Math.ceil(length / size));
  
  while (index < length) {
    result[resIndex++] = baseSlice(array, index, (index += size));
  }
  
  return result;
}
