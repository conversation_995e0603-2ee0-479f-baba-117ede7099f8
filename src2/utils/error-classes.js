/**
 * 自定义错误类
 * @description 重构自原始文件中的错误类定义，对应第2891-2900行
 * @original 原始代码行 2891-2900
 */

/**
 * 基础错误类
 * @description 所有自定义错误的基类
 * @original RN类
 */
export class BaseError extends Error {
  constructor(message, code, cause) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.cause = cause;
    this.timestamp = new Date();
    
    // 确保错误堆栈正确显示
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * 转换为JSON对象
   * @returns {Object} JSON表示
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      cause: this.cause,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }

  /**
   * 转换为字符串
   * @returns {string} 字符串表示
   */
  toString() {
    let result = `${this.name}: ${this.message}`;
    if (this.code) {
      result += ` (Code: ${this.code})`;
    }
    return result;
  }
}

/**
 * 中止错误类
 * @description 表示操作被中止的错误
 * @original nJ类
 */
export class AbortError extends Error {
  constructor(message = 'Operation was aborted') {
    super(message);
    this.name = "AbortError";
    this.code = 'ABORT_ERR';
    
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AbortError);
    }
  }

  /**
   * 检查错误是否为中止错误
   * @param {Error} error - 要检查的错误
   * @returns {boolean} 是否为中止错误
   */
  static isAbortError(error) {
    return error instanceof AbortError || error.name === 'AbortError';
  }
}

/**
 * 执行错误类
 * @description 表示命令执行失败的错误
 * @original ON类
 */
export class ExecutionError extends Error {
  constructor(message, stdout = '', stderr = '', code = null, signal = null) {
    super(message);
    this.name = "ExecutionError";
    this.stdout = stdout;
    this.stderr = stderr;
    this.code = code;
    this.signal = signal;
    
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ExecutionError);
    }
  }

  /**
   * 获取完整的错误信息
   * @returns {string} 完整错误信息
   */
  getFullMessage() {
    let message = this.message;
    
    if (this.code !== null) {
      message += ` (Exit code: ${this.code})`;
    }
    
    if (this.signal) {
      message += ` (Signal: ${this.signal})`;
    }
    
    if (this.stderr) {
      message += `\nStderr: ${this.stderr}`;
    }
    
    if (this.stdout) {
      message += `\nStdout: ${this.stdout}`;
    }
    
    return message;
  }

  /**
   * 转换为JSON对象
   * @returns {Object} JSON表示
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      stdout: this.stdout,
      stderr: this.stderr,
      code: this.code,
      signal: this.signal,
      stack: this.stack
    };
  }
}

/**
 * 超时错误类
 * @description 表示操作超时的错误
 */
export class TimeoutError extends BaseError {
  constructor(message = 'Operation timed out', timeout = null) {
    super(message, 'TIMEOUT_ERR');
    this.timeout = timeout;
  }

  /**
   * 获取超时信息
   * @returns {string} 超时信息
   */
  getTimeoutInfo() {
    return this.timeout ? `Timeout: ${this.timeout}ms` : 'Timeout occurred';
  }
}

/**
 * 网络错误类
 * @description 表示网络相关错误
 */
export class NetworkError extends BaseError {
  constructor(message, url = null, statusCode = null) {
    super(message, 'NETWORK_ERR');
    this.url = url;
    this.statusCode = statusCode;
  }

  /**
   * 获取网络错误详情
   * @returns {Object} 网络错误详情
   */
  getNetworkDetails() {
    return {
      url: this.url,
      statusCode: this.statusCode,
      message: this.message
    };
  }
}

/**
 * 文件系统错误类
 * @description 表示文件系统操作错误
 */
export class FileSystemError extends BaseError {
  constructor(message, path = null, operation = null) {
    super(message, 'FS_ERR');
    this.path = path;
    this.operation = operation;
  }

  /**
   * 获取文件系统错误详情
   * @returns {Object} 文件系统错误详情
   */
  getFileSystemDetails() {
    return {
      path: this.path,
      operation: this.operation,
      message: this.message
    };
  }
}

/**
 * 验证错误类
 * @description 表示数据验证错误
 */
export class ValidationError extends BaseError {
  constructor(message, field = null, value = null) {
    super(message, 'VALIDATION_ERR');
    this.field = field;
    this.value = value;
  }

  /**
   * 获取验证错误详情
   * @returns {Object} 验证错误详情
   */
  getValidationDetails() {
    return {
      field: this.field,
      value: this.value,
      message: this.message
    };
  }
}

/**
 * 配置错误类
 * @description 表示配置相关错误
 */
export class ConfigurationError extends BaseError {
  constructor(message, configKey = null, configValue = null) {
    super(message, 'CONFIG_ERR');
    this.configKey = configKey;
    this.configValue = configValue;
  }

  /**
   * 获取配置错误详情
   * @returns {Object} 配置错误详情
   */
  getConfigurationDetails() {
    return {
      configKey: this.configKey,
      configValue: this.configValue,
      message: this.message
    };
  }
}

/**
 * 权限错误类
 * @description 表示权限相关错误
 */
export class PermissionError extends BaseError {
  constructor(message, resource = null, requiredPermission = null) {
    super(message, 'PERMISSION_ERR');
    this.resource = resource;
    this.requiredPermission = requiredPermission;
  }

  /**
   * 获取权限错误详情
   * @returns {Object} 权限错误详情
   */
  getPermissionDetails() {
    return {
      resource: this.resource,
      requiredPermission: this.requiredPermission,
      message: this.message
    };
  }
}

/**
 * Shell错误类
 * @description 表示Shell命令执行错误
 * @original Hw类的实现
 */
export class ShellError extends Error {
  constructor(stdout, stderr, code, interrupted) {
    super("Shell command failed");
    this.name = "ShellError";
    this.stdout = stdout;
    this.stderr = stderr;
    this.code = code;
    this.interrupted = interrupted;

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ShellError);
    }
  }

  /**
   * 获取完整的错误信息
   * @returns {string} 完整错误信息
   */
  getFullMessage() {
    let message = this.message;

    if (this.code !== null && this.code !== undefined) {
      message += ` (Exit code: ${this.code})`;
    }

    if (this.interrupted) {
      message += ' (Interrupted)';
    }

    if (this.stderr) {
      message += `\nStderr: ${this.stderr}`;
    }

    if (this.stdout) {
      message += `\nStdout: ${this.stdout}`;
    }

    return message;
  }

  /**
   * 转换为JSON对象
   * @returns {Object} JSON表示
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      stdout: this.stdout,
      stderr: this.stderr,
      code: this.code,
      interrupted: this.interrupted,
      stack: this.stack
    };
  }
}

/**
 * Teleport操作错误类
 * @description 表示Teleport操作相关错误
 * @original Hw类的实现
 */
export class TeleportOperationError extends Error {
  constructor(message, formattedMessage) {
    super(message);
    this.name = "TeleportOperationError";
    this.formattedMessage = formattedMessage;

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, TeleportOperationError);
    }
  }

  /**
   * 获取格式化的错误消息
   * @returns {string} 格式化的错误消息
   */
  getFormattedMessage() {
    return this.formattedMessage || this.message;
  }

  /**
   * 转换为JSON对象
   * @returns {Object} JSON表示
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      formattedMessage: this.formattedMessage,
      stack: this.stack
    };
  }
}

/**
 * 错误工厂类
 * @description 用于创建各种类型的错误
 */
export class ErrorFactory {
  /**
   * 创建中止错误
   * @param {string} message - 错误消息
   * @returns {AbortError} 中止错误实例
   */
  static createAbortError(message) {
    return new AbortError(message);
  }

  /**
   * 创建执行错误
   * @param {string} message - 错误消息
   * @param {string} stdout - 标准输出
   * @param {string} stderr - 标准错误
   * @param {number} code - 退出代码
   * @param {string} signal - 信号
   * @returns {ExecutionError} 执行错误实例
   */
  static createExecutionError(message, stdout, stderr, code, signal) {
    return new ExecutionError(message, stdout, stderr, code, signal);
  }

  /**
   * 创建超时错误
   * @param {string} message - 错误消息
   * @param {number} timeout - 超时时间
   * @returns {TimeoutError} 超时错误实例
   */
  static createTimeoutError(message, timeout) {
    return new TimeoutError(message, timeout);
  }

  /**
   * 创建网络错误
   * @param {string} message - 错误消息
   * @param {string} url - URL
   * @param {number} statusCode - 状态码
   * @returns {NetworkError} 网络错误实例
   */
  static createNetworkError(message, url, statusCode) {
    return new NetworkError(message, url, statusCode);
  }

  /**
   * 创建文件系统错误
   * @param {string} message - 错误消息
   * @param {string} path - 文件路径
   * @param {string} operation - 操作类型
   * @returns {FileSystemError} 文件系统错误实例
   */
  static createFileSystemError(message, path, operation) {
    return new FileSystemError(message, path, operation);
  }

  /**
   * 创建验证错误
   * @param {string} message - 错误消息
   * @param {string} field - 字段名
   * @param {*} value - 字段值
   * @returns {ValidationError} 验证错误实例
   */
  static createValidationError(message, field, value) {
    return new ValidationError(message, field, value);
  }

  /**
   * 创建配置错误
   * @param {string} message - 错误消息
   * @param {string} configKey - 配置键
   * @param {*} configValue - 配置值
   * @returns {ConfigurationError} 配置错误实例
   */
  static createConfigurationError(message, configKey, configValue) {
    return new ConfigurationError(message, configKey, configValue);
  }

  /**
   * 创建权限错误
   * @param {string} message - 错误消息
   * @param {string} resource - 资源
   * @param {string} requiredPermission - 所需权限
   * @returns {PermissionError} 权限错误实例
   */
  static createPermissionError(message, resource, requiredPermission) {
    return new PermissionError(message, resource, requiredPermission);
  }

  /**
   * 从原生错误创建自定义错误
   * @param {Error} error - 原生错误
   * @param {string} type - 错误类型
   * @returns {BaseError} 自定义错误实例
   */
  static fromNativeError(error, type = 'BaseError') {
    const ErrorClass = this.getErrorClass(type);
    const customError = new ErrorClass(error.message);
    customError.cause = error;
    customError.stack = error.stack;
    return customError;
  }

  /**
   * 获取错误类
   * @param {string} type - 错误类型
   * @returns {Function} 错误类构造函数
   * @private
   */
  static getErrorClass(type) {
    const errorClasses = {
      BaseError,
      AbortError,
      ExecutionError,
      TimeoutError,
      NetworkError,
      FileSystemError,
      ValidationError,
      ConfigurationError,
      PermissionError
    };
    
    return errorClasses[type] || BaseError;
  }
}

/**
 * 错误处理工具
 * @description 提供错误处理的便捷方法
 */
export class ErrorUtils {
  /**
   * 检查错误是否为指定类型
   * @param {Error} error - 错误对象
   * @param {Function} ErrorClass - 错误类
   * @returns {boolean} 是否为指定类型
   */
  static isErrorType(error, ErrorClass) {
    return error instanceof ErrorClass;
  }

  /**
   * 获取错误的根本原因
   * @param {Error} error - 错误对象
   * @returns {Error} 根本原因错误
   */
  static getRootCause(error) {
    let current = error;
    while (current.cause) {
      current = current.cause;
    }
    return current;
  }

  /**
   * 格式化错误信息
   * @param {Error} error - 错误对象
   * @param {boolean} includeStack - 是否包含堆栈信息
   * @returns {string} 格式化后的错误信息
   */
  static formatError(error, includeStack = false) {
    let message = `${error.name}: ${error.message}`;
    
    if (error.code) {
      message += ` (${error.code})`;
    }
    
    if (includeStack && error.stack) {
      message += `\n${error.stack}`;
    }
    
    return message;
  }

  /**
   * 安全地执行函数并捕获错误
   * @param {Function} fn - 要执行的函数
   * @param {*} defaultValue - 默认返回值
   * @returns {*} 函数结果或默认值
   */
  static safeExecute(fn, defaultValue = null) {
    try {
      return fn();
    } catch (error) {
      console.error('Safe execution failed:', error);
      return defaultValue;
    }
  }

  /**
   * 安全地执行异步函数并捕获错误
   * @param {Function} fn - 要执行的异步函数
   * @param {*} defaultValue - 默认返回值
   * @returns {Promise<*>} 函数结果或默认值
   */
  static async safeExecuteAsync(fn, defaultValue = null) {
    try {
      return await fn();
    } catch (error) {
      console.error('Safe async execution failed:', error);
      return defaultValue;
    }
  }
}
