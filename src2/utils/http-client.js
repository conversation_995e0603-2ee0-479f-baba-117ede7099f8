/**
 * HTTP客户端工具
 * @description 重构自原始文件中的HTTP客户端相关代码，对应第2250-2350行
 * @original 原始代码行 2250-2350
 */

import { CustomError, HTTP_METHODS, createHeaders } from './http-utils.js';

/**
 * HTTP头部访问器
 * @description 用于管理HTTP头部的类
 * @original GB1类的实现
 */
export class HttpHeaders {
  constructor(headers = {}) {
    this._headers = {};
    
    // 初始化标准头部
    this.initializeStandardHeaders();
    
    // 设置传入的头部
    if (headers) {
      this.set(headers);
    }
  }

  /**
   * 初始化标准HTTP头部
   * @original 第2251行的accessor调用
   */
  initializeStandardHeaders() {
    const standardHeaders = [
      "Content-Type", 
      "Content-Length", 
      "Accept", 
      "Accept-Encoding", 
      "User-Agent", 
      "Authorization"
    ];
    
    standardHeaders.forEach(header => {
      this.accessor(header);
    });
  }

  /**
   * 创建头部访问器
   * @param {string} header - 头部名称
   * @original accessor方法的实现
   */
  accessor(header) {
    const normalizedHeader = this.normalizeHeader(header);
    const propertyName = header[0].toUpperCase() + header.slice(1);
    
    Object.defineProperty(this, propertyName, {
      get: () => this._headers[normalizedHeader],
      set: (value) => {
        this._headers[normalizedHeader] = value;
      },
      enumerable: true,
      configurable: true
    });
  }

  /**
   * 规范化头部名称
   * @param {string} name - 头部名称
   * @returns {string} 规范化后的头部名称
   */
  normalizeHeader(name) {
    return name.toLowerCase();
  }

  /**
   * 设置头部
   * @param {string|Object} name - 头部名称或头部对象
   * @param {string} value - 头部值
   */
  set(name, value) {
    if (typeof name === 'object') {
      Object.keys(name).forEach(key => {
        this.set(key, name[key]);
      });
      return;
    }
    
    const normalizedName = this.normalizeHeader(name);
    this._headers[normalizedName] = value;
  }

  /**
   * 获取头部值
   * @param {string} name - 头部名称
   * @returns {string} 头部值
   */
  get(name) {
    const normalizedName = this.normalizeHeader(name);
    return this._headers[normalizedName];
  }

  /**
   * 检查是否存在指定头部
   * @param {string} name - 头部名称
   * @returns {boolean} 是否存在
   */
  has(name) {
    const normalizedName = this.normalizeHeader(name);
    return normalizedName in this._headers;
  }

  /**
   * 删除头部
   * @param {string} name - 头部名称
   */
  delete(name) {
    const normalizedName = this.normalizeHeader(name);
    delete this._headers[normalizedName];
  }

  /**
   * 获取所有头部
   * @returns {Object} 所有头部
   */
  toJSON() {
    return { ...this._headers };
  }

  /**
   * 转换为字符串
   * @returns {string} 字符串表示
   */
  toString() {
    return Object.keys(this._headers)
      .map(key => `${key}: ${this._headers[key]}`)
      .join('\n');
  }
}

/**
 * 取消令牌
 * @description 用于取消HTTP请求的令牌
 * @original bn0类的实现
 */
export class CancelToken {
  constructor(executor) {
    this.promise = new Promise(resolve => {
      this._resolve = resolve;
    });
    
    this.reason = null;
    this.__CANCEL__ = true;
    
    if (executor) {
      executor(this.cancel.bind(this));
    }
  }

  /**
   * 取消请求
   * @param {string} message - 取消消息
   */
  cancel(message) {
    if (this.reason) {
      return;
    }
    
    this.reason = new CustomError(message || 'Request canceled', null, 'ERR_CANCELED');
    this._resolve(this.reason);
  }

  /**
   * 检查是否已取消
   * @returns {boolean} 是否已取消
   */
  get isCanceled() {
    return !!this.reason;
  }

  /**
   * 创建取消令牌源
   * @returns {Object} 取消令牌源
   */
  static source() {
    let cancel;
    const token = new CancelToken(c => {
      cancel = c;
    });
    
    return {
      token,
      cancel
    };
  }
}

/**
 * 响应类型处理器
 * @description 处理不同类型的HTTP响应
 * @original 第2267-2273行的响应类型处理
 */
export const ResponseTypeHandlers = {
  /**
   * 文本响应处理器
   * @param {Response} response - 响应对象
   * @returns {Promise<string>} 文本内容
   */
  text: (response) => {
    if (typeof response.text === 'function') {
      return response.text();
    }
    throw new CustomError("Response type 'text' is not supported", null, 'ERR_NOT_SUPPORT');
  },

  /**
   * ArrayBuffer响应处理器
   * @param {Response} response - 响应对象
   * @returns {Promise<ArrayBuffer>} ArrayBuffer内容
   */
  arrayBuffer: (response) => {
    if (typeof response.arrayBuffer === 'function') {
      return response.arrayBuffer();
    }
    throw new CustomError("Response type 'arrayBuffer' is not supported", null, 'ERR_NOT_SUPPORT');
  },

  /**
   * Blob响应处理器
   * @param {Response} response - 响应对象
   * @returns {Promise<Blob>} Blob内容
   */
  blob: (response) => {
    if (typeof response.blob === 'function') {
      return response.blob();
    }
    throw new CustomError("Response type 'blob' is not supported", null, 'ERR_NOT_SUPPORT');
  },

  /**
   * FormData响应处理器
   * @param {Response} response - 响应对象
   * @returns {Promise<FormData>} FormData内容
   */
  formData: (response) => {
    if (typeof response.formData === 'function') {
      return response.formData();
    }
    throw new CustomError("Response type 'formData' is not supported", null, 'ERR_NOT_SUPPORT');
  },

  /**
   * Stream响应处理器
   * @param {Response} response - 响应对象
   * @returns {Promise<ReadableStream>} Stream内容
   */
  stream: (response) => {
    if (typeof response.stream === 'function') {
      return response.stream();
    }
    throw new CustomError("Response type 'stream' is not supported", null, 'ERR_NOT_SUPPORT');
  }
};

/**
 * 类型验证器
 * @description 用于验证不同类型的值
 * @original 第2286-2290行的类型验证器
 */
export const TypeValidators = {
  object: (value) => typeof value === 'object' || 'an object',
  boolean: (value) => typeof value === 'boolean' || 'a boolean',
  number: (value) => typeof value === 'number' || 'a number',
  function: (value) => typeof value === 'function' || 'a function',
  string: (value) => typeof value === 'string' || 'a string',
  symbol: (value) => typeof value === 'symbol' || 'a symbol'
};

/**
 * HTTP客户端基类
 * @description 基础HTTP客户端实现
 * @original EB1类的实现
 */
export class HttpClient {
  constructor(config = {}) {
    this.config = config;
    this.headers = new HttpHeaders(config.headers);
  }

  /**
   * 发送请求
   * @param {Object} config - 请求配置
   * @returns {Promise} 请求Promise
   */
  async request(config) {
    const mergedConfig = this.mergeConfig(this.config, config);
    
    try {
      // 这里应该实现实际的HTTP请求逻辑
      // 为了演示，我们返回一个模拟响应
      return {
        data: null,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: mergedConfig
      };
    } catch (error) {
      throw new CustomError(error.message, mergedConfig, error.code);
    }
  }

  /**
   * 合并配置
   * @param {Object} config1 - 配置1
   * @param {Object} config2 - 配置2
   * @returns {Object} 合并后的配置
   */
  mergeConfig(config1, config2) {
    return {
      ...config1,
      ...config2,
      headers: {
        ...config1.headers,
        ...config2.headers
      }
    };
  }
}

// 为HTTP客户端添加便捷方法
HTTP_METHODS.forEach(method => {
  if (['delete', 'get', 'head', 'options'].includes(method)) {
    HttpClient.prototype[method] = function(url, config = {}) {
      return this.request({
        ...config,
        method: method,
        url: url,
        data: config.data
      });
    };
  }
});

['post', 'put', 'patch'].forEach(method => {
  HttpClient.prototype[method] = function(url, data, config = {}) {
    return this.request({
      ...config,
      method: method,
      url: url,
      data: data
    });
  };
  
  HttpClient.prototype[method + 'Form'] = function(url, data, config = {}) {
    return this.request({
      ...config,
      method: method,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config.headers
      },
      url: url,
      data: data
    });
  };
});

/**
 * HTTP状态码映射
 * @description HTTP状态码的双向映射
 * @original 第2315-2317行的状态码映射
 */
export const HttpStatusCode = {
  // 1xx Informational
  Continue: 100,
  SwitchingProtocols: 101,
  
  // 2xx Success
  Ok: 200,
  Created: 201,
  Accepted: 202,
  NoContent: 204,
  
  // 3xx Redirection
  MultipleChoices: 300,
  MovedPermanently: 301,
  Found: 302,
  NotModified: 304,
  
  // 4xx Client Error
  BadRequest: 400,
  Unauthorized: 401,
  Forbidden: 403,
  NotFound: 404,
  MethodNotAllowed: 405,
  Conflict: 409,
  UnprocessableEntity: 422,
  TooManyRequests: 429,
  
  // 5xx Server Error
  InternalServerError: 500,
  NotImplemented: 501,
  BadGateway: 502,
  ServiceUnavailable: 503,
  GatewayTimeout: 504
};

// 创建反向映射
Object.entries(HttpStatusCode).forEach(([name, code]) => {
  HttpStatusCode[code] = name;
});

/**
 * 模拟浏览器环境
 * @description 在Node.js环境中模拟浏览器对象
 * @original 第2336-2350行的浏览器环境模拟
 */
export const MockBrowserEnv = {
  /**
   * 模拟document对象
   */
  document: {
    visibilityState: "visible",
    documentElement: {
      lang: "en"
    },
    addEventListener: (event, callback) => {
      // 模拟事件监听器
    }
  },

  /**
   * 模拟window对象
   */
  window: {
    document: null, // 将在初始化时设置
    location: {
      href: "node://localhost",
      pathname: "/"
    },
    addEventListener: (event, callback) => {
      if (event === "beforeunload") {
        process.on("exit", () => {
          callback();
        });
      }
    }
  }
};

// 设置document引用
MockBrowserEnv.window.document = MockBrowserEnv.document;

/**
 * 检查是否为取消错误
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否为取消错误
 */
export function isCancel(error) {
  return error && error.__CANCEL__ === true;
}

/**
 * 创建默认HTTP客户端实例
 * @param {Object} config - 配置对象
 * @returns {HttpClient} HTTP客户端实例
 */
export function createHttpClient(config = {}) {
  return new HttpClient(config);
}
