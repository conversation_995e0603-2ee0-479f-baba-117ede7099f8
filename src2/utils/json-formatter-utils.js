/**
 * JSON格式化工具函数
 * @description 重构自原始文件中的JSON格式化代码，对应第3902-4000行
 * @original 原始代码行 3902-4000
 */

/**
 * 空格缓存数组
 * @description 预生成的空格字符串数组，用于性能优化
 * @original rC变量
 */
const SPACE_CACHE = new Array(20).fill(0).map((_, index) => " ".repeat(index));

/**
 * 换行符和缩进组合缓存
 * @description 预生成的换行符+缩进组合，用于性能优化
 * @original Mr1变量
 */
const LINE_INDENT_CACHE = {
  " ": {
    "\n": new Array(200).fill(0).map((_, index) => "\n" + " ".repeat(index)),
    "\r": new Array(200).fill(0).map((_, index) => "\r" + " ".repeat(index)),
    "\r\n": new Array(200).fill(0).map((_, index) => "\r\n" + " ".repeat(index))
  },
  "\t": {
    "\n": new Array(200).fill(0).map((_, index) => "\n" + "\t".repeat(index)),
    "\r": new Array(200).fill(0).map((_, index) => "\r" + "\t".repeat(index)),
    "\r\n": new Array(200).fill(0).map((_, index) => "\r\n" + "\t".repeat(index))
  }
};

/**
 * 换行符类型数组
 * @original pIA变量
 */
const LINE_ENDINGS = ["\n", "\r", "\r\n"];

/**
 * JSON格式化选项接口
 * @typedef {Object} JSONFormatOptions
 * @property {boolean} insertSpaces - 是否使用空格缩进
 * @property {number} tabSize - 制表符大小
 * @property {boolean} keepLines - 是否保持原有换行
 */

/**
 * JSON格式化器类
 * @description 提供JSON格式化功能
 */
export class JSONFormatter {
  constructor() {
    this.spaceCache = SPACE_CACHE;
    this.lineIndentCache = LINE_INDENT_CACHE;
    this.lineEndings = LINE_ENDINGS;
  }

  /**
   * 格式化JSON文本
   * @param {string} text - 要格式化的JSON文本
   * @param {Object} range - 格式化范围（可选）
   * @param {JSONFormatOptions} options - 格式化选项
   * @returns {Array} 格式化编辑数组
   * @original Rr1函数
   */
  format(text, range, options) {
    let startOffset, endOffset, scanStart, rangeStart, rangeEnd;
    
    if (range) {
      rangeStart = range.offset;
      rangeEnd = rangeStart + range.length;
      scanStart = rangeStart;
      
      // 找到范围开始前的行开始位置
      while (scanStart > 0 && !this.isLineBreak(text, scanStart - 1)) {
        scanStart--;
      }
      
      // 找到范围结束后的行结束位置
      let scanEnd = rangeEnd;
      while (scanEnd < text.length && !this.isLineBreak(text, scanEnd)) {
        scanEnd++;
      }
      
      const scanText = text.substring(scanStart, scanEnd);
      startOffset = this.computeIndentLevel(scanText, options);
    } else {
      const scanText = text;
      startOffset = 0;
      scanStart = 0;
      rangeStart = 0;
      rangeEnd = text.length;
    }

    const lineEnding = this.getLineEnding(options, text);
    const hasLineBreaks = this.lineEndings.includes(lineEnding);
    
    let lineCount = 0;
    let indentLevel = 0;
    let indentChar;
    
    if (options.insertSpaces) {
      indentChar = this.spaceCache[options.tabSize || 4] ?? 
                   this.repeatString(this.spaceCache[1], options.tabSize || 4);
    } else {
      indentChar = "\t";
    }
    
    const indentType = indentChar === "\t" ? "\t" : " ";
    const scanner = this.createJSONScanner(scanText, false);
    let hasError = false;

    // 生成缩进字符串
    const generateIndent = () => {
      if (lineCount > 1) {
        return this.repeatString(lineEnding, lineCount) + 
               this.repeatString(indentChar, startOffset + indentLevel);
      }
      
      const indentLength = indentChar.length * (startOffset + indentLevel);
      
      if (!hasLineBreaks || indentLength > this.lineIndentCache[indentType][lineEnding].length) {
        return lineEnding + this.repeatString(indentChar, startOffset + indentLevel);
      }
      
      if (indentLength <= 0) {
        return lineEnding;
      }
      
      return this.lineIndentCache[indentType][lineEnding][indentLength];
    };

    // 扫描下一个token
    const scanNext = () => {
      const token = scanner.scan();
      lineCount = 0;
      
      while (token === 15 || token === 14) { // 空白字符或换行
        if (token === 14 && options.keepLines) {
          lineCount += 1;
        } else if (token === 14) {
          lineCount = 1;
        }
        token = scanner.scan();
      }
      
      hasError = token === 16 || scanner.getTokenError() !== 0;
      return token;
    };

    const edits = [];
    
    // 添加编辑
    const addEdit = (content, start, end) => {
      if (!hasError && 
          (!range || start < rangeEnd && end > rangeStart) && 
          text.substring(start, end) !== content) {
        edits.push({
          offset: start,
          length: end - start,
          content: content
        });
      }
    };

    let token = scanNext();
    
    // 处理开始的换行
    if (options.keepLines && lineCount > 0) {
      addEdit(this.repeatString(lineEnding, lineCount), 0, 0);
    }
    
    // 处理第一个token前的缩进
    if (token !== 17) { // 不是EOF
      const tokenStart = scanner.getTokenOffset() + scanStart;
      const indent = indentChar.length * startOffset < 20 && options.insertSpaces ? 
                     this.spaceCache[indentChar.length * startOffset] : 
                     this.repeatString(indentChar, startOffset);
      addEdit(indent, scanStart, tokenStart);
    }

    // 处理所有tokens
    while (token !== 17) { // 不是EOF
      let tokenEnd = scanner.getTokenOffset() + scanner.getTokenLength() + scanStart;
      let nextToken = scanNext();
      let content = "";
      let needsIndent = false;

      // 处理空白字符
      while (lineCount === 0 && (nextToken === 12 || nextToken === 13)) { // 逗号或冒号
        const tokenStart = scanner.getTokenOffset() + scanStart;
        addEdit(this.spaceCache[1], tokenEnd, tokenStart);
        tokenEnd = scanner.getTokenOffset() + scanner.getTokenLength() + scanStart;
        needsIndent = nextToken === 12; // 逗号后需要缩进
        content = needsIndent ? generateIndent() : "";
        nextToken = scanNext();
      }

      // 处理结构字符
      if (nextToken === 2) { // 右大括号
        if (token !== 1) indentLevel--; // 不是左大括号
        if (options.keepLines && lineCount > 0 || !options.keepLines && token !== 1) {
          content = generateIndent();
        } else if (options.keepLines) {
          content = this.spaceCache[1];
        }
      } else if (nextToken === 4) { // 右中括号
        if (token !== 3) indentLevel--; // 不是左中括号
        if (options.keepLines && lineCount > 0 || !options.keepLines && token !== 3) {
          content = generateIndent();
        } else if (options.keepLines) {
          content = this.spaceCache[1];
        }
      } else {
        // 处理其他情况
        switch (token) {
          case 1: // 左大括号
          case 3: // 左中括号
            indentLevel++;
            if (options.keepLines && lineCount > 0 || !options.keepLines) {
              content = generateIndent();
            } else if (options.keepLines) {
              content = this.spaceCache[1];
            }
            break;
          case 12: // 逗号
            if (options.keepLines && lineCount > 0 || !options.keepLines) {
              content = generateIndent();
            } else if (options.keepLines) {
              content = this.spaceCache[1];
            }
            break;
          case 13: // 冒号
            content = this.spaceCache[1];
            break;
        }
      }

      if (content) {
        addEdit(content, tokenEnd, scanner.getTokenOffset() + scanStart);
      }

      token = nextToken;
    }

    return edits;
  }

  /**
   * 重复字符串
   * @param {string} str - 要重复的字符串
   * @param {number} count - 重复次数
   * @returns {string} 重复后的字符串
   * @original fi函数
   */
  repeatString(str, count) {
    return str.repeat(count);
  }

  /**
   * 检查是否为换行符
   * @param {string} text - 文本
   * @param {number} offset - 偏移量
   * @returns {boolean} 是否为换行符
   * @original y91函数
   */
  isLineBreak(text, offset) {
    const char = text.charAt(offset);
    return char === '\n' || char === '\r';
  }

  /**
   * 计算缩进级别
   * @param {string} text - 文本
   * @param {JSONFormatOptions} options - 选项
   * @returns {number} 缩进级别
   * @original P5Q函数
   */
  computeIndentLevel(text, options) {
    // @todo: 实现P5Q函数的实际逻辑
    return 0;
  }

  /**
   * 获取换行符类型
   * @param {JSONFormatOptions} options - 选项
   * @param {string} text - 文本
   * @returns {string} 换行符
   * @original S5Q函数
   */
  getLineEnding(options, text) {
    // @todo: 实现S5Q函数的实际逻辑
    // 检测文本中使用的换行符类型
    if (text.includes('\r\n')) return '\r\n';
    if (text.includes('\r')) return '\r';
    return '\n';
  }

  /**
   * 创建JSON扫描器
   * @param {string} text - 要扫描的文本
   * @param {boolean} ignoreTrivia - 是否忽略空白字符
   * @returns {Object} JSON扫描器
   * @original j91函数
   */
  createJSONScanner(text, ignoreTrivia) {
    // @todo: 实现j91函数的实际逻辑
    // 这里返回一个简化的扫描器实现
    let offset = 0;
    
    return {
      scan() {
        // 简化的token扫描逻辑
        if (offset >= text.length) return 17; // EOF
        
        const char = text.charAt(offset);
        offset++;
        
        switch (char) {
          case '{': return 1;  // 左大括号
          case '}': return 2;  // 右大括号
          case '[': return 3;  // 左中括号
          case ']': return 4;  // 右中括号
          case ',': return 12; // 逗号
          case ':': return 13; // 冒号
          case '\n': return 14; // 换行
          case ' ':
          case '\t': return 15; // 空白
          default: return 0;   // 其他
        }
      },
      
      getTokenOffset() {
        return offset - 1;
      },
      
      getTokenLength() {
        return 1;
      },
      
      getTokenError() {
        return 0;
      }
    };
  }
}

/**
 * JSON格式化工具函数
 */
export const JSONFormatterUtils = {
  /**
   * 格式化JSON字符串
   * @param {string} jsonString - JSON字符串
   * @param {JSONFormatOptions} options - 格式化选项
   * @returns {string} 格式化后的JSON字符串
   */
  formatJSON(jsonString, options = {}) {
    const formatter = new JSONFormatter();
    const defaultOptions = {
      insertSpaces: true,
      tabSize: 2,
      keepLines: false,
      ...options
    };
    
    const edits = formatter.format(jsonString, null, defaultOptions);
    
    // 应用编辑
    let result = jsonString;
    for (let i = edits.length - 1; i >= 0; i--) {
      const edit = edits[i];
      result = result.substring(0, edit.offset) + 
               edit.content + 
               result.substring(edit.offset + edit.length);
    }
    
    return result;
  },

  /**
   * 压缩JSON字符串
   * @param {string} jsonString - JSON字符串
   * @returns {string} 压缩后的JSON字符串
   */
  minifyJSON(jsonString) {
    try {
      return JSON.stringify(JSON.parse(jsonString));
    } catch (error) {
      throw new Error(`Invalid JSON: ${error.message}`);
    }
  },

  /**
   * 验证JSON字符串
   * @param {string} jsonString - JSON字符串
   * @returns {Object} 验证结果
   */
  validateJSON(jsonString) {
    try {
      JSON.parse(jsonString);
      return { valid: true, error: null };
    } catch (error) {
      return { valid: false, error: error.message };
    }
  },

  /**
   * 美化JSON字符串
   * @param {string} jsonString - JSON字符串
   * @param {number} indent - 缩进空格数
   * @returns {string} 美化后的JSON字符串
   */
  beautifyJSON(jsonString, indent = 2) {
    try {
      return JSON.stringify(JSON.parse(jsonString), null, indent);
    } catch (error) {
      throw new Error(`Invalid JSON: ${error.message}`);
    }
  }
};

// 创建默认JSON格式化器实例
export const jsonFormatter = new JSONFormatter();
