/**
 * Windows路径处理工具
 * @description 重构自原始文件中的Windows路径处理代码，对应第2839-2890行
 * @original 原始代码行 2839-2890
 */

import { isAbsolute, posix, resolve, sep } from "path";
import { execSync } from "node:child_process";
import * as win32Path from "node:path/win32";
import { createLazyLoader } from '../core/module-imports.js';

/**
 * 获取当前平台
 * @returns {string} 平台名称
 * @original P9函数的实现（推测）
 */
function getCurrentPlatform() {
  return process.platform === 'win32' ? 'windows' : process.platform;
}

/**
 * 检查目录是否存在（Windows）
 * @param {string} dirPath - 目录路径
 * @returns {boolean} 目录是否存在
 * @original Gr1函数
 */
function checkDirectoryExists(dirPath) {
  try {
    execSync(`dir "${dirPath}"`, {
      stdio: "pipe"
    });
    return true;
  } catch {
    return false;
  }
}

/**
 * 在Windows上查找可执行文件
 * @param {string} command - 命令名称
 * @returns {string|null} 可执行文件路径或null
 * @original r6Q函数
 */
function findWindowsExecutable(command) {
  if (command === "git") {
    const gitPaths = [
      "C:\\Program Files\\Git\\cmd\\git.exe",
      "C:\\Program Files (x86)\\Git\\cmd\\git.exe"
    ];
    
    for (const gitPath of gitPaths) {
      if (checkDirectoryExists(gitPath)) {
        return gitPath;
      }
    }
  }
  
  try {
    const result = execSync(`where.exe ${command}`, {
      stdio: "pipe",
      encoding: "utf8"
    }).trim().split('\r\n')[0] || null;
    
    return result;
  } catch {
    return null;
  }
}

/**
 * 初始化Windows环境
 * @description 设置Windows环境下的Shell路径
 * @original FIA函数
 */
export const initializeWindowsEnvironment = () => {
  if (getCurrentPlatform() === "windows") {
    const bashPath = getBashPath();
    process.env.SHELL = bashPath;
    console.log(`Using bash path: "${bashPath}"`);
  }
};

/**
 * 获取Bash路径（Windows）
 * @returns {string} Bash可执行文件路径
 * @throws {Error} 如果找不到Bash
 * @original IIA函数
 */
export const getBashPath = createLazyLoader(() => {
  // 检查环境变量中的自定义路径
  if (process.env.CLAUDE_CODE_GIT_BASH_PATH) {
    if (checkDirectoryExists(process.env.CLAUDE_CODE_GIT_BASH_PATH)) {
      return process.env.CLAUDE_CODE_GIT_BASH_PATH;
    }
    
    console.error(`Claude Code was unable to find CLAUDE_CODE_GIT_BASH_PATH path "${process.env.CLAUDE_CODE_GIT_BASH_PATH}"`);
    process.exit(1);
  }
  
  // 查找Git安装路径
  const gitPath = findWindowsExecutable("git");
  if (gitPath) {
    const bashPath = win32Path.join(gitPath, "..", "..", "bin", "bash.exe");
    if (checkDirectoryExists(bashPath)) {
      return bashPath;
    }
  }
  
  // 如果找不到，显示错误信息并退出
  console.error(
    "Claude Code on Windows requires git-bash (https://git-scm.com/downloads/win). " +
    "If installed but not in PATH, set environment variable pointing to your bash.exe, " +
    "similar to: CLAUDE_CODE_GIT_BASH_PATH=C:\\Program Files\\Git\\bin\\bash.exe"
  );
  process.exit(1);
});

/**
 * 将Windows路径转换为Cygwin路径
 * @param {string} windowsPath - Windows路径
 * @returns {string} Cygwin路径
 * @original Xi函数
 */
export function convertToCygwinPath(windowsPath) {
  // @todo: 实现GIA.quote的逻辑
  const quotedPath = `"${windowsPath}"`;
  
  return execSync(`cygpath -u ${quotedPath}`, {
    shell: getBashPath()
  }).toString().trim();
}

/**
 * Windows路径工具类
 * @description 提供Windows特定的路径操作
 */
export class WindowsPathUtils {
  /**
   * 检查是否为Windows平台
   * @returns {boolean} 是否为Windows平台
   */
  static isWindows() {
    return process.platform === 'win32';
  }

  /**
   * 规范化Windows路径
   * @param {string} path - 路径
   * @returns {string} 规范化后的路径
   */
  static normalize(path) {
    return win32Path.normalize(path);
  }

  /**
   * 解析Windows路径
   * @param {...string} paths - 路径片段
   * @returns {string} 解析后的路径
   */
  static resolve(...paths) {
    return win32Path.resolve(...paths);
  }

  /**
   * 连接Windows路径
   * @param {...string} paths - 路径片段
   * @returns {string} 连接后的路径
   */
  static join(...paths) {
    return win32Path.join(...paths);
  }

  /**
   * 获取Windows路径的相对路径
   * @param {string} from - 起始路径
   * @param {string} to - 目标路径
   * @returns {string} 相对路径
   */
  static relative(from, to) {
    return win32Path.relative(from, to);
  }

  /**
   * 获取Windows路径的目录名
   * @param {string} path - 路径
   * @returns {string} 目录名
   */
  static dirname(path) {
    return win32Path.dirname(path);
  }

  /**
   * 获取Windows路径的基础名
   * @param {string} path - 路径
   * @param {string} ext - 扩展名（可选）
   * @returns {string} 基础名
   */
  static basename(path, ext) {
    return win32Path.basename(path, ext);
  }

  /**
   * 获取Windows路径的扩展名
   * @param {string} path - 路径
   * @returns {string} 扩展名
   */
  static extname(path) {
    return win32Path.extname(path);
  }

  /**
   * 解析Windows路径为对象
   * @param {string} path - 路径
   * @returns {Object} 路径对象
   */
  static parse(path) {
    return win32Path.parse(path);
  }

  /**
   * 从路径对象格式化Windows路径
   * @param {Object} pathObject - 路径对象
   * @returns {string} 格式化后的路径
   */
  static format(pathObject) {
    return win32Path.format(pathObject);
  }

  /**
   * 检查Windows路径是否为绝对路径
   * @param {string} path - 路径
   * @returns {boolean} 是否为绝对路径
   */
  static isAbsolute(path) {
    return win32Path.isAbsolute(path);
  }

  /**
   * 转换路径分隔符为Windows格式
   * @param {string} path - 路径
   * @returns {string} Windows格式的路径
   */
  static toWindowsPath(path) {
    return path.replace(/\//g, '\\');
  }

  /**
   * 转换路径分隔符为POSIX格式
   * @param {string} path - 路径
   * @returns {string} POSIX格式的路径
   */
  static toPosixPath(path) {
    return path.replace(/\\/g, '/');
  }

  /**
   * 获取驱动器字母
   * @param {string} path - Windows路径
   * @returns {string|null} 驱动器字母或null
   */
  static getDriveLetter(path) {
    const match = path.match(/^([A-Za-z]):/);
    return match ? match[1].toUpperCase() : null;
  }

  /**
   * 检查路径是否为UNC路径
   * @param {string} path - 路径
   * @returns {boolean} 是否为UNC路径
   */
  static isUNCPath(path) {
    return path.startsWith('\\\\');
  }

  /**
   * 转义Windows路径中的特殊字符
   * @param {string} path - 路径
   * @returns {string} 转义后的路径
   */
  static escapePath(path) {
    return path.replace(/[&|<>^]/g, '^$&');
  }

  /**
   * 获取短路径名（8.3格式）
   * @param {string} longPath - 长路径
   * @returns {string} 短路径名
   */
  static getShortPath(longPath) {
    try {
      return execSync(`for %I in ("${longPath}") do @echo %~sI`, {
        shell: 'cmd.exe',
        encoding: 'utf8'
      }).trim();
    } catch {
      return longPath;
    }
  }

  /**
   * 检查路径是否存在
   * @param {string} path - 路径
   * @returns {boolean} 路径是否存在
   */
  static exists(path) {
    return checkDirectoryExists(path);
  }

  /**
   * 查找可执行文件
   * @param {string} command - 命令名
   * @returns {string|null} 可执行文件路径或null
   */
  static findExecutable(command) {
    return findWindowsExecutable(command);
  }

  /**
   * 获取环境变量路径数组
   * @returns {string[]} 路径数组
   */
  static getPathEnvironment() {
    const pathEnv = process.env.PATH || process.env.Path || '';
    return pathEnv.split(win32Path.delimiter).filter(Boolean);
  }

  /**
   * 添加路径到环境变量
   * @param {string} newPath - 新路径
   * @param {boolean} prepend - 是否添加到开头
   */
  static addToPath(newPath, prepend = false) {
    const currentPaths = this.getPathEnvironment();
    
    if (!currentPaths.includes(newPath)) {
      if (prepend) {
        currentPaths.unshift(newPath);
      } else {
        currentPaths.push(newPath);
      }
      
      process.env.PATH = currentPaths.join(win32Path.delimiter);
    }
  }
}

/**
 * 跨平台路径工具
 * @description 提供跨平台的路径操作
 */
export class CrossPlatformPathUtils {
  /**
   * 根据当前平台规范化路径
   * @param {string} path - 路径
   * @returns {string} 规范化后的路径
   */
  static normalize(path) {
    return WindowsPathUtils.isWindows() ? 
      WindowsPathUtils.normalize(path) : 
      posix.normalize(path);
  }

  /**
   * 根据当前平台解析路径
   * @param {...string} paths - 路径片段
   * @returns {string} 解析后的路径
   */
  static resolve(...paths) {
    return WindowsPathUtils.isWindows() ? 
      WindowsPathUtils.resolve(...paths) : 
      posix.resolve(...paths);
  }

  /**
   * 根据当前平台连接路径
   * @param {...string} paths - 路径片段
   * @returns {string} 连接后的路径
   */
  static join(...paths) {
    return WindowsPathUtils.isWindows() ? 
      WindowsPathUtils.join(...paths) : 
      posix.join(...paths);
  }

  /**
   * 获取当前平台的路径分隔符
   * @returns {string} 路径分隔符
   */
  static getSeparator() {
    return WindowsPathUtils.isWindows() ? '\\' : '/';
  }

  /**
   * 转换为当前平台的路径格式
   * @param {string} path - 路径
   * @returns {string} 平台特定格式的路径
   */
  static toPlatformPath(path) {
    return WindowsPathUtils.isWindows() ? 
      WindowsPathUtils.toWindowsPath(path) : 
      WindowsPathUtils.toPosixPath(path);
  }
}
