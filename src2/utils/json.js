/**
 * JSON处理工具函数
 * @description 提供安全的JSON解析和处理功能
 */

/**
 * 安全地解析JSON字符串
 * @param {string} jsonString - JSON字符串
 * @returns {Object|null} 解析后的对象，失败时返回null
 * @original if (!T7(s1)) process.stderr.write(A0.red(`Error: Invalid JSON provided to --settings`)), process.exit(1);
 */
export function parseJsonSafely(jsonString) {
  try {
    return JSON.parse(jsonString);
  } catch {
    return null;
  }
}

/**
 * 解析JSON字符串并抛出详细错误
 * @param {string} jsonString - JSON字符串
 * @returns {Object} 解析后的对象
 * @throws {Error} JSON解析失败时抛出详细错误
 */
export function parseJsonWithError(jsonString) {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    throw new Error(`Invalid JSON: ${error.message}`);
  }
}

/**
 * 安全地序列化对象为JSON字符串
 * @param {*} obj - 要序列化的对象
 * @param {number} space - 缩进空格数，默认为0
 * @returns {string|null} JSON字符串，失败时返回null
 */
export function stringifyJsonSafely(obj, space = 0) {
  try {
    return JSON.stringify(obj, null, space);
  } catch {
    return null;
  }
}

/**
 * 序列化对象为JSON字符串并抛出错误
 * @param {*} obj - 要序列化的对象
 * @param {number} space - 缩进空格数，默认为0
 * @returns {string} JSON字符串
 * @throws {Error} 序列化失败时抛出错误
 */
export function stringifyJsonWithError(obj, space = 0) {
  try {
    return JSON.stringify(obj, null, space);
  } catch (error) {
    throw new Error(`JSON serialization failed: ${error.message}`);
  }
}

/**
 * 检查字符串是否为有效的JSON
 * @param {string} str - 要检查的字符串
 * @returns {boolean} 是否为有效JSON
 */
export function isValidJson(str) {
  if (typeof str !== 'string') {
    return false;
  }
  
  try {
    JSON.parse(str);
    return true;
  } catch {
    return false;
  }
}

/**
 * 检查字符串是否看起来像JSON对象
 * @param {string} str - 要检查的字符串
 * @returns {boolean} 是否看起来像JSON对象
 * @original let A1 = s1.startsWith("{") && s1.endsWith("}");
 */
export function looksLikeJsonObject(str) {
  if (typeof str !== 'string') {
    return false;
  }
  
  const trimmed = str.trim();
  return trimmed.startsWith("{") && trimmed.endsWith("}");
}

/**
 * 检查字符串是否看起来像JSON数组
 * @param {string} str - 要检查的字符串
 * @returns {boolean} 是否看起来像JSON数组
 */
export function looksLikeJsonArray(str) {
  if (typeof str !== 'string') {
    return false;
  }
  
  const trimmed = str.trim();
  return trimmed.startsWith("[") && trimmed.endsWith("]");
}

/**
 * 深度合并JSON对象
 * @param {Object} target - 目标对象
 * @param {Object} source - 源对象
 * @returns {Object} 合并后的对象
 */
export function deepMergeJson(target, source) {
  if (!isObject(target) || !isObject(source)) {
    return source;
  }
  
  const result = { ...target };
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (isObject(source[key]) && isObject(result[key])) {
        result[key] = deepMergeJson(result[key], source[key]);
      } else {
        result[key] = source[key];
      }
    }
  }
  
  return result;
}

/**
 * 深度克隆JSON对象
 * @param {*} obj - 要克隆的对象
 * @returns {*} 克隆后的对象
 */
export function deepCloneJson(obj) {
  try {
    return JSON.parse(JSON.stringify(obj));
  } catch {
    return obj;
  }
}

/**
 * 从JSON对象中提取指定路径的值
 * @param {Object} obj - JSON对象
 * @param {string} path - 路径，使用点号分隔 (如 'a.b.c')
 * @param {*} defaultValue - 默认值
 * @returns {*} 提取的值或默认值
 */
export function getJsonValue(obj, path, defaultValue = undefined) {
  if (!isObject(obj) || typeof path !== 'string') {
    return defaultValue;
  }
  
  const keys = path.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current == null || typeof current !== 'object' || !(key in current)) {
      return defaultValue;
    }
    current = current[key];
  }
  
  return current;
}

/**
 * 设置JSON对象中指定路径的值
 * @param {Object} obj - JSON对象
 * @param {string} path - 路径，使用点号分隔 (如 'a.b.c')
 * @param {*} value - 要设置的值
 * @returns {Object} 修改后的对象
 */
export function setJsonValue(obj, path, value) {
  if (!isObject(obj) || typeof path !== 'string') {
    return obj;
  }
  
  const keys = path.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current) || !isObject(current[key])) {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[keys[keys.length - 1]] = value;
  return obj;
}

/**
 * 删除JSON对象中指定路径的值
 * @param {Object} obj - JSON对象
 * @param {string} path - 路径，使用点号分隔 (如 'a.b.c')
 * @returns {Object} 修改后的对象
 */
export function deleteJsonValue(obj, path) {
  if (!isObject(obj) || typeof path !== 'string') {
    return obj;
  }
  
  const keys = path.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current) || !isObject(current[key])) {
      return obj; // 路径不存在
    }
    current = current[key];
  }
  
  delete current[keys[keys.length - 1]];
  return obj;
}

/**
 * 扁平化JSON对象
 * @param {Object} obj - 要扁平化的对象
 * @param {string} prefix - 键前缀
 * @returns {Object} 扁平化后的对象
 */
export function flattenJson(obj, prefix = '') {
  const result = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (isObject(obj[key])) {
        Object.assign(result, flattenJson(obj[key], newKey));
      } else {
        result[newKey] = obj[key];
      }
    }
  }
  
  return result;
}

/**
 * 验证JSON对象是否符合简单的模式
 * @param {Object} obj - 要验证的对象
 * @param {Object} schema - 简单的模式对象
 * @returns {Object} 验证结果 { valid: boolean, errors: string[] }
 */
export function validateJsonSchema(obj, schema) {
  const errors = [];
  
  if (!isObject(obj)) {
    errors.push('Object is required');
    return { valid: false, errors };
  }
  
  if (!isObject(schema)) {
    errors.push('Schema is required');
    return { valid: false, errors };
  }
  
  // 简单的类型检查
  for (const key in schema) {
    if (schema.hasOwnProperty(key)) {
      const expectedType = schema[key];
      const actualValue = obj[key];
      
      if (expectedType === 'required' && !(key in obj)) {
        errors.push(`Missing required field: ${key}`);
      } else if (key in obj && typeof actualValue !== expectedType && expectedType !== 'required') {
        errors.push(`Field ${key} should be of type ${expectedType}, got ${typeof actualValue}`);
      }
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 检查值是否为对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为对象
 */
function isObject(value) {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
}

/**
 * 美化JSON字符串
 * @param {string} jsonString - JSON字符串
 * @param {number} space - 缩进空格数，默认为2
 * @returns {string|null} 美化后的JSON字符串，失败时返回null
 */
export function prettifyJson(jsonString, space = 2) {
  const obj = parseJsonSafely(jsonString);
  if (obj === null) {
    return null;
  }
  
  return stringifyJsonSafely(obj, space);
}

/**
 * 压缩JSON字符串（移除空白）
 * @param {string} jsonString - JSON字符串
 * @returns {string|null} 压缩后的JSON字符串，失败时返回null
 */
export function minifyJson(jsonString) {
  const obj = parseJsonSafely(jsonString);
  if (obj === null) {
    return null;
  }
  
  return stringifyJsonSafely(obj, 0);
}
