/**
 * Update命令处理器
 * 
 * 处理Claude Code的更新检查和安装命令
 * 
 * @original: update命令处理逻辑 (L56642-56837)
 */

import { logger } from '../../utils/logger.js';
import { executeCommand } from '../../utils/process.js';
import { APP_INFO } from '../../config/constants.js';

/**
 * 处理update命令
 * @original: LcB()函数 (L56642)
 */
export async function handleUpdateCommand() {
  logger.debug('处理update命令');
  
  try {
    // 记录遥测事件
    logTelemetryEvent('tengu_update_check', {});
    
    console.log(`Current version: ${APP_INFO.VERSION}`);
    console.log('Checking for updates...');
    
    logger.debug('update: 开始更新检查');
    logger.debug('update: 运行诊断');

    // 运行诊断
    const diagnostic = await runDiagnostic();
    
    logger.debug(`update: 安装类型: ${diagnostic.installationType}`);
    logger.debug(`update: 配置安装方法: ${diagnostic.configInstallMethod}`);

    // 检查多重安装
    if (diagnostic.multipleInstallations.length > 1) {
      console.log('');
      console.log(colorText('yellow', 'Warning: Multiple installations found'));
      
      for (const installation of diagnostic.multipleInstallations) {
        const current = diagnostic.installationType === installation.type ? ' (currently running)' : '';
        console.log(`- ${installation.type} at ${installation.path}${current}`);
      }
    }

    // 显示警告
    if (diagnostic.warnings.length > 0) {
      console.log('');
      
      for (const warning of diagnostic.warnings) {
        logger.debug(`update: 检测到警告: ${warning.issue}`);
        logger.debug(`update: 显示警告: ${warning.issue}`);
        
        console.log(colorText('yellow', `Warning: ${warning.issue}`));
        console.log(colorText('bold', `Fix: ${warning.fix}`));
      }
    }

    // 更新配置中的安装方法
    await updateInstallationMethodConfig(diagnostic);

    // 处理开发构建
    if (diagnostic.installationType === 'development') {
      console.log('');
      console.log(colorText('yellow', 'Warning: Cannot update development build'));
      process.exit(1);
    }

    // 处理原生安装
    if (diagnostic.installationType === 'native') {
      return await handleNativeUpdate();
    }

    // 处理npm安装
    return await handleNpmUpdate(diagnostic);

  } catch (error) {
    logger.error('Update命令失败:', error);
    console.error('Error: Failed to check for updates');
    process.exit(1);
  }
}

/**
 * 处理原生安装更新
 * @original: 原生更新逻辑 (L56703-56733)
 */
async function handleNativeUpdate() {
  logger.debug('update: 检测到原生安装，使用原生更新器');
  
  try {
    const updateResult = await checkNativeUpdate();
    
    if (!updateResult.latestVersion) {
      console.error('Failed to check for updates');
      process.exit(1);
    }

    if (updateResult.latestVersion === APP_INFO.VERSION) {
      console.log(colorText('green', `Claude Code is up to date (${APP_INFO.VERSION})`));
    } else if (updateResult.wasUpdated) {
      console.log(colorText('green', 
        `Successfully updated from ${APP_INFO.VERSION} to version ${updateResult.latestVersion}`
      ));
    } else {
      console.log(colorText('green', 
        `Claude Code is up to date (${APP_INFO.VERSION})`
      ));
    }

    process.exit(0);

  } catch (error) {
    console.error('Error: Failed to install native update');
    console.error(String(error));
    console.error('Try running "claude doctor" for diagnostics');
    process.exit(1);
  }
}

/**
 * 处理npm安装更新
 * @original: npm更新逻辑 (L56734-56837)
 */
async function handleNpmUpdate(diagnostic) {
  // 检查网络连接
  await checkNetworkConnectivity();
  
  logger.debug('update: 检查npm注册表的最新版本');
  logger.debug(`update: 包URL: ${APP_INFO.PACKAGE_URL}`);

  const command = `npm view ${APP_INFO.PACKAGE_URL}@latest version`;
  logger.debug(`update: 运行: ${command}`);

  const latestVersion = await getLatestVersionFromNpm();
  logger.debug(`update: npm最新版本: ${latestVersion || 'FAILED'}`);

  if (!latestVersion) {
    logger.debug('update: 从npm注册表获取最新版本失败');
    
    console.error(colorText('red', 'Failed to check for updates'));
    console.error('Unable to fetch latest version from npm registry');
    console.error('');
    console.error('Possible causes:');
    console.error('  • Network connectivity issues');
    console.error('  • npm registry is unreachable');
    console.error('  • Corporate proxy/firewall blocking npm');
    
    if (APP_INFO.PACKAGE_URL && !APP_INFO.PACKAGE_URL.includes('@anthropic-ai/claude-cli')) {
      console.error(`  • Package not found: ${APP_INFO.PACKAGE_URL}`);
    } else {
      console.error(`  • Package not found: ${APP_INFO.PACKAGE_URL || '@anthropic-ai/claude-cli'} version`);
    }
    
    console.error('  • Check if you need to login: npm whoami');
    process.exit(1);
  }

  if (latestVersion === APP_INFO.VERSION) {
    console.log(colorText('green', `Claude Code is up to date (${APP_INFO.VERSION})`));
    process.exit(0);
  }

  console.log(colorText('green', 
    `New version available: ${latestVersion} (current: ${APP_INFO.VERSION})`
  ));
  console.log('Installing update...');

  // 确定更新方法
  const { useLocal, method } = determineUpdateMethod(diagnostic);
  
  console.log(`Using ${method} installation update method...`);
  logger.debug(`update: 更新方法确定: ${method}`);
  logger.debug(`update: useLocalUpdate: ${useLocal}`);

  // 执行更新
  const installStatus = useLocal 
    ? await installOrUpdateClaudePackage()
    : await installGlobalPackage();

  logger.debug(`update: 安装状态: ${installStatus}`);

  // 处理安装结果
  await handleInstallResult(installStatus, latestVersion, useLocal);
}

/**
 * 确定更新方法
 * @param {Object} diagnostic - 诊断信息
 * @returns {Object} 更新方法信息
 */
function determineUpdateMethod(diagnostic) {
  let useLocal = false;
  let method = '';

  switch (diagnostic.installationType) {
    case 'npm-local':
      useLocal = true;
      method = 'local';
      break;
    case 'npm-global':
      useLocal = false;
      method = 'global';
      break;
    case 'unknown':
      const isLocal = detectLocalInstallation();
      useLocal = isLocal;
      method = isLocal ? 'local' : 'global';
      console.log(colorText('yellow', 'Warning: Could not determine installation type'));
      console.log(`Attempting ${method} update based on file detection...`);
      break;
    default:
      console.error(`Error: Cannot update ${diagnostic.installationType} installation`);
      process.exit(1);
  }

  return { useLocal, method };
}

/**
 * 处理安装结果
 * @param {string} status - 安装状态
 * @param {string} version - 版本号
 * @param {boolean} useLocal - 是否使用本地安装
 */
async function handleInstallResult(status, version, useLocal) {
  switch (status) {
    case 'success':
      console.log(colorText('green', 
        `Successfully updated from ${APP_INFO.VERSION} to version ${version}`
      ));
      break;

    case 'no_permissions':
      console.error('Error: Insufficient permissions to install update');
      
      if (useLocal) {
        console.error('Try manually updating with:');
        console.error(`  cd ~/.claude/local && npm update ${APP_INFO.PACKAGE_URL}`);
      } else {
        console.error('Try running with sudo or fix npm permissions');
        console.error('Or consider migrating to a local installation with:');
        console.error('  claude migrate-installer');
      }
      
      process.exit(1);
      break;

    case 'install_failed':
      console.error('Error: Failed to install update');
      
      if (useLocal) {
        console.error('Try manually updating with:');
        console.error(`  cd ~/.claude/local && npm update ${APP_INFO.PACKAGE_URL}`);
      } else {
        console.error('Or consider migrating to a local installation with:');
        console.error('  claude migrate-installer');
      }
      
      process.exit(1);
      break;

    case 'in_progress':
      console.error('Error: Another instance is currently performing an update');
      console.error('Please wait and try again later');
      process.exit(1);
      break;
  }

  process.exit(0);
}

// 辅助函数（需要实际实现）
async function runDiagnostic() { return { installationType: 'npm-local', configInstallMethod: 'local', multipleInstallations: [], warnings: [] }; }
async function updateInstallationMethodConfig(diagnostic) { /* 实现配置更新 */ }
async function checkNativeUpdate() { return { latestVersion: '1.0.72', wasUpdated: false }; }
async function checkNetworkConnectivity() { /* 实现网络检查 */ }
async function getLatestVersionFromNpm() { return '1.0.72'; /* 实现npm版本检查 */ }
function detectLocalInstallation() { return true; /* 实现本地安装检测 */ }
async function installOrUpdateClaudePackage() { return 'success'; /* 实现本地包安装 */ }
async function installGlobalPackage() { return 'success'; /* 实现全局包安装 */ }
function logTelemetryEvent(event, data) { logger.debug('遥测事件', { event, data }); }
function colorText(color, text) { return text; /* 实现颜色文本 */ }
