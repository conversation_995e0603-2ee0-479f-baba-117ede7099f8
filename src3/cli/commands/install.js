/**
 * Install命令处理器
 * 
 * 处理Claude Code原生构建的安装命令
 * 
 * @original: install命令处理逻辑 (L57915, L56913-57030)
 */

import { logger } from '../../utils/logger.js';
import { executeCommand } from '../../utils/process.js';

/**
 * 安装状态类型
 */
export const INSTALL_STATUS = {
  CHECKING: 'checking',
  CLEANING_NPM: 'cleaning-npm',
  INSTALLING: 'installing',
  SETTING_UP: 'setting-up',
  SET_UP: 'set-up',
  SUCCESS: 'success',
  ERROR: 'error'
};

/**
 * 处理install命令
 * @original: install命令action (L57915)
 * @param {string} target - 安装目标版本
 * @param {Object} options - 命令选项
 */
export async function handleInstallCommand(target, options) {
  const { force = false } = options;
  
  logger.debug('处理install命令', { target, force });
  
  try {
    // 记录遥测事件
    logTelemetryEvent('tengu_claude_install_command', {
      has_version: target ? 1 : 0,
      forced: force ? 1 : 0
    });

    // 执行安装流程
    const result = await performInstallation(target, force);
    
    if (result.success) {
      console.log('Claude Code successfully installed!');
      if (result.version && result.version !== 'current') {
        console.log(`Version: ${result.version}`);
      }
      if (result.setupMessages && result.setupMessages.length > 0) {
        console.log('\nSetup completed with the following messages:');
        result.setupMessages.forEach(message => {
          console.log(`  ${message}`);
        });
      }
    } else {
      console.error('Installation failed:', result.error);
      process.exit(1);
    }
    
  } catch (error) {
    logger.error('Install命令失败:', error);
    console.error('Installation failed:', error.message);
    process.exit(1);
  }
}

/**
 * 执行安装流程
 * @original: G()函数 (L56913)
 * @param {string} target - 目标版本
 * @param {boolean} force - 是否强制安装
 * @returns {Promise<Object>} 安装结果
 */
async function performInstallation(target, force) {
  const statusUpdates = [];
  
  try {
    logger.debug(`开始安装流程 (force=${force}, target=${target})`);
    
    // 1. 清理NPM安装
    updateStatus(INSTALL_STATUS.CLEANING_NPM);
    const cleanupResult = await cleanupNpmInstallations();
    
    if (cleanupResult.removed > 0) {
      logger.debug(`清理了 ${cleanupResult.removed} 个npm安装`);
    }
    
    if (cleanupResult.errors.length > 0) {
      logger.warn(`清理警告: ${cleanupResult.errors.join(', ')}`);
    }

    // 清理shell别名
    const aliasCleanup = cleanupShellAliases();
    if (aliasCleanup.length > 0) {
      logger.debug(`Shell别名清理: ${aliasCleanup.join('; ')}`);
    }

    // 2. 安装最新版本
    updateStatus(INSTALL_STATUS.INSTALLING, { version: target || 'stable' });
    logger.debug(`调用installLatest(force=true, target=${target}, forceReinstall=${force})`);
    
    const installResult = await installLatest(true, target, force);
    
    logger.debug(`installLatest返回 version=${installResult.latestVersion}, wasUpdated=${installResult.wasUpdated}`);
    
    if (!installResult.latestVersion) {
      throw new Error('Failed to retrieve version information during install');
    }

    if (!installResult.wasUpdated) {
      logger.debug('已经是最新版本');
    }

    // 3. 设置启动器
    updateStatus(INSTALL_STATUS.SETTING_UP);
    const setupMessages = await setupLauncher(true);
    
    logger.debug(`启动器设置完成，收到 ${setupMessages.length} 条消息`);
    
    if (setupMessages.length > 0) {
      setupMessages.forEach(message => {
        logger.debug(`设置消息: ${message}`);
      });
      
      updateStatus(INSTALL_STATUS.SET_UP, { messages: setupMessages });
      
      // 延迟显示成功状态
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      return {
        success: true,
        version: installResult.latestVersion || 'current',
        setupMessages
      };
    } else {
      logger.debug('Shell PATH已配置');
      updateStatus(INSTALL_STATUS.SUCCESS, { 
        version: installResult.latestVersion || 'current' 
      });
      
      return {
        success: true,
        version: installResult.latestVersion || 'current'
      };
    }

  } catch (error) {
    logger.error(`安装命令失败: ${error.message}`);
    updateStatus(INSTALL_STATUS.ERROR, { 
      message: error instanceof Error ? error.message : String(error) 
    });
    
    return {
      success: false,
      error: error.message
    };
  }

  function updateStatus(type, data = {}) {
    const status = { type, ...data };
    statusUpdates.push(status);
    logger.debug('安装状态更新', status);
  }
}

/**
 * 清理NPM安装
 * @original: QR8()函数 (L56848)
 * @returns {Promise<Object>} 清理结果
 */
async function cleanupNpmInstallations() {
  const errors = [];
  let removed = 0;

  logger.debug('尝试删除全局npm安装的@anthropic/claude-code');

  try {
    const result = await executeCommand('npm', ['uninstall', '-g', '@anthropic/claude-code'], {
      cwd: process.cwd()
    });

    if (result.code === 0) {
      removed++;
      logger.debug('删除了全局npm安装');
    } else if (result.stderr && !result.stderr.includes('npm ERR! code E404')) {
      errors.push('Failed to remove global npm installation');
      logger.error(`删除全局npm包失败: ${result.stderr}`);
    }
  } catch (error) {
    errors.push(`Failed to remove global npm installation: ${error.message}`);
  }

  // 删除本地安装目录
  const localPath = getLocalInstallPath();
  if (await directoryExists(localPath)) {
    try {
      await removeDirectory(localPath);
      removed++;
      logger.debug(`删除了本地安装目录 ${localPath}`);
    } catch (error) {
      errors.push(`Failed to remove ${localPath}: ${error.message}`);
      logger.error(`删除本地安装失败: ${error.message}`);
    }
  }

  // 清理配置
  await cleanupConfiguration();

  return { removed, errors };
}

/**
 * 清理Shell别名
 * @original: MgB()函数
 * @returns {string[]} 清理消息
 */
function cleanupShellAliases() {
  const messages = [];
  
  // 实现shell别名清理逻辑
  // 这里需要检查和清理各种shell配置文件中的别名
  
  return messages;
}

/**
 * 安装最新版本
 * @original: l01()函数
 * @param {boolean} force - 是否强制安装
 * @param {string} target - 目标版本
 * @param {boolean} forceReinstall - 是否强制重新安装
 * @returns {Promise<Object>} 安装结果
 */
async function installLatest(force, target, forceReinstall) {
  // 实现原生构建安装逻辑
  // 这里需要下载和安装Claude Code的原生构建
  
  return {
    latestVersion: '1.0.72',
    wasUpdated: true
  };
}

/**
 * 设置启动器
 * @original: c01()函数
 * @param {boolean} force - 是否强制设置
 * @returns {Promise<string[]>} 设置消息
 */
async function setupLauncher(force) {
  const messages = [];
  
  // 实现启动器设置逻辑
  // 这里需要设置PATH和shell集成
  
  return messages;
}

/**
 * 获取本地安装路径
 * @returns {string} 本地安装路径
 */
function getLocalInstallPath() {
  const os = require('os');
  const path = require('path');
  return path.join(os.homedir(), '.claude', 'local');
}

/**
 * 检查目录是否存在
 * @param {string} dir - 目录路径
 * @returns {Promise<boolean>} 是否存在
 */
async function directoryExists(dir) {
  try {
    const fs = require('fs').promises;
    const stat = await fs.stat(dir);
    return stat.isDirectory();
  } catch {
    return false;
  }
}

/**
 * 删除目录
 * @param {string} dir - 目录路径
 * @returns {Promise<void>}
 */
async function removeDirectory(dir) {
  const fs = require('fs').promises;
  await fs.rm(dir, { recursive: true, force: true });
}

/**
 * 清理配置
 * @returns {Promise<void>}
 */
async function cleanupConfiguration() {
  // 实现配置清理逻辑
}

/**
 * 记录遥测事件
 * @param {string} event - 事件名称
 * @param {Object} data - 事件数据
 */
function logTelemetryEvent(event, data) {
  logger.debug('遥测事件', { event, data });
}
