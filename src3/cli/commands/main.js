/**
 * 主命令处理器
 * 
 * 处理Claude Code的主要交互式会话命令
 * 
 * @original: ER8()函数中的主要action处理逻辑 (L57286-57500)
 */

import { logger } from '../../utils/logger.js';
import { createDefaultPermissionContext } from '../../core/permissions.js';
import { generateSessionId, setCurrentWorkingDirectory } from '../../core/session.js';
import { setupEnvironment } from '../../utils/environment.js';

/**
 * 处理主命令（默认的交互式会话）
 * @original: ER8()函数的action回调 (L57286)
 * @param {string} prompt - 用户提供的提示
 * @param {Object} options - 命令行选项
 */
export async function handleMainCommand(prompt, options) {
  try {
    logger.debug('处理主命令', { prompt, options });

    // 解构命令行选项
    const {
      debug = false,
      debugToStderr = false,
      verbose = false,
      print: isPrintMode,
      dangerouslySkipPermissions,
      allowedTools = [],
      disallowedTools = [],
      mcpConfig,
      outputFormat,
      inputFormat,
      permissionMode,
      addDir = [],
      fallbackModel,
      ide = false,
      continue: shouldContinue,
      resume,
      model,
      settings,
      strictMcpConfig,
      sessionId,
      maxTurns,
      systemPrompt,
      systemPromptFile,
      appendSystemPrompt,
      permissionPromptTool
    } = options;

    // 设置调试模式
    if (debug) {
      logger.setLevel('debug');
      logger.debug('调试模式已启用');
    }

    if (debugToStderr) {
      logger.debug('调试输出到stderr已启用');
    }

    // 设置详细模式
    if (verbose) {
      logger.info('详细模式已启用');
    }

    // 验证输出格式
    if (outputFormat && !['text', 'json', 'stream-json'].includes(outputFormat)) {
      throw new Error(`无效的输出格式: ${outputFormat}`);
    }

    // 验证输入格式
    if (inputFormat && !['text', 'stream-json'].includes(inputFormat)) {
      throw new Error(`无效的输入格式: ${inputFormat}`);
    }

    // 设置权限模式
    const resolvedPermissionMode = resolvePermissionMode({
      permissionModeCli: permissionMode,
      dangerouslySkipPermissions
    });

    logger.debug('权限模式已解析', { resolvedPermissionMode });

    // 处理会话ID
    let resolvedSessionId = sessionId;
    if (!resolvedSessionId && !shouldContinue && !resume) {
      resolvedSessionId = generateSessionId();
    }

    // 设置工作目录
    setCurrentWorkingDirectory(process.cwd());

    // 创建权限上下文
    const { toolPermissionContext, warnings } = createToolPermissionContext({
      allowedToolsCli: allowedTools,
      disallowedToolsCli: disallowedTools,
      permissionMode: resolvedPermissionMode,
      addDirs: addDir
    });

    // 显示警告
    warnings.forEach(warning => {
      logger.warn(warning);
    });

    // 处理打印模式
    if (isPrintMode) {
      return await handlePrintMode({
        prompt,
        outputFormat: outputFormat || 'text',
        inputFormat: inputFormat || 'text',
        toolPermissionContext,
        model,
        fallbackModel,
        maxTurns,
        systemPrompt,
        systemPromptFile,
        appendSystemPrompt,
        permissionPromptTool,
        mcpConfig,
        strictMcpConfig,
        sessionId: resolvedSessionId,
        verbose,
        continue: shouldContinue,
        resume
      });
    }

    // 处理交互式模式
    return await handleInteractiveMode({
      prompt,
      toolPermissionContext,
      model,
      ide,
      sessionId: resolvedSessionId,
      verbose,
      continue: shouldContinue,
      resume,
      mcpConfig,
      strictMcpConfig,
      settings,
      addDir
    });

  } catch (error) {
    logger.error('主命令处理失败:', error);
    throw error;
  }
}

/**
 * 解析权限模式
 * @original: c6B()函数 (L11175)
 * @param {Object} options - 权限选项
 * @returns {string} 解析后的权限模式
 */
function resolvePermissionMode({ permissionModeCli, dangerouslySkipPermissions }) {
  // 检查是否禁用绕过权限模式
  const settings = getGlobalSettings();
  const bypassDisabled = settings.permissions?.disableBypassPermissionsMode === 'disable';
  
  const modes = [];
  
  if (dangerouslySkipPermissions) {
    modes.push('bypassPermissions');
  }
  
  if (permissionModeCli) {
    modes.push(mapPermissionMode(permissionModeCli));
  }
  
  if (settings.permissions?.defaultMode) {
    modes.push(settings.permissions.defaultMode);
  }
  
  for (const mode of modes) {
    if (mode === 'bypassPermissions' && bypassDisabled) {
      logger.warn('绕过权限模式已被设置禁用');
      continue;
    }
    return mode;
  }
  
  return 'default';
}

/**
 * 创建工具权限上下文
 * @original: l6B()函数 (L11220)
 * @param {Object} options - 权限选项
 * @returns {Object} 包含权限上下文和警告的对象
 */
function createToolPermissionContext({ allowedToolsCli, disallowedToolsCli, permissionMode, addDirs }) {
  const allowedRules = parseToolRules(allowedToolsCli);
  const deniedRules = parseToolRules(disallowedToolsCli);
  
  let context = createDefaultPermissionContext();
  context = context.update({
    mode: permissionMode,
    alwaysAllowRules: { cliArg: allowedRules },
    alwaysDenyRules: { cliArg: deniedRules },
    isBypassPermissionsModeAvailable: permissionMode === 'bypassPermissions'
  });
  
  // 添加额外的工作目录
  const warnings = [];
  const globalSettings = getGlobalSettings();
  const additionalDirs = [
    ...(globalSettings.permissions?.additionalDirectories || []),
    ...addDirs
  ];
  
  for (const dir of additionalDirs) {
    const result = addDirectoryToContext(dir, context);
    if (result.resultType === 'success') {
      context = result.updatedPermissionContext;
    } else if (result.resultType !== 'alreadyInWorkingDirectory') {
      warnings.push(formatDirectoryError(result));
    }
  }
  
  return {
    toolPermissionContext: context,
    warnings
  };
}

/**
 * 处理打印模式（非交互式）
 * @original: 打印模式相关逻辑
 * @param {Object} options - 打印模式选项
 */
async function handlePrintMode(options) {
  logger.debug('进入打印模式', options);
  
  const {
    prompt,
    outputFormat,
    inputFormat,
    toolPermissionContext,
    model,
    fallbackModel,
    maxTurns,
    systemPrompt,
    systemPromptFile,
    appendSystemPrompt,
    permissionPromptTool,
    mcpConfig,
    strictMcpConfig,
    sessionId,
    verbose,
    continue: shouldContinue,
    resume
  } = options;

  // 实现打印模式的具体逻辑
  // 这里需要调用核心的对话处理逻辑
  logger.info('打印模式处理完成');
}

/**
 * 处理交互式模式
 * @original: 交互式模式相关逻辑
 * @param {Object} options - 交互式模式选项
 */
async function handleInteractiveMode(options) {
  logger.debug('进入交互式模式', options);
  
  const {
    prompt,
    toolPermissionContext,
    model,
    ide,
    sessionId,
    verbose,
    continue: shouldContinue,
    resume,
    mcpConfig,
    strictMcpConfig,
    settings,
    addDir
  } = options;

  // 实现交互式模式的具体逻辑
  // 这里需要启动React/Ink界面
  logger.info('交互式模式处理完成');
}

/**
 * 解析工具规则
 * @param {string[]} tools - 工具规则数组
 * @returns {string[]} 解析后的规则
 */
function parseToolRules(tools) {
  if (!tools || tools.length === 0) return [];
  
  return tools.flatMap(tool => 
    tool.includes(',') ? tool.split(',') : tool
  ).map(rule => rule.trim()).filter(rule => rule.length > 0);
}

/**
 * 获取全局设置
 * @returns {Object} 全局设置对象
 */
function getGlobalSettings() {
  // 这里应该从配置文件中读取设置
  // 暂时返回空对象
  return {};
}

/**
 * 映射权限模式
 * @param {string} mode - 原始权限模式
 * @returns {string} 映射后的权限模式
 */
function mapPermissionMode(mode) {
  switch (mode) {
    case 'bypassPermissions':
      return 'bypassPermissions';
    case 'acceptEdits':
      return 'acceptEdits';
    case 'plan':
      return 'plan';
    default:
      return 'default';
  }
}

/**
 * 添加目录到权限上下文
 * @param {string} dir - 目录路径
 * @param {Object} context - 权限上下文
 * @returns {Object} 添加结果
 */
function addDirectoryToContext(dir, context) {
  // 实现目录添加逻辑
  // 这里需要验证目录路径并更新上下文
  return {
    resultType: 'success',
    updatedPermissionContext: context
  };
}

/**
 * 格式化目录错误
 * @param {Object} result - 错误结果
 * @returns {string} 格式化的错误消息
 */
function formatDirectoryError(result) {
  switch (result.resultType) {
    case 'emptyPath':
      return '目录路径不能为空';
    case 'invalidPath':
      return `无效的目录路径: ${result.path}`;
    case 'notDirectory':
      return `路径不是目录: ${result.path}`;
    case 'permissionDenied':
      return `没有访问目录的权限: ${result.path}`;
    default:
      return `目录添加失败: ${result.resultType}`;
  }
}
