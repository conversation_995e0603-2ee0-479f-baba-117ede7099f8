/**
 * MCP命令处理器
 * 
 * 处理MCP服务器的配置和管理命令
 * 
 * @original: MCP相关命令处理逻辑 (L57686-57878)
 */

import { logger } from '../../utils/logger.js';
import { executeCommand } from '../../utils/process.js';
import { TRANSPORT_TYPES, CONFIG_SCOPES } from '../../config/constants.js';

/**
 * 设置MCP子命令
 * @original: MCP命令设置逻辑 (L57686)
 * @param {Command} mcpCommand - MCP命令对象
 */
export function handleMcpCommands(mcpCommand) {
  // serve命令
  mcpCommand.command('serve')
    .description('Start the Claude Code MCP server')
    .helpOption('-h, --help', 'Display help for command')
    .option('-d, --debug', 'Enable debug mode', () => true)
    .option('--verbose', 'Override verbose mode setting from config', () => true)
    .action(handleMcpServe);

  // add命令
  mcpCommand.command('add <name> <commandOrUrl> [args...]')
    .description('Add a server')
    .option('-s, --scope <scope>', 'Configuration scope (local, user, or project)', 'local')
    .option('-t, --transport <transport>', 'Transport type (stdio, sse, http)', 'stdio')
    .option('-e, --env <env...>', 'Set environment variables (e.g. -e KEY=value)')
    .option('-H, --header <header...>', 'Set HTTP headers for SSE and HTTP transports (e.g. -H "X-Api-Key: abc123" -H "X-Custom: value")')
    .helpOption('-h, --help', 'Display help for command')
    .action(handleMcpAdd);

  // remove命令
  mcpCommand.command('remove <name>')
    .description('Remove an MCP server')
    .option('-s, --scope <scope>', 'Configuration scope (local, user, or project) - if not specified, removes from whichever scope it exists in')
    .helpOption('-h, --help', 'Display help for command')
    .action(handleMcpRemove);

  // list命令
  mcpCommand.command('list')
    .description('List configured MCP servers')
    .helpOption('-h, --help', 'Display help for command')
    .action(handleMcpList);

  // get命令
  mcpCommand.command('get <name>')
    .description('Get details about an MCP server')
    .helpOption('-h, --help', 'Display help for command')
    .action(handleMcpGet);

  // add-json命令
  mcpCommand.command('add-json <name> <json>')
    .description('Add an MCP server (stdio or SSE) with a JSON string')
    .option('-s, --scope <scope>', 'Configuration scope (local, user, or project)', 'local')
    .helpOption('-h, --help', 'Display help for command')
    .action(handleMcpAddJson);

  // add-from-claude-desktop命令
  mcpCommand.command('add-from-claude-desktop')
    .description('Import MCP servers from Claude Desktop (Mac and WSL only)')
    .option('-s, --scope <scope>', 'Configuration scope (local, user, or project)', 'local')
    .helpOption('-h, --help', 'Display help for command')
    .action(handleMcpAddFromClaudeDesktop);

  // reset-project-choices命令
  mcpCommand.command('reset-project-choices')
    .description('Reset all approved and rejected project-scoped (.mcp.json) servers within this project')
    .helpOption('-h, --help', 'Display help for command')
    .action(handleMcpResetProjectChoices);
}

/**
 * 处理MCP serve命令
 * @original: serve命令action (L57687)
 * @param {Object} options - 命令选项
 */
async function handleMcpServe(options) {
  const { debug, verbose } = options;
  
  logger.debug('启动MCP服务器', { debug, verbose });
  
  try {
    const workingDir = process.cwd();
    
    if (!directoryExists(workingDir)) {
      console.error(`Error: Directory ${workingDir} does not exist`);
      process.exit(1);
    }

    // 记录遥测事件
    logTelemetryEvent('tengu_mcp_start', {});

    // 启动MCP服务器
    await startMcpServer({ debug, verbose });
    
  } catch (error) {
    console.error('Error: Failed to start MCP server:', error.message);
    process.exit(1);
  }
}

/**
 * 处理MCP add命令
 * @original: add命令action (L57698)
 * @param {string} name - 服务器名称
 * @param {string} commandOrUrl - 命令或URL
 * @param {string[]} args - 参数数组
 * @param {Object} options - 命令选项
 */
async function handleMcpAdd(name, commandOrUrl, args, options) {
  if (!name) {
    console.error('Error: Server name is required.');
    console.error('Usage: claude mcp add <name> <command> [args...]');
    process.exit(1);
  }
  
  if (!commandOrUrl) {
    console.error('Error: Command is required when server name is provided.');
    console.error('Usage: claude mcp add <name> <command> [args...]');
    process.exit(1);
  }

  try {
    const scope = parseConfigScope(options.scope);
    const transport = parseTransportType(options.transport);
    
    // 记录遥测事件
    logTelemetryEvent('tengu_mcp_add', {
      name,
      scope,
      transport
    });

    // 解析环境变量
    const env = parseEnvironmentVariables(options.env || []);
    
    // 解析HTTP头
    const headers = parseHttpHeaders(options.header || []);

    // 创建服务器配置
    const serverConfig = createServerConfig({
      name,
      commandOrUrl,
      args,
      transport,
      env,
      headers
    });

    // 添加服务器
    await addMcpServer(name, serverConfig, scope);
    
    console.log(`Added MCP server "${name}" with ${transport} transport to ${scope} configuration`);
    
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

/**
 * 处理MCP remove命令
 * @original: remove命令action (L57743)
 * @param {string} name - 服务器名称
 * @param {Object} options - 命令选项
 */
async function handleMcpRemove(name, options) {
  try {
    if (options.scope) {
      const scope = parseConfigScope(options.scope);
      
      logTelemetryEvent('tengu_mcp_delete', {
        name,
        scope
      });

      const removed = await removeMcpServer(name, scope);
      
      if (removed) {
        console.log(`Removed MCP server "${name}" from ${scope} configuration`);
      } else {
        console.log(`MCP server "${name}" not found in ${scope} configuration`);
        process.exit(1);
      }
    } else {
      // 从所有作用域中查找并删除
      const scopes = ['local', 'user', 'project'];
      let removed = false;
      
      for (const scope of scopes) {
        if (await removeMcpServer(name, scope)) {
          console.log(`Removed MCP server "${name}" from ${scope} configuration`);
          removed = true;
          break;
        }
      }
      
      if (!removed) {
        console.log(`MCP server "${name}" not found in any configuration`);
        process.exit(1);
      }
    }
    
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

/**
 * 处理MCP list命令
 * @original: list命令action (L57787)
 */
async function handleMcpList() {
  logTelemetryEvent('tengu_mcp_list', {});
  
  const servers = await getAllMcpServers();
  
  if (Object.keys(servers).length === 0) {
    console.log('No MCP servers configured. Use `claude mcp add` to add a server.');
  } else {
    console.log('Checking MCP server health...\n');
    
    // 显示服务器列表和健康状态
    await displayMcpServerList(servers);
  }
  
  process.exit(0);
}

/**
 * 处理MCP get命令
 * @original: get命令action (L57802)
 * @param {string} name - 服务器名称
 */
async function handleMcpGet(name) {
  logTelemetryEvent('tengu_mcp_get', { name });
  
  const server = await getMcpServer(name);
  
  if (!server) {
    console.error(`No MCP server found with name: ${name}`);
    process.exit(1);
  }
  
  // 显示服务器详细信息
  displayMcpServerDetails(server);
  
  console.log(`\nTo remove this server, run: claude mcp remove "${name}" -s ${server.scope}`);
  process.exit(0);
}

/**
 * 处理MCP add-json命令
 * @original: add-json命令action (L57830)
 * @param {string} name - 服务器名称
 * @param {string} json - JSON配置字符串
 * @param {Object} options - 命令选项
 */
async function handleMcpAddJson(name, json, options) {
  try {
    const scope = parseConfigScope(options.scope);
    const config = JSON.parse(json);
    
    await addMcpServerFromJson(name, config, scope);
    
    const transport = config && typeof config === 'object' && 'type' in config 
      ? String(config.type || 'stdio') 
      : 'stdio';
    
    console.log(`Added MCP server "${name}" with ${transport} transport to ${scope} configuration`);
    
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

/**
 * 处理MCP add-from-claude-desktop命令
 * @original: add-from-claude-desktop命令action (L57844)
 * @param {Object} options - 命令选项
 */
async function handleMcpAddFromClaudeDesktop(options) {
  try {
    const scope = parseConfigScope(options.scope);
    const platform = process.platform;
    
    logTelemetryEvent('tengu_mcp_add', {
      scope,
      source: 'claude-desktop'
    });

    const imported = await importFromClaudeDesktop(scope, platform);
    
    if (imported.length > 0) {
      console.log(`Imported ${imported.length} MCP server(s) from Claude Desktop to ${scope} configuration:`);
      imported.forEach(server => {
        console.log(`  - ${server.name}`);
      });
    } else {
      console.log('No MCP servers found in Claude Desktop configuration');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

/**
 * 处理MCP reset-project-choices命令
 * @original: reset-project-choices命令action (L57869)
 */
async function handleMcpResetProjectChoices() {
  logTelemetryEvent('tengu_mcp_reset_mcpjson_choices', {});
  
  const settings = getProjectSettings();
  
  updateProjectSettings({
    ...settings,
    enabledMcpjsonServers: [],
    disabledMcpjsonServers: [],
    enableAllProjectMcpServers: false
  });
  
  console.log('All project-scoped (.mcp.json) server approvals and rejections have been reset.');
  console.log('You will be prompted for approval next time you start Claude Code.');
  
  process.exit(0);
}

// 辅助函数

function parseConfigScope(scope) {
  if (!CONFIG_SCOPES.includes(scope)) {
    throw new Error(`Invalid scope: ${scope}. Must be one of: ${CONFIG_SCOPES.join(', ')}`);
  }
  return scope;
}

function parseTransportType(transport) {
  if (!TRANSPORT_TYPES.includes(transport)) {
    throw new Error(`Invalid transport: ${transport}. Must be one of: ${TRANSPORT_TYPES.join(', ')}`);
  }
  return transport;
}

function parseEnvironmentVariables(envArray) {
  const env = {};
  for (const envVar of envArray) {
    const [key, ...valueParts] = envVar.split('=');
    if (!key || valueParts.length === 0) {
      throw new Error(`Invalid environment variable format: ${envVar}`);
    }
    env[key] = valueParts.join('=');
  }
  return env;
}

function parseHttpHeaders(headerArray) {
  const headers = {};
  for (const header of headerArray) {
    const [key, ...valueParts] = header.split(':');
    if (!key || valueParts.length === 0) {
      throw new Error(`Invalid header format: ${header}`);
    }
    headers[key.trim()] = valueParts.join(':').trim();
  }
  return headers;
}

// 这些函数需要实际的实现
async function startMcpServer(options) { /* 实现MCP服务器启动 */ }
async function addMcpServer(name, config, scope) { /* 实现添加MCP服务器 */ }
async function removeMcpServer(name, scope) { /* 实现删除MCP服务器 */ }
async function getAllMcpServers() { /* 实现获取所有MCP服务器 */ }
async function getMcpServer(name) { /* 实现获取单个MCP服务器 */ }
async function addMcpServerFromJson(name, config, scope) { /* 实现从JSON添加服务器 */ }
async function importFromClaudeDesktop(scope, platform) { /* 实现从Claude Desktop导入 */ }
function directoryExists(dir) { return true; /* 实现目录存在检查 */ }
function logTelemetryEvent(event, data) { /* 实现遥测事件记录 */ }
function createServerConfig(options) { /* 实现服务器配置创建 */ }
function displayMcpServerList(servers) { /* 实现服务器列表显示 */ }
function displayMcpServerDetails(server) { /* 实现服务器详情显示 */ }
function getProjectSettings() { return {}; /* 实现项目设置获取 */ }
function updateProjectSettings(settings) { /* 实现项目设置更新 */ }
