/**
 * 配置和设置管理
 * 
 * 管理Claude Code的各种配置选项，包括用户设置、项目设置、全局设置等
 * 
 * @original: 配置管理相关代码 (L6360-6468, L57634-57685)
 */

import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';
import { logger } from '../utils/logger.js';
import { readJSONFile, writeJSONFile, ensureDirectoryExists } from '../utils/filesystem.js';

/**
 * 配置作用域枚举
 */
export const CONFIG_SCOPES = {
  GLOBAL: 'global',
  USER: 'user',
  PROJECT: 'project',
  LOCAL: 'local'
};

/**
 * 配置键枚举
 */
export const CONFIG_KEYS = {
  THEME: 'theme',
  VERBOSE: 'verbose',
  MODEL: 'model',
  FALLBACK_MODEL: 'fallbackModel',
  PERMISSION_MODE: 'permissionMode',
  AUTO_UPDATES: 'autoUpdates',
  COST_WARNINGS: 'costWarnings',
  ALLOWED_TOOLS: 'allowedTools',
  DISALLOWED_TOOLS: 'disallowedTools',
  IGNORE_PATTERNS: 'ignorePatterns',
  MCP_SERVERS: 'mcpServers',
  CUSTOM_API_KEY_RESPONSES: 'customApiKeyResponses',
  ONBOARDING: 'onboarding'
};

/**
 * 默认配置值
 */
export const DEFAULT_CONFIG = {
  [CONFIG_KEYS.THEME]: 'dark',
  [CONFIG_KEYS.VERBOSE]: false,
  [CONFIG_KEYS.MODEL]: null,
  [CONFIG_KEYS.FALLBACK_MODEL]: null,
  [CONFIG_KEYS.PERMISSION_MODE]: 'ask',
  [CONFIG_KEYS.AUTO_UPDATES]: true,
  [CONFIG_KEYS.COST_WARNINGS]: true,
  [CONFIG_KEYS.ALLOWED_TOOLS]: [],
  [CONFIG_KEYS.DISALLOWED_TOOLS]: [],
  [CONFIG_KEYS.IGNORE_PATTERNS]: [],
  [CONFIG_KEYS.MCP_SERVERS]: {},
  [CONFIG_KEYS.CUSTOM_API_KEY_RESPONSES]: {
    approved: [],
    rejected: []
  },
  [CONFIG_KEYS.ONBOARDING]: {
    hasCompletedOnboarding: false,
    lastOnboardingVersion: null
  }
};

/**
 * 配置管理器类
 */
export class ConfigManager {
  constructor() {
    this.configCache = new Map();
    this.configPaths = new Map();
    this.watchers = new Map();
    
    this.initializeConfigPaths();
  }

  /**
   * 初始化配置路径
   */
  initializeConfigPaths() {
    const userHome = homedir();
    const projectRoot = process.cwd();

    this.configPaths.set(CONFIG_SCOPES.GLOBAL, join(userHome, '.claude', 'config.json'));
    this.configPaths.set(CONFIG_SCOPES.USER, join(userHome, '.claude', 'settings.json'));
    this.configPaths.set(CONFIG_SCOPES.PROJECT, join(projectRoot, '.claude', 'settings.json'));
    this.configPaths.set(CONFIG_SCOPES.LOCAL, join(projectRoot, '.claude', 'local.json'));
  }

  /**
   * 获取配置值
   * @original: 配置获取逻辑 (L57634-57636)
   * @param {string} key - 配置键
   * @param {string} scope - 配置作用域
   * @returns {*} 配置值
   */
  get(key, scope = null) {
    if (scope) {
      const config = this.loadConfig(scope);
      return config[key] ?? DEFAULT_CONFIG[key];
    }

    // 按优先级顺序查找配置
    const scopes = [
      CONFIG_SCOPES.LOCAL,
      CONFIG_SCOPES.PROJECT,
      CONFIG_SCOPES.USER,
      CONFIG_SCOPES.GLOBAL
    ];

    for (const currentScope of scopes) {
      const config = this.loadConfig(currentScope);
      if (config[key] !== undefined) {
        return config[key];
      }
    }

    return DEFAULT_CONFIG[key];
  }

  /**
   * 设置配置值
   * @original: 配置设置逻辑 (L57634-57636)
   * @param {string} key - 配置键
   * @param {*} value - 配置值
   * @param {string} scope - 配置作用域
   * @returns {Promise<void>}
   */
  async set(key, value, scope = CONFIG_SCOPES.USER) {
    const config = this.loadConfig(scope);
    config[key] = value;
    
    await this.saveConfig(scope, config);
    
    logger.debug('配置已更新', { key, value, scope });
  }

  /**
   * 删除配置值
   * @param {string} key - 配置键
   * @param {string} scope - 配置作用域
   * @returns {Promise<void>}
   */
  async delete(key, scope = CONFIG_SCOPES.USER) {
    const config = this.loadConfig(scope);
    delete config[key];
    
    await this.saveConfig(scope, config);
    
    logger.debug('配置已删除', { key, scope });
  }

  /**
   * 加载配置
   * @param {string} scope - 配置作用域
   * @returns {Object} 配置对象
   */
  loadConfig(scope) {
    // 检查缓存
    if (this.configCache.has(scope)) {
      return this.configCache.get(scope);
    }

    const configPath = this.configPaths.get(scope);
    let config = {};

    try {
      if (existsSync(configPath)) {
        const content = readFileSync(configPath, 'utf8');
        config = JSON.parse(content);
      }
    } catch (error) {
      logger.warn('配置文件读取失败', { scope, configPath, error: error.message });
    }

    // 缓存配置
    this.configCache.set(scope, config);
    
    return config;
  }

  /**
   * 保存配置
   * @param {string} scope - 配置作用域
   * @param {Object} config - 配置对象
   * @returns {Promise<void>}
   */
  async saveConfig(scope, config) {
    const configPath = this.configPaths.get(scope);
    
    try {
      // 确保目录存在
      await ensureDirectoryExists(dirname(configPath));
      
      // 写入配置文件
      const content = JSON.stringify(config, null, 2);
      writeFileSync(configPath, content, 'utf8');
      
      // 更新缓存
      this.configCache.set(scope, config);
      
      logger.debug('配置已保存', { scope, configPath });

    } catch (error) {
      logger.error('配置保存失败', { scope, configPath, error: error.message });
      throw error;
    }
  }

  /**
   * 获取合并后的配置
   * @returns {Object} 合并后的配置
   */
  getMergedConfig() {
    const merged = { ...DEFAULT_CONFIG };

    // 按优先级顺序合并配置
    const scopes = [
      CONFIG_SCOPES.GLOBAL,
      CONFIG_SCOPES.USER,
      CONFIG_SCOPES.PROJECT,
      CONFIG_SCOPES.LOCAL
    ];

    for (const scope of scopes) {
      const config = this.loadConfig(scope);
      Object.assign(merged, config);
    }

    return merged;
  }

  /**
   * 验证配置值
   * @param {string} key - 配置键
   * @param {*} value - 配置值
   * @returns {Object} 验证结果
   */
  validateConfig(key, value) {
    const validators = {
      [CONFIG_KEYS.THEME]: (val) => ['dark', 'light', 'auto'].includes(val),
      [CONFIG_KEYS.VERBOSE]: (val) => typeof val === 'boolean',
      [CONFIG_KEYS.AUTO_UPDATES]: (val) => typeof val === 'boolean',
      [CONFIG_KEYS.COST_WARNINGS]: (val) => typeof val === 'boolean',
      [CONFIG_KEYS.PERMISSION_MODE]: (val) => ['ask', 'allow', 'deny', 'bypass'].includes(val),
      [CONFIG_KEYS.ALLOWED_TOOLS]: (val) => Array.isArray(val),
      [CONFIG_KEYS.DISALLOWED_TOOLS]: (val) => Array.isArray(val),
      [CONFIG_KEYS.IGNORE_PATTERNS]: (val) => Array.isArray(val)
    };

    const validator = validators[key];
    
    if (!validator) {
      return { valid: true };
    }

    const isValid = validator(value);
    
    return {
      valid: isValid,
      error: isValid ? null : `Invalid value for ${key}: ${value}`
    };
  }

  /**
   * 重置配置到默认值
   * @param {string} scope - 配置作用域
   * @param {Array} keys - 要重置的键（可选）
   * @returns {Promise<void>}
   */
  async resetConfig(scope, keys = null) {
    const config = keys 
      ? this.loadConfig(scope)
      : {};

    if (keys) {
      // 只重置指定的键
      for (const key of keys) {
        if (DEFAULT_CONFIG.hasOwnProperty(key)) {
          config[key] = DEFAULT_CONFIG[key];
        }
      }
    } else {
      // 重置所有配置
      Object.assign(config, DEFAULT_CONFIG);
    }

    await this.saveConfig(scope, config);
    
    logger.info('配置已重置', { scope, keys: keys || 'all' });
  }

  /**
   * 清除配置缓存
   * @param {string} scope - 配置作用域（可选）
   */
  clearCache(scope = null) {
    if (scope) {
      this.configCache.delete(scope);
    } else {
      this.configCache.clear();
    }
    
    logger.debug('配置缓存已清除', { scope: scope || 'all' });
  }

  /**
   * 监听配置文件变化
   * @param {string} scope - 配置作用域
   * @param {Function} callback - 变化回调
   */
  watchConfig(scope, callback) {
    const configPath = this.configPaths.get(scope);
    
    if (!existsSync(configPath)) {
      return;
    }

    const fs = require('fs');
    
    const watcher = fs.watchFile(configPath, (curr, prev) => {
      if (curr.mtime !== prev.mtime) {
        this.clearCache(scope);
        callback(this.loadConfig(scope));
      }
    });

    this.watchers.set(scope, watcher);
    
    logger.debug('配置文件监听已启动', { scope, configPath });
  }

  /**
   * 停止监听配置文件
   * @param {string} scope - 配置作用域
   */
  unwatchConfig(scope) {
    const watcher = this.watchers.get(scope);
    
    if (watcher) {
      const fs = require('fs');
      fs.unwatchFile(this.configPaths.get(scope));
      this.watchers.delete(scope);
      
      logger.debug('配置文件监听已停止', { scope });
    }
  }

  /**
   * 导出配置
   * @param {string} scope - 配置作用域
   * @returns {Object} 配置对象
   */
  exportConfig(scope) {
    return { ...this.loadConfig(scope) };
  }

  /**
   * 导入配置
   * @param {string} scope - 配置作用域
   * @param {Object} config - 配置对象
   * @param {boolean} merge - 是否合并现有配置
   * @returns {Promise<void>}
   */
  async importConfig(scope, config, merge = true) {
    let finalConfig = config;
    
    if (merge) {
      const existingConfig = this.loadConfig(scope);
      finalConfig = { ...existingConfig, ...config };
    }

    await this.saveConfig(scope, finalConfig);
    
    logger.info('配置已导入', { scope, merge });
  }
}

/**
 * 全局配置管理器实例
 */
export const globalConfigManager = new ConfigManager();

/**
 * 便捷函数：获取配置值
 * @param {string} key - 配置键
 * @param {string} scope - 配置作用域
 * @returns {*} 配置值
 */
export function getConfig(key, scope = null) {
  return globalConfigManager.get(key, scope);
}

/**
 * 便捷函数：设置配置值
 * @param {string} key - 配置键
 * @param {*} value - 配置值
 * @param {string} scope - 配置作用域
 * @returns {Promise<void>}
 */
export async function setConfig(key, value, scope = CONFIG_SCOPES.USER) {
  return globalConfigManager.set(key, value, scope);
}

/**
 * 便捷函数：获取合并后的配置
 * @returns {Object} 合并后的配置
 */
export function getMergedConfig() {
  return globalConfigManager.getMergedConfig();
}

/**
 * 便捷函数：重置配置
 * @param {string} scope - 配置作用域
 * @param {Array} keys - 要重置的键
 * @returns {Promise<void>}
 */
export async function resetConfig(scope, keys = null) {
  return globalConfigManager.resetConfig(scope, keys);
}
