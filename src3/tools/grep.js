/**
 * Grep工具实现
 * 
 * 基于ripgrep的高性能代码搜索工具
 * 
 * @original: Grep工具相关代码 (L15947-15959, L19337-19380)
 */

import { logger } from '../utils/logger.js';
import { executeRipgrep } from '../services/search.js';

/**
 * Grep工具名称
 * @original: W11变量 (L15946)
 */
export const GREP_TOOL_NAME = 'Grep';

/**
 * 输出模式枚举
 */
export const OUTPUT_MODES = {
  CONTENT: 'content',
  FILES_WITH_MATCHES: 'files_with_matches',
  COUNT: 'count'
};

/**
 * Grep工具类
 */
export class GrepTool {
  constructor(options = {}) {
    this.name = GREP_TOOL_NAME;
    this.maxResults = options.maxResults || 1000;
    this.defaultOutputMode = options.defaultOutputMode || OUTPUT_MODES.FILES_WITH_MATCHES;
  }

  /**
   * 工具描述
   * @original: G$0()函数 (L15947)
   * @returns {string} 工具描述
   */
  async description() {
    return `A powerful search tool built on ripgrep

Usage:
- ALWAYS use ${GREP_TOOL_NAME} for search tasks. NEVER invoke \`grep\` or \`rg\` as a Bash command. The ${GREP_TOOL_NAME} tool has been optimized for correct permissions and access.
- Supports full regex syntax (e.g., "log.*Error", "function\\s+\\w+")
- Filter files with glob parameter (e.g., "*.js", "**/*.tsx") or type parameter (e.g., "js", "py", "rust")
- Output modes: "content" shows matching lines, "files_with_matches" shows only file paths (default), "count" shows match counts
- Use Task tool for open-ended searches requiring multiple rounds
- Pattern syntax: Uses ripgrep (not grep) - literal braces need escaping (use \`interface\\{\\}\` to find \`interface{}\` in Go code)
- Multiline matching: By default patterns match within single lines only. For cross-line patterns like \`struct \\{[\\s\\S]*?field\`, use \`multiline: true\``;
  }

  /**
   * 验证输入
   * @param {Object} input - 输入参数
   * @param {Object} context - 验证上下文
   * @returns {Promise<Object>} 验证结果
   */
  async validateInput(input, context) {
    if (!input.pattern || typeof input.pattern !== 'string') {
      return {
        result: false,
        message: 'pattern is required and must be a string'
      };
    }

    if (input.pattern.trim().length === 0) {
      return {
        result: false,
        message: 'pattern cannot be empty'
      };
    }

    // 验证输出模式
    if (input.output_mode && !Object.values(OUTPUT_MODES).includes(input.output_mode)) {
      return {
        result: false,
        message: `Invalid output_mode. Must be one of: ${Object.values(OUTPUT_MODES).join(', ')}`
      };
    }

    return { result: true };
  }

  /**
   * 执行搜索
   * @param {Object} input - 输入参数
   * @param {Object} context - 执行上下文
   * @returns {Promise<Object>} 搜索结果
   */
  async call(input, context) {
    const {
      pattern,
      glob = undefined,
      type = undefined,
      output_mode = this.defaultOutputMode,
      case_sensitive = false,
      multiline = false,
      max_results = this.maxResults,
      context_lines = 2,
      directory = process.cwd()
    } = input;

    logger.debug('执行Grep搜索', { 
      pattern, 
      glob, 
      type, 
      outputMode: output_mode,
      directory 
    });

    try {
      // 构建ripgrep选项
      const options = {
        pattern,
        directory,
        glob,
        type,
        caseSensitive: case_sensitive,
        multiline,
        maxResults: max_results,
        contextLines: context_lines,
        outputMode: output_mode
      };

      // 执行搜索
      const searchResult = await executeRipgrep(options);

      // 处理搜索结果
      return this.processSearchResult(searchResult, options);

    } catch (error) {
      logger.error('Grep搜索失败', { pattern, error: error.message });
      
      return {
        type: 'error',
        message: `Search failed: ${error.message}`,
        pattern,
        directory
      };
    }
  }

  /**
   * 处理搜索结果
   * @param {Object} searchResult - 搜索结果
   * @param {Object} options - 搜索选项
   * @returns {Object} 处理后的结果
   */
  processSearchResult(searchResult, options) {
    const { outputMode, pattern } = options;

    switch (outputMode) {
      case OUTPUT_MODES.CONTENT:
        return this.formatContentResult(searchResult, pattern);
      
      case OUTPUT_MODES.FILES_WITH_MATCHES:
        return this.formatFilesResult(searchResult, pattern);
      
      case OUTPUT_MODES.COUNT:
        return this.formatCountResult(searchResult, pattern);
      
      default:
        return this.formatFilesResult(searchResult, pattern);
    }
  }

  /**
   * 格式化内容结果
   * @param {Object} searchResult - 搜索结果
   * @param {string} pattern - 搜索模式
   * @returns {Object} 格式化结果
   */
  formatContentResult(searchResult, pattern) {
    if (!searchResult.matches || searchResult.matches.length === 0) {
      return {
        type: 'no_matches',
        message: `No matches found for pattern: ${pattern}`,
        pattern
      };
    }

    const content = searchResult.matches.map(match => {
      const lines = match.lines.map(line => 
        `${line.number}: ${line.content}`
      ).join('\n');
      
      return `${match.file}:\n${lines}`;
    }).join('\n\n');

    return {
      type: 'content',
      content,
      pattern,
      totalMatches: searchResult.totalMatches,
      fileCount: searchResult.matches.length
    };
  }

  /**
   * 格式化文件结果
   * @param {Object} searchResult - 搜索结果
   * @param {string} pattern - 搜索模式
   * @returns {Object} 格式化结果
   */
  formatFilesResult(searchResult, pattern) {
    if (!searchResult.matches || searchResult.matches.length === 0) {
      return {
        type: 'no_matches',
        message: `No matches found for pattern: ${pattern}`,
        pattern
      };
    }

    const files = searchResult.matches.map(match => match.file);
    const content = files.join('\n');

    return {
      type: 'files',
      content,
      pattern,
      fileCount: files.length,
      totalMatches: searchResult.totalMatches
    };
  }

  /**
   * 格式化计数结果
   * @param {Object} searchResult - 搜索结果
   * @param {string} pattern - 搜索模式
   * @returns {Object} 格式化结果
   */
  formatCountResult(searchResult, pattern) {
    const fileCount = searchResult.matches ? searchResult.matches.length : 0;
    const totalMatches = searchResult.totalMatches || 0;

    const content = `Pattern: ${pattern}\n` +
                   `Files with matches: ${fileCount}\n` +
                   `Total matches: ${totalMatches}`;

    return {
      type: 'count',
      content,
      pattern,
      fileCount,
      totalMatches
    };
  }

  /**
   * 检查工具是否启用
   * @returns {boolean} 是否启用
   */
  isEnabled() {
    return true;
  }

  /**
   * 获取输入模式
   * @returns {Object} 输入模式定义
   */
  get inputSchema() {
    return {
      type: 'object',
      properties: {
        pattern: {
          type: 'string',
          description: 'The regex pattern to search for'
        },
        glob: {
          type: 'string',
          description: 'Glob pattern to filter files (e.g., "*.js", "**/*.tsx")'
        },
        type: {
          type: 'string',
          description: 'File type filter (e.g., "js", "py", "rust")'
        },
        output_mode: {
          type: 'string',
          enum: Object.values(OUTPUT_MODES),
          description: 'Output format: content (matching lines), files_with_matches (file paths), count (match counts)'
        },
        case_sensitive: {
          type: 'boolean',
          description: 'Whether the search should be case sensitive'
        },
        multiline: {
          type: 'boolean',
          description: 'Enable multiline matching for cross-line patterns'
        },
        max_results: {
          type: 'number',
          description: 'Maximum number of results to return'
        },
        context_lines: {
          type: 'number',
          description: 'Number of context lines to show around matches'
        },
        directory: {
          type: 'string',
          description: 'Directory to search in (defaults to current working directory)'
        }
      },
      required: ['pattern']
    };
  }

  /**
   * 渲染工具使用消息
   * @param {Object} input - 输入参数
   * @param {Object} options - 渲染选项
   * @returns {string} 渲染结果
   */
  renderToolUseMessage(input, options) {
    const { verbose } = options;
    
    if (verbose) {
      const filters = [];
      if (input.glob) filters.push(`glob: ${input.glob}`);
      if (input.type) filters.push(`type: ${input.type}`);
      
      const filterText = filters.length > 0 ? ` (${filters.join(', ')})` : '';
      return `Searching for pattern: ${input.pattern}${filterText}`;
    }
    
    return `🔍 ${input.pattern}`;
  }

  /**
   * 渲染工具结果消息
   * @param {Object} result - 工具结果
   * @param {Object} input - 输入参数
   * @param {Object} options - 渲染选项
   * @returns {string} 渲染结果
   */
  renderToolResultMessage(result, input, options) {
    const { verbose } = options;
    
    if (result.type === 'error') {
      return `❌ Search failed: ${result.message}`;
    }
    
    if (result.type === 'no_matches') {
      return `🔍 No matches found for: ${result.pattern}`;
    }
    
    if (verbose) {
      return `Search completed: ${result.fileCount} files, ${result.totalMatches} matches`;
    }
    
    return `✅ Found ${result.fileCount} files`;
  }
}

/**
 * 创建Grep工具实例
 * @param {Object} options - 配置选项
 * @returns {GrepTool} Grep工具实例
 */
export function createGrepTool(options = {}) {
  return new GrepTool(options);
}

/**
 * 默认Grep工具实例
 */
export const grepTool = createGrepTool();

/**
 * 便捷函数：搜索代码
 * @param {string} pattern - 搜索模式
 * @param {Object} options - 搜索选项
 * @returns {Promise<Object>} 搜索结果
 */
export async function searchCode(pattern, options = {}) {
  return grepTool.call({ pattern, ...options }, {
    abortController: new AbortController()
  });
}
