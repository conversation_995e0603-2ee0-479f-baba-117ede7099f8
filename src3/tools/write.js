/**
 * Write工具实现
 * 
 * 提供安全的文件写入功能
 * 
 * @original: Write工具相关代码 (L19381-19430, L20000-20100)
 */

import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { dirname } from 'path';
import { logger } from '../utils/logger.js';
import { safeWriteFile, checkFileAccess } from '../utils/filesystem.js';

/**
 * Write工具名称
 */
export const WRITE_TOOL_NAME = 'Write';

/**
 * 最大文件大小（10MB）
 */
export const MAX_FILE_SIZE = 10 * 1024 * 1024;

/**
 * Write工具类
 */
export class WriteTool {
  constructor(options = {}) {
    this.name = WRITE_TOOL_NAME;
    this.maxFileSize = options.maxFileSize || MAX_FILE_SIZE;
    this.createDirectories = options.createDirectories !== false;
    this.backupExisting = options.backupExisting || false;
  }

  /**
   * 工具描述
   * @original: Write工具描述逻辑
   * @returns {string} 工具描述
   */
  async description() {
    return `Writes content to a file on the local filesystem.

Usage:
- The file_path parameter must be an absolute path, not a relative path
- If the file already exists, it will be completely overwritten
- If the directory doesn't exist, it will be created automatically
- Content should be provided as a string
- Binary content is not supported - use this tool only for text files
- Maximum file size: ${this.formatFileSize(this.maxFileSize)}
- The tool will create parent directories if they don't exist
- Use Edit tool instead if you want to modify specific parts of an existing file

Safety features:
- Validates file path format
- Checks for write permissions
- Creates backup of existing files (if enabled)
- Validates content size limits`;
  }

  /**
   * 验证输入
   * @param {Object} input - 输入参数
   * @param {Object} context - 验证上下文
   * @returns {Promise<Object>} 验证结果
   */
  async validateInput(input, context) {
    if (!input.file_path || typeof input.file_path !== 'string') {
      return {
        result: false,
        message: 'file_path is required and must be a string'
      };
    }

    if (!input.content && input.content !== '') {
      return {
        result: false,
        message: 'content is required (can be empty string)'
      };
    }

    if (typeof input.content !== 'string') {
      return {
        result: false,
        message: 'content must be a string'
      };
    }

    // 检查路径是否为绝对路径
    if (!input.file_path.startsWith('/') && !input.file_path.match(/^[A-Za-z]:\\/)) {
      return {
        result: false,
        message: 'file_path must be an absolute path, not a relative path'
      };
    }

    // 检查内容大小
    const contentSize = Buffer.byteLength(input.content, 'utf8');
    if (contentSize > this.maxFileSize) {
      return {
        result: false,
        message: `Content size (${this.formatFileSize(contentSize)}) exceeds maximum allowed size (${this.formatFileSize(this.maxFileSize)})`
      };
    }

    return { result: true };
  }

  /**
   * 写入文件
   * @param {Object} input - 输入参数
   * @param {Object} context - 执行上下文
   * @returns {Promise<Object>} 写入结果
   */
  async call(input, context) {
    const { file_path, content } = input;
    
    logger.debug('写入文件', { filePath: file_path, contentLength: content.length });

    try {
      // 检查文件访问权限
      const accessCheck = checkFileAccess(file_path);
      const fileExists = accessCheck.exists;

      // 创建目录（如果需要）
      if (this.createDirectories) {
        const dir = dirname(file_path);
        if (!existsSync(dir)) {
          mkdirSync(dir, { recursive: true });
          logger.debug('创建目录', { directory: dir });
        }
      }

      // 备份现有文件（如果启用）
      if (this.backupExisting && fileExists) {
        await this.createBackup(file_path);
      }

      // 写入文件
      const writeResult = safeWriteFile(file_path, content);
      
      if (!writeResult.success) {
        return {
          type: 'error',
          message: writeResult.error,
          filePath: file_path
        };
      }

      // 统计信息
      const lines = content.split('\n').length;
      const size = Buffer.byteLength(content, 'utf8');

      logger.debug('文件写入成功', { 
        filePath: file_path, 
        lines, 
        size,
        existed: fileExists 
      });

      return {
        type: 'success',
        message: fileExists 
          ? `File overwritten: ${file_path} (${lines} lines, ${this.formatFileSize(size)})`
          : `File created: ${file_path} (${lines} lines, ${this.formatFileSize(size)})`,
        filePath: file_path,
        lines,
        size,
        existed: fileExists
      };

    } catch (error) {
      logger.error('文件写入失败', { filePath: file_path, error: error.message });
      
      return {
        type: 'error',
        message: `Failed to write file: ${error.message}`,
        filePath: file_path
      };
    }
  }

  /**
   * 创建文件备份
   * @param {string} filePath - 文件路径
   * @returns {Promise<void>}
   */
  async createBackup(filePath) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = `${filePath}.backup.${timestamp}`;
      
      const readResult = safeReadFile(filePath);
      if (readResult.success) {
        const writeResult = safeWriteFile(backupPath, readResult.content);
        if (writeResult.success) {
          logger.debug('文件备份创建成功', { original: filePath, backup: backupPath });
        }
      }
    } catch (error) {
      logger.warn('文件备份创建失败', { filePath, error: error.message });
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化的文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 bytes';
    
    const units = ['bytes', 'KB', 'MB', 'GB'];
    const k = 1024;
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${(bytes / Math.pow(k, i)).toFixed(1)} ${units[i]}`;
  }

  /**
   * 检查工具是否启用
   * @returns {boolean} 是否启用
   */
  isEnabled() {
    return true;
  }

  /**
   * 获取输入模式
   * @returns {Object} 输入模式定义
   */
  get inputSchema() {
    return {
      type: 'object',
      properties: {
        file_path: {
          type: 'string',
          description: 'The absolute path to the file to write'
        },
        content: {
          type: 'string',
          description: 'The content to write to the file'
        }
      },
      required: ['file_path', 'content']
    };
  }

  /**
   * 渲染工具使用消息
   * @param {Object} input - 输入参数
   * @param {Object} options - 渲染选项
   * @returns {string} 渲染结果
   */
  renderToolUseMessage(input, options) {
    const { verbose } = options;
    
    if (verbose) {
      const lines = input.content.split('\n').length;
      const size = Buffer.byteLength(input.content, 'utf8');
      return `Writing file: ${input.file_path} (${lines} lines, ${this.formatFileSize(size)})`;
    }
    
    return `📝 ${input.file_path}`;
  }

  /**
   * 渲染工具结果消息
   * @param {Object} result - 工具结果
   * @param {Object} input - 输入参数
   * @param {Object} options - 渲染选项
   * @returns {string} 渲染结果
   */
  renderToolResultMessage(result, input, options) {
    const { verbose } = options;
    
    if (result.type === 'error') {
      return `❌ ${result.message}`;
    }
    
    if (verbose) {
      return result.message;
    }
    
    return result.existed 
      ? `✅ Overwrote ${input.file_path}`
      : `✅ Created ${input.file_path}`;
  }

  /**
   * 映射工具结果到工具结果块参数
   * @param {Object} result - 工具结果
   * @param {string} toolUseId - 工具使用ID
   * @returns {Object} 工具结果块参数
   */
  mapToolResultToToolResultBlockParam(result, toolUseId) {
    return {
      tool_use_id: toolUseId,
      type: 'tool_result',
      content: result.message || result.content
    };
  }
}

/**
 * 创建Write工具实例
 * @param {Object} options - 配置选项
 * @returns {WriteTool} Write工具实例
 */
export function createWriteTool(options = {}) {
  return new WriteTool(options);
}

/**
 * 默认Write工具实例
 */
export const writeTool = createWriteTool();

/**
 * 便捷函数：写入文件
 * @param {string} filePath - 文件路径
 * @param {string} content - 文件内容
 * @returns {Promise<Object>} 写入结果
 */
export async function writeFile(filePath, content) {
  return writeTool.call({ file_path: filePath, content }, {
    abortController: new AbortController()
  });
}
