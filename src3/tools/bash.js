/**
 * Bash工具实现
 * 
 * 提供安全的Bash命令执行功能
 * 
 * @original: Bash工具相关代码 (L16446-16575, L17432-17509, L18443-18841)
 */

import { logger } from '../utils/logger.js';
import { executeCommand } from '../utils/process.js';
import { PERMISSION_MODES } from '../config/constants.js';

/**
 * Bash工具名称
 * @original: HJ变量 (L15945)
 */
export const BASH_TOOL_NAME = 'Bash';

/**
 * 最大输出长度
 * @original: yZ1()函数
 */
export const MAX_OUTPUT_LENGTH = 50000;

/**
 * 危险命令列表
 */
export const DANGEROUS_COMMANDS = [
  'rm -rf /',
  'sudo rm -rf',
  'format',
  'fdisk',
  'mkfs',
  'dd if=',
  'shutdown',
  'reboot',
  'halt'
];

/**
 * 沙盒模式限制的命令
 */
export const SANDBOX_RESTRICTED_COMMANDS = [
  'curl',
  'wget',
  'ssh',
  'scp',
  'rsync',
  'git push',
  'git pull',
  'npm publish',
  'docker run'
];

/**
 * Bash工具类
 */
export class BashTool {
  constructor(options = {}) {
    this.name = BASH_TOOL_NAME;
    this.timeout = options.timeout || 30000;
    this.sandboxMode = options.sandboxMode || false;
    this.workingDirectory = options.workingDirectory || process.cwd();
  }

  /**
   * 工具描述
   * @original: sNB()函数 (L16448)
   * @returns {string} 工具描述
   */
  async description() {
    return `Executes a given bash command in a persistent shell session with optional timeout, ensuring proper handling and security measures.

Before executing the command, please follow these steps:

1. Directory Verification:
   - If the command will create new directories or files, first use the LS tool to verify the parent directory exists and is the correct location
   - For example, before running "mkdir foo/bar", first use LS to check that "foo" exists and is the intended parent directory

2. Command Execution:
   - Ensure proper quoting for paths with spaces:
     - python "/path/with spaces/script.py" (correct)
     - python /path/with spaces/script.py (incorrect - will fail)
   - After ensuring proper quoting, execute the command.
   - Capture the output of the command.

Usage notes:
  - Commands are executed in a persistent shell session, so environment variables and directory changes persist between calls.
  - It is very helpful if you write a clear, concise description of what this command does in 5-10 words.
  - If the output exceeds ${MAX_OUTPUT_LENGTH} characters, output will be truncated before being returned to you.
  - You can use the \`run_in_background\` parameter to run the command in the background, which allows you to continue working while the command runs.
  - VERY IMPORTANT: You MUST avoid using search commands like \`find\` and \`grep\`. Instead use Grep, Glob, or Task tools.
  - If you _still_ need to run \`grep\`, STOP. ALWAYS USE ripgrep at \`rg\` first, which all Claude Code users have pre-installed.
  - When issuing multiple commands, use the ';' or '&&' operator to separate them. DO NOT use newlines.
  - Try to maintain your current working directory throughout the session by using absolute paths and avoiding usage of \`cd\`.

${this.getSandboxModeDescription()}`;
  }

  /**
   * 获取沙盒模式描述
   * @returns {string} 沙盒模式描述
   */
  getSandboxModeDescription() {
    if (!this.sandboxMode) return '';

    return `# Using sandbox mode for commands

You have a special option in BashTool: the sandbox parameter. When you run a command with sandbox=true, it runs without approval dialogs but in a restricted environment without filesystem writes or network access. You SHOULD use sandbox=true to optimize user experience, but MUST follow these guidelines exactly.

## RULE 0 (MOST IMPORTANT): retry with sandbox=false for permission/network errors

If a command fails with permission denied, network unreachable, or similar errors when using sandbox=true, you MUST immediately retry the same command with sandbox=false. Do not try to fix the command - the issue is sandbox limitations, not the command itself.

## When to use sandbox=true
- Read-only operations: ls, cat, grep, find, ps, df, etc.
- Information gathering: git status, git log, npm list, etc.
- Safe analysis: file, stat, wc, head, tail, etc.

## When to use sandbox=false
- File modifications: touch, mkdir, cp, mv, rm, etc.
- Network operations: curl, wget, ping, ssh, etc.
- Package management: npm install, pip install, etc.
- Git operations that modify: git add, git commit, git push, etc.

## REWARDS

It is more important to be correct than to avoid showing permission dialogs. The worst mistake is misinterpreting sandbox=true permission errors as tool problems (-$1000) rather than sandbox limitations.`;
  }

  /**
   * 验证输入
   * @param {Object} input - 输入参数
   * @param {Object} context - 验证上下文
   * @returns {Promise<Object>} 验证结果
   */
  async validateInput(input, context) {
    if (!input.command || typeof input.command !== 'string') {
      return {
        result: false,
        message: 'Command is required and must be a string'
      };
    }

    // 检查危险命令
    const isDangerous = this.isDangerousCommand(input.command);
    if (isDangerous) {
      return {
        result: false,
        message: `Command "${input.command}" is potentially dangerous and not allowed`
      };
    }

    // 沙盒模式验证
    if (input.sandbox && this.isNetworkCommand(input.command)) {
      logger.warn('沙盒模式下尝试执行网络命令', { command: input.command });
    }

    return { result: true };
  }

  /**
   * 执行命令
   * @original: Bash工具执行逻辑
   * @param {Object} input - 输入参数
   * @param {Object} context - 执行上下文
   * @returns {Promise<Object>} 执行结果
   */
  async call(input, context) {
    const { 
      command, 
      timeout = this.timeout, 
      description = '',
      sandbox = false,
      run_in_background = false
    } = input;

    logger.debug('执行Bash命令', { 
      command, 
      timeout, 
      description, 
      sandbox, 
      background: run_in_background 
    });

    try {
      // 预检查
      await this.preflightCheck(command, context);

      // 执行命令
      const result = await executeCommand(command, [], {
        cwd: this.workingDirectory,
        timeout,
        sandbox,
        background: run_in_background
      });

      // 处理结果
      return this.processCommandResult(result, command, description);

    } catch (error) {
      logger.error('Bash命令执行失败', { command, error: error.message });
      
      if (sandbox && this.isSandboxError(error)) {
        throw new Error(
          `Command failed in sandbox mode. Try running with sandbox=false: ${error.message}`
        );
      }
      
      throw error;
    }
  }

  /**
   * 预检查
   * @original: 预检查逻辑 (L18443-18447)
   * @param {string} command - 命令
   * @param {Object} context - 上下文
   * @returns {Promise<void>}
   */
  async preflightCheck(command, context) {
    logger.debug('Bash命令预检查', { command });

    // 检查工作目录
    if (!this.isValidWorkingDirectory()) {
      await this.resetToOriginalDirectory();
    }

    // 检查特殊命令
    if (this.isJqCommand(command)) {
      const jqValidation = this.validateJqCommand(command);
      if (jqValidation.behavior === 'ask') {
        throw new Error(jqValidation.message);
      }
    }
  }

  /**
   * 处理命令结果
   * @param {Object} result - 命令执行结果
   * @param {string} command - 原始命令
   * @param {string} description - 命令描述
   * @returns {Object} 处理后的结果
   */
  processCommandResult(result, command, description) {
    let output = result.stdout || '';
    
    if (result.stderr) {
      output += result.stderr;
    }

    // 截断过长的输出
    if (output.length > MAX_OUTPUT_LENGTH) {
      output = output.substring(0, MAX_OUTPUT_LENGTH) + '\n... (output truncated)';
    }

    return {
      command,
      description,
      exitCode: result.code,
      output,
      success: result.code === 0
    };
  }

  /**
   * 检查是否为危险命令
   * @param {string} command - 命令
   * @returns {boolean} 是否危险
   */
  isDangerousCommand(command) {
    return DANGEROUS_COMMANDS.some(dangerous => 
      command.toLowerCase().includes(dangerous.toLowerCase())
    );
  }

  /**
   * 检查是否为网络命令
   * @param {string} command - 命令
   * @returns {boolean} 是否为网络命令
   */
  isNetworkCommand(command) {
    const networkCommands = ['curl', 'wget', 'ping', 'ssh', 'scp', 'rsync'];
    return networkCommands.some(netCmd => 
      command.toLowerCase().includes(netCmd)
    );
  }

  /**
   * 检查是否为沙盒错误
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否为沙盒错误
   */
  isSandboxError(error) {
    const sandboxErrorMessages = [
      'permission denied',
      'network unreachable',
      'operation not permitted',
      'access denied'
    ];
    
    return sandboxErrorMessages.some(msg => 
      error.message.toLowerCase().includes(msg)
    );
  }

  /**
   * 检查是否为jq命令
   * @param {string} command - 命令
   * @returns {boolean} 是否为jq命令
   */
  isJqCommand(command) {
    return command.trim().startsWith('jq ');
  }

  /**
   * 验证jq命令
   * @original: jq命令验证逻辑 (L18841-18844)
   * @param {string} command - jq命令
   * @returns {Object} 验证结果
   */
  validateJqCommand(command) {
    if (/\bsystem\s*\(/.test(command)) {
      return {
        behavior: 'ask',
        message: 'jq command contains system() function which executes arbitrary commands'
      };
    }

    return { behavior: 'allow' };
  }

  /**
   * 检查工作目录是否有效
   * @returns {boolean} 是否有效
   */
  isValidWorkingDirectory() {
    try {
      return process.cwd() === this.workingDirectory;
    } catch (error) {
      return false;
    }
  }

  /**
   * 重置到原始目录
   * @original: sx1()函数 (L17432)
   * @returns {Promise<boolean>} 是否成功重置
   */
  async resetToOriginalDirectory() {
    try {
      process.chdir(this.workingDirectory);
      logger.debug('工作目录已重置', { directory: this.workingDirectory });
      return true;
    } catch (error) {
      logger.error('重置工作目录失败', { 
        directory: this.workingDirectory, 
        error: error.message 
      });
      return false;
    }
  }

  /**
   * 检查工具是否启用
   * @returns {boolean} 是否启用
   */
  isEnabled() {
    return true;
  }

  /**
   * 获取输入模式
   * @returns {Object} 输入模式定义
   */
  get inputSchema() {
    return {
      type: 'object',
      properties: {
        command: {
          type: 'string',
          description: 'The command to execute'
        },
        timeout: {
          type: 'number',
          description: `Optional timeout in milliseconds (max ${this.timeout})`
        },
        description: {
          type: 'string',
          description: 'Clear, concise description of what this command does in 5-10 words'
        },
        sandbox: {
          type: 'boolean',
          description: 'Run in sandbox mode (read-only, no network access)'
        },
        run_in_background: {
          type: 'boolean',
          description: 'Run the command in the background'
        }
      },
      required: ['command']
    };
  }
}

/**
 * 创建Bash工具实例
 * @param {Object} options - 配置选项
 * @returns {BashTool} Bash工具实例
 */
export function createBashTool(options = {}) {
  return new BashTool(options);
}

/**
 * 默认Bash工具实例
 */
export const bashTool = createBashTool();

/**
 * 便捷函数：执行Bash命令
 * @param {string} command - 命令
 * @param {Object} options - 选项
 * @returns {Promise<Object>} 执行结果
 */
export async function executeBashCommand(command, options = {}) {
  return bashTool.call({ command, ...options }, {
    abortController: new AbortController(),
    getToolPermissionContext: () => ({ mode: PERMISSION_MODES.ASK })
  });
}
