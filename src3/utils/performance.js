/**
 * 性能监控工具
 * 
 * 提供性能测量、监控和分析功能
 * 
 * @original: 性能监控相关代码 (L2022-2200, L53070-53079)
 */

import { logger } from './logger.js';
import { formatDuration, formatBytes } from './helpers.js';

/**
 * 性能计时器类
 */
export class PerformanceTimer {
  constructor() {
    this.timers = new Map();
    this.marks = new Map();
    this.measures = new Map();
  }

  /**
   * 开始计时
   * @param {string} name - 计时器名称
   */
  start(name) {
    this.timers.set(name, {
      startTime: performance.now(),
      endTime: null,
      duration: null
    });
  }

  /**
   * 结束计时
   * @param {string} name - 计时器名称
   * @returns {number} 持续时间（毫秒）
   */
  end(name) {
    const timer = this.timers.get(name);
    
    if (!timer) {
      logger.warn('计时器不存在', { name });
      return 0;
    }

    timer.endTime = performance.now();
    timer.duration = timer.endTime - timer.startTime;

    return timer.duration;
  }

  /**
   * 获取计时结果
   * @param {string} name - 计时器名称
   * @returns {Object|null} 计时结果
   */
  getTimer(name) {
    return this.timers.get(name) || null;
  }

  /**
   * 获取所有计时器
   * @returns {Map} 所有计时器
   */
  getAllTimers() {
    return new Map(this.timers);
  }

  /**
   * 清除计时器
   * @param {string} name - 计时器名称
   */
  clear(name) {
    this.timers.delete(name);
  }

  /**
   * 清除所有计时器
   */
  clearAll() {
    this.timers.clear();
  }

  /**
   * 创建性能标记
   * @param {string} name - 标记名称
   */
  mark(name) {
    const timestamp = performance.now();
    this.marks.set(name, timestamp);
    
    if (typeof performance.mark === 'function') {
      performance.mark(name);
    }
  }

  /**
   * 测量两个标记之间的时间
   * @param {string} name - 测量名称
   * @param {string} startMark - 开始标记
   * @param {string} endMark - 结束标记
   * @returns {number} 持续时间（毫秒）
   */
  measure(name, startMark, endMark) {
    const startTime = this.marks.get(startMark);
    const endTime = this.marks.get(endMark);

    if (!startTime || !endTime) {
      logger.warn('标记不存在', { startMark, endMark });
      return 0;
    }

    const duration = endTime - startTime;
    this.measures.set(name, {
      startMark,
      endMark,
      duration,
      timestamp: performance.now()
    });

    if (typeof performance.measure === 'function') {
      try {
        performance.measure(name, startMark, endMark);
      } catch (error) {
        logger.warn('性能测量失败', { name, error: error.message });
      }
    }

    return duration;
  }

  /**
   * 获取测量结果
   * @param {string} name - 测量名称
   * @returns {Object|null} 测量结果
   */
  getMeasure(name) {
    return this.measures.get(name) || null;
  }

  /**
   * 获取性能报告
   * @returns {Object} 性能报告
   */
  getReport() {
    const report = {
      timers: {},
      marks: {},
      measures: {},
      summary: {
        totalTimers: this.timers.size,
        totalMarks: this.marks.size,
        totalMeasures: this.measures.size
      }
    };

    // 处理计时器
    for (const [name, timer] of this.timers) {
      report.timers[name] = {
        duration: timer.duration,
        formattedDuration: timer.duration ? formatDuration(timer.duration) : 'N/A',
        startTime: timer.startTime,
        endTime: timer.endTime,
        isRunning: timer.endTime === null
      };
    }

    // 处理标记
    for (const [name, timestamp] of this.marks) {
      report.marks[name] = {
        timestamp,
        formattedTime: formatDuration(timestamp)
      };
    }

    // 处理测量
    for (const [name, measure] of this.measures) {
      report.measures[name] = {
        ...measure,
        formattedDuration: formatDuration(measure.duration)
      };
    }

    return report;
  }
}

/**
 * 内存监控器类
 */
export class MemoryMonitor {
  constructor() {
    this.snapshots = [];
    this.maxSnapshots = 100;
  }

  /**
   * 获取当前内存使用情况
   * @returns {Object} 内存使用情况
   */
  getCurrentUsage() {
    const usage = process.memoryUsage();
    
    return {
      rss: usage.rss,
      heapTotal: usage.heapTotal,
      heapUsed: usage.heapUsed,
      external: usage.external,
      arrayBuffers: usage.arrayBuffers || 0,
      timestamp: Date.now(),
      formatted: {
        rss: formatBytes(usage.rss),
        heapTotal: formatBytes(usage.heapTotal),
        heapUsed: formatBytes(usage.heapUsed),
        external: formatBytes(usage.external),
        arrayBuffers: formatBytes(usage.arrayBuffers || 0)
      }
    };
  }

  /**
   * 创建内存快照
   * @param {string} label - 快照标签
   * @returns {Object} 内存快照
   */
  takeSnapshot(label = '') {
    const snapshot = {
      label,
      ...this.getCurrentUsage()
    };

    this.snapshots.push(snapshot);

    // 限制快照数量
    if (this.snapshots.length > this.maxSnapshots) {
      this.snapshots.shift();
    }

    return snapshot;
  }

  /**
   * 比较两个快照
   * @param {Object} snapshot1 - 快照1
   * @param {Object} snapshot2 - 快照2
   * @returns {Object} 比较结果
   */
  compareSnapshots(snapshot1, snapshot2) {
    return {
      rss: snapshot2.rss - snapshot1.rss,
      heapTotal: snapshot2.heapTotal - snapshot1.heapTotal,
      heapUsed: snapshot2.heapUsed - snapshot1.heapUsed,
      external: snapshot2.external - snapshot1.external,
      arrayBuffers: snapshot2.arrayBuffers - snapshot1.arrayBuffers,
      duration: snapshot2.timestamp - snapshot1.timestamp,
      formatted: {
        rss: formatBytes(snapshot2.rss - snapshot1.rss),
        heapTotal: formatBytes(snapshot2.heapTotal - snapshot1.heapTotal),
        heapUsed: formatBytes(snapshot2.heapUsed - snapshot1.heapUsed),
        external: formatBytes(snapshot2.external - snapshot1.external),
        arrayBuffers: formatBytes(snapshot2.arrayBuffers - snapshot1.arrayBuffers),
        duration: formatDuration(snapshot2.timestamp - snapshot1.timestamp)
      }
    };
  }

  /**
   * 获取内存趋势
   * @param {number} count - 最近的快照数量
   * @returns {Object} 内存趋势
   */
  getTrend(count = 10) {
    const recentSnapshots = this.snapshots.slice(-count);
    
    if (recentSnapshots.length < 2) {
      return null;
    }

    const first = recentSnapshots[0];
    const last = recentSnapshots[recentSnapshots.length - 1];
    
    return this.compareSnapshots(first, last);
  }

  /**
   * 获取所有快照
   * @returns {Array} 快照列表
   */
  getSnapshots() {
    return [...this.snapshots];
  }

  /**
   * 清除快照
   */
  clearSnapshots() {
    this.snapshots = [];
  }

  /**
   * 检查内存泄漏
   * @param {number} threshold - 阈值（字节）
   * @returns {Object} 检查结果
   */
  checkMemoryLeak(threshold = 50 * 1024 * 1024) { // 50MB
    const trend = this.getTrend();
    
    if (!trend) {
      return {
        hasLeak: false,
        message: '数据不足，无法检测内存泄漏'
      };
    }

    const heapGrowth = trend.heapUsed;
    const hasLeak = heapGrowth > threshold;

    return {
      hasLeak,
      heapGrowth,
      threshold,
      message: hasLeak 
        ? `检测到可能的内存泄漏，堆内存增长 ${formatBytes(heapGrowth)}`
        : '未检测到内存泄漏',
      trend
    };
  }
}

/**
 * 性能监控器类
 * @original: BdB()函数 (L53070-53079)
 */
export class PerformanceMonitor {
  constructor() {
    this.timer = new PerformanceTimer();
    this.memory = new MemoryMonitor();
    this.metrics = new Map();
    this.startTime = Date.now();
    this.isMonitoring = false;
    this.monitoringInterval = null;
  }

  /**
   * 开始监控
   * @param {Object} options - 监控选项
   */
  startMonitoring(options = {}) {
    const {
      interval = 5000, // 5秒
      memorySnapshots = true,
      logMetrics = false
    } = options;

    if (this.isMonitoring) {
      logger.warn('性能监控已在运行');
      return;
    }

    this.isMonitoring = true;
    this.timer.mark('monitoring_start');

    if (memorySnapshots) {
      this.memory.takeSnapshot('monitoring_start');
    }

    this.monitoringInterval = setInterval(() => {
      if (memorySnapshots) {
        this.memory.takeSnapshot(`interval_${Date.now()}`);
      }

      if (logMetrics) {
        this.logCurrentMetrics();
      }

      // 检查内存泄漏
      const leakCheck = this.memory.checkMemoryLeak();
      if (leakCheck.hasLeak) {
        logger.warn('内存泄漏警告', leakCheck);
      }

    }, interval);

    logger.info('性能监控已启动', { interval });
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    this.timer.mark('monitoring_end');
    this.memory.takeSnapshot('monitoring_end');

    const duration = this.timer.measure('total_monitoring', 'monitoring_start', 'monitoring_end');
    
    logger.info('性能监控已停止', { 
      duration: formatDuration(duration),
      snapshots: this.memory.getSnapshots().length
    });
  }

  /**
   * 记录自定义指标
   * @param {string} name - 指标名称
   * @param {*} value - 指标值
   * @param {Object} metadata - 元数据
   */
  recordMetric(name, value, metadata = {}) {
    const metric = {
      name,
      value,
      metadata,
      timestamp: Date.now()
    };

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    this.metrics.get(name).push(metric);

    // 限制每个指标的历史记录数量
    const history = this.metrics.get(name);
    if (history.length > 1000) {
      history.shift();
    }
  }

  /**
   * 获取指标
   * @param {string} name - 指标名称
   * @returns {Array} 指标历史
   */
  getMetric(name) {
    return this.metrics.get(name) || [];
  }

  /**
   * 获取所有指标
   * @returns {Map} 所有指标
   */
  getAllMetrics() {
    return new Map(this.metrics);
  }

  /**
   * 记录运行时间
   * @original: 运行时间记录逻辑 (L53070-53079)
   */
  recordUptime() {
    const uptime = Math.round(process.uptime() * 1000);
    this.recordMetric('uptime', uptime, {
      formatted: formatDuration(uptime),
      processUptime: process.uptime()
    });

    // 记录遥测事件（如果需要）
    if (typeof logTelemetryEvent === 'function') {
      logTelemetryEvent('tengu_timer', {
        uptime_ms: uptime
      });
    }
  }

  /**
   * 记录当前指标
   */
  logCurrentMetrics() {
    const memoryUsage = this.memory.getCurrentUsage();
    const uptime = Date.now() - this.startTime;

    logger.debug('性能指标', {
      uptime: formatDuration(uptime),
      memory: memoryUsage.formatted,
      timers: this.timer.getAllTimers().size,
      metrics: this.metrics.size
    });
  }

  /**
   * 获取性能摘要
   * @returns {Object} 性能摘要
   */
  getSummary() {
    const uptime = Date.now() - this.startTime;
    const memoryUsage = this.memory.getCurrentUsage();
    const timerReport = this.timer.getReport();

    return {
      uptime: {
        ms: uptime,
        formatted: formatDuration(uptime)
      },
      memory: memoryUsage,
      timers: timerReport,
      metrics: {
        count: this.metrics.size,
        names: Array.from(this.metrics.keys())
      },
      monitoring: {
        isActive: this.isMonitoring,
        snapshots: this.memory.getSnapshots().length
      }
    };
  }

  /**
   * 导出性能数据
   * @param {string} format - 导出格式 ('json' | 'csv')
   * @returns {string} 导出的数据
   */
  exportData(format = 'json') {
    const summary = this.getSummary();
    const snapshots = this.memory.getSnapshots();
    const metrics = {};

    // 转换指标数据
    for (const [name, history] of this.metrics) {
      metrics[name] = history;
    }

    const data = {
      summary,
      snapshots,
      metrics,
      exportTime: new Date().toISOString()
    };

    switch (format) {
      case 'json':
        return JSON.stringify(data, null, 2);
      
      case 'csv':
        // 简化的CSV导出（仅内存快照）
        const csvLines = ['timestamp,label,rss,heapTotal,heapUsed,external'];
        for (const snapshot of snapshots) {
          csvLines.push([
            snapshot.timestamp,
            snapshot.label || '',
            snapshot.rss,
            snapshot.heapTotal,
            snapshot.heapUsed,
            snapshot.external
          ].join(','));
        }
        return csvLines.join('\n');
      
      default:
        throw new Error(`不支持的导出格式: ${format}`);
    }
  }

  /**
   * 重置所有数据
   */
  reset() {
    this.timer.clearAll();
    this.memory.clearSnapshots();
    this.metrics.clear();
    this.startTime = Date.now();
    
    logger.info('性能监控数据已重置');
  }
}

/**
 * 全局性能监控器实例
 */
export const globalPerformanceMonitor = new PerformanceMonitor();

/**
 * 便捷函数：开始性能计时
 * @param {string} name - 计时器名称
 */
export function startTimer(name) {
  globalPerformanceMonitor.timer.start(name);
}

/**
 * 便捷函数：结束性能计时
 * @param {string} name - 计时器名称
 * @returns {number} 持续时间（毫秒）
 */
export function endTimer(name) {
  return globalPerformanceMonitor.timer.end(name);
}

/**
 * 便捷函数：创建性能标记
 * @param {string} name - 标记名称
 */
export function mark(name) {
  globalPerformanceMonitor.timer.mark(name);
}

/**
 * 便捷函数：测量性能
 * @param {string} name - 测量名称
 * @param {string} startMark - 开始标记
 * @param {string} endMark - 结束标记
 * @returns {number} 持续时间（毫秒）
 */
export function measure(name, startMark, endMark) {
  return globalPerformanceMonitor.timer.measure(name, startMark, endMark);
}

/**
 * 便捷函数：记录指标
 * @param {string} name - 指标名称
 * @param {*} value - 指标值
 * @param {Object} metadata - 元数据
 */
export function recordMetric(name, value, metadata = {}) {
  globalPerformanceMonitor.recordMetric(name, value, metadata);
}

/**
 * 便捷函数：获取性能摘要
 * @returns {Object} 性能摘要
 */
export function getPerformanceSummary() {
  return globalPerformanceMonitor.getSummary();
}
