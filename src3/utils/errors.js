/**
 * 错误处理工具
 * 
 * 定义各种错误类型和错误处理工具函数
 * 
 * @original: 错误类定义 (L2891-2934, L22666-22686, L25221-25230)
 */

import { logger } from './logger.js';

/**
 * 基础错误类
 */
export class BaseError extends Error {
  constructor(message, code = null, details = {}) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
    
    // 保持堆栈跟踪
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * 转换为JSON格式
   * @returns {Object} JSON表示
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      details: this.details,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }
}

/**
 * 网络相关错误
 * @original: RN类 (L2891)
 */
export class NetworkError extends BaseError {
  constructor(message, url = null, statusCode = null) {
    super(message, 'NETWORK_ERROR', { url, statusCode });
    this.url = url;
    this.statusCode = statusCode;
  }
}

/**
 * JSON解析错误
 * @original: nJ类 (L2892-2897)
 */
export class JSONParseError extends BaseError {
  constructor(originalError) {
    super('JSON解析失败', 'JSON_PARSE_ERROR', { originalError: originalError.message });
    this.originalError = originalError;
  }
}

/**
 * 命令执行错误
 * @original: ON类 (L2898-2911)
 */
export class CommandExecutionError extends BaseError {
  constructor(message, stdout = '', stderr = '', exitCode = null) {
    super(message, 'COMMAND_EXECUTION_ERROR', { stdout, stderr, exitCode });
    this.stdout = stdout;
    this.stderr = stderr;
    this.exitCode = exitCode;
  }
}

/**
 * 格式化错误
 * @original: Hw类 (L2912-2934)
 */
export class FormattedError extends BaseError {
  constructor(message, formattedMessage) {
    super(message, 'FORMATTED_ERROR');
    this.formattedMessage = formattedMessage;
  }
}

/**
 * 重试错误
 * @original: Xv类 (L22666-22676)
 */
export class RetryError extends BaseError {
  constructor(message, originalError, retryContext = {}) {
    super(message, 'RETRY_ERROR', { originalError: originalError.message, retryContext });
    this.originalError = originalError;
    this.retryContext = retryContext;
  }
}

/**
 * 模型回退错误
 * @original: qv1类 (L22677-22686)
 */
export class ModelFallbackError extends BaseError {
  constructor(originalModel, fallbackModel, reason) {
    super(`模型 ${originalModel} 不可用，回退到 ${fallbackModel}`, 'MODEL_FALLBACK_ERROR', {
      originalModel,
      fallbackModel,
      reason
    });
    this.originalModel = originalModel;
    this.fallbackModel = fallbackModel;
  }
}

/**
 * 令牌限制错误
 * @original: Db1类 (L25221-25230)
 */
export class TokenLimitError extends BaseError {
  constructor(tokenCount, maxTokens, context = '') {
    super(
      `令牌数量 (${tokenCount}) 超过最大限制 (${maxTokens})${context ? `: ${context}` : ''}`,
      'TOKEN_LIMIT_ERROR',
      { tokenCount, maxTokens, context }
    );
    this.tokenCount = tokenCount;
    this.maxTokens = maxTokens;
  }
}

/**
 * 文件大小错误
 */
export class FileSizeError extends BaseError {
  constructor(filePath, fileSize, maxSize) {
    super(
      `文件大小 (${fileSize} 字节) 超过最大限制 (${maxSize} 字节): ${filePath}`,
      'FILE_SIZE_ERROR',
      { filePath, fileSize, maxSize }
    );
    this.filePath = filePath;
    this.fileSize = fileSize;
    this.maxSize = maxSize;
  }
}

/**
 * 权限错误
 */
export class PermissionError extends BaseError {
  constructor(operation, resource = null) {
    super(
      `权限不足，无法执行操作: ${operation}${resource ? ` (资源: ${resource})` : ''}`,
      'PERMISSION_ERROR',
      { operation, resource }
    );
    this.operation = operation;
    this.resource = resource;
  }
}

/**
 * 配置错误
 */
export class ConfigurationError extends BaseError {
  constructor(configKey, reason = null) {
    super(
      `配置错误: ${configKey}${reason ? ` - ${reason}` : ''}`,
      'CONFIGURATION_ERROR',
      { configKey, reason }
    );
    this.configKey = configKey;
  }
}

/**
 * 验证错误
 */
export class ValidationError extends BaseError {
  constructor(field, value, rule) {
    super(
      `验证失败: ${field} = "${value}" 不符合规则 "${rule}"`,
      'VALIDATION_ERROR',
      { field, value, rule }
    );
    this.field = field;
    this.value = value;
    this.rule = rule;
  }
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
  constructor() {
    this.errorCounts = new Map();
    this.errorHistory = [];
    this.maxHistorySize = 1000;
  }

  /**
   * 处理错误
   * @param {Error} error - 错误对象
   * @param {Object} context - 错误上下文
   * @returns {Object} 处理结果
   */
  handleError(error, context = {}) {
    try {
      // 记录错误
      this.recordError(error, context);

      // 根据错误类型进行特殊处理
      const handlingResult = this.processErrorByType(error, context);

      // 记录到日志
      this.logError(error, context, handlingResult);

      return {
        handled: true,
        error,
        context,
        result: handlingResult
      };

    } catch (handlingError) {
      logger.error('错误处理器本身出错', { 
        originalError: error.message,
        handlingError: handlingError.message 
      });

      return {
        handled: false,
        error,
        handlingError
      };
    }
  }

  /**
   * 记录错误
   * @param {Error} error - 错误对象
   * @param {Object} context - 错误上下文
   */
  recordError(error, context) {
    const errorKey = `${error.name}:${error.message}`;
    const count = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, count + 1);

    // 添加到历史记录
    this.errorHistory.push({
      error: error.toJSON ? error.toJSON() : {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      context,
      timestamp: new Date().toISOString(),
      count: count + 1
    });

    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory.shift();
    }
  }

  /**
   * 根据错误类型处理
   * @param {Error} error - 错误对象
   * @param {Object} context - 错误上下文
   * @returns {Object} 处理结果
   */
  processErrorByType(error, context) {
    switch (error.constructor) {
      case NetworkError:
        return this.handleNetworkError(error, context);
      
      case CommandExecutionError:
        return this.handleCommandError(error, context);
      
      case TokenLimitError:
        return this.handleTokenLimitError(error, context);
      
      case PermissionError:
        return this.handlePermissionError(error, context);
      
      case ValidationError:
        return this.handleValidationError(error, context);
      
      default:
        return this.handleGenericError(error, context);
    }
  }

  /**
   * 处理网络错误
   * @param {NetworkError} error - 网络错误
   * @param {Object} context - 错误上下文
   * @returns {Object} 处理结果
   */
  handleNetworkError(error, context) {
    return {
      type: 'network',
      recoverable: true,
      suggestion: '请检查网络连接并重试'
    };
  }

  /**
   * 处理命令执行错误
   * @param {CommandExecutionError} error - 命令执行错误
   * @param {Object} context - 错误上下文
   * @returns {Object} 处理结果
   */
  handleCommandError(error, context) {
    return {
      type: 'command',
      recoverable: error.exitCode !== 127, // 127表示命令未找到
      suggestion: error.exitCode === 127 ? '请检查命令是否已安装' : '请检查命令参数和权限'
    };
  }

  /**
   * 处理令牌限制错误
   * @param {TokenLimitError} error - 令牌限制错误
   * @param {Object} context - 错误上下文
   * @returns {Object} 处理结果
   */
  handleTokenLimitError(error, context) {
    return {
      type: 'token_limit',
      recoverable: true,
      suggestion: '请减少输入内容或使用分页参数'
    };
  }

  /**
   * 处理权限错误
   * @param {PermissionError} error - 权限错误
   * @param {Object} context - 错误上下文
   * @returns {Object} 处理结果
   */
  handlePermissionError(error, context) {
    return {
      type: 'permission',
      recoverable: false,
      suggestion: '请检查文件权限或联系管理员'
    };
  }

  /**
   * 处理验证错误
   * @param {ValidationError} error - 验证错误
   * @param {Object} context - 错误上下文
   * @returns {Object} 处理结果
   */
  handleValidationError(error, context) {
    return {
      type: 'validation',
      recoverable: true,
      suggestion: `请检查 ${error.field} 字段的值是否符合要求`
    };
  }

  /**
   * 处理通用错误
   * @param {Error} error - 通用错误
   * @param {Object} context - 错误上下文
   * @returns {Object} 处理结果
   */
  handleGenericError(error, context) {
    return {
      type: 'generic',
      recoverable: false,
      suggestion: '请查看详细错误信息并联系技术支持'
    };
  }

  /**
   * 记录错误到日志
   * @param {Error} error - 错误对象
   * @param {Object} context - 错误上下文
   * @param {Object} handlingResult - 处理结果
   */
  logError(error, context, handlingResult) {
    const logLevel = handlingResult.recoverable ? 'warn' : 'error';
    
    logger[logLevel]('错误处理完成', {
      errorName: error.name,
      errorMessage: error.message,
      errorCode: error.code,
      context,
      handlingResult
    });
  }

  /**
   * 获取错误统计
   * @returns {Object} 错误统计信息
   */
  getErrorStatistics() {
    const totalErrors = this.errorHistory.length;
    const errorsByType = new Map();
    const recentErrors = this.errorHistory.slice(-10);

    for (const record of this.errorHistory) {
      const errorType = record.error.name;
      const count = errorsByType.get(errorType) || 0;
      errorsByType.set(errorType, count + 1);
    }

    return {
      totalErrors,
      errorsByType: Object.fromEntries(errorsByType),
      recentErrors,
      mostCommonErrors: Array.from(this.errorCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
    };
  }

  /**
   * 清除错误历史
   */
  clearErrorHistory() {
    this.errorHistory = [];
    this.errorCounts.clear();
    logger.info('错误历史已清除');
  }
}

/**
 * 错误恢复器类
 */
export class ErrorRecovery {
  constructor() {
    this.recoveryStrategies = new Map();
    this.setupDefaultStrategies();
  }

  /**
   * 设置默认恢复策略
   */
  setupDefaultStrategies() {
    // 网络错误恢复策略
    this.recoveryStrategies.set('NetworkError', async (error, context) => {
      logger.info('尝试网络错误恢复', { url: error.url });
      
      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return { recovered: true, action: 'retry' };
    });

    // 命令执行错误恢复策略
    this.recoveryStrategies.set('CommandExecutionError', async (error, context) => {
      if (error.exitCode === 127) {
        return { 
          recovered: false, 
          action: 'install_dependency',
          suggestion: '请安装所需的命令行工具'
        };
      }

      return { recovered: false, action: 'manual_intervention' };
    });
  }

  /**
   * 尝试错误恢复
   * @param {Error} error - 错误对象
   * @param {Object} context - 错误上下文
   * @returns {Promise<Object>} 恢复结果
   */
  async attemptRecovery(error, context = {}) {
    const strategy = this.recoveryStrategies.get(error.constructor.name);
    
    if (!strategy) {
      return {
        recovered: false,
        action: 'no_strategy',
        message: '没有可用的恢复策略'
      };
    }

    try {
      const result = await strategy(error, context);
      
      logger.info('错误恢复尝试完成', {
        errorType: error.constructor.name,
        recovered: result.recovered,
        action: result.action
      });

      return result;

    } catch (recoveryError) {
      logger.error('错误恢复失败', {
        originalError: error.message,
        recoveryError: recoveryError.message
      });

      return {
        recovered: false,
        action: 'recovery_failed',
        error: recoveryError.message
      };
    }
  }

  /**
   * 注册恢复策略
   * @param {string} errorType - 错误类型
   * @param {Function} strategy - 恢复策略函数
   */
  registerRecoveryStrategy(errorType, strategy) {
    this.recoveryStrategies.set(errorType, strategy);
    logger.debug('恢复策略已注册', { errorType });
  }

  /**
   * 移除恢复策略
   * @param {string} errorType - 错误类型
   */
  removeRecoveryStrategy(errorType) {
    this.recoveryStrategies.delete(errorType);
    logger.debug('恢复策略已移除', { errorType });
  }
}

/**
 * 全局错误处理器实例
 */
export const globalErrorHandler = new ErrorHandler();

/**
 * 全局错误恢复器实例
 */
export const globalErrorRecovery = new ErrorRecovery();

/**
 * 便捷函数：处理错误
 * @param {Error} error - 错误对象
 * @param {Object} context - 错误上下文
 * @returns {Object} 处理结果
 */
export function handleError(error, context = {}) {
  return globalErrorHandler.handleError(error, context);
}

/**
 * 便捷函数：尝试错误恢复
 * @param {Error} error - 错误对象
 * @param {Object} context - 错误上下文
 * @returns {Promise<Object>} 恢复结果
 */
export async function attemptErrorRecovery(error, context = {}) {
  return globalErrorRecovery.attemptRecovery(error, context);
}

/**
 * 便捷函数：创建格式化错误
 * @param {string} message - 错误消息
 * @param {string} formattedMessage - 格式化消息
 * @returns {FormattedError} 格式化错误实例
 */
export function createFormattedError(message, formattedMessage) {
  return new FormattedError(message, formattedMessage);
}

/**
 * 便捷函数：创建命令执行错误
 * @param {string} message - 错误消息
 * @param {string} stdout - 标准输出
 * @param {string} stderr - 标准错误
 * @param {number} exitCode - 退出代码
 * @returns {CommandExecutionError} 命令执行错误实例
 */
export function createCommandExecutionError(message, stdout = '', stderr = '', exitCode = null) {
  return new CommandExecutionError(message, stdout, stderr, exitCode);
}

/**
 * 便捷函数：创建令牌限制错误
 * @param {number} tokenCount - 当前令牌数
 * @param {number} maxTokens - 最大令牌数
 * @param {string} context - 错误上下文
 * @returns {TokenLimitError} 令牌限制错误实例
 */
export function createTokenLimitError(tokenCount, maxTokens, context = '') {
  return new TokenLimitError(tokenCount, maxTokens, context);
}

/**
 * 便捷函数：获取错误统计
 * @returns {Object} 错误统计信息
 */
export function getErrorStatistics() {
  return globalErrorHandler.getErrorStatistics();
}
