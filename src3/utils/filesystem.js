/**
 * 文件系统工具函数
 * 
 * 提供文件和目录操作、路径处理等功能
 * 
 * @original: 文件系统相关函数 (L3766-3898, L50597-50806)
 */

import { readFileSync, writeFileSync, existsSync, statSync, mkdirSync } from 'fs';
import { join, resolve, relative, dirname, basename, extname, isAbsolute } from 'path';
import { homedir } from 'os';
import { logger } from './logger.js';

/**
 * 安全读取文件
 * @original: fO()函数 (L3767)
 * @param {string} filePath - 文件路径
 * @param {string} encoding - 编码格式
 * @returns {Object} 读取结果
 */
export function safeReadFile(filePath, encoding = 'utf8') {
  try {
    if (!existsSync(filePath)) {
      return {
        success: false,
        error: `File not found: ${filePath}`,
        content: null
      };
    }

    const stats = statSync(filePath);
    if (!stats.isFile()) {
      return {
        success: false,
        error: `Path is not a file: ${filePath}`,
        content: null
      };
    }

    const content = readFileSync(filePath, encoding);
    
    logger.debug('文件读取成功', { filePath, size: content.length });
    
    return {
      success: true,
      error: null,
      content,
      stats: {
        size: stats.size,
        mtime: stats.mtime,
        ctime: stats.ctime
      }
    };

  } catch (error) {
    logger.error('文件读取失败', { filePath, error: error.message });
    
    return {
      success: false,
      error: error.message,
      content: null
    };
  }
}

/**
 * 安全写入文件
 * @param {string} filePath - 文件路径
 * @param {string} content - 文件内容
 * @param {string} encoding - 编码格式
 * @returns {Object} 写入结果
 */
export function safeWriteFile(filePath, content, encoding = 'utf8') {
  try {
    // 确保目录存在
    const dir = dirname(filePath);
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }

    writeFileSync(filePath, content, encoding);
    
    logger.debug('文件写入成功', { filePath, size: content.length });
    
    return {
      success: true,
      error: null
    };

  } catch (error) {
    logger.error('文件写入失败', { filePath, error: error.message });
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 获取文件统计信息
 * @original: O5Q()函数 (L3785)
 * @param {string} filePath - 文件路径
 * @returns {Object} 文件统计信息
 */
export function getFileStats(filePath) {
  try {
    const stats = statSync(filePath);
    const content = readFileSync(filePath, 'utf8');
    
    // 计算行数
    const lines = content.split('\n');
    const nonEmptyLines = lines.filter(line => line.trim().length > 0);
    
    return {
      success: true,
      stats: {
        size: stats.size,
        lines: lines.length,
        nonEmptyLines: nonEmptyLines.length,
        mtime: stats.mtime,
        ctime: stats.ctime,
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory()
      }
    };

  } catch (error) {
    return {
      success: false,
      error: error.message,
      stats: null
    };
  }
}

/**
 * 检查文件是否存在且可读
 * @original: Vh()函数 (L3792)
 * @param {string} filePath - 文件路径
 * @returns {Object} 检查结果
 */
export function checkFileAccess(filePath) {
  try {
    if (!filePath) {
      return {
        exists: false,
        readable: false,
        isFile: false,
        isDirectory: false,
        error: 'No file path provided'
      };
    }

    const absolutePath = isAbsolute(filePath) ? filePath : resolve(process.cwd(), filePath);
    
    if (!existsSync(absolutePath)) {
      return {
        exists: false,
        readable: false,
        isFile: false,
        isDirectory: false,
        path: absolutePath
      };
    }

    const stats = statSync(absolutePath);
    
    return {
      exists: true,
      readable: true,
      isFile: stats.isFile(),
      isDirectory: stats.isDirectory(),
      path: absolutePath,
      size: stats.size,
      mtime: stats.mtime
    };

  } catch (error) {
    return {
      exists: false,
      readable: false,
      isFile: false,
      isDirectory: false,
      error: error.message
    };
  }
}

/**
 * 处理Tab字符转换为空格
 * @original: vi()函数 (L3807)
 * @param {string} content - 文件内容
 * @returns {string} 转换后的内容
 */
export function convertTabsToSpaces(content) {
  if (!content) return content;
  
  return content.replace(/^\t+/gm, match => '  '.repeat(match.length));
}

/**
 * 解析文件路径
 * @original: Ch()函数 (L3810)
 * @param {string} filePath - 文件路径
 * @returns {string} 解析后的绝对路径
 */
export function resolvePath(filePath) {
  if (!filePath) return undefined;
  
  if (isAbsolute(filePath)) {
    return filePath;
  }
  
  return resolve(process.cwd(), filePath);
}

/**
 * 获取相对路径信息
 * @original: qr1()函数 (L3813)
 * @param {string} filePath - 文件路径
 * @returns {Object} 路径信息
 */
export function getPathInfo(filePath) {
  const absolutePath = resolvePath(filePath);
  const relativePath = absolutePath ? relative(process.cwd(), absolutePath) : undefined;
  
  return {
    absolutePath,
    relativePath,
    isAbsolute: absolutePath ? isAbsolute(filePath) : false,
    exists: absolutePath ? existsSync(absolutePath) : false
  };
}

/**
 * 格式化文件内容显示
 * @original: fy()函数 (L3844)
 * @param {Object} fileInfo - 文件信息
 * @returns {string} 格式化后的内容
 */
export function formatFileContent({ content, startLine = 1 }) {
  if (!content) return '';
  
  const lines = content.split('\n');
  const formattedLines = lines.map((line, index) => {
    const lineNumber = startLine + index;
    return `${lineNumber.toString().padStart(4, ' ')}: ${line}`;
  });
  
  return formattedLines.join('\n');
}

/**
 * 检查文件是否为空
 * @original: cIA()函数 (L3857)
 * @param {string} filePath - 文件路径
 * @returns {boolean} 是否为空
 */
export function isFileEmpty(filePath) {
  try {
    if (!existsSync(filePath)) return true;
    
    const stats = statSync(filePath);
    return stats.size === 0;
    
  } catch (error) {
    logger.error('检查文件是否为空失败', { filePath, error: error.message });
    return true;
  }
}

/**
 * 格式化文件大小
 * @original: wY()函数 (L3878)
 * @param {number} bytes - 字节数
 * @returns {string} 格式化的文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 bytes';
  
  const units = ['bytes', 'KB', 'MB', 'GB', 'TB'];
  const k = 1024;
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${(bytes / Math.pow(k, i)).toFixed(1)} ${units[i]}`;
}

/**
 * 获取文件扩展名
 * @original: MY()函数 (L3886)
 * @param {string} filePath - 文件路径
 * @returns {string} 文件扩展名
 */
export function getFileExtension(filePath) {
  if (!filePath) return 'unknown';
  
  const ext = extname(filePath);
  return ext ? ext.slice(1).toLowerCase() : 'unknown';
}

/**
 * 解析路径（支持~符号）
 * @original: SK1()函数 (L3891)
 * @param {string} inputPath - 输入路径
 * @param {string} basePath - 基础路径
 * @returns {string} 解析后的路径
 */
export function expandPath(inputPath, basePath = process.cwd()) {
  if (!inputPath) return basePath;
  
  if (inputPath.startsWith('~/')) {
    return join(homedir(), inputPath.substring(2));
  }
  
  if (isAbsolute(inputPath)) {
    return inputPath;
  }
  
  const relativePath = inputPath.startsWith('./') ? inputPath : `./${inputPath}`;
  return resolve(basePath, relativePath);
}

/**
 * 创建目录（递归）
 * @param {string} dirPath - 目录路径
 * @returns {Object} 创建结果
 */
export function ensureDirectory(dirPath) {
  try {
    if (!existsSync(dirPath)) {
      mkdirSync(dirPath, { recursive: true });
      logger.debug('目录创建成功', { dirPath });
    }
    
    return {
      success: true,
      error: null
    };
    
  } catch (error) {
    logger.error('目录创建失败', { dirPath, error: error.message });
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 获取文件名（不含扩展名）
 * @param {string} filePath - 文件路径
 * @returns {string} 文件名
 */
export function getFileName(filePath) {
  if (!filePath) return '';
  
  const base = basename(filePath);
  const ext = extname(base);
  
  return ext ? base.slice(0, -ext.length) : base;
}

/**
 * 获取目录名
 * @param {string} filePath - 文件路径
 * @returns {string} 目录名
 */
export function getDirectoryName(filePath) {
  if (!filePath) return '';
  return dirname(filePath);
}

/**
 * 检查路径是否在指定目录内
 * @param {string} filePath - 文件路径
 * @param {string} dirPath - 目录路径
 * @returns {boolean} 是否在目录内
 */
export function isPathInDirectory(filePath, dirPath) {
  if (!filePath || !dirPath) return false;
  
  const absoluteFilePath = resolve(filePath);
  const absoluteDirPath = resolve(dirPath);
  
  const relativePath = relative(absoluteDirPath, absoluteFilePath);
  
  return !relativePath.startsWith('..') && !isAbsolute(relativePath);
}

/**
 * 获取文件的MIME类型
 * @param {string} filePath - 文件路径
 * @returns {string} MIME类型
 */
export function getMimeType(filePath) {
  const ext = getFileExtension(filePath);
  
  const mimeTypes = {
    'txt': 'text/plain',
    'html': 'text/html',
    'css': 'text/css',
    'js': 'application/javascript',
    'json': 'application/json',
    'xml': 'application/xml',
    'pdf': 'application/pdf',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'svg': 'image/svg+xml',
    'mp4': 'video/mp4',
    'mp3': 'audio/mpeg',
    'zip': 'application/zip',
    'tar': 'application/x-tar',
    'gz': 'application/gzip'
  };
  
  return mimeTypes[ext] || 'application/octet-stream';
}

/**
 * 批量检查文件存在性
 * @param {string[]} filePaths - 文件路径数组
 * @returns {Object} 检查结果
 */
export function checkMultipleFiles(filePaths) {
  if (!Array.isArray(filePaths)) return { existing: [], missing: [] };
  
  const existing = [];
  const missing = [];
  
  for (const filePath of filePaths) {
    if (existsSync(filePath)) {
      existing.push(filePath);
    } else {
      missing.push(filePath);
    }
  }
  
  return { existing, missing };
}
