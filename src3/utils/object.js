/**
 * 对象处理工具函数
 * 
 * 提供各种对象操作、合并、转换等功能
 * 
 * @original: Lodash对象处理函数 (L1814-1943)
 */

import { logger } from './logger.js';

/**
 * 深度克隆对象
 * @param {*} value - 要克隆的值
 * @returns {*} 克隆后的值
 */
export function cloneDeep(value) {
  if (value === null || typeof value !== 'object') {
    return value;
  }
  
  if (value instanceof Date) {
    return new Date(value.getTime());
  }
  
  if (value instanceof Array) {
    return value.map(item => cloneDeep(item));
  }
  
  if (value instanceof RegExp) {
    return new RegExp(value);
  }
  
  if (typeof value === 'object') {
    const cloned = {};
    for (const key in value) {
      if (value.hasOwnProperty(key)) {
        cloned[key] = cloneDeep(value[key]);
      }
    }
    return cloned;
  }
  
  return value;
}

/**
 * 浅克隆对象
 * @param {*} value - 要克隆的值
 * @returns {*} 克隆后的值
 */
export function clone(value) {
  if (value === null || typeof value !== 'object') {
    return value;
  }
  
  if (value instanceof Array) {
    return value.slice();
  }
  
  if (value instanceof Date) {
    return new Date(value.getTime());
  }
  
  if (typeof value === 'object') {
    return { ...value };
  }
  
  return value;
}

/**
 * 合并对象
 * @param {Object} target - 目标对象
 * @param {...Object} sources - 源对象
 * @returns {Object} 合并后的对象
 */
export function merge(target, ...sources) {
  if (!target || typeof target !== 'object') {
    target = {};
  }
  
  for (const source of sources) {
    if (!source || typeof source !== 'object') continue;
    
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        const sourceValue = source[key];
        const targetValue = target[key];
        
        if (isPlainObject(sourceValue) && isPlainObject(targetValue)) {
          target[key] = merge(targetValue, sourceValue);
        } else {
          target[key] = sourceValue;
        }
      }
    }
  }
  
  return target;
}

/**
 * 深度合并对象（不修改原对象）
 * @param {...Object} objects - 要合并的对象
 * @returns {Object} 合并后的新对象
 */
export function mergeDeep(...objects) {
  const result = {};
  
  for (const obj of objects) {
    if (obj && typeof obj === 'object') {
      merge(result, obj);
    }
  }
  
  return result;
}

/**
 * 检查是否为普通对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为普通对象
 */
function isPlainObject(value) {
  if (!value || typeof value !== 'object') {
    return false;
  }
  
  if (Object.prototype.toString.call(value) !== '[object Object]') {
    return false;
  }
  
  if (Object.getPrototypeOf(value) === null) {
    return true;
  }
  
  let proto = value;
  while (Object.getPrototypeOf(proto) !== null) {
    proto = Object.getPrototypeOf(proto);
  }
  
  return Object.getPrototypeOf(value) === proto;
}

/**
 * 获取对象的所有键
 * @param {Object} obj - 输入对象
 * @returns {string[]} 键数组
 */
export function keys(obj) {
  if (!obj || typeof obj !== 'object') return [];
  return Object.keys(obj);
}

/**
 * 获取对象的所有值
 * @param {Object} obj - 输入对象
 * @returns {Array} 值数组
 */
export function values(obj) {
  if (!obj || typeof obj !== 'object') return [];
  return Object.values(obj);
}

/**
 * 获取对象的键值对数组
 * @param {Object} obj - 输入对象
 * @returns {Array} 键值对数组
 */
export function entries(obj) {
  if (!obj || typeof obj !== 'object') return [];
  return Object.entries(obj);
}

/**
 * 从键值对数组创建对象
 * @param {Array} pairs - 键值对数组
 * @returns {Object} 创建的对象
 */
export function fromPairs(pairs) {
  if (!Array.isArray(pairs)) return {};
  
  const result = {};
  for (const pair of pairs) {
    if (Array.isArray(pair) && pair.length >= 2) {
      result[pair[0]] = pair[1];
    }
  }
  
  return result;
}

/**
 * 选择对象的指定属性
 * @original: JoB()函数 (L1881)
 * @param {Object} obj - 输入对象
 * @param {string[]} props - 要选择的属性名数组
 * @returns {Object} 包含选定属性的新对象
 */
export function pick(obj, props) {
  if (!obj || typeof obj !== 'object') return {};
  if (!Array.isArray(props)) return {};
  
  const result = {};
  for (const prop of props) {
    if (prop in obj) {
      result[prop] = obj[prop];
    }
  }
  
  return result;
}

/**
 * 排除对象的指定属性
 * @param {Object} obj - 输入对象
 * @param {string[]} props - 要排除的属性名数组
 * @returns {Object} 排除指定属性后的新对象
 */
export function omit(obj, props) {
  if (!obj || typeof obj !== 'object') return {};
  if (!Array.isArray(props)) return { ...obj };
  
  const result = {};
  const excludeSet = new Set(props);
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key) && !excludeSet.has(key)) {
      result[key] = obj[key];
    }
  }
  
  return result;
}

/**
 * 根据路径获取对象属性值
 * @param {Object} obj - 输入对象
 * @param {string|Array} path - 属性路径
 * @param {*} defaultValue - 默认值
 * @returns {*} 属性值
 */
export function get(obj, path, defaultValue) {
  if (!obj || typeof obj !== 'object') return defaultValue;
  
  const keys = Array.isArray(path) ? path : path.split('.');
  let result = obj;
  
  for (const key of keys) {
    if (result == null || typeof result !== 'object') {
      return defaultValue;
    }
    result = result[key];
  }
  
  return result === undefined ? defaultValue : result;
}

/**
 * 根据路径设置对象属性值
 * @param {Object} obj - 输入对象
 * @param {string|Array} path - 属性路径
 * @param {*} value - 要设置的值
 * @returns {Object} 对象本身
 */
export function set(obj, path, value) {
  if (!obj || typeof obj !== 'object') return obj;
  
  const keys = Array.isArray(path) ? path : path.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {};
    }
    
    current = current[key];
  }
  
  current[keys[keys.length - 1]] = value;
  return obj;
}

/**
 * 检查对象是否有指定路径的属性
 * @param {Object} obj - 输入对象
 * @param {string|Array} path - 属性路径
 * @returns {boolean} 是否有该属性
 */
export function has(obj, path) {
  if (!obj || typeof obj !== 'object') return false;
  
  const keys = Array.isArray(path) ? path : path.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current == null || typeof current !== 'object' || !(key in current)) {
      return false;
    }
    current = current[key];
  }
  
  return true;
}

/**
 * 删除对象的指定路径属性
 * @param {Object} obj - 输入对象
 * @param {string|Array} path - 属性路径
 * @returns {boolean} 是否成功删除
 */
export function unset(obj, path) {
  if (!obj || typeof obj !== 'object') return false;
  
  const keys = Array.isArray(path) ? path : path.split('.');
  if (keys.length === 0) return false;
  
  if (keys.length === 1) {
    if (keys[0] in obj) {
      delete obj[keys[0]];
      return true;
    }
    return false;
  }
  
  let current = obj;
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current) || typeof current[key] !== 'object') {
      return false;
    }
    current = current[key];
  }
  
  const lastKey = keys[keys.length - 1];
  if (lastKey in current) {
    delete current[lastKey];
    return true;
  }
  
  return false;
}

/**
 * 扁平化对象
 * @param {Object} obj - 输入对象
 * @param {string} separator - 分隔符
 * @returns {Object} 扁平化后的对象
 */
export function flatten(obj, separator = '.') {
  const result = {};
  
  function flattenRecursive(current, prefix = '') {
    for (const key in current) {
      if (current.hasOwnProperty(key)) {
        const newKey = prefix ? `${prefix}${separator}${key}` : key;
        const value = current[key];
        
        if (isPlainObject(value)) {
          flattenRecursive(value, newKey);
        } else {
          result[newKey] = value;
        }
      }
    }
  }
  
  flattenRecursive(obj);
  return result;
}

/**
 * 反扁平化对象
 * @param {Object} obj - 扁平化的对象
 * @param {string} separator - 分隔符
 * @returns {Object} 反扁平化后的对象
 */
export function unflatten(obj, separator = '.') {
  const result = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      set(result, key.split(separator), obj[key]);
    }
  }
  
  return result;
}

/**
 * 检查对象是否为空
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为空
 */
export function isEmpty(value) {
  if (value == null) return true;
  
  if (Array.isArray(value) || typeof value === 'string') {
    return value.length === 0;
  }
  
  if (typeof value === 'object') {
    return Object.keys(value).length === 0;
  }
  
  return false;
}

/**
 * 转换对象的值
 * @param {Object} obj - 输入对象
 * @param {Function} iteratee - 转换函数
 * @returns {Object} 转换后的对象
 */
export function mapValues(obj, iteratee) {
  if (!obj || typeof obj !== 'object') return {};
  
  const result = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      result[key] = iteratee(obj[key], key, obj);
    }
  }
  
  return result;
}

/**
 * 转换对象的键
 * @param {Object} obj - 输入对象
 * @param {Function} iteratee - 转换函数
 * @returns {Object} 转换后的对象
 */
export function mapKeys(obj, iteratee) {
  if (!obj || typeof obj !== 'object') return {};
  
  const result = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = iteratee(obj[key], key, obj);
      result[newKey] = obj[key];
    }
  }
  
  return result;
}
