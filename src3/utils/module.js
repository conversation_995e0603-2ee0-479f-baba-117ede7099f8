/**
 * 模块管理工具函数
 * 
 * 提供模块导出、属性定义、懒加载等核心工具函数
 * 
 * @original: 行142-154的核心工具函数
 */

import { createRequire } from 'node:module';

/**
 * 创建模块导出工具函数
 * 用于创建CommonJS风格的模块导出
 * 
 * @original: var E = (A, B) => () => ... (L142)
 * @param {Function} moduleFactory - 模块工厂函数
 * @param {Object} exportsCache - 导出缓存对象
 * @returns {Function} 模块导出函数
 */
export function createModuleExports(moduleFactory, exportsCache) {
  return () => {
    if (exportsCache || moduleFactory) {
      const exports = { exports: {} };
      moduleFactory(exports.exports, exports);
      exportsCache = exports.exports;
    }
    return exportsCache;
  };
}

/**
 * 定义对象属性工具函数
 * 批量定义对象的getter/setter属性
 * 
 * @original: var Mj = (A, B) => ... (L145)
 * @param {Object} target - 目标对象
 * @param {Object} properties - 属性定义对象
 */
export function defineProperties(target, properties) {
  for (const key in properties) {
    Object.defineProperty(target, key, {
      get: properties[key],
      enumerable: true,
      configurable: true,
      set: value => properties[key] = () => value
    });
  }
}

/**
 * 创建懒加载函数
 * 创建一个只在首次调用时执行的函数
 * 
 * @original: var gA1 = (A, B) => () => ... (L153)
 * @param {Function} initializer - 初始化函数
 * @param {*} cachedResult - 缓存的结果
 * @returns {Function} 懒加载函数
 */
export function createLazyLoader(initializer, cachedResult) {
  return () => {
    if (initializer) {
      cachedResult = initializer();
      initializer = null; // 清除初始化函数，避免重复执行
    }
    return cachedResult;
  };
}

/**
 * 创建模块require实例
 * 基于当前模块URL创建require函数
 * 
 * @original: var J1 = xcB(import.meta.url) (L154)
 * @param {string} moduleUrl - 模块URL，通常是import.meta.url
 * @returns {Function} require函数
 */
export function createModuleRequire(moduleUrl) {
  return createRequire(moduleUrl);
}

/**
 * 模块导出管理器类
 * 提供更高级的模块导出管理功能
 */
export class ModuleExportManager {
  constructor() {
    this.exports = {};
    this.lazyLoaders = new Map();
  }

  /**
   * 注册懒加载模块
   * @param {string} name - 模块名称
   * @param {Function} loader - 加载函数
   */
  registerLazyModule(name, loader) {
    this.lazyLoaders.set(name, createLazyLoader(loader));
  }

  /**
   * 获取模块
   * @param {string} name - 模块名称
   * @returns {*} 模块导出
   */
  getModule(name) {
    if (this.lazyLoaders.has(name)) {
      const loader = this.lazyLoaders.get(name);
      return loader();
    }
    return this.exports[name];
  }

  /**
   * 设置模块导出
   * @param {string} name - 模块名称
   * @param {*} value - 导出值
   */
  setExport(name, value) {
    this.exports[name] = value;
  }

  /**
   * 批量设置导出
   * @param {Object} exports - 导出对象
   */
  setExports(exports) {
    Object.assign(this.exports, exports);
  }

  /**
   * 获取所有导出
   * @returns {Object} 所有导出
   */
  getAllExports() {
    const result = { ...this.exports };
    
    // 加载所有懒加载模块
    for (const [name, loader] of this.lazyLoaders) {
      result[name] = loader();
    }
    
    return result;
  }
}

/**
 * 全局模块导出管理器实例
 */
export const globalModuleManager = new ModuleExportManager();

/**
 * 工具函数：安全地获取嵌套属性
 * @param {Object} obj - 目标对象
 * @param {string} path - 属性路径，如 'a.b.c'
 * @param {*} defaultValue - 默认值
 * @returns {*} 属性值或默认值
 */
export function getNestedProperty(obj, path, defaultValue = undefined) {
  if (!obj || typeof obj !== 'object') return defaultValue;
  
  const keys = path.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current == null || typeof current !== 'object' || !(key in current)) {
      return defaultValue;
    }
    current = current[key];
  }
  
  return current;
}

/**
 * 工具函数：安全地设置嵌套属性
 * @param {Object} obj - 目标对象
 * @param {string} path - 属性路径，如 'a.b.c'
 * @param {*} value - 要设置的值
 * @returns {Object} 目标对象
 */
export function setNestedProperty(obj, path, value) {
  if (!obj || typeof obj !== 'object') {
    throw new Error('目标必须是对象');
  }
  
  const keys = path.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[keys[keys.length - 1]] = value;
  return obj;
}

/**
 * 创建代理对象，支持懒加载属性
 * @param {Object} target - 目标对象
 * @param {Object} lazyProperties - 懒加载属性定义
 * @returns {Proxy} 代理对象
 */
export function createLazyProxy(target, lazyProperties) {
  return new Proxy(target, {
    get(obj, prop) {
      if (prop in lazyProperties && typeof lazyProperties[prop] === 'function') {
        // 懒加载属性
        const value = lazyProperties[prop]();
        obj[prop] = value; // 缓存结果
        delete lazyProperties[prop]; // 删除加载器
        return value;
      }
      return obj[prop];
    }
  });
}
