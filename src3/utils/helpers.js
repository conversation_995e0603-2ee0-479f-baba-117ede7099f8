/**
 * 通用工具函数库
 * 
 * 提供各种通用的工具函数，包括字符串处理、数组操作、对象处理等
 * 
 * @original: 工具函数相关代码 (L1299-1400, L1615-1700, L2022-2200)
 */

import { logger } from './logger.js';
import { randomUUID } from 'crypto';
import { join, resolve } from 'path';
import { homedir } from 'os';

/**
 * 生成随机文件名
 * @original: rmB()函数 (L1299-1302)
 * @param {string} prefix - 文件名前缀
 * @param {string} extension - 文件扩展名
 * @returns {string} 生成的文件名
 */
export function generateRandomFileName(prefix = 'claude-prompt', extension = '.md') {
  const uuid = randomUUID();
  return join(homedir(), `${prefix}-${uuid}${extension}`);
}

/**
 * 去除字符串末尾空白字符
 * @original: BlB()函数 (L1315-1318)
 * @param {string} str - 输入字符串
 * @returns {number} 最后一个非空白字符的索引
 */
export function trimEndIndex(str) {
  let index = str.length;
  const whitespaceRegex = /\s/;
  
  while (index-- && whitespaceRegex.test(str.charAt(index)));
  
  return index;
}

/**
 * 去除字符串首尾空白字符
 * @original: DlB()函数 (L1322-1324)
 * @param {string} str - 输入字符串
 * @returns {string} 处理后的字符串
 */
export function trimString(str) {
  if (!str) return str;
  
  const startWhitespaceRegex = /^\s+/;
  return str.slice(0, trimEndIndex(str) + 1).replace(startWhitespaceRegex, '');
}

/**
 * 转换为数字
 * @original: WlB()函数 (L1331-1340)
 * @param {*} value - 要转换的值
 * @returns {number} 转换后的数字
 */
export function toNumber(value) {
  if (typeof value === 'number') return value;
  
  if (isSymbol(value)) return NaN;
  
  if (isObject(value)) {
    const other = typeof value.valueOf === 'function' ? value.valueOf() : value;
    value = isObject(other) ? `${other}` : other;
  }
  
  if (typeof value !== 'string') return value === 0 ? value : +value;
  
  value = trimString(value);
  const isBinary = /^0b[01]+$/i.test(value);
  
  return isBinary ? parseInt(value.slice(2), 2) : +value;
}

/**
 * 转换为有限数字
 * @original: XlB()函数 (L1346-1352)
 * @param {*} value - 要转换的值
 * @returns {number} 转换后的有限数字
 */
export function toFinite(value) {
  if (!value) return value === 0 ? value : 0;
  
  value = toNumber(value);
  
  if (value === Infinity || value === -Infinity) {
    const sign = value < 0 ? -1 : 1;
    return sign * Number.MAX_VALUE;
  }
  
  return value === value ? value : 0;
}

/**
 * 转换为整数
 * @original: VlB()函数 (L1355-1359)
 * @param {*} value - 要转换的值
 * @returns {number} 转换后的整数
 */
export function toInteger(value) {
  const result = toFinite(value);
  const remainder = result % 1;
  
  return result === result ? (remainder ? result - remainder : result) : 0;
}

/**
 * 检查值是否为NaN
 * @original: slB()函数 (L1370-1372)
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为NaN
 */
export function isNaN(value) {
  return value !== value;
}

/**
 * 在数组中查找值的索引
 * @original: rlB()函数 (L1374-1378)
 * @param {Array} array - 数组
 * @param {*} value - 要查找的值
 * @param {number} fromIndex - 开始索引
 * @returns {number} 值的索引，未找到返回-1
 */
export function indexOf(array, value, fromIndex = 0) {
  const length = array ? array.length : 0;
  let index = fromIndex - 1;
  
  while (++index < length) {
    if (array[index] === value) return index;
  }
  
  return -1;
}

/**
 * 在数组中查找值（支持NaN）
 * @original: olB()函数 (L1381-1383)
 * @param {Array} array - 数组
 * @param {*} value - 要查找的值
 * @param {number} fromIndex - 开始索引
 * @returns {number} 值的索引，未找到返回-1
 */
export function baseIndexOf(array, value, fromIndex = 0) {
  return value === value ? indexOf(array, value, fromIndex) : findIndex(array, isNaN, fromIndex);
}

/**
 * 检查数组是否包含某个值
 * @original: tlB()函数 (L1385-1388)
 * @param {Array} array - 数组
 * @param {*} value - 要查找的值
 * @returns {boolean} 是否包含该值
 */
export function includes(array, value) {
  const length = array ? array.length : 0;
  return !!length && baseIndexOf(array, value, 0) > -1;
}

/**
 * 查找数组中满足条件的元素索引
 * @original: alB()函数 (L1363-1367)
 * @param {Array} array - 数组
 * @param {Function} predicate - 判断函数
 * @param {number} fromIndex - 开始索引
 * @param {boolean} fromRight - 是否从右开始
 * @returns {number} 元素索引，未找到返回-1
 */
export function findIndex(array, predicate, fromIndex = 0, fromRight = false) {
  const length = array.length;
  let index = fromIndex + (fromRight ? 1 : -1);
  
  while (fromRight ? index-- : ++index < length) {
    if (predicate(array[index], index, array)) return index;
  }
  
  return -1;
}

/**
 * 检查值是否为对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为对象
 */
export function isObject(value) {
  const type = typeof value;
  return value != null && (type === 'object' || type === 'function');
}

/**
 * 检查值是否为Symbol
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为Symbol
 */
export function isSymbol(value) {
  return typeof value === 'symbol' || 
    (isObjectLike(value) && Object.prototype.toString.call(value) === '[object Symbol]');
}

/**
 * 检查值是否为类对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为类对象
 */
export function isObjectLike(value) {
  return value != null && typeof value === 'object';
}

/**
 * 检查值是否为数组
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为数组
 */
export function isArray(value) {
  return Array.isArray(value);
}

/**
 * 检查值是否为函数
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为函数
 */
export function isFunction(value) {
  return typeof value === 'function';
}

/**
 * 检查值是否为字符串
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为字符串
 */
export function isString(value) {
  return typeof value === 'string' || 
    (isObjectLike(value) && Object.prototype.toString.call(value) === '[object String]');
}

/**
 * 检查值是否为空
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为空
 */
export function isEmpty(value) {
  if (value == null) return true;
  
  if (isArray(value) || isString(value)) {
    return !value.length;
  }
  
  if (isObject(value)) {
    return !Object.keys(value).length;
  }
  
  return false;
}

/**
 * 获取数组的最后一个元素
 * @original: lrB()函数 (L1754-1757)
 * @param {Array} array - 数组
 * @returns {*} 最后一个元素
 */
export function last(array) {
  const length = array ? array.length : 0;
  return length ? array[length - 1] : undefined;
}

/**
 * 深度克隆对象
 * @param {*} value - 要克隆的值
 * @returns {*} 克隆后的值
 */
export function cloneDeep(value) {
  if (value === null || typeof value !== 'object') return value;
  
  if (value instanceof Date) return new Date(value.getTime());
  
  if (value instanceof Array) {
    return value.map(item => cloneDeep(item));
  }
  
  if (typeof value === 'object') {
    const cloned = {};
    for (const key in value) {
      if (value.hasOwnProperty(key)) {
        cloned[key] = cloneDeep(value[key]);
      }
    }
    return cloned;
  }
  
  return value;
}

/**
 * 合并对象
 * @param {Object} target - 目标对象
 * @param {...Object} sources - 源对象
 * @returns {Object} 合并后的对象
 */
export function merge(target, ...sources) {
  if (!isObject(target)) return target;
  
  for (const source of sources) {
    if (!isObject(source)) continue;
    
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        const sourceValue = source[key];
        const targetValue = target[key];
        
        if (isObject(sourceValue) && isObject(targetValue)) {
          target[key] = merge(targetValue, sourceValue);
        } else {
          target[key] = sourceValue;
        }
      }
    }
  }
  
  return target;
}

/**
 * 获取对象的键数组
 * @param {Object} object - 对象
 * @returns {Array} 键数组
 */
export function keys(object) {
  return isObject(object) ? Object.keys(object) : [];
}

/**
 * 获取对象的值数组
 * @param {Object} object - 对象
 * @returns {Array} 值数组
 */
export function values(object) {
  return isObject(object) ? Object.values(object) : [];
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {Object} options - 选项
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait = 0, options = {}) {
  let lastArgs;
  let lastThis;
  let maxWait;
  let result;
  let timerId;
  let lastCallTime;
  let lastInvokeTime = 0;
  let leading = false;
  let maxing = false;
  let trailing = true;

  if (!isFunction(func)) {
    throw new TypeError('Expected a function');
  }

  wait = toNumber(wait) || 0;
  
  if (isObject(options)) {
    leading = !!options.leading;
    maxing = 'maxWait' in options;
    maxWait = maxing ? Math.max(toNumber(options.maxWait) || 0, wait) : maxWait;
    trailing = 'trailing' in options ? !!options.trailing : trailing;
  }

  function invokeFunc(time) {
    const args = lastArgs;
    const thisArg = lastThis;

    lastArgs = lastThis = undefined;
    lastInvokeTime = time;
    result = func.apply(thisArg, args);
    return result;
  }

  function leadingEdge(time) {
    lastInvokeTime = time;
    timerId = setTimeout(timerExpired, wait);
    return leading ? invokeFunc(time) : result;
  }

  function remainingWait(time) {
    const timeSinceLastCall = time - lastCallTime;
    const timeSinceLastInvoke = time - lastInvokeTime;
    const timeWaiting = wait - timeSinceLastCall;

    return maxing
      ? Math.min(timeWaiting, maxWait - timeSinceLastInvoke)
      : timeWaiting;
  }

  function shouldInvoke(time) {
    const timeSinceLastCall = time - lastCallTime;
    const timeSinceLastInvoke = time - lastInvokeTime;

    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||
      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));
  }

  function timerExpired() {
    const time = Date.now();
    if (shouldInvoke(time)) {
      return trailingEdge(time);
    }
    timerId = setTimeout(timerExpired, remainingWait(time));
  }

  function trailingEdge(time) {
    timerId = undefined;

    if (trailing && lastArgs) {
      return invokeFunc(time);
    }
    lastArgs = lastThis = undefined;
    return result;
  }

  function cancel() {
    if (timerId !== undefined) {
      clearTimeout(timerId);
    }
    lastInvokeTime = 0;
    lastArgs = lastCallTime = lastThis = timerId = undefined;
  }

  function flush() {
    return timerId === undefined ? result : trailingEdge(Date.now());
  }

  function debounced(...args) {
    const time = Date.now();
    const isInvoking = shouldInvoke(time);

    lastArgs = args;
    lastThis = this;
    lastCallTime = time;

    if (isInvoking) {
      if (timerId === undefined) {
        return leadingEdge(lastCallTime);
      }
      if (maxing) {
        clearTimeout(timerId);
        timerId = setTimeout(timerExpired, wait);
        return invokeFunc(lastCallTime);
      }
    }
    if (timerId === undefined) {
      timerId = setTimeout(timerExpired, wait);
    }
    return result;
  }

  debounced.cancel = cancel;
  debounced.flush = flush;
  return debounced;
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {Object} options - 选项
 * @returns {Function} 节流后的函数
 */
export function throttle(func, wait = 0, options = {}) {
  let leading = true;
  let trailing = true;

  if (!isFunction(func)) {
    throw new TypeError('Expected a function');
  }

  if (isObject(options)) {
    leading = 'leading' in options ? !!options.leading : leading;
    trailing = 'trailing' in options ? !!options.trailing : trailing;
  }

  return debounce(func, wait, {
    leading,
    maxWait: wait,
    trailing
  });
}

/**
 * 延迟执行函数
 * @param {Function} func - 要延迟执行的函数
 * @param {number} wait - 延迟时间（毫秒）
 * @param {...*} args - 函数参数
 * @returns {number} 定时器ID
 */
export function delay(func, wait = 0, ...args) {
  if (!isFunction(func)) {
    throw new TypeError('Expected a function');
  }
  
  return setTimeout(func, toNumber(wait) || 0, ...args);
}

/**
 * 生成唯一ID
 * @param {string} prefix - ID前缀
 * @returns {string} 唯一ID
 */
export function uniqueId(prefix = '') {
  const id = Date.now().toString(36) + Math.random().toString(36).substr(2);
  return prefix + id;
}

/**
 * 随机数生成
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 随机数
 */
export function random(min = 0, max = 1) {
  if (min > max) {
    [min, max] = [max, min];
  }
  
  return Math.random() * (max - min) + min;
}

/**
 * 随机整数生成
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 随机整数
 */
export function randomInt(min = 0, max = 100) {
  return Math.floor(random(min, max + 1));
}

/**
 * 格式化字节大小
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的大小
 */
export function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * 格式化时间持续
 * @param {number} ms - 毫秒数
 * @returns {string} 格式化后的时间
 */
export function formatDuration(ms) {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
  return `${(ms / 3600000).toFixed(1)}h`;
}

/**
 * 安全的JSON解析
 * @param {string} str - JSON字符串
 * @param {*} defaultValue - 默认值
 * @returns {*} 解析结果
 */
export function safeJsonParse(str, defaultValue = null) {
  try {
    return JSON.parse(str);
  } catch (error) {
    logger.warn('JSON解析失败', { str, error: error.message });
    return defaultValue;
  }
}

/**
 * 安全的JSON字符串化
 * @param {*} obj - 要字符串化的对象
 * @param {*} defaultValue - 默认值
 * @returns {string} JSON字符串
 */
export function safeJsonStringify(obj, defaultValue = '{}') {
  try {
    return JSON.stringify(obj);
  } catch (error) {
    logger.warn('JSON字符串化失败', { obj, error: error.message });
    return defaultValue;
  }
}

/**
 * 睡眠函数
 * @param {number} ms - 睡眠时间（毫秒）
 * @returns {Promise} Promise对象
 */
export function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 重试函数
 * @param {Function} fn - 要重试的函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delay - 重试间隔（毫秒）
 * @returns {Promise} Promise对象
 */
export async function retry(fn, maxRetries = 3, delayMs = 1000) {
  let lastError;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (i < maxRetries) {
        logger.warn(`重试 ${i + 1}/${maxRetries}`, { error: error.message });
        await sleep(delayMs);
      }
    }
  }
  
  throw lastError;
}
