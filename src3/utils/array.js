/**
 * 数组处理工具函数
 * 
 * 提供各种数组操作、过滤、转换等功能
 * 
 * @original: Lodash数组处理函数 (L1447-1984)
 */

import { logger } from './logger.js';

/**
 * 查找数组中元素的索引
 * @original: kiB()函数 (L1451)
 * @param {Array} array - 输入数组
 * @param {*} value - 要查找的值
 * @returns {number} 元素索引，未找到返回-1
 */
export function findIndex(array, value) {
  if (!Array.isArray(array)) return -1;
  
  for (let i = 0; i < array.length; i++) {
    if (array[i] && array[i][0] === value) {
      return i;
    }
  }
  
  return -1;
}

/**
 * 移除数组中的元素
 * @original: viB()函数 (L1459)
 * @param {Array} array - 输入数组
 * @param {*} value - 要移除的值
 * @returns {boolean} 是否成功移除
 */
export function remove(array, value) {
  if (!Array.isArray(array)) return false;
  
  const index = findIndex(array, value);
  if (index > -1) {
    array.splice(index, 1);
    return true;
  }
  
  return false;
}

/**
 * 获取数组中的元素
 * @original: biB()函数 (L1468)
 * @param {Array} array - 输入数组
 * @param {*} key - 要获取的键
 * @returns {*} 元素值
 */
export function get(array, key) {
  if (!Array.isArray(array)) return undefined;
  
  const index = findIndex(array, key);
  return index > -1 ? array[index][1] : undefined;
}

/**
 * 检查数组中是否包含指定元素
 * @original: fiB()函数 (L1474)
 * @param {Array} array - 输入数组
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否包含
 */
export function has(array, value) {
  return findIndex(array, value) > -1;
}

/**
 * 设置数组中的元素
 * @original: hiB()函数 (L1478)
 * @param {Array} array - 输入数组
 * @param {*} key - 键
 * @param {*} value - 值
 * @returns {Array} 数组本身
 */
export function set(array, key, value) {
  if (!Array.isArray(array)) return array;
  
  const index = findIndex(array, key);
  if (index > -1) {
    array[index][1] = value;
  } else {
    array.push([key, value]);
  }
  
  return array;
}

/**
 * 展平数组（一层）
 * @original: InB()函数 (L1550)
 * @param {Array} array - 输入数组
 * @returns {Array} 展平后的数组
 */
export function flatten(array) {
  if (!Array.isArray(array)) return [];
  
  const result = [];
  for (const item of array) {
    if (Array.isArray(item)) {
      result.push(...item);
    } else {
      result.push(item);
    }
  }
  
  return result;
}

/**
 * 深度展平数组
 * @param {Array} array - 输入数组
 * @param {number} depth - 展平深度，默认为Infinity
 * @returns {Array} 展平后的数组
 */
export function flattenDeep(array, depth = Infinity) {
  if (!Array.isArray(array)) return [];
  
  const result = [];
  
  function flattenRecursive(arr, currentDepth) {
    for (const item of arr) {
      if (Array.isArray(item) && currentDepth > 0) {
        flattenRecursive(item, currentDepth - 1);
      } else {
        result.push(item);
      }
    }
  }
  
  flattenRecursive(array, depth);
  return result;
}

/**
 * 数组切片
 * @original: EnB()函数 (L1570)
 * @param {Array} array - 输入数组
 * @param {number} start - 开始索引
 * @param {number} end - 结束索引
 * @returns {Array} 切片后的数组
 */
export function slice(array, start = 0, end) {
  if (!Array.isArray(array)) return [];
  
  const length = array.length;
  end = end === undefined ? length : end;
  
  if (!start && end >= length) {
    return array.slice();
  }
  
  return array.slice(start, end);
}

/**
 * 过滤数组
 * @original: prB()函数 (L1759)
 * @param {Array} array - 输入数组
 * @param {Function} predicate - 过滤函数
 * @returns {Array} 过滤后的数组
 */
export function filter(array, predicate) {
  if (!Array.isArray(array)) return [];
  
  const result = [];
  for (let i = 0; i < array.length; i++) {
    const item = array[i];
    if (predicate(item, i, array)) {
      result.push(item);
    }
  }
  
  return result;
}

/**
 * 映射数组
 * @param {Array} array - 输入数组
 * @param {Function} iteratee - 映射函数
 * @returns {Array} 映射后的数组
 */
export function map(array, iteratee) {
  if (!Array.isArray(array)) return [];
  
  const result = [];
  for (let i = 0; i < array.length; i++) {
    result.push(iteratee(array[i], i, array));
  }
  
  return result;
}

/**
 * 获取数组的最后一个元素
 * @original: lrB()函数 (L1754)
 * @param {Array} array - 输入数组
 * @returns {*} 最后一个元素
 */
export function last(array) {
  const length = array?.length;
  return length ? array[length - 1] : undefined;
}

/**
 * 获取数组的第一个元素
 * @param {Array} array - 输入数组
 * @returns {*} 第一个元素
 */
export function first(array) {
  return array?.length ? array[0] : undefined;
}

/**
 * 获取数组的头部（除最后一个元素外的所有元素）
 * @param {Array} array - 输入数组
 * @returns {Array} 头部数组
 */
export function initial(array) {
  const length = array?.length;
  return length ? array.slice(0, -1) : [];
}

/**
 * 获取数组的尾部（除第一个元素外的所有元素）
 * @param {Array} array - 输入数组
 * @returns {Array} 尾部数组
 */
export function tail(array) {
  const length = array?.length;
  return length ? array.slice(1) : [];
}

/**
 * 数组去重
 * @param {Array} array - 输入数组
 * @param {Function} iteratee - 比较函数
 * @returns {Array} 去重后的数组
 */
export function uniq(array, iteratee) {
  if (!Array.isArray(array)) return [];
  
  if (!iteratee) {
    return [...new Set(array)];
  }
  
  const seen = new Set();
  const result = [];
  
  for (const item of array) {
    const computed = iteratee(item);
    if (!seen.has(computed)) {
      seen.add(computed);
      result.push(item);
    }
  }
  
  return result;
}

/**
 * 根据属性去重
 * @param {Array} array - 输入数组
 * @param {string} property - 属性名
 * @returns {Array} 去重后的数组
 */
export function uniqBy(array, property) {
  return uniq(array, item => item[property]);
}

/**
 * 数组分组
 * @param {Array} array - 输入数组
 * @param {Function|string} iteratee - 分组函数或属性名
 * @returns {Object} 分组后的对象
 */
export function groupBy(array, iteratee) {
  if (!Array.isArray(array)) return {};
  
  const result = {};
  const getKey = typeof iteratee === 'function' 
    ? iteratee 
    : item => item[iteratee];
  
  for (const item of array) {
    const key = getKey(item);
    if (!result[key]) {
      result[key] = [];
    }
    result[key].push(item);
  }
  
  return result;
}

/**
 * 数组排序
 * @param {Array} array - 输入数组
 * @param {Function|string} iteratee - 排序函数或属性名
 * @param {string} order - 排序顺序 'asc' 或 'desc'
 * @returns {Array} 排序后的数组
 */
export function sortBy(array, iteratee, order = 'asc') {
  if (!Array.isArray(array)) return [];
  
  const getValue = typeof iteratee === 'function'
    ? iteratee
    : item => item[iteratee];
  
  return array.slice().sort((a, b) => {
    const aVal = getValue(a);
    const bVal = getValue(b);
    
    if (aVal < bVal) return order === 'asc' ? -1 : 1;
    if (aVal > bVal) return order === 'asc' ? 1 : -1;
    return 0;
  });
}

/**
 * 数组求和
 * @param {Array} array - 输入数组
 * @param {Function|string} iteratee - 求和函数或属性名
 * @returns {number} 求和结果
 */
export function sumBy(array, iteratee) {
  if (!Array.isArray(array)) return 0;
  
  const getValue = typeof iteratee === 'function'
    ? iteratee
    : item => item[iteratee];
  
  return array.reduce((sum, item) => sum + (getValue(item) || 0), 0);
}

/**
 * 数组分块
 * @param {Array} array - 输入数组
 * @param {number} size - 块大小
 * @returns {Array} 分块后的数组
 */
export function chunk(array, size = 1) {
  if (!Array.isArray(array) || size < 1) return [];
  
  const result = [];
  for (let i = 0; i < array.length; i += size) {
    result.push(array.slice(i, i + size));
  }
  
  return result;
}

/**
 * 数组交集
 * @param {...Array} arrays - 输入数组
 * @returns {Array} 交集数组
 */
export function intersection(...arrays) {
  if (arrays.length === 0) return [];
  if (arrays.length === 1) return arrays[0].slice();
  
  const [first, ...rest] = arrays;
  return first.filter(item => 
    rest.every(arr => arr.includes(item))
  );
}

/**
 * 数组并集
 * @param {...Array} arrays - 输入数组
 * @returns {Array} 并集数组
 */
export function union(...arrays) {
  const result = [];
  const seen = new Set();
  
  for (const array of arrays) {
    if (Array.isArray(array)) {
      for (const item of array) {
        if (!seen.has(item)) {
          seen.add(item);
          result.push(item);
        }
      }
    }
  }
  
  return result;
}

/**
 * 数组差集
 * @param {Array} array - 基础数组
 * @param {...Array} values - 要排除的数组
 * @returns {Array} 差集数组
 */
export function difference(array, ...values) {
  if (!Array.isArray(array)) return [];
  
  const excludeSet = new Set();
  for (const arr of values) {
    if (Array.isArray(arr)) {
      for (const item of arr) {
        excludeSet.add(item);
      }
    }
  }
  
  return array.filter(item => !excludeSet.has(item));
}

/**
 * 压缩数组（将多个数组的对应位置元素组合）
 * @param {...Array} arrays - 输入数组
 * @returns {Array} 压缩后的数组
 */
export function zip(...arrays) {
  const length = Math.max(...arrays.map(arr => arr?.length || 0));
  const result = [];
  
  for (let i = 0; i < length; i++) {
    result.push(arrays.map(arr => arr?.[i]));
  }
  
  return result;
}
