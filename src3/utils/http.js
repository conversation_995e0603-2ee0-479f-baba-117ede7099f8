/**
 * HTTP请求工具函数
 * 
 * 提供HTTP请求、响应处理、错误处理等功能
 * 
 * @original: Axios相关代码和HTTP处理逻辑 (L2208-2350, L53378-53406)
 */

import { logger } from './logger.js';

/**
 * HTTP错误类
 */
export class HttpError extends Error {
  constructor(message, status, response) {
    super(message);
    this.name = 'HttpError';
    this.status = status;
    this.response = response;
  }
}

/**
 * 网络错误类
 */
export class NetworkError extends Error {
  constructor(message, code) {
    super(message);
    this.name = 'NetworkError';
    this.code = code;
  }
}

/**
 * 超时错误类
 */
export class TimeoutError extends Error {
  constructor(message, timeout) {
    super(message);
    this.name = 'TimeoutError';
    this.timeout = timeout;
  }
}

/**
 * HTTP客户端类
 */
export class HttpClient {
  constructor(options = {}) {
    this.baseURL = options.baseURL || '';
    this.timeout = options.timeout || 30000;
    this.headers = options.headers || {};
    this.interceptors = {
      request: [],
      response: []
    };
  }

  /**
   * 添加请求拦截器
   * @param {Function} onFulfilled - 成功处理函数
   * @param {Function} onRejected - 错误处理函数
   */
  addRequestInterceptor(onFulfilled, onRejected) {
    this.interceptors.request.push({ onFulfilled, onRejected });
  }

  /**
   * 添加响应拦截器
   * @param {Function} onFulfilled - 成功处理函数
   * @param {Function} onRejected - 错误处理函数
   */
  addResponseInterceptor(onFulfilled, onRejected) {
    this.interceptors.response.push({ onFulfilled, onRejected });
  }

  /**
   * 执行请求
   * @param {Object} config - 请求配置
   * @returns {Promise} 请求结果
   */
  async request(config) {
    try {
      // 合并配置
      const finalConfig = this.mergeConfig(config);
      
      // 执行请求拦截器
      const processedConfig = await this.processRequestInterceptors(finalConfig);
      
      // 发送请求
      const response = await this.sendRequest(processedConfig);
      
      // 执行响应拦截器
      return await this.processResponseInterceptors(response);
      
    } catch (error) {
      // 执行响应拦截器（错误处理）
      return await this.processResponseInterceptors(null, error);
    }
  }

  /**
   * GET请求
   * @param {string} url - 请求URL
   * @param {Object} config - 请求配置
   * @returns {Promise} 请求结果
   */
  async get(url, config = {}) {
    return this.request({
      ...config,
      method: 'GET',
      url
    });
  }

  /**
   * POST请求
   * @param {string} url - 请求URL
   * @param {*} data - 请求数据
   * @param {Object} config - 请求配置
   * @returns {Promise} 请求结果
   */
  async post(url, data, config = {}) {
    return this.request({
      ...config,
      method: 'POST',
      url,
      data
    });
  }

  /**
   * PUT请求
   * @param {string} url - 请求URL
   * @param {*} data - 请求数据
   * @param {Object} config - 请求配置
   * @returns {Promise} 请求结果
   */
  async put(url, data, config = {}) {
    return this.request({
      ...config,
      method: 'PUT',
      url,
      data
    });
  }

  /**
   * DELETE请求
   * @param {string} url - 请求URL
   * @param {Object} config - 请求配置
   * @returns {Promise} 请求结果
   */
  async delete(url, config = {}) {
    return this.request({
      ...config,
      method: 'DELETE',
      url
    });
  }

  /**
   * 合并配置
   * @param {Object} config - 请求配置
   * @returns {Object} 合并后的配置
   */
  mergeConfig(config) {
    const url = this.buildURL(config.url);
    
    return {
      method: 'GET',
      timeout: this.timeout,
      headers: { ...this.headers },
      ...config,
      url
    };
  }

  /**
   * 构建完整URL
   * @param {string} url - 相对URL
   * @returns {string} 完整URL
   */
  buildURL(url) {
    if (!url) return this.baseURL;
    if (url.startsWith('http://') || url.startsWith('https://')) return url;
    
    const base = this.baseURL.endsWith('/') ? this.baseURL.slice(0, -1) : this.baseURL;
    const path = url.startsWith('/') ? url : `/${url}`;
    
    return base + path;
  }

  /**
   * 处理请求拦截器
   * @param {Object} config - 请求配置
   * @returns {Promise<Object>} 处理后的配置
   */
  async processRequestInterceptors(config) {
    let processedConfig = config;
    
    for (const interceptor of this.interceptors.request) {
      try {
        if (interceptor.onFulfilled) {
          processedConfig = await interceptor.onFulfilled(processedConfig);
        }
      } catch (error) {
        if (interceptor.onRejected) {
          processedConfig = await interceptor.onRejected(error);
        } else {
          throw error;
        }
      }
    }
    
    return processedConfig;
  }

  /**
   * 处理响应拦截器
   * @param {Object} response - 响应对象
   * @param {Error} error - 错误对象
   * @returns {Promise} 处理结果
   */
  async processResponseInterceptors(response, error) {
    let result = response;
    let currentError = error;
    
    for (const interceptor of this.interceptors.response) {
      try {
        if (currentError) {
          if (interceptor.onRejected) {
            result = await interceptor.onRejected(currentError);
            currentError = null;
          }
        } else if (interceptor.onFulfilled) {
          result = await interceptor.onFulfilled(result);
        }
      } catch (err) {
        currentError = err;
        result = null;
      }
    }
    
    if (currentError) {
      throw currentError;
    }
    
    return result;
  }

  /**
   * 发送请求
   * @param {Object} config - 请求配置
   * @returns {Promise<Object>} 响应对象
   */
  async sendRequest(config) {
    const { url, method, headers, data, timeout } = config;
    
    logger.debug('发送HTTP请求', { method, url, headers });
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    try {
      const fetchOptions = {
        method,
        headers,
        signal: controller.signal
      };
      
      if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
        if (typeof data === 'object') {
          fetchOptions.body = JSON.stringify(data);
          fetchOptions.headers['Content-Type'] = 'application/json';
        } else {
          fetchOptions.body = data;
        }
      }
      
      const response = await fetch(url, fetchOptions);
      clearTimeout(timeoutId);
      
      const responseData = await this.parseResponse(response);
      
      if (!response.ok) {
        throw new HttpError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          responseData
        );
      }
      
      logger.debug('HTTP请求成功', { 
        method, 
        url, 
        status: response.status,
        dataLength: responseData ? JSON.stringify(responseData).length : 0
      });
      
      return {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        config
      };
      
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new TimeoutError(`Request timeout after ${timeout}ms`, timeout);
      }
      
      if (error instanceof HttpError) {
        throw error;
      }
      
      logger.error('HTTP请求失败', { method, url, error: error.message });
      throw new NetworkError(error.message, error.code);
    }
  }

  /**
   * 解析响应数据
   * @param {Response} response - Fetch响应对象
   * @returns {Promise<*>} 解析后的数据
   */
  async parseResponse(response) {
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    }
    
    if (contentType && contentType.includes('text/')) {
      return await response.text();
    }
    
    return await response.arrayBuffer();
  }
}

/**
 * 默认HTTP客户端实例
 */
export const httpClient = new HttpClient();

/**
 * 便捷的请求方法
 */
export const get = (url, config) => httpClient.get(url, config);
export const post = (url, data, config) => httpClient.post(url, data, config);
export const put = (url, data, config) => httpClient.put(url, data, config);
export const del = (url, config) => httpClient.delete(url, config);

/**
 * 创建HTTP客户端实例
 * @param {Object} options - 配置选项
 * @returns {HttpClient} HTTP客户端实例
 */
export function createHttpClient(options) {
  return new HttpClient(options);
}

/**
 * 检查网络连接
 * @original: 网络连接检查逻辑
 * @param {string} url - 测试URL
 * @param {number} timeout - 超时时间
 * @returns {Promise<boolean>} 是否连接成功
 */
export async function checkNetworkConnectivity(url = 'https://www.google.com', timeout = 5000) {
  try {
    const response = await fetch(url, {
      method: 'HEAD',
      signal: AbortSignal.timeout(timeout)
    });
    
    return response.ok;
    
  } catch (error) {
    logger.warn('网络连接检查失败', { url, error: error.message });
    return false;
  }
}

/**
 * 下载文件
 * @param {string} url - 文件URL
 * @param {Object} options - 下载选项
 * @returns {Promise<ArrayBuffer>} 文件数据
 */
export async function downloadFile(url, options = {}) {
  const { timeout = 30000, onProgress } = options;
  
  logger.debug('开始下载文件', { url });
  
  const response = await fetch(url, {
    signal: AbortSignal.timeout(timeout)
  });
  
  if (!response.ok) {
    throw new HttpError(
      `Failed to download file: ${response.status} ${response.statusText}`,
      response.status
    );
  }
  
  const contentLength = response.headers.get('content-length');
  const total = contentLength ? parseInt(contentLength, 10) : 0;
  
  if (onProgress && total > 0) {
    const reader = response.body.getReader();
    const chunks = [];
    let loaded = 0;
    
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      chunks.push(value);
      loaded += value.length;
      
      onProgress({ loaded, total, percentage: (loaded / total) * 100 });
    }
    
    const uint8Array = new Uint8Array(loaded);
    let offset = 0;
    
    for (const chunk of chunks) {
      uint8Array.set(chunk, offset);
      offset += chunk.length;
    }
    
    return uint8Array.buffer;
  }
  
  return await response.arrayBuffer();
}
