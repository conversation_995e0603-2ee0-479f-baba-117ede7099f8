/**
 * 流处理工具
 * 
 * 提供各种流处理功能，包括异步迭代器、流转换、缓冲等
 * 
 * @original: 流处理相关代码 (L28400-28453, L55327-55389)
 */

import { logger } from './logger.js';

/**
 * 流处理工具类
 */
export class StreamProcessor {
  constructor() {
    this.activeStreams = new Set();
  }

  /**
   * 获取异步迭代器的最后一个值
   * @original: Ov()函数 (L28400-28405)
   * @param {AsyncIterable} asyncIterable - 异步迭代器
   * @returns {Promise<*>} 最后一个值
   */
  async getLastValue(asyncIterable) {
    const NO_VALUE = Symbol('NO_VALUE');
    let lastValue = NO_VALUE;

    try {
      for await (const value of asyncIterable) {
        lastValue = value;
      }

      return lastValue === NO_VALUE ? undefined : lastValue;

    } catch (error) {
      logger.error('获取流最后值失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 过滤结果类型的流
   * @original: XjB()函数 (L28406-28411)
   * @param {AsyncIterable} asyncIterable - 异步迭代器
   * @returns {AsyncGenerator} 过滤后的异步生成器
   */
  async *filterResultStream(asyncIterable) {
    try {
      for await (const item of asyncIterable) {
        if (item.type === 'result') {
          yield item;
        }
      }
    } catch (error) {
      logger.error('过滤结果流失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 并发处理流
   * @original: VjB()函数 (L28412-28445)
   * @param {Array} asyncIterables - 异步迭代器数组
   * @param {number} concurrency - 并发数量
   * @returns {AsyncGenerator} 并发处理的异步生成器
   */
  async *processConcurrentStreams(asyncIterables, concurrency = Infinity) {
    const createPromise = (iterable) => {
      const iterator = iterable[Symbol.asyncIterator]();
      return iterator.next().then(({ value, done }) => ({
        value,
        done,
        iterator
      }));
    };

    let activePromises = asyncIterables.slice(0, concurrency).map(createPromise);
    let pendingIterables = asyncIterables.slice(concurrency);

    try {
      while (activePromises.length > 0) {
        const { value, done, iterator } = await Promise.race(activePromises);
        
        // 移除已完成的Promise
        const completedIndex = activePromises.findIndex(p => p === Promise.resolve({ value, done, iterator }));
        if (completedIndex !== -1) {
          activePromises.splice(completedIndex, 1);
        }

        if (!done) {
          yield value;
          
          // 继续处理同一个迭代器
          activePromises.push(createPromise([iterator]));
        }

        // 如果有待处理的迭代器且有空闲槽位，添加新的
        if (pendingIterables.length > 0 && activePromises.length < concurrency) {
          const nextIterable = pendingIterables.shift();
          activePromises.push(createPromise(nextIterable));
        }
      }

    } catch (error) {
      logger.error('并发流处理失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 收集异步迭代器的所有值
   * @original: SN0()函数 (L28446-28450)
   * @param {AsyncIterable} asyncIterable - 异步迭代器
   * @returns {Promise<Array>} 收集的值数组
   */
  async collectAll(asyncIterable) {
    const results = [];

    try {
      for await (const value of asyncIterable) {
        results.push(value);
      }

      return results;

    } catch (error) {
      logger.error('收集流值失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 将数组转换为异步生成器
   * @original: CjB()函数 (L28451-28453)
   * @param {Array} array - 数组
   * @returns {AsyncGenerator} 异步生成器
   */
  async *arrayToAsyncGenerator(array) {
    for (const item of array) {
      yield item;
    }
  }

  /**
   * 创建流缓冲区
   * @param {number} capacity - 缓冲区容量
   * @returns {Object} 流缓冲区对象
   */
  createStreamBuffer(capacity = 100) {
    return new StreamBuffer(capacity);
  }

  /**
   * 创建流队列
   * @returns {Object} 流队列对象
   */
  createStreamQueue() {
    return new StreamQueue();
  }

  /**
   * 注册活动流
   * @param {string} streamId - 流ID
   */
  registerActiveStream(streamId) {
    this.activeStreams.add(streamId);
  }

  /**
   * 注销活动流
   * @param {string} streamId - 流ID
   */
  unregisterActiveStream(streamId) {
    this.activeStreams.delete(streamId);
  }

  /**
   * 获取活动流数量
   * @returns {number} 活动流数量
   */
  getActiveStreamCount() {
    return this.activeStreams.size;
  }
}

/**
 * 流缓冲区类
 * @original: J$0类 (L16745-17297)
 */
export class StreamBuffer {
  constructor(capacity = 100) {
    this.capacity = capacity;
    this.buffer = [];
    this.readIndex = 0;
    this.writeIndex = 0;
    this.size = 0;
  }

  /**
   * 写入数据到缓冲区
   * @param {*} data - 要写入的数据
   * @returns {boolean} 是否写入成功
   */
  write(data) {
    if (this.size >= this.capacity) {
      logger.warn('流缓冲区已满，丢弃数据');
      return false;
    }

    this.buffer[this.writeIndex] = data;
    this.writeIndex = (this.writeIndex + 1) % this.capacity;
    this.size++;

    return true;
  }

  /**
   * 从缓冲区读取数据
   * @returns {*} 读取的数据
   */
  read() {
    if (this.size === 0) {
      return undefined;
    }

    const data = this.buffer[this.readIndex];
    this.buffer[this.readIndex] = undefined;
    this.readIndex = (this.readIndex + 1) % this.capacity;
    this.size--;

    return data;
  }

  /**
   * 检查缓冲区是否为空
   * @returns {boolean} 是否为空
   */
  isEmpty() {
    return this.size === 0;
  }

  /**
   * 检查缓冲区是否已满
   * @returns {boolean} 是否已满
   */
  isFull() {
    return this.size >= this.capacity;
  }

  /**
   * 清空缓冲区
   */
  clear() {
    this.buffer = [];
    this.readIndex = 0;
    this.writeIndex = 0;
    this.size = 0;
  }

  /**
   * 获取缓冲区状态
   * @returns {Object} 缓冲区状态
   */
  getStatus() {
    return {
      capacity: this.capacity,
      size: this.size,
      isEmpty: this.isEmpty(),
      isFull: this.isFull(),
      utilization: this.size / this.capacity
    };
  }
}

/**
 * 流队列类
 * @original: FT0类 (L55327-55389)
 */
export class StreamQueue {
  constructor() {
    this.returned = false;
    this.queue = [];
    this.resolvers = [];
    this.rejectors = [];
  }

  /**
   * 推送数据到队列
   * @param {*} data - 要推送的数据
   */
  push(data) {
    if (this.returned) {
      logger.warn('尝试向已返回的流队列推送数据');
      return;
    }

    if (this.resolvers.length > 0) {
      const resolver = this.resolvers.shift();
      resolver({ value: data, done: false });
    } else {
      this.queue.push(data);
    }
  }

  /**
   * 标记队列结束
   * @param {Error} error - 可选的错误对象
   */
  return(error = null) {
    this.returned = true;

    if (error) {
      // 拒绝所有等待的Promise
      while (this.rejectors.length > 0) {
        const rejector = this.rejectors.shift();
        rejector(error);
      }
    } else {
      // 解决所有等待的Promise
      while (this.resolvers.length > 0) {
        const resolver = this.resolvers.shift();
        resolver({ value: undefined, done: true });
      }
    }
  }

  /**
   * 异步迭代器实现
   * @returns {AsyncIterator} 异步迭代器
   */
  [Symbol.asyncIterator]() {
    return {
      next: () => {
        if (this.queue.length > 0) {
          const value = this.queue.shift();
          return Promise.resolve({ value, done: false });
        }

        if (this.returned) {
          return Promise.resolve({ value: undefined, done: true });
        }

        return new Promise((resolve, reject) => {
          this.resolvers.push(resolve);
          this.rejectors.push(reject);
        });
      }
    };
  }

  /**
   * 获取队列状态
   * @returns {Object} 队列状态
   */
  getStatus() {
    return {
      queueLength: this.queue.length,
      waitingResolvers: this.resolvers.length,
      returned: this.returned
    };
  }
}

/**
 * 流转换器类
 */
export class StreamTransformer {
  /**
   * 映射流
   * @param {AsyncIterable} stream - 输入流
   * @param {Function} mapper - 映射函数
   * @returns {AsyncGenerator} 映射后的流
   */
  async *map(stream, mapper) {
    try {
      for await (const item of stream) {
        yield await mapper(item);
      }
    } catch (error) {
      logger.error('流映射失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 过滤流
   * @param {AsyncIterable} stream - 输入流
   * @param {Function} predicate - 过滤条件
   * @returns {AsyncGenerator} 过滤后的流
   */
  async *filter(stream, predicate) {
    try {
      for await (const item of stream) {
        if (await predicate(item)) {
          yield item;
        }
      }
    } catch (error) {
      logger.error('流过滤失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 批处理流
   * @param {AsyncIterable} stream - 输入流
   * @param {number} batchSize - 批处理大小
   * @returns {AsyncGenerator} 批处理后的流
   */
  async *batch(stream, batchSize) {
    let batch = [];

    try {
      for await (const item of stream) {
        batch.push(item);
        
        if (batch.length >= batchSize) {
          yield batch;
          batch = [];
        }
      }

      // 处理剩余的项目
      if (batch.length > 0) {
        yield batch;
      }

    } catch (error) {
      logger.error('流批处理失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 限制流速率
   * @param {AsyncIterable} stream - 输入流
   * @param {number} delayMs - 延迟毫秒数
   * @returns {AsyncGenerator} 限速后的流
   */
  async *throttle(stream, delayMs) {
    try {
      for await (const item of stream) {
        yield item;
        
        if (delayMs > 0) {
          await new Promise(resolve => setTimeout(resolve, delayMs));
        }
      }
    } catch (error) {
      logger.error('流限速失败', { error: error.message });
      throw error;
    }
  }
}

/**
 * 全局流处理器实例
 */
export const globalStreamProcessor = new StreamProcessor();

/**
 * 全局流转换器实例
 */
export const globalStreamTransformer = new StreamTransformer();

/**
 * 便捷函数：获取流的最后一个值
 * @param {AsyncIterable} stream - 异步迭代器
 * @returns {Promise<*>} 最后一个值
 */
export async function getLastStreamValue(stream) {
  return globalStreamProcessor.getLastValue(stream);
}

/**
 * 便捷函数：收集流的所有值
 * @param {AsyncIterable} stream - 异步迭代器
 * @returns {Promise<Array>} 收集的值数组
 */
export async function collectStreamValues(stream) {
  return globalStreamProcessor.collectAll(stream);
}

/**
 * 便捷函数：将数组转换为流
 * @param {Array} array - 数组
 * @returns {AsyncGenerator} 异步生成器
 */
export async function arrayToStream(array) {
  return globalStreamProcessor.arrayToAsyncGenerator(array);
}

/**
 * 便捷函数：创建流缓冲区
 * @param {number} capacity - 缓冲区容量
 * @returns {StreamBuffer} 流缓冲区实例
 */
export function createStreamBuffer(capacity = 100) {
  return globalStreamProcessor.createStreamBuffer(capacity);
}

/**
 * 便捷函数：创建流队列
 * @returns {StreamQueue} 流队列实例
 */
export function createStreamQueue() {
  return globalStreamProcessor.createStreamQueue();
}

/**
 * 便捷函数：映射流
 * @param {AsyncIterable} stream - 输入流
 * @param {Function} mapper - 映射函数
 * @returns {AsyncGenerator} 映射后的流
 */
export async function mapStream(stream, mapper) {
  return globalStreamTransformer.map(stream, mapper);
}

/**
 * 便捷函数：过滤流
 * @param {AsyncIterable} stream - 输入流
 * @param {Function} predicate - 过滤条件
 * @returns {AsyncGenerator} 过滤后的流
 */
export async function filterStream(stream, predicate) {
  return globalStreamTransformer.filter(stream, predicate);
}

/**
 * 便捷函数：批处理流
 * @param {AsyncIterable} stream - 输入流
 * @param {number} batchSize - 批处理大小
 * @returns {AsyncGenerator} 批处理后的流
 */
export async function batchStream(stream, batchSize) {
  return globalStreamTransformer.batch(stream, batchSize);
}

/**
 * 便捷函数：限制流速率
 * @param {AsyncIterable} stream - 输入流
 * @param {number} delayMs - 延迟毫秒数
 * @returns {AsyncGenerator} 限速后的流
 */
export async function throttleStream(stream, delayMs) {
  return globalStreamTransformer.throttle(stream, delayMs);
}
