/**
 * 文件缓存系统
 * 
 * 提供高效的文件读取缓存，避免重复读取相同文件
 * 
 * @original: nFA类 (L2789-2838)
 */

import { readFileSync, statSync } from 'fs';
import { detectEncoding } from './encoding.js';
import { logger } from './logger.js';

/**
 * 文件缓存类
 * 缓存文件内容和元数据，基于修改时间自动失效
 * 
 * @original: nFA类
 */
export class FileCache {
  constructor(maxCacheSize = 1000) {
    this.cache = new Map();
    this.maxCacheSize = maxCacheSize;
  }

  /**
   * 读取文件内容（带缓存）
   * 
   * @original: readFile方法 (L2792-2824)
   * @param {string} filePath - 文件路径
   * @returns {Object} 包含content和encoding的对象
   * @throws {Error} 文件不存在或读取失败时抛出错误
   */
  readFile(filePath) {
    let stats;
    
    try {
      stats = statSync(filePath);
    } catch (error) {
      // 文件不存在或无法访问，清除缓存并抛出错误
      this.cache.delete(filePath);
      throw error;
    }

    const cacheKey = filePath;
    const cachedEntry = this.cache.get(cacheKey);

    // 检查缓存是否有效（基于修改时间）
    if (cachedEntry && cachedEntry.mtime === stats.mtimeMs) {
      logger.debug('文件缓存命中', { filePath });
      return {
        content: cachedEntry.content,
        encoding: cachedEntry.encoding
      };
    }

    // 缓存未命中或已过期，重新读取文件
    logger.debug('读取文件到缓存', { filePath });
    
    const encoding = detectEncoding(filePath);
    const content = readFileSync(filePath, { encoding })
      .replaceAll('\r\n', '\n'); // 统一换行符

    // 更新缓存
    this.cache.set(cacheKey, {
      content,
      encoding,
      mtime: stats.mtimeMs
    });

    // 检查缓存大小限制
    this._enforceMaxCacheSize();

    return { content, encoding };
  }

  /**
   * 清空所有缓存
   * 
   * @original: clear方法 (L2825-2827)
   */
  clear() {
    logger.debug('清空文件缓存');
    this.cache.clear();
  }

  /**
   * 使特定文件的缓存失效
   * 
   * @original: invalidate方法 (L2828-2830)
   * @param {string} filePath - 文件路径
   */
  invalidate(filePath) {
    logger.debug('使文件缓存失效', { filePath });
    this.cache.delete(filePath);
  }

  /**
   * 获取缓存统计信息
   * 
   * @original: getStats方法 (L2831-2836)
   * @returns {Object} 缓存统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      entries: Array.from(this.cache.keys())
    };
  }

  /**
   * 强制执行最大缓存大小限制
   * 使用LRU策略删除最旧的条目
   * 
   * @private
   */
  _enforceMaxCacheSize() {
    if (this.cache.size > this.maxCacheSize) {
      // 删除最旧的条目（Map保持插入顺序）
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        logger.debug('删除最旧的缓存条目', { filePath: oldestKey });
        this.cache.delete(oldestKey);
      }
    }
  }

  /**
   * 预加载文件到缓存
   * 
   * @param {string[]} filePaths - 要预加载的文件路径数组
   * @returns {Promise<void>}
   */
  async preload(filePaths) {
    logger.debug('预加载文件到缓存', { count: filePaths.length });
    
    const promises = filePaths.map(async (filePath) => {
      try {
        this.readFile(filePath);
      } catch (error) {
        logger.warn('预加载文件失败', { filePath, error: error.message });
      }
    });

    await Promise.all(promises);
  }

  /**
   * 获取缓存命中率统计
   * 
   * @returns {Object} 命中率统计
   */
  getHitRateStats() {
    // 这需要额外的计数器来跟踪命中和未命中
    // 为简化起见，这里返回基本信息
    return {
      cacheSize: this.cache.size,
      maxCacheSize: this.maxCacheSize,
      utilizationRate: (this.cache.size / this.maxCacheSize * 100).toFixed(2) + '%'
    };
  }
}

/**
 * 全局文件缓存实例
 * @original: aFA变量 (L2838)
 */
export const globalFileCache = new FileCache();

/**
 * 便捷函数：读取文件（使用全局缓存）
 * 
 * @param {string} filePath - 文件路径
 * @returns {Object} 包含content和encoding的对象
 */
export function readFileWithCache(filePath) {
  return globalFileCache.readFile(filePath);
}

/**
 * 便捷函数：清空全局缓存
 */
export function clearGlobalCache() {
  globalFileCache.clear();
}

/**
 * 便捷函数：使全局缓存中的文件失效
 * 
 * @param {string} filePath - 文件路径
 */
export function invalidateGlobalCache(filePath) {
  globalFileCache.invalidate(filePath);
}
