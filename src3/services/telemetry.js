/**
 * 遥测和指标服务
 * 
 * 管理Claude Code的遥测数据收集、指标上报、性能监控等
 * 
 * @original: 遥测相关代码 (L8795-8884, L2083-2102, L5779-5803)
 */

import { logger } from '../utils/logger.js';
import { APP_INFO } from '../config/constants.js';

/**
 * 遥测事件类型枚举
 */
export const TELEMETRY_EVENTS = {
  SESSION_START: 'tengu_init',
  SESSION_END: 'tengu_exit',
  TOOL_DECISION: 'tengu_tool_decision',
  API_USAGE: 'tengu_api_usage',
  ERROR: 'tengu_error',
  UPDATE_CHECK: 'tengu_update_check',
  OAUTH_API_KEY: 'tengu_oauth_api_key',
  MCP_LIST: 'tengu_mcp_list',
  INSTALL_COMMAND: 'tengu_claude_install_command',
  DOCTOR_COMMAND: 'tengu_doctor_command',
  SETUP_TOKEN_COMMAND: 'tengu_setup_token_command',
  MIGRATE_INSTALLER_COMMAND: 'tengu_migrate_installer_command'
};

/**
 * 指标名称枚举
 */
export const METRICS = {
  SESSION_COUNT: 'claude_code.session.count',
  LINES_OF_CODE_COUNT: 'claude_code.lines_of_code.count',
  PULL_REQUEST_COUNT: 'claude_code.pull_request.count',
  COMMIT_COUNT: 'claude_code.commit.count',
  COST_USAGE: 'claude_code.cost.usage',
  TOKEN_USAGE: 'claude_code.token.usage',
  CODE_EDIT_TOOL_DECISION: 'claude_code.code_edit_tool.decision',
  ACTIVE_TIME_TOTAL: 'claude_code.active_time.total'
};

/**
 * 遥测管理器类
 */
export class TelemetryManager {
  constructor(options = {}) {
    this.enabled = this.isTelemetryEnabled();
    this.endpoint = options.endpoint || 'https://api.anthropic.com/api/claude_code/metrics';
    this.timeout = options.timeout || 5000;
    this.batchSize = options.batchSize || 100;
    this.flushInterval = options.flushInterval || 30000; // 30秒
    
    this.eventQueue = [];
    this.isShutdown = false;
    this.flushTimer = null;
    
    if (this.enabled) {
      this.startBatchFlush();
    }
  }

  /**
   * 检查遥测是否启用
   * @original: _AB()函数 (L8795)
   * @returns {boolean} 是否启用遥测
   */
  isTelemetryEnabled() {
    // 检查环境变量禁用标志
    if (process.env.DISABLE_TELEMETRY || 
        process.env.CLAUDE_CODE_DISABLE_NONESSENTIAL_TRAFFIC ||
        process.env.CLAUDE_CODE_USE_BEDROCK ||
        process.env.CLAUDE_CODE_USE_VERTEX) {
      return false;
    }

    // 检查显式启用标志
    return Boolean(process.env.CLAUDE_CODE_ENABLE_TELEMETRY);
  }

  /**
   * 记录遥测事件
   * @original: C1()函数调用模式
   * @param {string} event - 事件名称
   * @param {Object} data - 事件数据
   */
  logEvent(event, data = {}) {
    if (!this.enabled || this.isShutdown) {
      return;
    }

    const eventData = {
      event,
      timestamp: new Date().toISOString(),
      sessionId: this.getSessionId(),
      version: APP_INFO.VERSION,
      platform: process.platform,
      nodeVersion: process.version,
      ...data
    };

    this.eventQueue.push(eventData);
    
    logger.debug('遥测事件已记录', { event, dataKeys: Object.keys(data) });

    // 如果队列满了，立即刷新
    if (this.eventQueue.length >= this.batchSize) {
      this.flush();
    }
  }

  /**
   * 启动批量刷新
   */
  startBatchFlush() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  /**
   * 刷新事件队列
   * @returns {Promise<void>}
   */
  async flush() {
    if (this.eventQueue.length === 0 || this.isShutdown) {
      return;
    }

    const events = [...this.eventQueue];
    this.eventQueue = [];

    try {
      await this.sendEvents(events);
      logger.debug('遥测事件已发送', { count: events.length });
    } catch (error) {
      logger.warn('遥测事件发送失败', { 
        count: events.length, 
        error: error.message 
      });
      
      // 将事件放回队列（最多重试一次）
      if (events.length < this.batchSize * 2) {
        this.eventQueue.unshift(...events);
      }
    }
  }

  /**
   * 发送事件到服务器
   * @param {Array} events - 事件数组
   * @returns {Promise<void>}
   */
  async sendEvents(events) {
    const { createHttpClient } = await import('../utils/http.js');
    
    const httpClient = createHttpClient({
      timeout: this.timeout
    });

    const payload = {
      events,
      metadata: {
        client: 'claude-code',
        version: APP_INFO.VERSION,
        platform: process.platform
      }
    };

    const response = await httpClient.post(this.endpoint, payload);
    
    if (response.status !== 200) {
      throw new Error(`Telemetry server returned ${response.status}: ${response.statusText}`);
    }

    logger.info('遥测数据发送成功', { 
      endpoint: this.endpoint,
      eventCount: events.length 
    });
  }

  /**
   * 获取会话ID
   * @returns {string} 会话ID
   */
  getSessionId() {
    // 从会话管理器获取当前会话ID
    const { getSessionState } = require('./session.js');
    return getSessionState().sessionId;
  }

  /**
   * 关闭遥测管理器
   * @returns {Promise<void>}
   */
  async shutdown() {
    this.isShutdown = true;
    
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }

    // 最后一次刷新
    await this.flush();
    
    logger.info('遥测管理器已关闭');
  }
}

/**
 * 指标管理器类
 */
export class MetricsManager {
  constructor() {
    this.meters = new Map();
    this.counters = new Map();
    this.histograms = new Map();
    this.gauges = new Map();
  }

  /**
   * 初始化指标
   * @original: hj0()函数 (L2083)
   * @param {Object} meter - 指标计量器
   * @param {Function} createCounter - 创建计数器函数
   */
  initializeMetrics(meter, createCounter) {
    this.meters.set('default', meter);

    // 创建各种计数器
    this.counters.set(METRICS.SESSION_COUNT, createCounter(METRICS.SESSION_COUNT, {
      description: 'Count of CLI sessions started'
    }));

    this.counters.set(METRICS.LINES_OF_CODE_COUNT, createCounter(METRICS.LINES_OF_CODE_COUNT, {
      description: 'Count of lines of code modified, with the \'type\' attribute indicating whether lines were added or removed'
    }));

    this.counters.set(METRICS.PULL_REQUEST_COUNT, createCounter(METRICS.PULL_REQUEST_COUNT, {
      description: 'Number of pull requests created'
    }));

    this.counters.set(METRICS.COMMIT_COUNT, createCounter(METRICS.COMMIT_COUNT, {
      description: 'Number of git commits created'
    }));

    this.counters.set(METRICS.COST_USAGE, createCounter(METRICS.COST_USAGE, {
      description: 'Cost of the Claude Code session',
      unit: 'USD'
    }));

    this.counters.set(METRICS.TOKEN_USAGE, createCounter(METRICS.TOKEN_USAGE, {
      description: 'Number of tokens used',
      unit: 'tokens'
    }));

    this.counters.set(METRICS.CODE_EDIT_TOOL_DECISION, createCounter(METRICS.CODE_EDIT_TOOL_DECISION, {
      description: 'Count of code editing tool permission decisions (accept/reject) for Edit, MultiEdit, Write, and NotebookEdit tools'
    }));

    this.counters.set(METRICS.ACTIVE_TIME_TOTAL, createCounter(METRICS.ACTIVE_TIME_TOTAL, {
      description: 'Total active time in seconds',
      unit: 's'
    }));

    logger.debug('指标已初始化', { counterCount: this.counters.size });
  }

  /**
   * 增加计数器
   * @param {string} metric - 指标名称
   * @param {number} value - 增加值
   * @param {Object} attributes - 属性
   */
  incrementCounter(metric, value = 1, attributes = {}) {
    const counter = this.counters.get(metric);
    
    if (counter) {
      counter.add(value, attributes);
      logger.debug('计数器已增加', { metric, value, attributes });
    } else {
      logger.warn('计数器不存在', { metric });
    }
  }

  /**
   * 记录直方图值
   * @param {string} metric - 指标名称
   * @param {number} value - 值
   * @param {Object} attributes - 属性
   */
  recordHistogram(metric, value, attributes = {}) {
    const histogram = this.histograms.get(metric);
    
    if (histogram) {
      histogram.record(value, attributes);
      logger.debug('直方图值已记录', { metric, value, attributes });
    } else {
      logger.warn('直方图不存在', { metric });
    }
  }

  /**
   * 设置仪表值
   * @param {string} metric - 指标名称
   * @param {number} value - 值
   * @param {Object} attributes - 属性
   */
  setGauge(metric, value, attributes = {}) {
    const gauge = this.gauges.get(metric);
    
    if (gauge) {
      gauge.record(value, attributes);
      logger.debug('仪表值已设置', { metric, value, attributes });
    } else {
      logger.warn('仪表不存在', { metric });
    }
  }

  /**
   * 获取指标统计
   * @returns {Object} 指标统计
   */
  getMetricsStats() {
    return {
      counters: this.counters.size,
      histograms: this.histograms.size,
      gauges: this.gauges.size,
      meters: this.meters.size
    };
  }
}

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  constructor() {
    this.timers = new Map();
    this.measurements = new Map();
  }

  /**
   * 开始计时
   * @param {string} name - 计时器名称
   */
  startTimer(name) {
    this.timers.set(name, {
      startTime: Date.now(),
      startHrTime: process.hrtime()
    });
  }

  /**
   * 结束计时
   * @param {string} name - 计时器名称
   * @returns {Object} 计时结果
   */
  endTimer(name) {
    const timer = this.timers.get(name);
    
    if (!timer) {
      logger.warn('计时器不存在', { name });
      return null;
    }

    const endTime = Date.now();
    const endHrTime = process.hrtime(timer.startHrTime);
    
    const result = {
      name,
      duration: endTime - timer.startTime,
      precisionDuration: endHrTime[0] * 1000 + endHrTime[1] / 1000000
    };

    this.timers.delete(name);
    this.measurements.set(name, result);

    logger.debug('计时完成', result);
    
    return result;
  }

  /**
   * 获取测量结果
   * @param {string} name - 测量名称
   * @returns {Object|null} 测量结果
   */
  getMeasurement(name) {
    return this.measurements.get(name) || null;
  }

  /**
   * 获取所有测量结果
   * @returns {Object} 所有测量结果
   */
  getAllMeasurements() {
    return Object.fromEntries(this.measurements);
  }

  /**
   * 清除测量结果
   * @param {string} name - 测量名称（可选）
   */
  clearMeasurements(name = null) {
    if (name) {
      this.measurements.delete(name);
    } else {
      this.measurements.clear();
    }
  }
}

/**
 * 使用情况跟踪器类
 */
export class UsageTracker {
  constructor() {
    this.usage = {
      sessions: 0,
      apiCalls: 0,
      toolUses: 0,
      errors: 0,
      totalCost: 0,
      totalTokens: 0,
      totalDuration: 0
    };
  }

  /**
   * 跟踪会话
   */
  trackSession() {
    this.usage.sessions++;
    globalTelemetryManager.logEvent(TELEMETRY_EVENTS.SESSION_START, {
      sessionCount: this.usage.sessions
    });
  }

  /**
   * 跟踪API调用
   * @param {Object} callData - 调用数据
   */
  trackAPICall(callData) {
    this.usage.apiCalls++;
    this.usage.totalCost += callData.cost || 0;
    this.usage.totalTokens += (callData.inputTokens || 0) + (callData.outputTokens || 0);
    this.usage.totalDuration += callData.duration || 0;

    globalTelemetryManager.logEvent(TELEMETRY_EVENTS.API_USAGE, {
      cost: callData.cost,
      inputTokens: callData.inputTokens,
      outputTokens: callData.outputTokens,
      duration: callData.duration,
      model: callData.model
    });
  }

  /**
   * 跟踪工具使用
   * @param {Object} toolData - 工具数据
   */
  trackToolUse(toolData) {
    this.usage.toolUses++;
    
    globalTelemetryManager.logEvent(TELEMETRY_EVENTS.TOOL_DECISION, {
      toolName: toolData.name,
      decision: toolData.decision,
      source: toolData.source
    });
  }

  /**
   * 跟踪错误
   * @param {Object} errorData - 错误数据
   */
  trackError(errorData) {
    this.usage.errors++;
    
    globalTelemetryManager.logEvent(TELEMETRY_EVENTS.ERROR, {
      errorType: errorData.type,
      errorMessage: errorData.message,
      stackTrace: errorData.stack
    });
  }

  /**
   * 获取使用统计
   * @returns {Object} 使用统计
   */
  getUsageStats() {
    return { ...this.usage };
  }

  /**
   * 重置使用统计
   */
  resetUsageStats() {
    this.usage = {
      sessions: 0,
      apiCalls: 0,
      toolUses: 0,
      errors: 0,
      totalCost: 0,
      totalTokens: 0,
      totalDuration: 0
    };
  }
}

/**
 * OpenTelemetry集成类
 */
export class OpenTelemetryIntegration {
  constructor() {
    this.initialized = false;
    this.meterProvider = null;
    this.loggerProvider = null;
    this.tracerProvider = null;
  }

  /**
   * 初始化OpenTelemetry
   * @original: OpenTelemetry初始化逻辑 (L8812-8856)
   * @returns {Promise<Object>} 初始化结果
   */
  async initialize() {
    if (this.initialized) {
      return { success: true };
    }

    try {
      // 这里需要导入OpenTelemetry相关模块
      // 由于这是重构的代码，我们先创建基础结构
      
      const serviceAttributes = {
        'service.name': 'claude-code',
        'service.version': APP_INFO.VERSION,
        'os.type': process.platform,
        'os.version': require('os').release(),
        'node.version': process.version
      };

      logger.debug('OpenTelemetry服务属性', serviceAttributes);

      this.initialized = true;
      
      return { success: true, attributes: serviceAttributes };

    } catch (error) {
      logger.error('OpenTelemetry初始化失败', { error: error.message });
      
      return { success: false, error: error.message };
    }
  }

  /**
   * 关闭OpenTelemetry
   * @original: OpenTelemetry关闭逻辑 (L8860-8877)
   * @returns {Promise<void>}
   */
  async shutdown() {
    if (!this.initialized) {
      return;
    }

    const timeoutMs = parseInt(process.env.CLAUDE_CODE_OTEL_SHUTDOWN_TIMEOUT_MS || '1000');
    
    try {
      const shutdownPromises = [];
      
      if (this.meterProvider) {
        shutdownPromises.push(this.meterProvider.shutdown());
      }
      
      if (this.loggerProvider) {
        shutdownPromises.push(this.loggerProvider.shutdown());
      }
      
      if (this.tracerProvider) {
        shutdownPromises.push(this.tracerProvider.shutdown());
      }

      await Promise.race([
        Promise.all(shutdownPromises),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Shutdown timeout')), timeoutMs)
        )
      ]);

      logger.info('OpenTelemetry已关闭');

    } catch (error) {
      logger.error(`OpenTelemetry关闭失败 (超时: ${timeoutMs}ms)`, { error: error.message });
      
      console.error(`
Warning: OpenTelemetry shutdown timed out after ${timeoutMs}ms

To resolve this issue, you can:
1. Increase the timeout by setting CLAUDE_CODE_OTEL_SHUTDOWN_TIMEOUT_MS env var (e.g., 5000 for 5 seconds)
2. Check if your OpenTelemetry backend is experiencing scalability issues
3. Disable OpenTelemetry by unsetting CLAUDE_CODE_ENABLE_TELEMETRY env var

Current timeout: ${timeoutMs}ms
`);
      
      throw error;
    } finally {
      this.initialized = false;
    }
  }

  /**
   * 获取计量器
   * @param {string} name - 计量器名称
   * @returns {Object} 计量器对象
   */
  getMeter(name = 'default') {
    return this.meters.get(name);
  }
}

/**
 * 全局遥测管理器实例
 */
export const globalTelemetryManager = new TelemetryManager();

/**
 * 全局指标管理器实例
 */
export const globalMetricsManager = new MetricsManager();

/**
 * 全局使用情况跟踪器实例
 */
export const globalUsageTracker = new UsageTracker();

/**
 * 全局OpenTelemetry集成实例
 */
export const globalOpenTelemetryIntegration = new OpenTelemetryIntegration();

/**
 * 便捷函数：记录遥测事件
 * @param {string} event - 事件名称
 * @param {Object} data - 事件数据
 */
export function logTelemetryEvent(event, data = {}) {
  return globalTelemetryManager.logEvent(event, data);
}

/**
 * 便捷函数：增加指标计数器
 * @param {string} metric - 指标名称
 * @param {number} value - 增加值
 * @param {Object} attributes - 属性
 */
export function incrementMetric(metric, value = 1, attributes = {}) {
  return globalMetricsManager.incrementCounter(metric, value, attributes);
}

/**
 * 便捷函数：跟踪API使用
 * @param {Object} callData - 调用数据
 */
export function trackAPIUsage(callData) {
  return globalUsageTracker.trackAPICall(callData);
}

/**
 * 便捷函数：跟踪工具使用
 * @param {Object} toolData - 工具数据
 */
export function trackToolUsage(toolData) {
  return globalUsageTracker.trackToolUse(toolData);
}
