/**
 * 代码搜索服务
 * 
 * 基于ripgrep的高性能代码搜索功能
 * 
 * @original: 行2700-2785的ripgrep集成代码
 */

import { execFile } from 'child_process';
import { join, resolve } from 'path';
import { fileURLToPath } from 'node:url';
import { logger } from '../utils/logger.js';
import { executeCommand } from '../utils/process.js';

/**
 * 检查是否在Bun环境中运行
 * @original: Kw()函数 (L2701)
 * @returns {boolean} 是否在Bun环境中
 */
function isRunningInBun() {
  return typeof Bun !== "undefined" && 
         !!Bun?.embeddedFiles && 
         Array.isArray(Bun?.embeddedFiles) && 
         (Bun?.embeddedFiles?.length ?? 0) > 0;
}

/**
 * 获取ripgrep可执行文件路径和参数
 * @original: Ts1()函数 (L2716)
 * @returns {Object} 包含rgPath和rgArgs的对象
 */
function getRipgrepConfig() {
  if (isRunningInBun()) {
    return {
      rgPath: process.execPath,
      rgArgs: ["--ripgrep"]
    };
  }

  // 获取内置ripgrep路径
  const currentFileUrl = import.meta.url;
  const currentFilePath = fileURLToPath(currentFileUrl);
  const baseDir = join(currentFilePath, "../../../");
  
  const ripgrepDir = resolve(baseDir, "vendor", "ripgrep");
  
  if (process.platform === "win32") {
    return {
      rgPath: resolve(ripgrepDir, "x64-win32", "rg.exe"),
      rgArgs: []
    };
  }
  
  return {
    rgPath: resolve(ripgrepDir, `${process.arch}-${process.platform}`, "rg"),
    rgArgs: []
  };
}

/**
 * 执行ripgrep命令
 * @original: f0Q()函数 (L2722)
 * @param {string[]} args - ripgrep参数
 * @param {string} searchPath - 搜索路径
 * @param {AbortSignal} signal - 中止信号
 * @param {Function} callback - 回调函数
 * @returns {ChildProcess} 子进程对象
 */
function executeRipgrepCommand(args, searchPath, signal, callback) {
  const { rgPath, rgArgs } = getRipgrepConfig();
  
  return execFile(rgPath, [...rgArgs, ...args, searchPath], {
    maxBuffer: 20000000, // 20MB缓冲区
    signal: signal,
    timeout: 10000 // 10秒超时
  }, callback);
}

/**
 * 执行ripgrep搜索
 * @original: PO()函数 (L2733)
 * @param {string[]} args - ripgrep参数
 * @param {string} searchPath - 搜索路径
 * @param {AbortSignal} signal - 中止信号
 * @returns {Promise<string[]>} 搜索结果行数组
 */
export async function executeRipgrep(args, searchPath, signal) {
  // 在非Bun环境中，确保ripgrep已签名（macOS）
  if (!isRunningInBun()) {
    await ensureRipgrepSigned();
  }

  return new Promise(resolve => {
    executeRipgrepCommand(args, searchPath, signal, (error, stdout, stderr) => {
      if (error) {
        // 处理不同的退出码
        if (error.code !== 1 && error.code !== 2) {
          logger.error('ripgrep执行失败:', error);
          resolve([]);
        } else if (error.code === 2 && stdout && stdout.trim().length > 0) {
          // 退出码2但有输出，返回结果
          resolve(stdout.trim().split('\n').filter(Boolean));
        } else {
          if (error.code === 2) {
            logger.error('ripgrep错误(2)，无输出:', JSON.stringify(error), stderr);
          }
          resolve([]);
        }
      } else {
        resolve(stdout.trim().split('\n').filter(Boolean));
      }
    });
  });
}

/**
 * 搜索文件列表
 * @original: k6A()函数 (L2748)
 * @param {string} searchPath - 搜索路径
 * @param {AbortSignal} signal - 中止信号
 * @param {number} limit - 结果数量限制
 * @returns {Promise<string[]>} 文件路径数组
 */
export async function searchFiles(searchPath, signal, limit) {
  try {
    const results = await executeRipgrep(["-l", "."], searchPath, signal);
    return results.slice(0, limit);
  } catch (error) {
    logger.error('文件搜索失败:', error);
    return [];
  }
}

/**
 * 统计目录中的文件数量（估算）
 * @original: uC1变量 (L2755)
 * @param {string} searchPath - 搜索路径
 * @param {AbortSignal} signal - 中止信号
 * @param {string[]} excludePatterns - 排除模式
 * @returns {Promise<number>} 估算的文件数量
 */
export async function estimateFileCount(searchPath, signal, excludePatterns = []) {
  try {
    const args = ["--files", "--hidden"];
    
    // 添加排除模式
    excludePatterns.forEach(pattern => {
      args.push("--glob", `!${pattern}`);
    });

    const results = await executeRipgrep(args, searchPath, signal);
    const totalCount = results.length;

    if (totalCount === 0) return 0;

    // 返回四舍五入到最近的10的幂
    const magnitude = Math.floor(Math.log10(totalCount));
    const base = Math.pow(10, magnitude);
    return Math.round(totalCount / base) * base;
    
  } catch (error) {
    logger.error('文件计数失败:', error);
    return 0;
  }
}

/**
 * 确保ripgrep在macOS上已签名
 * @original: h0Q()函数 (L2771)
 * @returns {Promise<void>}
 */
let ripgrepSigned = false;
async function ensureRipgrepSigned() {
  if (process.platform !== "darwin" || ripgrepSigned) {
    return;
  }

  ripgrepSigned = true;
  const { rgPath } = getRipgrepConfig();

  try {
    // 检查是否需要签名
    const codesignCheck = await executeCommand("codesign", ["-vv", "-d", rgPath], {
      preserveOutputOnError: false
    });

    const needsSigning = !codesignCheck.stdout
      .split('\n')
      .find(line => line.includes("linker-signed"));

    if (!needsSigning) return;

    // 对ripgrep进行签名
    logger.debug('对ripgrep进行代码签名');
    const signResult = await executeCommand("codesign", [
      "--sign", "-", "--force", 
      "--preserve-metadata=entitlements,requirements,flags,runtime", 
      rgPath
    ]);

    if (signResult.code !== 0) {
      throw new Error(`签名失败: ${signResult.stdout} ${signResult.stderr}`);
    }

    // 移除隔离属性
    const xattrResult = await executeCommand("xattr", ["-d", "com.apple.quarantine", rgPath]);
    
    if (xattrResult.code !== 0) {
      throw new Error(`移除隔离属性失败: ${xattrResult.stdout} ${xattrResult.stderr}`);
    }

    logger.debug('ripgrep签名完成');
    
  } catch (error) {
    logger.error('ripgrep签名失败:', error);
  }
}

/**
 * 在文本中搜索模式
 * @param {string} pattern - 搜索模式
 * @param {string} searchPath - 搜索路径
 * @param {Object} options - 搜索选项
 * @returns {Promise<string[]>} 匹配的行
 */
export async function searchInFiles(pattern, searchPath, options = {}) {
  const {
    caseSensitive = false,
    wholeWord = false,
    regex = false,
    maxResults = 1000,
    signal = null
  } = options;

  const args = [];

  if (!caseSensitive) args.push("-i");
  if (wholeWord) args.push("-w");
  if (!regex) args.push("-F"); // 固定字符串搜索
  if (maxResults) args.push("-m", maxResults.toString());

  args.push(pattern);

  try {
    return await executeRipgrep(args, searchPath, signal);
  } catch (error) {
    logger.error('文本搜索失败:', error);
    return [];
  }
}

/**
 * 模糊搜索引擎类
 * @original: Ef1类 (L33830-34081)
 */
export class FuzzySearchEngine {
  constructor(options = {}) {
    this.options = {
      threshold: 0.6,
      distance: 100,
      maxPatternLength: 32,
      keys: ['title', 'content'],
      ...options
    };

    this.index = new Map();
    this.documents = [];
  }

  /**
   * 添加文档到索引
   * @param {Array} documents - 文档数组
   * @returns {Promise<void>}
   */
  async addDocuments(documents) {
    try {
      this.documents = documents;
      await this.buildIndex();

      logger.debug('模糊搜索索引构建完成', { documentCount: documents.length });

    } catch (error) {
      logger.error('构建模糊搜索索引失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 构建搜索索引
   * @returns {Promise<void>}
   */
  async buildIndex() {
    this.index.clear();

    for (let docIndex = 0; docIndex < this.documents.length; docIndex++) {
      const document = this.documents[docIndex];

      for (const key of this.options.keys) {
        const value = this.getValueByKey(document, key);

        if (value && typeof value === 'string') {
          const tokens = this.tokenize(value);

          for (const token of tokens) {
            if (!this.index.has(token)) {
              this.index.set(token, new Set());
            }

            this.index.get(token).add(docIndex);
          }
        }
      }
    }
  }

  /**
   * 模糊搜索
   * @param {string} query - 搜索查询
   * @param {Object} options - 搜索选项
   * @returns {Promise<Array>} 搜索结果
   */
  async search(query, options = {}) {
    try {
      const searchOptions = { ...this.options, ...options };
      const queryTokens = this.tokenize(query);
      const candidates = new Set();

      // 查找候选文档
      for (const token of queryTokens) {
        const matches = this.index.get(token);
        if (matches) {
          for (const docIndex of matches) {
            candidates.add(docIndex);
          }
        }
      }

      // 计算相似度分数
      const results = Array.from(candidates)
        .map(docIndex => {
          const document = this.documents[docIndex];
          const score = this.calculateDocumentScore(query, document);

          return {
            item: document,
            score,
            refIndex: docIndex
          };
        })
        .filter(result => result.score >= searchOptions.threshold)
        .sort((a, b) => b.score - a.score)
        .slice(0, searchOptions.maxResults || 100);

      return results;

    } catch (error) {
      logger.error('模糊搜索失败', { query, error: error.message });
      throw error;
    }
  }

  /**
   * 计算文档分数
   * @param {string} query - 查询字符串
   * @param {Object} document - 文档对象
   * @returns {number} 分数
   */
  calculateDocumentScore(query, document) {
    let maxScore = 0;

    for (const key of this.options.keys) {
      const value = this.getValueByKey(document, key);

      if (value && typeof value === 'string') {
        const score = this.calculateStringSimilarity(query, value);
        maxScore = Math.max(maxScore, score);
      }
    }

    return maxScore;
  }

  /**
   * 计算字符串相似度
   * @param {string} pattern - 模式字符串
   * @param {string} text - 目标字符串
   * @returns {number} 相似度分数
   */
  calculateStringSimilarity(pattern, text) {
    const lowerPattern = pattern.toLowerCase();
    const lowerText = text.toLowerCase();

    if (lowerText.includes(lowerPattern)) {
      return 1 - (lowerText.indexOf(lowerPattern) / lowerText.length);
    }

    // 使用编辑距离算法
    const distance = this.levenshteinDistance(lowerPattern, lowerText);
    const maxLength = Math.max(pattern.length, text.length);

    return 1 - (distance / maxLength);
  }

  /**
   * 计算编辑距离
   * @param {string} a - 字符串A
   * @param {string} b - 字符串B
   * @returns {number} 编辑距离
   */
  levenshteinDistance(a, b) {
    const matrix = Array(a.length + 1).fill(null).map(() => Array(b.length + 1).fill(0));

    for (let i = 0; i <= a.length; i++) matrix[i][0] = i;
    for (let j = 0; j <= b.length; j++) matrix[0][j] = j;

    for (let i = 1; i <= a.length; i++) {
      for (let j = 1; j <= b.length; j++) {
        const cost = a[i - 1] === b[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost
        );
      }
    }

    return matrix[a.length][b.length];
  }

  /**
   * 分词
   * @param {string} text - 文本
   * @returns {Array} 词汇数组
   */
  tokenize(text) {
    return text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 0);
  }

  /**
   * 根据键获取值
   * @param {Object} obj - 对象
   * @param {string} key - 键
   * @returns {*} 值
   */
  getValueByKey(obj, key) {
    const keys = key.split('.');
    let current = obj;

    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k];
      } else {
        return undefined;
      }
    }

    return current;
  }
}

/**
 * 全局模糊搜索引擎实例
 */
export const globalFuzzySearchEngine = new FuzzySearchEngine();

/**
 * 便捷函数：模糊搜索
 * @param {Array} documents - 文档数组
 * @param {string} query - 搜索查询
 * @param {Object} options - 搜索选项
 * @returns {Promise<Array>} 搜索结果
 */
export async function fuzzySearch(documents, query, options = {}) {
  const engine = new FuzzySearchEngine(options);
  await engine.addDocuments(documents);
  return engine.search(query, options);
}
