/**
 * 图像处理服务
 * 
 * 管理图像的读取、处理、压缩和格式转换
 * 
 * @original: 图像处理相关代码 (L25680-25727, L9683-9845)
 */

import { logger } from '../utils/logger.js';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join, extname } from 'path';

/**
 * 支持的图像格式
 */
export const SUPPORTED_IMAGE_FORMATS = {
  PNG: 'png',
  JPEG: 'jpeg',
  JPG: 'jpg',
  WEBP: 'webp',
  GIF: 'gif',
  BMP: 'bmp',
  TIFF: 'tiff'
};

/**
 * 图像压缩质量级别
 */
export const COMPRESSION_LEVELS = {
  HIGH: 1.0,
  MEDIUM_HIGH: 0.75,
  MEDIUM: 0.5,
  LOW: 0.25
};

/**
 * 图像处理器类
 */
export class ImageProcessor {
  constructor() {
    this.sharp = null;
    this.initialized = false;
  }

  /**
   * 初始化Sharp库
   * @original: fPB()函数 (L25074-25076)
   * @returns {Promise<Object>} Sharp实例
   */
  async initializeSharp() {
    if (this.sharp) {
      return this.sharp;
    }

    try {
      // 检查是否为Bun环境
      if (this.isBunEnvironment()) {
        try {
          const sharpModule = await import('sharp');
          this.sharp = sharpModule.default || sharpModule;
          this.initialized = true;
          return this.sharp;
        } catch (error) {
          logger.warn('Bun环境下Sharp加载失败，尝试其他方式', { error: error.message });
        }
      }

      // 标准Node.js环境
      const sharpModule = await import('sharp');
      this.sharp = sharpModule.default || sharpModule;
      this.initialized = true;
      
      logger.debug('Sharp库初始化成功');
      return this.sharp;

    } catch (error) {
      logger.error('Sharp库初始化失败', { error: error.message });
      throw new Error(`图像处理库初始化失败: ${error.message}`);
    }
  }

  /**
   * 检查是否为Bun环境
   * @returns {boolean} 是否为Bun环境
   */
  isBunEnvironment() {
    return typeof Bun !== 'undefined' && !!Bun?.embeddedFiles;
  }

  /**
   * 读取图像文件
   * @original: sF8()函数 (L25711-25727)
   * @param {string} filePath - 图像文件路径
   * @returns {Promise<Object>} 图像数据对象
   */
  async readImageFile(filePath) {
    try {
      if (!existsSync(filePath)) {
        throw new Error(`图像文件不存在: ${filePath}`);
      }

      const imageBuffer = readFileSync(filePath);
      const sharp = await this.initializeSharp();
      
      // 获取图像元数据
      const metadata = await sharp(imageBuffer).metadata();
      
      return {
        buffer: imageBuffer,
        metadata,
        sharp,
        filePath,
        format: metadata.format,
        width: metadata.width,
        height: metadata.height,
        size: imageBuffer.length
      };

    } catch (error) {
      logger.error('读取图像文件失败', { filePath, error: error.message });
      throw error;
    }
  }

  /**
   * 压缩图像
   * @original: lF8()函数 (L25645-25679)
   * @param {Object} imageData - 图像数据对象
   * @param {Array} qualityLevels - 压缩质量级别数组
   * @returns {Promise<Object>} 压缩后的图像数据
   */
  async compressImage(imageData, qualityLevels = Object.values(COMPRESSION_LEVELS)) {
    const { sharp, buffer: originalBuffer } = imageData;
    
    for (const quality of qualityLevels) {
      try {
        logger.debug('尝试图像压缩', { quality });

        let compressedBuffer;
        
        if (imageData.format === 'jpeg' || imageData.format === 'jpg') {
          compressedBuffer = await sharp(originalBuffer)
            .jpeg({ quality: Math.round(quality * 100) })
            .toBuffer();
        } else if (imageData.format === 'png') {
          compressedBuffer = await sharp(originalBuffer)
            .png({ quality: Math.round(quality * 100) })
            .toBuffer();
        } else if (imageData.format === 'webp') {
          compressedBuffer = await sharp(originalBuffer)
            .webp({ quality: Math.round(quality * 100) })
            .toBuffer();
        } else {
          // 对于其他格式，转换为JPEG
          compressedBuffer = await sharp(originalBuffer)
            .jpeg({ quality: Math.round(quality * 100) })
            .toBuffer();
        }

        const compressionRatio = compressedBuffer.length / originalBuffer.length;
        
        logger.debug('图像压缩完成', { 
          quality,
          originalSize: originalBuffer.length,
          compressedSize: compressedBuffer.length,
          compressionRatio
        });

        return {
          ...imageData,
          buffer: compressedBuffer,
          originalBuffer,
          compressionRatio,
          quality,
          size: compressedBuffer.length
        };

      } catch (error) {
        logger.warn('图像压缩失败，尝试下一个质量级别', { 
          quality, 
          error: error.message 
        });
        continue;
      }
    }

    throw new Error('所有压缩质量级别都失败');
  }

  /**
   * 调整图像尺寸
   * @original: iF8()函数 (L25680-25691)
   * @param {Object} imageData - 图像数据对象
   * @param {number} maxWidth - 最大宽度
   * @param {number} maxHeight - 最大高度
   * @param {Object} options - 调整选项
   * @returns {Promise<Buffer>} 调整后的图像缓冲区
   */
  async resizeImage(imageData, maxWidth = 800, maxHeight = 800, options = {}) {
    const { sharp, buffer } = imageData;
    const { fit = 'inside', withoutEnlargement = true } = options;

    try {
      const resizedBuffer = await sharp(buffer)
        .resize(maxWidth, maxHeight, {
          fit,
          withoutEnlargement
        })
        .toBuffer();

      logger.debug('图像尺寸调整完成', {
        originalSize: `${imageData.width}x${imageData.height}`,
        targetSize: `${maxWidth}x${maxHeight}`,
        outputSize: resizedBuffer.length
      });

      return resizedBuffer;

    } catch (error) {
      logger.error('图像尺寸调整失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 创建缩略图
   * @original: nF8()函数 (L25692-25701)
   * @param {Object} imageData - 图像数据对象
   * @param {number} size - 缩略图尺寸
   * @returns {Promise<Buffer>} 缩略图缓冲区
   */
  async createThumbnail(imageData, size = 600) {
    return this.resizeImage(imageData, size, size, {
      fit: 'inside',
      withoutEnlargement: true
    });
  }

  /**
   * 创建小图标
   * @original: aF8()函数 (L25702-25710)
   * @param {Object} imageData - 图像数据对象
   * @param {number} size - 图标尺寸
   * @returns {Promise<Buffer>} 图标缓冲区
   */
  async createIcon(imageData, size = 400) {
    return this.resizeImage(imageData, size, size, {
      fit: 'inside',
      withoutEnlargement: true
    });
  }

  /**
   * 检查图像文件大小
   * @original: rF8()函数 (L25728-25752)
   * @param {string} filePath - 图像文件路径
   * @param {number} maxSizeBytes - 最大文件大小（字节）
   * @returns {Promise<Object>} 检查结果
   */
  async checkImageSize(filePath, maxSizeBytes = 5 * 1024 * 1024) { // 5MB默认
    try {
      const stats = require('fs').statSync(filePath);
      const fileSize = stats.size;

      if (fileSize > maxSizeBytes) {
        return {
          valid: false,
          fileSize,
          maxSizeBytes,
          exceedsLimit: true,
          message: `图像文件大小 (${this.formatFileSize(fileSize)}) 超过限制 (${this.formatFileSize(maxSizeBytes)})`
        };
      }

      return {
        valid: true,
        fileSize,
        maxSizeBytes,
        exceedsLimit: false
      };

    } catch (error) {
      logger.error('检查图像文件大小失败', { filePath, error: error.message });
      throw error;
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化的文件大小
   */
  formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * 检查是否为支持的图像格式
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否为支持的格式
   */
  isSupportedImageFormat(filePath) {
    const ext = extname(filePath).toLowerCase().slice(1);
    return Object.values(SUPPORTED_IMAGE_FORMATS).includes(ext);
  }

  /**
   * 获取图像MIME类型
   * @param {string} format - 图像格式
   * @returns {string} MIME类型
   */
  getMimeType(format) {
    const mimeTypes = {
      [SUPPORTED_IMAGE_FORMATS.PNG]: 'image/png',
      [SUPPORTED_IMAGE_FORMATS.JPEG]: 'image/jpeg',
      [SUPPORTED_IMAGE_FORMATS.JPG]: 'image/jpeg',
      [SUPPORTED_IMAGE_FORMATS.WEBP]: 'image/webp',
      [SUPPORTED_IMAGE_FORMATS.GIF]: 'image/gif',
      [SUPPORTED_IMAGE_FORMATS.BMP]: 'image/bmp',
      [SUPPORTED_IMAGE_FORMATS.TIFF]: 'image/tiff'
    };

    return mimeTypes[format] || 'application/octet-stream';
  }
}

/**
 * 剪贴板图像管理器类
 */
export class ClipboardImageManager {
  constructor() {
    this.tempImagePath = null;
  }

  /**
   * 从剪贴板获取图像
   * @original: k71()函数 (L9683-9770)
   * @param {string} tempPath - 临时文件路径
   * @returns {Promise<Object>} 剪贴板图像信息
   */
  async getClipboardImage(tempPath) {
    try {
      const commands = this.getClipboardCommands(tempPath);
      
      // 检查剪贴板是否有图像
      const checkResult = await this.executeCommand(commands.checkImage);
      
      if (checkResult.code !== 0) {
        return {
          hasImage: false,
          message: '剪贴板中没有图像'
        };
      }

      // 保存图像到临时文件
      const saveResult = await this.executeCommand(commands.saveImage);
      
      if (saveResult.code !== 0) {
        throw new Error(`保存剪贴板图像失败: ${saveResult.stderr}`);
      }

      this.tempImagePath = tempPath;

      return {
        hasImage: true,
        filePath: tempPath,
        message: '成功从剪贴板获取图像'
      };

    } catch (error) {
      logger.error('从剪贴板获取图像失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 从剪贴板获取文件路径
   * @original: Yj1()函数 (L9771-9845)
   * @returns {Promise<string|null>} 文件路径
   */
  async getClipboardFilePath() {
    try {
      const command = "osascript -e 'get POSIX path of (the clipboard as «class furl»)'";
      const result = await this.executeCommand(command);

      if (result.code === 0 && result.stdout) {
        const filePath = result.stdout.trim();
        
        if (existsSync(filePath)) {
          return filePath;
        }
      }

      return null;

    } catch (error) {
      logger.warn('从剪贴板获取文件路径失败', { error: error.message });
      return null;
    }
  }

  /**
   * 获取剪贴板命令
   * @param {string} tempPath - 临时文件路径
   * @returns {Object} 命令对象
   */
  getClipboardCommands(tempPath) {
    if (process.platform === 'darwin') {
      return {
        checkImage: "osascript -e 'the clipboard as «class PNGf»'",
        saveImage: `osascript -e 'set png_data to (the clipboard as «class PNGf»)' -e 'set fp to open for access POSIX file "${tempPath}" with write permission' -e 'write png_data to fp' -e 'close access fp'`,
        getPath: "osascript -e 'get POSIX path of (the clipboard as «class furl»)'",
        deleteFile: `rm -f "${tempPath}"`
      };
    }

    // 其他平台的命令可以在这里添加
    throw new Error(`不支持的平台: ${process.platform}`);
  }

  /**
   * 执行命令
   * @param {string} command - 要执行的命令
   * @returns {Promise<Object>} 执行结果
   */
  async executeCommand(command) {
    const { executeCommand } = await import('../utils/process.js');
    
    return executeCommand('sh', ['-c', command], {
      timeout: 10000
    });
  }

  /**
   * 清理临时图像文件
   */
  cleanupTempImage() {
    if (this.tempImagePath && existsSync(this.tempImagePath)) {
      try {
        const fs = require('fs');
        fs.unlinkSync(this.tempImagePath);
        logger.debug('临时图像文件已清理', { path: this.tempImagePath });
        this.tempImagePath = null;
      } catch (error) {
        logger.warn('清理临时图像文件失败', { 
          path: this.tempImagePath, 
          error: error.message 
        });
      }
    }
  }
}

/**
 * 图像优化器类
 */
export class ImageOptimizer {
  constructor(processor) {
    this.processor = processor || new ImageProcessor();
  }

  /**
   * 优化图像以适应API限制
   * @original: 图像优化逻辑 (L25680-25727)
   * @param {string} filePath - 图像文件路径
   * @param {Object} options - 优化选项
   * @returns {Promise<Object>} 优化后的图像数据
   */
  async optimizeForAPI(filePath, options = {}) {
    const {
      maxSizeBytes = 5 * 1024 * 1024, // 5MB
      maxWidth = 800,
      maxHeight = 800,
      targetFormat = 'jpeg'
    } = options;

    try {
      // 读取原始图像
      const imageData = await this.processor.readImageFile(filePath);
      
      // 检查文件大小
      if (imageData.size <= maxSizeBytes) {
        logger.debug('图像文件大小在限制内，无需优化');
        return imageData;
      }

      // 调整尺寸
      const resizedBuffer = await this.processor.resizeImage(
        imageData, 
        maxWidth, 
        maxHeight
      );

      // 如果调整尺寸后仍然过大，进行压缩
      if (resizedBuffer.length > maxSizeBytes) {
        const compressedData = await this.processor.compressImage({
          ...imageData,
          buffer: resizedBuffer
        });

        return compressedData;
      }

      return {
        ...imageData,
        buffer: resizedBuffer,
        size: resizedBuffer.length,
        optimized: true
      };

    } catch (error) {
      logger.error('图像优化失败', { filePath, error: error.message });
      throw error;
    }
  }

  /**
   * 批量优化图像
   * @param {Array} filePaths - 图像文件路径数组
   * @param {Object} options - 优化选项
   * @returns {Promise<Array>} 优化结果数组
   */
  async optimizeBatch(filePaths, options = {}) {
    const results = [];

    for (const filePath of filePaths) {
      try {
        const optimizedImage = await this.optimizeForAPI(filePath, options);
        results.push({
          filePath,
          success: true,
          data: optimizedImage
        });
      } catch (error) {
        results.push({
          filePath,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }
}

/**
 * 全局图像处理器实例
 */
export const globalImageProcessor = new ImageProcessor();

/**
 * 全局剪贴板图像管理器实例
 */
export const globalClipboardImageManager = new ClipboardImageManager();

/**
 * 全局图像优化器实例
 */
export const globalImageOptimizer = new ImageOptimizer(globalImageProcessor);

/**
 * 便捷函数：读取并优化图像
 * @param {string} filePath - 图像文件路径
 * @param {Object} options - 优化选项
 * @returns {Promise<Object>} 优化后的图像数据
 */
export async function readAndOptimizeImage(filePath, options = {}) {
  return globalImageOptimizer.optimizeForAPI(filePath, options);
}

/**
 * 便捷函数：从剪贴板获取图像
 * @param {string} tempPath - 临时文件路径
 * @returns {Promise<Object>} 剪贴板图像信息
 */
export async function getImageFromClipboard(tempPath) {
  return globalClipboardImageManager.getClipboardImage(tempPath);
}

/**
 * 便捷函数：检查图像文件大小
 * @param {string} filePath - 图像文件路径
 * @param {number} maxSizeBytes - 最大文件大小
 * @returns {Promise<Object>} 检查结果
 */
export async function checkImageFileSize(filePath, maxSizeBytes) {
  return globalImageProcessor.checkImageSize(filePath, maxSizeBytes);
}
