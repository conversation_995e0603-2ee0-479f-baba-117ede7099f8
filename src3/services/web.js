/**
 * 网络请求和URL处理服务
 * 
 * 管理HTTP请求、URL验证、域名安全检查等网络相关功能
 * 
 * @original: 网络请求相关代码 (L32660-32800, L53391-53837)
 */

import { logger } from '../utils/logger.js';
import { logTelemetryEvent } from './telemetry.js';

/**
 * 网络错误类型
 */
export class NetworkFetchError extends Error {
  constructor(url) {
    super(`Claude Code无法从 ${url} 获取数据`);
    this.name = 'NetworkFetchError';
    this.url = url;
  }
}

/**
 * 域名验证错误类型
 */
export class DomainVerificationError extends Error {
  constructor(domain) {
    super(`无法验证域名 ${domain} 是否安全。这可能是由于网络限制或企业安全策略阻止了claude.ai。`);
    this.name = 'DomainVerificationError';
    this.domain = domain;
  }
}

/**
 * 网络请求管理器类
 */
export class WebRequestManager {
  constructor() {
    this.axios = null;
    this.domainCache = new Map();
    this.cacheTimeout = 300000; // 5分钟缓存
  }

  /**
   * 初始化HTTP客户端
   * @returns {Promise<Object>} Axios实例
   */
  async initializeHTTPClient() {
    if (this.axios) {
      return this.axios;
    }

    try {
      const axios = await import('axios');
      this.axios = axios.default || axios;
      
      // 设置默认配置
      this.axios.defaults.timeout = 30000;
      this.axios.defaults.headers.common['User-Agent'] = 'Claude-Code/1.0';
      
      logger.debug('HTTP客户端初始化成功');
      return this.axios;

    } catch (error) {
      logger.error('HTTP客户端初始化失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 验证URL是否安全
   * @original: IK8()函数 (L32693-32726)
   * @param {string} url - 要验证的URL
   * @returns {Promise<boolean>} URL是否安全
   */
  async validateURLSafety(url) {
    try {
      const domain = this.extractDomain(url);
      
      if (!domain) {
        throw new Error('无效的URL格式');
      }

      // 检查缓存
      const cached = this.getDomainFromCache(domain);
      if (cached !== null) {
        return cached;
      }

      // 调用Claude.ai API验证域名
      const axios = await this.initializeHTTPClient();
      const response = await axios.get(
        `https://claude.ai/api/web/domain_info?domain=${encodeURIComponent(domain)}`
      );

      if (response.status === 200 && response.data) {
        const isSafe = response.data.safe === true;
        this.setDomainCache(domain, isSafe);
        
        logger.debug('域名安全验证完成', { domain, isSafe });
        return isSafe;
      }

      throw new DomainVerificationError(domain);

    } catch (error) {
      if (error instanceof DomainVerificationError) {
        throw error;
      }

      logger.error('URL安全验证失败', { url, error: error.message });
      throw new DomainVerificationError(this.extractDomain(url));
    }
  }

  /**
   * 获取网页内容
   * @original: hxB()函数 (L32727-32752)
   * @param {string} url - 目标URL
   * @param {Object} options - 请求选项
   * @param {Object} context - 请求上下文
   * @returns {Promise<Object>} 网页内容
   */
  async fetchWebContent(url, options = {}, context = {}) {
    try {
      // 验证URL格式
      if (!this.isValidURL(url)) {
        throw new Error('无效的URL格式');
      }

      // 验证域名安全性
      await this.validateURLSafety(url);

      const axios = await this.initializeHTTPClient();
      
      const requestConfig = {
        url,
        method: 'GET',
        timeout: options.timeout || 30000,
        maxRedirects: options.maxRedirects || 5,
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'DNT': '1',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          ...options.headers
        }
      };

      logger.debug('开始获取网页内容', { url });

      const response = await axios(requestConfig);

      if (response.status !== 200) {
        throw new NetworkFetchError(url);
      }

      const content = {
        url,
        status: response.status,
        headers: response.headers,
        data: response.data,
        contentType: response.headers['content-type'] || '',
        contentLength: response.headers['content-length'] || response.data.length,
        fetchedAt: new Date().toISOString()
      };

      logger.info('网页内容获取成功', { 
        url, 
        status: response.status,
        contentLength: content.contentLength
      });

      logTelemetryEvent('web_content_fetched', {
        domain: this.extractDomain(url),
        status: response.status,
        contentLength: content.contentLength
      });

      return content;

    } catch (error) {
      if (error instanceof NetworkFetchError || error instanceof DomainVerificationError) {
        throw error;
      }

      logger.error('获取网页内容失败', { url, error: error.message });
      throw new NetworkFetchError(url);
    }
  }

  /**
   * 下载文件
   * @original: gxB()函数 (L32753-32800)
   * @param {string} url - 文件URL
   * @param {string} outputPath - 输出路径
   * @param {Object} options - 下载选项
   * @returns {Promise<Object>} 下载结果
   */
  async downloadFile(url, outputPath, options = {}) {
    try {
      if (!this.isValidURL(url)) {
        throw new Error('无效的URL格式');
      }

      // 验证域名安全性
      await this.validateURLSafety(url);

      const axios = await this.initializeHTTPClient();
      const fs = require('fs');

      logger.info('开始下载文件', { url, outputPath });

      const response = await axios({
        url,
        method: 'GET',
        responseType: 'stream',
        timeout: options.timeout || 60000,
        headers: options.headers || {}
      });

      if (response.status !== 200) {
        throw new NetworkFetchError(url);
      }

      // 创建输出目录
      const outputDir = require('path').dirname(outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      // 写入文件
      const writer = fs.createWriteStream(outputPath);
      response.data.pipe(writer);

      return new Promise((resolve, reject) => {
        writer.on('finish', () => {
          const stats = fs.statSync(outputPath);
          
          logger.info('文件下载完成', { 
            url, 
            outputPath, 
            fileSize: stats.size 
          });

          resolve({
            success: true,
            url,
            outputPath,
            fileSize: stats.size,
            downloadedAt: new Date().toISOString()
          });
        });

        writer.on('error', (error) => {
          logger.error('文件写入失败', { outputPath, error: error.message });
          reject(error);
        });

        response.data.on('error', (error) => {
          logger.error('下载流错误', { url, error: error.message });
          reject(error);
        });
      });

    } catch (error) {
      logger.error('文件下载失败', { url, outputPath, error: error.message });
      throw error;
    }
  }

  /**
   * 验证URL格式
   * @param {string} url - URL字符串
   * @returns {boolean} 是否为有效URL
   */
  isValidURL(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch (error) {
      return false;
    }
  }

  /**
   * 提取域名
   * @param {string} url - URL字符串
   * @returns {string|null} 域名
   */
  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      return null;
    }
  }

  /**
   * 从缓存获取域名验证结果
   * @param {string} domain - 域名
   * @returns {boolean|null} 缓存的验证结果
   */
  getDomainFromCache(domain) {
    const cached = this.domainCache.get(domain);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.isSafe;
    }

    return null;
  }

  /**
   * 设置域名验证缓存
   * @param {string} domain - 域名
   * @param {boolean} isSafe - 是否安全
   */
  setDomainCache(domain, isSafe) {
    this.domainCache.set(domain, {
      isSafe,
      timestamp: Date.now()
    });
  }

  /**
   * 清除域名缓存
   */
  clearDomainCache() {
    this.domainCache.clear();
    logger.debug('域名缓存已清除');
  }
}

/**
 * URL解析器类
 */
export class URLParser {
  /**
   * 解析URL组件
   * @param {string} url - URL字符串
   * @returns {Object} URL组件
   */
  parseURL(url) {
    try {
      const urlObj = new URL(url);
      
      return {
        protocol: urlObj.protocol,
        hostname: urlObj.hostname,
        port: urlObj.port,
        pathname: urlObj.pathname,
        search: urlObj.search,
        hash: urlObj.hash,
        origin: urlObj.origin,
        href: urlObj.href
      };

    } catch (error) {
      logger.error('URL解析失败', { url, error: error.message });
      return null;
    }
  }

  /**
   * 构建URL
   * @param {Object} components - URL组件
   * @returns {string} 构建的URL
   */
  buildURL(components) {
    try {
      const { protocol, hostname, port, pathname, search, hash } = components;
      
      let url = `${protocol}//${hostname}`;
      
      if (port) {
        url += `:${port}`;
      }
      
      if (pathname) {
        url += pathname;
      }
      
      if (search) {
        url += search.startsWith('?') ? search : `?${search}`;
      }
      
      if (hash) {
        url += hash.startsWith('#') ? hash : `#${hash}`;
      }

      return url;

    } catch (error) {
      logger.error('URL构建失败', { components, error: error.message });
      throw error;
    }
  }

  /**
   * 规范化URL
   * @param {string} url - 原始URL
   * @returns {string} 规范化的URL
   */
  normalizeURL(url) {
    try {
      const urlObj = new URL(url);
      
      // 移除默认端口
      if ((urlObj.protocol === 'http:' && urlObj.port === '80') ||
          (urlObj.protocol === 'https:' && urlObj.port === '443')) {
        urlObj.port = '';
      }

      // 规范化路径
      if (urlObj.pathname === '') {
        urlObj.pathname = '/';
      }

      return urlObj.href;

    } catch (error) {
      logger.error('URL规范化失败', { url, error: error.message });
      return url;
    }
  }
}

/**
 * 全局网络请求管理器实例
 */
export const globalWebRequestManager = new WebRequestManager();

/**
 * 全局URL解析器实例
 */
export const globalURLParser = new URLParser();

/**
 * 便捷函数：获取网页内容
 * @param {string} url - 目标URL
 * @param {Object} options - 请求选项
 * @param {Object} context - 请求上下文
 * @returns {Promise<Object>} 网页内容
 */
export async function fetchWebContent(url, options = {}, context = {}) {
  return globalWebRequestManager.fetchWebContent(url, options, context);
}

/**
 * 便捷函数：下载文件
 * @param {string} url - 文件URL
 * @param {string} outputPath - 输出路径
 * @param {Object} options - 下载选项
 * @returns {Promise<Object>} 下载结果
 */
export async function downloadFile(url, outputPath, options = {}) {
  return globalWebRequestManager.downloadFile(url, outputPath, options);
}

/**
 * 便捷函数：验证URL安全性
 * @param {string} url - 要验证的URL
 * @returns {Promise<boolean>} URL是否安全
 */
export async function validateURLSafety(url) {
  return globalWebRequestManager.validateURLSafety(url);
}

/**
 * 便捷函数：解析URL
 * @param {string} url - URL字符串
 * @returns {Object} URL组件
 */
export function parseURL(url) {
  return globalURLParser.parseURL(url);
}

/**
 * 便捷函数：规范化URL
 * @param {string} url - 原始URL
 * @returns {string} 规范化的URL
 */
export function normalizeURL(url) {
  return globalURLParser.normalizeURL(url);
}
