/**
 * 自动更新服务
 * 
 * 管理Claude Code的版本检查、自动更新、安装管理等功能
 * 
 * @original: 更新相关代码 (L56640-56836, L51960-52040, L57878-57915)
 */

import { logger } from '../utils/logger.js';
import { executeCommand } from '../utils/process.js';
import { APP_INFO } from '../config/constants.js';
import { getConfig, setConfig } from '../config/settings.js';
import { logTelemetryEvent } from './telemetry.js';

/**
 * 安装类型枚举
 */
export const INSTALLATION_TYPES = {
  NATIVE: 'native',
  NPM_LOCAL: 'npm-local',
  NPM_GLOBAL: 'npm-global',
  DEVELOPMENT: 'development',
  UNKNOWN: 'unknown'
};

/**
 * 更新状态枚举
 */
export const UPDATE_STATUS = {
  SUCCESS: 'success',
  NO_PERMISSIONS: 'no_permissions',
  INSTALL_FAILED: 'install_failed',
  IN_PROGRESS: 'in_progress',
  UP_TO_DATE: 'up_to_date'
};

/**
 * 安装状态枚举
 */
export const INSTALL_STATUS = {
  CHECKING: 'checking',
  CLEANING_NPM: 'cleaning-npm',
  INSTALLING: 'installing',
  SETTING_UP: 'setting-up',
  SET_UP: 'set-up',
  SUCCESS: 'success',
  ERROR: 'error'
};

/**
 * 自动更新器类
 */
export class AutoUpdater {
  constructor(options = {}) {
    this.checkInterval = options.checkInterval || 1800000; // 30分钟
    this.timeout = options.timeout || 30000;
    this.enabled = this.isAutoUpdateEnabled();
    this.isChecking = false;
    this.lastCheckTime = null;
    this.checkTimer = null;
  }

  /**
   * 检查是否启用自动更新
   * @original: Do()函数 (L6467)
   * @returns {boolean} 是否启用自动更新
   */
  isAutoUpdateEnabled() {
    if (process.env.DISABLE_AUTOUPDATER || 
        process.env.CLAUDE_CODE_DISABLE_NONESSENTIAL_TRAFFIC) {
      return false;
    }

    const config = getConfig('autoUpdates');
    return config !== false;
  }

  /**
   * 检查更新
   * @original: LcB()函数 (L56642-56836)
   * @returns {Promise<Object>} 更新检查结果
   */
  async checkForUpdates() {
    if (this.isChecking) {
      return { status: 'already_checking' };
    }

    this.isChecking = true;
    this.lastCheckTime = Date.now();

    try {
      logTelemetryEvent('tengu_update_check', {});
      
      logger.info(`当前版本: ${APP_INFO.VERSION}`);
      logger.info('检查更新中...');

      // 运行诊断
      const diagnostic = await this.runDiagnostic();
      logger.debug('更新诊断完成', diagnostic);

      // 检查多重安装
      if (diagnostic.multipleInstallations.length > 1) {
        logger.warn('发现多个安装', { 
          installations: diagnostic.multipleInstallations 
        });
      }

      // 显示警告
      for (const warning of diagnostic.warnings) {
        logger.warn(`更新警告: ${warning.issue}`, { fix: warning.fix });
      }

      // 更新安装方法配置
      await this.updateInstallMethodConfig(diagnostic);

      // 根据安装类型执行更新检查
      return await this.performUpdateCheck(diagnostic);

    } catch (error) {
      logger.error('更新检查失败', { error: error.message });
      
      return {
        status: 'error',
        error: error.message
      };
    } finally {
      this.isChecking = false;
    }
  }

  /**
   * 运行诊断
   * @returns {Promise<Object>} 诊断结果
   */
  async runDiagnostic() {
    // 实现诊断逻辑
    return {
      installationType: await this.detectInstallationType(),
      configInstallMethod: getConfig('installMethod') || 'not set',
      multipleInstallations: [],
      warnings: []
    };
  }

  /**
   * 检测安装类型
   * @returns {Promise<string>} 安装类型
   */
  async detectInstallationType() {
    // 检查是否为开发构建
    if (process.env.NODE_ENV === 'development') {
      return INSTALLATION_TYPES.DEVELOPMENT;
    }

    // 检查是否为本地npm安装
    if (await this.isLocalNpmInstallation()) {
      return INSTALLATION_TYPES.NPM_LOCAL;
    }

    // 检查是否为全局npm安装
    if (await this.isGlobalNpmInstallation()) {
      return INSTALLATION_TYPES.NPM_GLOBAL;
    }

    // 检查是否为原生安装
    if (await this.isNativeInstallation()) {
      return INSTALLATION_TYPES.NATIVE;
    }

    return INSTALLATION_TYPES.UNKNOWN;
  }

  /**
   * 检查是否为本地npm安装
   * @returns {Promise<boolean>} 是否为本地npm安装
   */
  async isLocalNpmInstallation() {
    // 实现本地npm安装检测逻辑
    return false;
  }

  /**
   * 检查是否为全局npm安装
   * @returns {Promise<boolean>} 是否为全局npm安装
   */
  async isGlobalNpmInstallation() {
    // 实现全局npm安装检测逻辑
    return false;
  }

  /**
   * 检查是否为原生安装
   * @returns {Promise<boolean>} 是否为原生安装
   */
  async isNativeInstallation() {
    // 实现原生安装检测逻辑
    return false;
  }

  /**
   * 更新安装方法配置
   * @param {Object} diagnostic - 诊断结果
   * @returns {Promise<void>}
   */
  async updateInstallMethodConfig(diagnostic) {
    const currentConfig = getConfig('installMethod');
    
    if (!currentConfig) {
      let installMethod = 'unknown';
      
      switch (diagnostic.installationType) {
        case INSTALLATION_TYPES.NPM_LOCAL:
          installMethod = 'local';
          break;
        case INSTALLATION_TYPES.NPM_GLOBAL:
          installMethod = 'global';
          break;
        case INSTALLATION_TYPES.NATIVE:
          installMethod = 'native';
          break;
      }

      await setConfig('installMethod', installMethod);
      logger.info('安装方法配置已更新', { installMethod });
    }
  }

  /**
   * 执行更新检查
   * @param {Object} diagnostic - 诊断结果
   * @returns {Promise<Object>} 更新结果
   */
  async performUpdateCheck(diagnostic) {
    switch (diagnostic.installationType) {
      case INSTALLATION_TYPES.DEVELOPMENT:
        logger.warn('开发构建无法更新');
        return { status: 'development_build' };

      case INSTALLATION_TYPES.NATIVE:
        return await this.performNativeUpdate();

      case INSTALLATION_TYPES.NPM_LOCAL:
      case INSTALLATION_TYPES.NPM_GLOBAL:
        return await this.performNpmUpdate(diagnostic.installationType);

      default:
        throw new Error(`无法更新未知安装类型: ${diagnostic.installationType}`);
    }
  }

  /**
   * 执行原生更新
   * @returns {Promise<Object>} 更新结果
   */
  async performNativeUpdate() {
    try {
      const updateResult = await this.checkNativeUpdate();
      
      if (!updateResult.latestVersion) {
        throw new Error('无法检查更新');
      }

      if (updateResult.latestVersion === APP_INFO.VERSION) {
        logger.info(`Claude Code已是最新版本 (${APP_INFO.VERSION})`);
        return { status: UPDATE_STATUS.UP_TO_DATE };
      }

      if (updateResult.wasUpdated) {
        logger.info(`成功从 ${APP_INFO.VERSION} 更新到 ${updateResult.latestVersion}`);
        return { 
          status: UPDATE_STATUS.SUCCESS,
          oldVersion: APP_INFO.VERSION,
          newVersion: updateResult.latestVersion
        };
      }

      logger.info(`Claude Code已是最新版本 (${APP_INFO.VERSION})`);
      return { status: UPDATE_STATUS.UP_TO_DATE };

    } catch (error) {
      logger.error('原生更新失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 检查原生更新
   * @returns {Promise<Object>} 原生更新结果
   */
  async checkNativeUpdate() {
    // 实现原生更新检查逻辑
    return {
      latestVersion: APP_INFO.VERSION,
      wasUpdated: false
    };
  }

  /**
   * 执行npm更新
   * @param {string} installationType - 安装类型
   * @returns {Promise<Object>} 更新结果
   */
  async performNpmUpdate(installationType) {
    try {
      // 获取最新版本
      const latestVersion = await this.getLatestNpmVersion();
      
      if (!latestVersion) {
        throw new Error('无法获取最新版本信息');
      }

      if (latestVersion === APP_INFO.VERSION) {
        logger.info(`Claude Code已是最新版本 (${APP_INFO.VERSION})`);
        return { status: UPDATE_STATUS.UP_TO_DATE };
      }

      logger.info(`发现新版本: ${latestVersion} (当前: ${APP_INFO.VERSION})`);
      logger.info('正在安装更新...');

      // 执行更新
      const updateResult = await this.performNpmInstall(installationType);
      
      if (updateResult === UPDATE_STATUS.SUCCESS) {
        logger.info(`成功从 ${APP_INFO.VERSION} 更新到 ${latestVersion}`);
        return {
          status: UPDATE_STATUS.SUCCESS,
          oldVersion: APP_INFO.VERSION,
          newVersion: latestVersion
        };
      }

      return { status: updateResult };

    } catch (error) {
      logger.error('npm更新失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 获取最新npm版本
   * @returns {Promise<string|null>} 最新版本
   */
  async getLatestNpmVersion() {
    try {
      const command = `npm view ${APP_INFO.PACKAGE_URL}@latest version`;
      const result = await executeCommand('npm', ['view', `${APP_INFO.PACKAGE_URL}@latest`, 'version'], {
        timeout: this.timeout
      });

      if (result.code === 0 && result.stdout) {
        return result.stdout.trim();
      }

      return null;

    } catch (error) {
      logger.error('获取npm最新版本失败', { error: error.message });
      return null;
    }
  }

  /**
   * 执行npm安装
   * @param {string} installationType - 安装类型
   * @returns {Promise<string>} 安装状态
   */
  async performNpmInstall(installationType) {
    const isLocal = installationType === INSTALLATION_TYPES.NPM_LOCAL;
    
    try {
      if (isLocal) {
        return await this.installLocalPackage();
      } else {
        return await this.installGlobalPackage();
      }
    } catch (error) {
      logger.error('npm安装失败', { installationType, error: error.message });
      return UPDATE_STATUS.INSTALL_FAILED;
    }
  }

  /**
   * 安装本地包
   * @original: km()函数 (L17713)
   * @returns {Promise<string>} 安装状态
   */
  async installLocalPackage() {
    try {
      const result = await executeCommand('npm', ['install', `${APP_INFO.PACKAGE_URL}@latest`], {
        cwd: this.getLocalInstallPath(),
        maxBuffer: 1000000
      });

      if (result.code !== 0) {
        if (result.code === 190) {
          return UPDATE_STATUS.IN_PROGRESS;
        }
        throw new Error(`npm安装失败: ${result.stderr}`);
      }

      // 更新配置
      await setConfig('installMethod', 'local');
      
      return UPDATE_STATUS.SUCCESS;

    } catch (error) {
      logger.error('本地包安装失败', { error: error.message });
      return UPDATE_STATUS.INSTALL_FAILED;
    }
  }

  /**
   * 安装全局包
   * @returns {Promise<string>} 安装状态
   */
  async installGlobalPackage() {
    try {
      const result = await executeCommand('npm', ['install', '-g', `${APP_INFO.PACKAGE_URL}@latest`], {
        timeout: this.timeout
      });

      if (result.code !== 0) {
        if (result.stderr && result.stderr.includes('EACCES')) {
          return UPDATE_STATUS.NO_PERMISSIONS;
        }
        throw new Error(`全局npm安装失败: ${result.stderr}`);
      }

      return UPDATE_STATUS.SUCCESS;

    } catch (error) {
      logger.error('全局包安装失败', { error: error.message });
      return UPDATE_STATUS.INSTALL_FAILED;
    }
  }

  /**
   * 获取本地安装路径
   * @returns {string} 本地安装路径
   */
  getLocalInstallPath() {
    const path = require('path');
    const os = require('os');
    
    return path.join(os.homedir(), '.claude', 'local');
  }

  /**
   * 启动自动检查
   */
  startAutoCheck() {
    if (!this.enabled || this.checkTimer) {
      return;
    }

    this.checkTimer = setInterval(() => {
      this.checkForUpdates().catch(error => {
        logger.error('自动更新检查失败', { error: error.message });
      });
    }, this.checkInterval);

    logger.debug('自动更新检查已启动', { interval: this.checkInterval });
  }

  /**
   * 停止自动检查
   */
  stopAutoCheck() {
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = null;
      logger.debug('自动更新检查已停止');
    }
  }

  /**
   * 获取更新状态
   * @returns {Object} 更新状态
   */
  getUpdateStatus() {
    return {
      enabled: this.enabled,
      isChecking: this.isChecking,
      lastCheckTime: this.lastCheckTime,
      currentVersion: APP_INFO.VERSION
    };
  }
}

/**
 * 安装管理器类
 */
export class InstallationManager {
  constructor() {
    this.isInstalling = false;
    this.installationStatus = null;
  }

  /**
   * 安装Claude Code
   * @original: 安装命令逻辑 (L56913-56958)
   * @param {Object} options - 安装选项
   * @returns {Promise<Object>} 安装结果
   */
  async install(options = {}) {
    if (this.isInstalling) {
      return { status: 'already_installing' };
    }

    this.isInstalling = true;
    
    try {
      const { force = false, target = 'latest' } = options;
      
      logger.info('开始安装过程', { force, target });

      // 清理旧安装
      this.updateStatus({ type: INSTALL_STATUS.CLEANING_NPM });
      const cleanupResult = await this.cleanupOldInstallations();
      
      if (cleanupResult.removed > 0) {
        logger.info(`清理了 ${cleanupResult.removed} 个旧安装`);
      }

      // 安装新版本
      this.updateStatus({ 
        type: INSTALL_STATUS.INSTALLING, 
        version: target || 'stable' 
      });
      
      const installResult = await this.installLatest(true, target, force);
      
      if (!installResult.latestVersion) {
        throw new Error('安装过程中无法获取版本信息');
      }

      // 设置启动器
      this.updateStatus({ type: INSTALL_STATUS.SETTING_UP });
      const setupMessages = await this.setupLauncher(true);
      
      if (setupMessages.length > 0) {
        this.updateStatus({
          type: INSTALL_STATUS.SET_UP,
          messages: setupMessages
        });
        
        // 延迟显示成功状态
        setTimeout(() => {
          this.updateStatus({
            type: INSTALL_STATUS.SUCCESS,
            version: installResult.latestVersion || 'current',
            setupMessages
          });
        }, 2000);
      } else {
        this.updateStatus({
          type: INSTALL_STATUS.SUCCESS,
          version: installResult.latestVersion || 'current'
        });
      }

      logTelemetryEvent('tengu_claude_install_command', {
        has_version: installResult.latestVersion ? 1 : 0,
        forced: force ? 1 : 0
      });

      return {
        status: 'success',
        version: installResult.latestVersion,
        setupMessages
      };

    } catch (error) {
      logger.error('安装失败', { error: error.message });
      
      this.updateStatus({
        type: INSTALL_STATUS.ERROR,
        message: error.message
      });

      return {
        status: 'error',
        error: error.message
      };
    } finally {
      this.isInstalling = false;
    }
  }

  /**
   * 清理旧安装
   * @original: QR8()函数 (L56848)
   * @returns {Promise<Object>} 清理结果
   */
  async cleanupOldInstallations() {
    const errors = [];
    let removed = 0;

    try {
      // 移除全局npm安装
      logger.debug('尝试移除全局npm安装');
      
      const result = await executeCommand('npm', ['uninstall', '-g', '@anthropic/claude-code'], {
        cwd: process.cwd()
      });

      if (result.code === 0) {
        removed++;
        logger.debug('已移除全局npm安装');
      } else if (result.stderr && !result.stderr.includes('npm ERR! code E404')) {
        errors.push('移除全局npm安装失败');
        logger.error('移除全局npm安装失败', { stderr: result.stderr });
      }

      // 移除本地安装目录
      const localPath = this.getLocalInstallPath();
      const fs = require('fs');
      
      if (fs.existsSync(localPath)) {
        try {
          fs.rmSync(localPath, { recursive: true, force: true });
          removed++;
          logger.debug('已移除本地安装', { path: localPath });
        } catch (error) {
          errors.push(`移除 ${localPath} 失败: ${error.message}`);
          logger.error('移除本地安装失败', { path: localPath, error: error.message });
        }
      }

      return { removed, errors };

    } catch (error) {
      logger.error('清理旧安装失败', { error: error.message });
      return { removed, errors: [error.message] };
    }
  }

  /**
   * 安装最新版本
   * @param {boolean} force - 是否强制安装
   * @param {string} target - 目标版本
   * @param {boolean} forceReinstall - 是否强制重新安装
   * @returns {Promise<Object>} 安装结果
   */
  async installLatest(force = false, target = 'latest', forceReinstall = false) {
    // 实现最新版本安装逻辑
    return {
      latestVersion: target === 'latest' ? await this.getLatestVersion() : target,
      wasUpdated: true
    };
  }

  /**
   * 设置启动器
   * @param {boolean} force - 是否强制设置
   * @returns {Promise<Array>} 设置消息
   */
  async setupLauncher(force = false) {
    // 实现启动器设置逻辑
    return [];
  }

  /**
   * 获取最新版本
   * @returns {Promise<string>} 最新版本
   */
  async getLatestVersion() {
    // 实现最新版本获取逻辑
    return APP_INFO.VERSION;
  }

  /**
   * 获取本地安装路径
   * @returns {string} 本地安装路径
   */
  getLocalInstallPath() {
    const path = require('path');
    const os = require('os');
    
    return path.join(os.homedir(), '.claude', 'local');
  }

  /**
   * 更新安装状态
   * @param {Object} status - 状态对象
   */
  updateStatus(status) {
    this.installationStatus = status;
    logger.debug('安装状态已更新', status);
  }

  /**
   * 获取安装状态
   * @returns {Object|null} 安装状态
   */
  getInstallationStatus() {
    return this.installationStatus;
  }
}

/**
 * 迁移管理器类
 */
export class MigrationManager {
  /**
   * 迁移到本地安装
   * @original: 迁移安装器逻辑 (L57878-57903)
   * @returns {Promise<Object>} 迁移结果
   */
  async migrateToLocalInstallation() {
    try {
      // 检查是否已经是本地安装
      if (await this.isLocalInstallation()) {
        return {
          success: true,
          message: '已经是本地安装，无需迁移'
        };
      }

      logTelemetryEvent('tengu_migrate_installer_command', {});

      // 执行迁移逻辑
      const migrationResult = await this.performMigration();
      
      if (migrationResult.success) {
        logger.info('迁移完成');
        return {
          success: true,
          message: '迁移完成，请重启Claude CLI以使用新安装'
        };
      }

      return migrationResult;

    } catch (error) {
      logger.error('迁移失败', { error: error.message });
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 检查是否为本地安装
   * @returns {Promise<boolean>} 是否为本地安装
   */
  async isLocalInstallation() {
    // 实现本地安装检测逻辑
    return false;
  }

  /**
   * 执行迁移
   * @returns {Promise<Object>} 迁移结果
   */
  async performMigration() {
    // 实现迁移逻辑
    return { success: true };
  }
}

/**
 * 全局自动更新器实例
 */
export const globalAutoUpdater = new AutoUpdater();

/**
 * 全局安装管理器实例
 */
export const globalInstallationManager = new InstallationManager();

/**
 * 全局迁移管理器实例
 */
export const globalMigrationManager = new MigrationManager();

/**
 * 便捷函数：检查更新
 * @returns {Promise<Object>} 更新检查结果
 */
export async function checkForUpdates() {
  return globalAutoUpdater.checkForUpdates();
}

/**
 * 便捷函数：安装Claude Code
 * @param {Object} options - 安装选项
 * @returns {Promise<Object>} 安装结果
 */
export async function installClaudeCode(options = {}) {
  return globalInstallationManager.install(options);
}

/**
 * 便捷函数：迁移到本地安装
 * @returns {Promise<Object>} 迁移结果
 */
export async function migrateToLocalInstallation() {
  return globalMigrationManager.migrateToLocalInstallation();
}
