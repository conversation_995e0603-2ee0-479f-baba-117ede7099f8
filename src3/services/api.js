/**
 * Anthropic API客户端服务
 * 
 * 提供与Anthropic API的交互功能，包括认证、请求处理、错误处理等
 * 
 * @original: API相关代码 (L5195-5323, L8533-8595, L55407-55580)
 */

import { logger } from '../utils/logger.js';
import { createHttpClient } from '../utils/http.js';
import { APP_INFO, API_ENDPOINTS } from '../config/constants.js';

/**
 * API错误类
 */
export class AnthropicAPIError extends Error {
  constructor(message, status, response) {
    super(message);
    this.name = 'AnthropicAPIError';
    this.status = status;
    this.response = response;
  }
}

/**
 * 认证错误类
 */
export class AuthenticationError extends Error {
  constructor(message, source) {
    super(message);
    this.name = 'AuthenticationError';
    this.source = source;
  }
}

/**
 * Anthropic API客户端类
 */
export class AnthropicAPIClient {
  constructor(options = {}) {
    this.baseURL = options.baseURL || API_ENDPOINTS.BASE_API_URL;
    this.timeout = options.timeout || 30000;
    this.userAgent = this.createUserAgent();
    this.httpClient = createHttpClient({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'User-Agent': this.userAgent,
        'anthropic-beta': options.beta || 'context-management-2025-06-27'
      }
    });

    // 添加请求拦截器
    this.httpClient.addRequestInterceptor(
      this.addAuthHeaders.bind(this),
      this.handleRequestError.bind(this)
    );

    // 添加响应拦截器
    this.httpClient.addResponseInterceptor(
      this.handleResponse.bind(this),
      this.handleResponseError.bind(this)
    );
  }

  /**
   * 创建User-Agent字符串
   * @original: Yx()函数 (L8533)
   * @returns {string} User-Agent字符串
   */
  createUserAgent() {
    const entrypoint = process.env.CLAUDE_CODE_ENTRYPOINT || 'cli';
    return `claude-cli/${APP_INFO.VERSION} (external, ${entrypoint})`;
  }

  /**
   * 添加认证头
   * @original: 认证头添加逻辑 (L8564-8580)
   * @param {Object} config - 请求配置
   * @returns {Object} 更新后的配置
   */
  async addAuthHeaders(config) {
    const authInfo = this.getAuthInfo();
    
    if (authInfo.error) {
      throw new AuthenticationError(authInfo.error, 'no_auth');
    }

    // 合并认证头
    config.headers = {
      ...config.headers,
      ...authInfo.headers
    };

    return config;
  }

  /**
   * 获取认证信息
   * @original: 认证信息获取逻辑 (L8564-8580)
   * @returns {Object} 认证信息
   */
  getAuthInfo() {
    // 检查OAuth token
    const oauthToken = this.getOAuthToken();
    if (oauthToken) {
      return {
        headers: {
          'Authorization': `Bearer ${oauthToken.accessToken}`,
          'anthropic-beta': 'context-management-2025-06-27'
        }
      };
    }

    // 检查API key
    const apiKey = this.getAPIKey();
    if (apiKey) {
      return {
        headers: {
          'x-api-key': apiKey
        }
      };
    }

    return {
      error: 'No API key available'
    };
  }

  /**
   * 获取OAuth token
   * @returns {Object|null} OAuth token信息
   */
  getOAuthToken() {
    // 实现OAuth token获取逻辑
    // 这里需要从配置中读取token信息
    return null;
  }

  /**
   * 获取API key
   * @returns {string|null} API key
   */
  getAPIKey() {
    // 优先级：环境变量 > 配置文件 > keychain
    if (process.env.ANTHROPIC_API_KEY) {
      return process.env.ANTHROPIC_API_KEY;
    }

    if (process.env.CLAUDE_CODE_OAUTH_TOKEN) {
      return process.env.CLAUDE_CODE_OAUTH_TOKEN;
    }

    // 从配置文件或keychain读取
    return this.getStoredAPIKey();
  }

  /**
   * 获取存储的API key
   * @returns {string|null} 存储的API key
   */
  getStoredAPIKey() {
    // 实现从配置文件或keychain读取API key的逻辑
    return null;
  }

  /**
   * 处理请求错误
   * @param {Error} error - 请求错误
   * @returns {Promise<never>} 抛出错误
   */
  async handleRequestError(error) {
    logger.error('API请求错误', { error: error.message });
    throw error;
  }

  /**
   * 处理响应
   * @param {Object} response - 响应对象
   * @returns {Object} 处理后的响应
   */
  async handleResponse(response) {
    logger.debug('API响应成功', { 
      status: response.status,
      url: response.config.url 
    });
    
    return response;
  }

  /**
   * 处理响应错误
   * @param {Error} error - 响应错误
   * @returns {Promise<never>} 抛出错误
   */
  async handleResponseError(error) {
    if (error.status) {
      const apiError = new AnthropicAPIError(
        `API request failed: ${error.status} ${error.statusText}`,
        error.status,
        error.response
      );
      
      logger.error('API响应错误', { 
        status: error.status,
        message: error.message,
        url: error.config?.url
      });
      
      throw apiError;
    }
    
    throw error;
  }

  /**
   * 获取用户配置文件
   * @original: i91()函数 (L5211)
   * @returns {Promise<Object>} 用户配置文件
   */
  async getUserProfile() {
    const response = await this.httpClient.get('/api/oauth/profile');
    return response.data;
  }

  /**
   * 获取Claude CLI配置文件
   * @original: 配置文件获取逻辑 (L5195-5208)
   * @param {string} accountUuid - 账户UUID
   * @returns {Promise<Object>} CLI配置文件
   */
  async getClaudeCliProfile(accountUuid) {
    const response = await this.httpClient.get('/api/claude_cli_profile', {
      params: {
        account_uuid: accountUuid
      }
    });
    
    return response.data;
  }

  /**
   * 创建API key
   * @original: FWA()函数 (L5307)
   * @param {string} accessToken - 访问token
   * @returns {Promise<string>} API key
   */
  async createAPIKey(accessToken) {
    try {
      const response = await this.httpClient.post(API_ENDPOINTS.API_KEY_URL, null, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      const apiKey = response.data?.raw_key;
      
      if (!apiKey) {
        throw new Error('No API key returned from server');
      }

      // 记录遥测事件
      this.logTelemetryEvent('tengu_oauth_api_key', {
        status: 'success',
        statusCode: response.status
      });

      return apiKey;

    } catch (error) {
      this.logTelemetryEvent('tengu_oauth_api_key', {
        status: 'failure',
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * 检查指标是否启用
   * @original: 指标检查逻辑 (L8591-8598)
   * @returns {Promise<Object>} 指标状态
   */
  async checkMetricsEnabled() {
    try {
      const response = await this.httpClient.get(
        'https://api.anthropic.com/api/claude_code/organizations/metrics_enabled',
        {
          timeout: 5000
        }
      );

      logger.info(`指标选择退出API响应: enabled=${response.data.metrics_logging_enabled}`);
      
      return {
        enabled: response.data.metrics_logging_enabled,
        hasError: false
      };

    } catch (error) {
      logger.error('检查指标状态失败', { error: error.message });
      
      return {
        enabled: true, // 默认启用
        hasError: true,
        error: error.message
      };
    }
  }

  /**
   * 获取代码会话列表
   * @original: 代码会话获取逻辑 (L56117-56127)
   * @param {string} organizationId - 组织ID
   * @returns {Promise<Array>} 代码会话列表
   */
  async getCodeSessions(organizationId) {
    const url = `/api/oauth/organizations/${organizationId}/code/sessions`;
    
    logger.info(`获取代码会话: ${url}`);
    
    try {
      const response = await this.httpClient.get(url);
      
      if (response.status !== 200) {
        throw new Error(`Failed to fetch code sessions: ${response.statusText}`);
      }

      // 这里应该有数据验证逻辑
      return response.data;

    } catch (error) {
      logger.error('获取代码会话失败', { organizationId, error: error.message });
      throw error;
    }
  }

  /**
   * 创建代码会话
   * @original: 代码会话创建逻辑 (L56353-56386)
   * @param {string} organizationId - 组织ID
   * @param {Object} sessionData - 会话数据
   * @returns {Promise<Object>} 创建的会话
   */
  async createCodeSession(organizationId, sessionData) {
    const url = `/api/oauth/organizations/${organizationId}/code/sessions`;
    
    logger.info(`创建代码会话: ${url}`);
    logger.info(`请求体: ${JSON.stringify(sessionData)}`);
    
    try {
      const response = await this.httpClient.post(url, sessionData);
      
      if (response.status !== 200 && response.status !== 201) {
        logger.error(`API请求失败，状态码 ${response.status}: ${response.statusText}\n\n响应数据: ${JSON.stringify(response.data, null, 2)}`);
        return null;
      }

      // 这里应该有数据验证逻辑
      return response.data;

    } catch (error) {
      logger.error('创建代码会话失败', { organizationId, error: error.message });
      
      if (error.response) {
        logger.error('错误数据', { data: error.response.data });
      }
      
      throw error;
    }
  }

  /**
   * 恢复代码会话
   * @original: 代码会话恢复逻辑 (L56204-56206)
   * @param {string} organizationId - 组织ID
   * @param {string} sessionId - 会话ID
   * @param {Object} resumeData - 恢复数据
   * @returns {Promise<Object>} 恢复结果
   */
  async resumeCodeSession(organizationId, sessionId, resumeData) {
    const url = `/api/oauth/organizations/${organizationId}/code/sessions/${sessionId}/resume`;
    
    try {
      const response = await this.httpClient.post(url, resumeData);
      return response.data;

    } catch (error) {
      logger.error('恢复代码会话失败', { organizationId, sessionId, error: error.message });
      throw error;
    }
  }

  /**
   * 发送消息到Claude
   * @original: 消息发送逻辑 (L55407-55580)
   * @param {Object} messageData - 消息数据
   * @returns {Promise<Object>} 响应结果
   */
  async sendMessage(messageData) {
    try {
      const response = await this.httpClient.post('/v1/messages', messageData);
      return response.data;

    } catch (error) {
      logger.error('发送消息失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 流式发送消息
   * @param {Object} messageData - 消息数据
   * @param {Function} onChunk - 数据块处理函数
   * @returns {Promise<void>}
   */
  async streamMessage(messageData, onChunk) {
    try {
      // 实现流式请求逻辑
      const response = await this.httpClient.post('/v1/messages', {
        ...messageData,
        stream: true
      });

      // 处理流式响应
      // 这里需要实现SSE或WebSocket处理逻辑

    } catch (error) {
      logger.error('流式消息发送失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 记录遥测事件
   * @param {string} event - 事件名称
   * @param {Object} data - 事件数据
   */
  logTelemetryEvent(event, data) {
    logger.debug('API遥测事件', { event, data });
  }
}

/**
 * API认证管理器
 */
export class APIAuthManager {
  constructor() {
    this.tokenCache = new Map();
  }

  /**
   * 获取认证状态
   * @original: Fg()函数 (L5358-5376)
   * @returns {Object} 认证状态
   */
  getAuthStatus() {
    if (process.env.ANTHROPIC_AUTH_TOKEN) {
      return {
        source: 'ANTHROPIC_AUTH_TOKEN',
        hasToken: true
      };
    }

    if (process.env.CLAUDE_CODE_OAUTH_TOKEN) {
      return {
        source: 'CLAUDE_CODE_OAUTH_TOKEN',
        hasToken: true
      };
    }

    if (this.hasStoredAPIKey()) {
      return {
        source: 'apiKeyHelper',
        hasToken: true
      };
    }

    const oauthData = this.getOAuthData();
    if (this.isValidOAuthData(oauthData)) {
      return {
        source: 'claude.ai',
        hasToken: true
      };
    }

    return {
      source: null,
      hasToken: false
    };
  }

  /**
   * 检查是否有存储的API key
   * @returns {boolean} 是否有存储的API key
   */
  hasStoredAPIKey() {
    // 实现API key存储检查逻辑
    return false;
  }

  /**
   * 获取OAuth数据
   * @returns {Object|null} OAuth数据
   */
  getOAuthData() {
    // 实现OAuth数据获取逻辑
    return null;
  }

  /**
   * 验证OAuth数据是否有效
   * @param {Object} data - OAuth数据
   * @returns {boolean} 是否有效
   */
  isValidOAuthData(data) {
    if (!data) return false;
    
    return !!(data.scopes && data.accessToken);
  }

  /**
   * 存储API key
   * @original: IWA()函数 (L5396-5412)
   * @param {string} apiKey - API key
   */
  storeAPIKey(apiKey) {
    if (!this.isValidAPIKeyFormat(apiKey)) {
      throw new Error('Invalid API key format. API key must contain only alphanumeric characters, dashes, and underscores.');
    }

    const config = this.getConfig();
    
    // 清理现有存储
    this.clearStoredAPIKey();

    // 在macOS上使用keychain存储
    if (process.platform === 'darwin') {
      try {
        const serviceName = this.getKeychainServiceName();
        this.executeCommand(`security add-generic-password -a $USER -s "${serviceName}" -w ${apiKey}`);
      } catch (error) {
        logger.error('Keychain存储失败', error);
        config.primaryApiKey = apiKey;
      }
    } else {
      config.primaryApiKey = apiKey;
    }

    // 更新批准列表
    if (!config.customApiKeyResponses) {
      config.customApiKeyResponses = {
        approved: [],
        rejected: []
      };
    }

    if (!config.customApiKeyResponses.approved) {
      config.customApiKeyResponses.approved = [];
    }

    const keyHash = this.hashAPIKey(apiKey);
    if (!config.customApiKeyResponses.approved.includes(keyHash)) {
      config.customApiKeyResponses.approved.push(keyHash);
    }

    this.saveConfig(config);
    this.clearTokenCache();
  }

  /**
   * 验证API key格式
   * @original: fB4()函数 (L5396)
   * @param {string} apiKey - API key
   * @returns {boolean} 是否有效
   */
  isValidAPIKeyFormat(apiKey) {
    if (!apiKey || typeof apiKey !== 'string') return false;
    
    // API key应该只包含字母数字字符、破折号和下划线
    return /^[a-zA-Z0-9_-]+$/.test(apiKey);
  }

  /**
   * 检查API key是否已批准
   * @original: BsA()函数 (L5416)
   * @param {string} apiKey - API key
   * @returns {boolean} 是否已批准
   */
  isAPIKeyApproved(apiKey) {
    const config = this.getConfig();
    const keyHash = this.hashAPIKey(apiKey);
    
    return config.customApiKeyResponses?.approved?.includes(keyHash) ?? false;
  }

  /**
   * 清除存储的API key
   * @original: QsA()函数 (L5421)
   */
  clearStoredAPIKey() {
    this.clearKeychainAPIKey();
    
    const config = this.getConfig();
    config.primaryApiKey = undefined;
    this.saveConfig(config);
    this.clearTokenCache();
  }

  /**
   * 清除keychain中的API key
   * @original: DsA()函数 (L5424)
   */
  clearKeychainAPIKey() {
    if (process.platform === 'darwin') {
      try {
        const serviceName = this.getKeychainServiceName();
        this.executeCommand(`security delete-generic-password -s "${serviceName}"`);
      } catch (error) {
        // 忽略删除错误
      }
    }
  }

  /**
   * 获取keychain服务名称
   * @returns {string} 服务名称
   */
  getKeychainServiceName() {
    return 'claude-code-api-key';
  }

  /**
   * 哈希API key
   * @param {string} apiKey - API key
   * @returns {string} 哈希值
   */
  hashAPIKey(apiKey) {
    // 实现API key哈希逻辑
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(apiKey).digest('hex').substring(0, 16);
  }

  /**
   * 获取配置
   * @returns {Object} 配置对象
   */
  getConfig() {
    // 实现配置获取逻辑
    return {};
  }

  /**
   * 保存配置
   * @param {Object} config - 配置对象
   */
  saveConfig(config) {
    // 实现配置保存逻辑
  }

  /**
   * 清除token缓存
   */
  clearTokenCache() {
    this.tokenCache.clear();
  }

  /**
   * 执行命令
   * @param {string} command - 命令
   * @returns {string} 命令输出
   */
  executeCommand(command) {
    // 实现命令执行逻辑
    return '';
  }
}

/**
 * 全局API客户端实例
 */
export const globalAPIClient = new AnthropicAPIClient();

/**
 * 全局认证管理器实例
 */
export const globalAuthManager = new APIAuthManager();

/**
 * 便捷函数：发送消息
 * @param {Object} messageData - 消息数据
 * @returns {Promise<Object>} 响应结果
 */
export async function sendMessage(messageData) {
  return globalAPIClient.sendMessage(messageData);
}

/**
 * 便捷函数：获取用户配置文件
 * @returns {Promise<Object>} 用户配置文件
 */
export async function getUserProfile() {
  return globalAPIClient.getUserProfile();
}

/**
 * 便捷函数：检查认证状态
 * @returns {Object} 认证状态
 */
export function getAuthStatus() {
  return globalAuthManager.getAuthStatus();
}
