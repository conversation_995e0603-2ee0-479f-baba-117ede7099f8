/**
 * 数据库和存储管理服务
 * 
 * 管理会话数据、消息存储、检查点和数据持久化
 * 
 * @original: 数据库相关代码 (L5865-6224, L6127-6168)
 */

import { logger } from '../utils/logger.js';
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';

/**
 * 消息类型枚举
 */
export const MESSAGE_TYPES = {
  USER: 'user',
  ASSISTANT: 'assistant',
  SYSTEM: 'system',
  TOOL_USE: 'tool_use',
  TOOL_RESULT: 'tool_result'
};

/**
 * 存储类型枚举
 */
export const STORAGE_TYPES = {
  SESSION: 'session',
  MESSAGE: 'message',
  CHECKPOINT: 'checkpoint',
  SUMMARY: 'summary',
  TRANSCRIPT: 'transcript'
};

/**
 * 数据库管理器类
 * @original: I$2类 (L5865-6064)
 */
export class DatabaseManager {
  constructor() {
    this.summaries = new Map();
    this.messages = [];
    this.checkpoints = [];
    this.sessionFile = null;
    this.autoSaveInterval = null;
    this.isDirty = false;
  }

  /**
   * 初始化数据库
   * @param {string} sessionFile - 会话文件路径
   * @returns {Promise<void>}
   */
  async initialize(sessionFile) {
    try {
      this.sessionFile = sessionFile;
      
      // 确保目录存在
      const sessionDir = dirname(sessionFile);
      if (!existsSync(sessionDir)) {
        mkdirSync(sessionDir, { recursive: true });
      }

      // 加载现有数据
      await this.loadFromFile();

      // 启动自动保存
      this.startAutoSave();

      logger.info('数据库初始化完成', { sessionFile });

    } catch (error) {
      logger.error('数据库初始化失败', { sessionFile, error: error.message });
      throw error;
    }
  }

  /**
   * 从文件加载数据
   * @returns {Promise<void>}
   */
  async loadFromFile() {
    if (!this.sessionFile || !existsSync(this.sessionFile)) {
      return;
    }

    try {
      const content = readFileSync(this.sessionFile, 'utf8');
      const lines = content.trim().split('\n').filter(line => line.trim());

      for (const line of lines) {
        const entry = JSON.parse(line);
        
        switch (entry.type) {
          case STORAGE_TYPES.MESSAGE:
            this.messages.push(entry);
            break;
          
          case STORAGE_TYPES.CHECKPOINT:
            this.checkpoints.push(entry);
            break;
          
          case STORAGE_TYPES.SUMMARY:
            this.summaries.set(entry.sessionId, entry.summary);
            break;
          
          default:
            logger.warn('未知的存储条目类型', { type: entry.type });
        }
      }

      logger.debug('数据库加载完成', {
        messages: this.messages.length,
        checkpoints: this.checkpoints.length,
        summaries: this.summaries.size
      });

    } catch (error) {
      logger.error('从文件加载数据失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 保存数据到文件
   * @returns {Promise<void>}
   */
  async saveToFile() {
    if (!this.sessionFile) {
      return;
    }

    try {
      const entries = [];

      // 添加消息
      for (const message of this.messages) {
        entries.push({
          type: STORAGE_TYPES.MESSAGE,
          ...message
        });
      }

      // 添加检查点
      for (const checkpoint of this.checkpoints) {
        entries.push({
          type: STORAGE_TYPES.CHECKPOINT,
          ...checkpoint
        });
      }

      // 添加摘要
      for (const [sessionId, summary] of this.summaries) {
        entries.push({
          type: STORAGE_TYPES.SUMMARY,
          sessionId,
          summary
        });
      }

      // 写入文件
      const content = entries.map(entry => JSON.stringify(entry)).join('\n');
      writeFileSync(this.sessionFile, content, 'utf8');

      this.isDirty = false;
      
      logger.debug('数据库保存完成', { 
        sessionFile: this.sessionFile,
        entryCount: entries.length 
      });

    } catch (error) {
      logger.error('保存数据到文件失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 插入消息链
   * @original: pM1()函数 (L6065-6068)
   * @param {Array} messageChain - 消息链
   * @param {boolean} isUpdate - 是否为更新操作
   * @returns {Promise<string|null>} 最后一条消息的UUID
   */
  async insertMessageChain(messageChain, isUpdate = false) {
    try {
      if (isUpdate) {
        // 更新现有消息
        for (const message of messageChain) {
          const existingIndex = this.messages.findIndex(m => m.uuid === message.uuid);
          if (existingIndex !== -1) {
            this.messages[existingIndex] = message;
          } else {
            this.messages.push(message);
          }
        }
      } else {
        // 添加新消息
        this.messages.push(...messageChain);
      }

      this.isDirty = true;
      
      const lastMessage = messageChain[messageChain.length - 1];
      
      logger.debug('消息链插入完成', {
        messageCount: messageChain.length,
        isUpdate,
        lastMessageUuid: lastMessage?.uuid
      });

      return lastMessage?.uuid || null;

    } catch (error) {
      logger.error('插入消息链失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 插入检查点
   * @original: cZ0()函数 (L6072-6074)
   * @param {Object} checkpoint - 检查点数据
   * @returns {Promise<void>}
   */
  async insertCheckpoint(checkpoint) {
    try {
      const checkpointEntry = {
        ...checkpoint,
        timestamp: new Date().toISOString(),
        id: this.generateId()
      };

      this.checkpoints.push(checkpointEntry);
      this.isDirty = true;

      logger.debug('检查点插入完成', { checkpointId: checkpointEntry.id });

    } catch (error) {
      logger.error('插入检查点失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 获取所有转录
   * @original: X$2()函数 (L6127-6140)
   * @returns {Promise<Array>} 转录列表
   */
  async getAllTranscripts() {
    try {
      const transcripts = this.messages.map((message, index) => {
        const summary = this.summaries.get(message.sessionId);
        
        return {
          id: message.uuid || `msg_${index}`,
          sessionId: message.sessionId,
          summary,
          messages: [message],
          timestamp: message.timestamp || new Date().toISOString()
        };
      });

      return transcripts;

    } catch (error) {
      logger.error('获取所有转录失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 添加摘要
   * @original: V$2()函数 (L6141-6147)
   * @param {string} sessionId - 会话ID
   * @param {string} summary - 摘要内容
   * @returns {Promise<void>}
   */
  async appendSummary(sessionId, summary) {
    try {
      this.summaries.set(sessionId, summary);
      this.isDirty = true;

      logger.debug('摘要添加完成', { sessionId });

    } catch (error) {
      logger.error('添加摘要失败', { sessionId, error: error.message });
      throw error;
    }
  }

  /**
   * 获取最后的日志
   * @original: C$2()函数 (L6179-6211)
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Array|null>} 最后的日志条目
   */
  async getLastLog(sessionId) {
    try {
      const sessionMessages = this.messages.filter(m => m.sessionId === sessionId);
      
      if (sessionMessages.length === 0) {
        return null;
      }

      // 返回最后一条消息的相关信息
      const lastMessage = sessionMessages[sessionMessages.length - 1];
      
      return [{
        uuid: lastMessage.uuid,
        content: lastMessage.content,
        timestamp: lastMessage.timestamp,
        type: lastMessage.type
      }];

    } catch (error) {
      logger.error('获取最后日志失败', { sessionId, error: error.message });
      return null;
    }
  }

  /**
   * 启动自动保存
   * @param {number} intervalMs - 保存间隔（毫秒）
   */
  startAutoSave(intervalMs = 30000) { // 30秒
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
    }

    this.autoSaveInterval = setInterval(async () => {
      if (this.isDirty) {
        try {
          await this.saveToFile();
        } catch (error) {
          logger.error('自动保存失败', { error: error.message });
        }
      }
    }, intervalMs);

    logger.debug('自动保存已启动', { intervalMs });
  }

  /**
   * 停止自动保存
   */
  stopAutoSave() {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
      logger.debug('自动保存已停止');
    }
  }

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  generateId() {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 清理旧数据
   * @param {number} maxAge - 最大保留时间（毫秒）
   * @returns {Promise<number>} 清理的条目数量
   */
  async cleanupOldData(maxAge = 30 * 24 * 60 * 60 * 1000) { // 30天
    let cleanedCount = 0;
    const cutoffTime = Date.now() - maxAge;

    try {
      // 清理旧消息
      const originalMessageCount = this.messages.length;
      this.messages = this.messages.filter(message => {
        const messageTime = new Date(message.timestamp || 0).getTime();
        return messageTime > cutoffTime;
      });
      cleanedCount += originalMessageCount - this.messages.length;

      // 清理旧检查点
      const originalCheckpointCount = this.checkpoints.length;
      this.checkpoints = this.checkpoints.filter(checkpoint => {
        const checkpointTime = new Date(checkpoint.timestamp || 0).getTime();
        return checkpointTime > cutoffTime;
      });
      cleanedCount += originalCheckpointCount - this.checkpoints.length;

      if (cleanedCount > 0) {
        this.isDirty = true;
        await this.saveToFile();
        
        logger.info('旧数据清理完成', { 
          cleanedCount,
          remainingMessages: this.messages.length,
          remainingCheckpoints: this.checkpoints.length
        });
      }

      return cleanedCount;

    } catch (error) {
      logger.error('清理旧数据失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 获取数据库统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    return {
      messageCount: this.messages.length,
      checkpointCount: this.checkpoints.length,
      summaryCount: this.summaries.size,
      sessionFile: this.sessionFile,
      isDirty: this.isDirty,
      autoSaveActive: !!this.autoSaveInterval
    };
  }

  /**
   * 备份数据库
   * @param {string} backupPath - 备份路径
   * @returns {Promise<void>}
   */
  async backup(backupPath) {
    try {
      if (!this.sessionFile || !existsSync(this.sessionFile)) {
        throw new Error('没有可备份的会话文件');
      }

      const fs = require('fs');
      fs.copyFileSync(this.sessionFile, backupPath);

      logger.info('数据库备份完成', { 
        source: this.sessionFile,
        backup: backupPath 
      });

    } catch (error) {
      logger.error('数据库备份失败', { backupPath, error: error.message });
      throw error;
    }
  }

  /**
   * 从备份恢复数据库
   * @param {string} backupPath - 备份路径
   * @returns {Promise<void>}
   */
  async restore(backupPath) {
    try {
      if (!existsSync(backupPath)) {
        throw new Error('备份文件不存在');
      }

      const fs = require('fs');
      
      if (this.sessionFile) {
        fs.copyFileSync(backupPath, this.sessionFile);
      }

      // 重新加载数据
      await this.loadFromFile();

      logger.info('数据库恢复完成', { 
        backup: backupPath,
        sessionFile: this.sessionFile 
      });

    } catch (error) {
      logger.error('数据库恢复失败', { backupPath, error: error.message });
      throw error;
    }
  }
}

/**
 * 会话管理器类
 */
export class SessionManager {
  constructor(databaseManager) {
    this.databaseManager = databaseManager || new DatabaseManager();
    this.activeSessions = new Map();
    this.sessionIndex = new Map();
  }

  /**
   * 创建新会话
   * @param {Object} sessionData - 会话数据
   * @returns {Promise<string>} 会话ID
   */
  async createSession(sessionData = {}) {
    try {
      const sessionId = this.generateSessionId();
      
      const session = {
        id: sessionId,
        title: sessionData.title || '新会话',
        description: sessionData.description || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        messageCount: 0,
        ...sessionData
      };

      this.activeSessions.set(sessionId, session);
      this.sessionIndex.set(sessionId, session);

      logger.info('新会话创建完成', { sessionId, title: session.title });

      return sessionId;

    } catch (error) {
      logger.error('创建会话失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 获取会话
   * @param {string} sessionId - 会话ID
   * @returns {Object|null} 会话对象
   */
  getSession(sessionId) {
    return this.activeSessions.get(sessionId) || null;
  }

  /**
   * 更新会话
   * @param {string} sessionId - 会话ID
   * @param {Object} updates - 更新数据
   * @returns {Promise<void>}
   */
  async updateSession(sessionId, updates) {
    try {
      const session = this.getSession(sessionId);
      
      if (!session) {
        throw new Error(`会话不存在: ${sessionId}`);
      }

      const updatedSession = {
        ...session,
        ...updates,
        updatedAt: new Date().toISOString()
      };

      this.activeSessions.set(sessionId, updatedSession);
      this.sessionIndex.set(sessionId, updatedSession);

      logger.debug('会话更新完成', { sessionId });

    } catch (error) {
      logger.error('更新会话失败', { sessionId, error: error.message });
      throw error;
    }
  }

  /**
   * 删除会话
   * @param {string} sessionId - 会话ID
   * @returns {Promise<void>}
   */
  async deleteSession(sessionId) {
    try {
      this.activeSessions.delete(sessionId);
      this.sessionIndex.delete(sessionId);

      // 删除相关消息
      this.databaseManager.messages = this.databaseManager.messages.filter(
        message => message.sessionId !== sessionId
      );

      // 删除相关检查点
      this.databaseManager.checkpoints = this.databaseManager.checkpoints.filter(
        checkpoint => checkpoint.sessionId !== sessionId
      );

      // 删除摘要
      this.databaseManager.summaries.delete(sessionId);

      this.databaseManager.isDirty = true;

      logger.info('会话删除完成', { sessionId });

    } catch (error) {
      logger.error('删除会话失败', { sessionId, error: error.message });
      throw error;
    }
  }

  /**
   * 获取所有会话
   * @returns {Array} 会话列表
   */
  getAllSessions() {
    return Array.from(this.activeSessions.values())
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
  }

  /**
   * 搜索会话
   * @param {string} query - 搜索查询
   * @returns {Array} 匹配的会话列表
   */
  searchSessions(query) {
    const lowerQuery = query.toLowerCase();
    
    return this.getAllSessions().filter(session => 
      session.title.toLowerCase().includes(lowerQuery) ||
      session.description.toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * 生成会话ID
   * @returns {string} 会话ID
   */
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 转录管理器类
 */
export class TranscriptManager {
  constructor(databaseManager) {
    this.databaseManager = databaseManager || new DatabaseManager();
  }

  /**
   * 从文件加载转录
   * @original: lZ0()函数 (L6148-6164)
   * @param {string} filePath - 文件路径
   * @returns {Promise<Object>} 转录数据
   */
  async loadTranscriptFromFile(filePath) {
    try {
      if (!existsSync(filePath)) {
        return {
          messages: new Map(),
          summaries: new Map(),
          checkpoints: new Map()
        };
      }

      const content = readFileSync(filePath, 'utf8');
      const lines = content.trim().split('\n').filter(line => line.trim());

      const messages = new Map();
      const summaries = new Map();
      const checkpoints = new Map();

      for (const line of lines) {
        const entry = JSON.parse(line);
        
        switch (entry.type) {
          case STORAGE_TYPES.MESSAGE:
            messages.set(entry.uuid, entry);
            break;
          
          case STORAGE_TYPES.SUMMARY:
            summaries.set(entry.sessionId, entry.summary);
            break;
          
          case STORAGE_TYPES.CHECKPOINT:
            checkpoints.set(entry.id, entry);
            break;
        }
      }

      logger.debug('转录加载完成', {
        filePath,
        messageCount: messages.size,
        summaryCount: summaries.size,
        checkpointCount: checkpoints.size
      });

      return { messages, summaries, checkpoints };

    } catch (error) {
      logger.error('加载转录失败', { filePath, error: error.message });
      throw error;
    }
  }

  /**
   * 按会话ID加载转录
   * @original: pZ0()函数 (L6165-6168)
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Object>} 转录数据
   */
  async loadTranscriptBySessionId(sessionId) {
    const { homedir } = require('os');
    const { join } = require('path');
    
    const transcriptPath = join(homedir(), '.claude', 'transcripts', `${sessionId}.jsonl`);
    return this.loadTranscriptFromFile(transcriptPath);
  }

  /**
   * 导出转录
   * @param {string} sessionId - 会话ID
   * @param {string} format - 导出格式
   * @returns {Promise<string>} 导出的内容
   */
  async exportTranscript(sessionId, format = 'json') {
    try {
      const transcript = await this.loadTranscriptBySessionId(sessionId);
      
      switch (format) {
        case 'json':
          return JSON.stringify(transcript, null, 2);
        
        case 'markdown':
          return this.convertToMarkdown(transcript);
        
        case 'text':
          return this.convertToText(transcript);
        
        default:
          throw new Error(`不支持的导出格式: ${format}`);
      }

    } catch (error) {
      logger.error('导出转录失败', { sessionId, format, error: error.message });
      throw error;
    }
  }

  /**
   * 转换为Markdown格式
   * @param {Object} transcript - 转录数据
   * @returns {string} Markdown内容
   */
  convertToMarkdown(transcript) {
    let markdown = '# 会话转录\n\n';
    
    for (const [uuid, message] of transcript.messages) {
      markdown += `## ${message.type === MESSAGE_TYPES.USER ? '用户' : '助手'}\n\n`;
      markdown += `${message.content}\n\n`;
      markdown += `*时间: ${message.timestamp}*\n\n---\n\n`;
    }

    return markdown;
  }

  /**
   * 转换为纯文本格式
   * @param {Object} transcript - 转录数据
   * @returns {string} 纯文本内容
   */
  convertToText(transcript) {
    let text = '';
    
    for (const [uuid, message] of transcript.messages) {
      const speaker = message.type === MESSAGE_TYPES.USER ? '用户' : '助手';
      text += `${speaker}: ${message.content}\n\n`;
    }

    return text;
  }
}

/**
 * 全局数据库管理器实例
 */
export const globalDatabaseManager = new DatabaseManager();

/**
 * 全局会话管理器实例
 */
export const globalSessionManager = new SessionManager(globalDatabaseManager);

/**
 * 全局转录管理器实例
 */
export const globalTranscriptManager = new TranscriptManager(globalDatabaseManager);

/**
 * 便捷函数：初始化数据库
 * @param {string} sessionFile - 会话文件路径
 * @returns {Promise<void>}
 */
export async function initializeDatabase(sessionFile) {
  return globalDatabaseManager.initialize(sessionFile);
}

/**
 * 便捷函数：插入消息链
 * @param {Array} messageChain - 消息链
 * @param {boolean} isUpdate - 是否为更新操作
 * @returns {Promise<string|null>} 最后一条消息的UUID
 */
export async function insertMessageChain(messageChain, isUpdate = false) {
  return globalDatabaseManager.insertMessageChain(messageChain, isUpdate);
}

/**
 * 便捷函数：插入检查点
 * @param {Object} checkpoint - 检查点数据
 * @returns {Promise<void>}
 */
export async function insertCheckpoint(checkpoint) {
  return globalDatabaseManager.insertCheckpoint(checkpoint);
}

/**
 * 便捷函数：创建会话
 * @param {Object} sessionData - 会话数据
 * @returns {Promise<string>} 会话ID
 */
export async function createSession(sessionData = {}) {
  return globalSessionManager.createSession(sessionData);
}

/**
 * 便捷函数：获取所有转录
 * @returns {Promise<Array>} 转录列表
 */
export async function getAllTranscripts() {
  return globalDatabaseManager.getAllTranscripts();
}
