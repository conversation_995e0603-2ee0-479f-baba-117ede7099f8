/**
 * 基础UI组件
 * 
 * 提供基础的React/Ink UI组件，包括Box、Text等
 * 
 * @original: React/Ink基础组件 (L6723-6777, L7439-7485)
 */

import React from 'react';
import { logger } from '../../utils/logger.js';

/**
 * Box组件 - 布局容器
 * @original: ink-box组件使用 (L6751)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} Box组件
 */
export function Box({
  children,
  flexDirection = 'row',
  alignItems,
  justifyContent,
  flexGrow,
  flexShrink,
  flexBasis,
  width,
  height,
  minWidth,
  minHeight,
  maxWidth,
  maxHeight,
  marginTop,
  marginBottom,
  marginLeft,
  marginRight,
  marginX,
  marginY,
  margin,
  paddingTop,
  paddingBottom,
  paddingLeft,
  paddingRight,
  paddingX,
  paddingY,
  padding,
  borderStyle,
  borderColor,
  borderTopColor,
  borderBottomColor,
  borderLeftColor,
  borderRightColor,
  borderDimColor,
  gap,
  overflowX,
  overflowY,
  display,
  position,
  top,
  bottom,
  left,
  right,
  ...rest
}) {
  const style = {
    flexDirection,
    alignItems,
    justifyContent,
    flexGrow,
    flexShrink,
    flexBasis,
    width,
    height,
    minWidth,
    minHeight,
    maxWidth,
    maxHeight,
    marginTop,
    marginBottom,
    marginLeft,
    marginRight,
    marginX,
    marginY,
    margin,
    paddingTop,
    paddingBottom,
    paddingLeft,
    paddingRight,
    paddingX,
    paddingY,
    padding,
    borderStyle,
    borderColor,
    borderTopColor,
    borderBottomColor,
    borderLeftColor,
    borderRightColor,
    borderDimColor,
    gap,
    overflowX,
    overflowY,
    display,
    position,
    top,
    bottom,
    left,
    right
  };

  // 过滤掉undefined的样式属性
  const filteredStyle = Object.fromEntries(
    Object.entries(style).filter(([_, value]) => value !== undefined)
  );

  return React.createElement('ink-box', {
    style: filteredStyle,
    ...rest
  }, children);
}

/**
 * Text组件 - 文本显示
 * @original: ink-text组件使用 (L6762)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} Text组件
 */
export function Text({
  children,
  color,
  backgroundColor,
  bold,
  italic,
  underline,
  strikethrough,
  inverse,
  dimColor,
  wrap = 'wrap',
  ...rest
}) {
  if (children === undefined || children === null) {
    return null;
  }

  const style = {
    color,
    backgroundColor,
    bold,
    italic,
    underline,
    strikethrough,
    inverse,
    dimColor,
    flexGrow: 0,
    flexShrink: 1
  };

  // 过滤掉undefined的样式属性
  const filteredStyle = Object.fromEntries(
    Object.entries(style).filter(([_, value]) => value !== undefined)
  );

  return React.createElement('ink-text', {
    style: filteredStyle,
    wrap,
    ...rest
  }, children);
}

/**
 * Newline组件 - 换行
 * @original: 换行组件 (L6775)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} Newline组件
 */
export function Newline({ count = 1 }) {
  return React.createElement('ink-text', null, '\n'.repeat(count));
}

/**
 * Spacer组件 - 空白填充
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} Spacer组件
 */
export function Spacer() {
  return React.createElement(Box, {
    flexGrow: 1
  });
}

/**
 * Fragment组件 - React Fragment的别名
 */
export const Fragment = React.Fragment;

/**
 * 颜色常量
 */
export const COLORS = {
  // 基础颜色
  black: 'black',
  red: 'red',
  green: 'green',
  yellow: 'yellow',
  blue: 'blue',
  magenta: 'magenta',
  cyan: 'cyan',
  white: 'white',
  gray: 'gray',
  grey: 'grey',
  
  // 语义化颜色
  primary: 'blue',
  secondary: 'gray',
  success: 'green',
  warning: 'yellow',
  error: 'red',
  info: 'cyan',
  
  // 应用特定颜色
  claude: 'cyan',
  ide: 'blue',
  permission: 'yellow',
  suggestion: 'green',
  secondaryText: 'gray',
  secondaryBorder: 'gray',
  diffAddedWord: 'green',
  diffRemovedWord: 'red'
};

/**
 * 边框样式常量
 */
export const BORDER_STYLES = {
  single: 'single',
  double: 'double',
  round: 'round',
  bold: 'bold',
  singleDouble: 'singleDouble',
  doubleSingle: 'doubleSingle',
  classic: 'classic'
};

/**
 * 特殊字符常量
 * @original: s0对象 (多处使用)
 */
export const SYMBOLS = {
  tick: '✓',
  cross: '✗',
  warning: '⚠',
  info: 'ℹ',
  pointer: '❯',
  arrowUp: '↑',
  arrowDown: '↓',
  arrowLeft: '←',
  arrowRight: '→',
  bullet: '•',
  ellipsis: '…',
  hamburger: '☰',
  heart: '♥',
  star: '★',
  circle: '●',
  square: '■',
  diamond: '♦',
  radioOn: '◉',
  radioOff: '◯',
  checkboxOn: '☑',
  checkboxOff: '☐',
  questionMarkPrefix: '?',
  oneHalf: '½',
  oneThird: '⅓',
  oneQuarter: '¼',
  oneFifth: '⅕',
  oneSixth: '⅙',
  oneEighth: '⅛',
  twoThirds: '⅔',
  twoFifths: '⅖',
  threeQuarters: '¾',
  threeFifths: '⅗',
  threEighths: '⅜',
  fourFifths: '⅘',
  fiveSixths: '⅚',
  fiveEighths: '⅝',
  sevenEighths: '⅞'
};

/**
 * 创建带样式的文本组件
 * @param {string} color - 文本颜色
 * @param {boolean} bold - 是否加粗
 * @param {boolean} dim - 是否变暗
 * @returns {Function} 文本组件函数
 */
export function createStyledText(color, bold = false, dim = false) {
  return function StyledText({ children, ...props }) {
    return React.createElement(Text, {
      color,
      bold,
      dimColor: dim,
      ...props
    }, children);
  };
}

/**
 * 预定义的样式文本组件
 */
export const BoldText = createStyledText(undefined, true);
export const DimText = createStyledText(undefined, false, true);
export const ErrorText = createStyledText(COLORS.error);
export const SuccessText = createStyledText(COLORS.success);
export const WarningText = createStyledText(COLORS.warning);
export const InfoText = createStyledText(COLORS.info);
export const SecondaryText = createStyledText(COLORS.secondaryText);

/**
 * 创建带边框的容器组件
 * @param {string} borderColor - 边框颜色
 * @param {string} borderStyle - 边框样式
 * @returns {Function} 容器组件函数
 */
export function createBorderedBox(borderColor = COLORS.secondaryBorder, borderStyle = BORDER_STYLES.round) {
  return function BorderedBox({ children, ...props }) {
    return React.createElement(Box, {
      borderStyle,
      borderColor,
      padding: 1,
      ...props
    }, children);
  };
}

/**
 * 预定义的边框容器组件
 */
export const ErrorBox = createBorderedBox(COLORS.error);
export const SuccessBox = createBorderedBox(COLORS.success);
export const WarningBox = createBorderedBox(COLORS.warning);
export const InfoBox = createBorderedBox(COLORS.info);

/**
 * 加载指示器组件
 * @original: L5组件 (L55716)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 加载指示器
 */
export function LoadingSpinner({ text = 'Loading...', ...props }) {
  const [frame, setFrame] = React.useState(0);
  const frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];

  React.useEffect(() => {
    const interval = setInterval(() => {
      setFrame(prev => (prev + 1) % frames.length);
    }, 80);

    return () => clearInterval(interval);
  }, []);

  return React.createElement(Box, props,
    React.createElement(Text, { color: COLORS.cyan }, frames[frame]),
    React.createElement(Text, null, ` ${text}`)
  );
}

/**
 * 分隔线组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 分隔线
 */
export function Divider({ 
  character = '─', 
  length = 40, 
  color = COLORS.secondaryBorder,
  ...props 
}) {
  return React.createElement(Text, {
    color,
    ...props
  }, character.repeat(length));
}

/**
 * 标题组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 标题
 */
export function Heading({ 
  children, 
  level = 1, 
  color = COLORS.primary,
  ...props 
}) {
  const prefix = '#'.repeat(level);
  
  return React.createElement(Text, {
    bold: true,
    color,
    ...props
  }, `${prefix} ${children}`);
}

/**
 * 高级组件工具函数
 */

/**
 * 条件渲染组件
 * @param {boolean} condition - 条件
 * @param {JSX.Element} children - 子组件
 * @returns {JSX.Element|null} 条件渲染结果
 */
export function ConditionalRender({ condition, children }) {
  return condition ? children : null;
}

/**
 * 列表渲染组件
 * @param {Array} items - 列表项
 * @param {Function} renderItem - 渲染函数
 * @param {string} keyExtractor - 键提取函数
 * @returns {JSX.Element} 列表组件
 */
export function List({ items, renderItem, keyExtractor = (item, index) => index }) {
  if (!Array.isArray(items)) {
    return null;
  }

  return React.createElement(Box, {
    flexDirection: 'column'
  }, items.map((item, index) => 
    React.createElement(React.Fragment, {
      key: keyExtractor(item, index)
    }, renderItem(item, index))
  ));
}
