/**
 * 对话框UI组件
 * 
 * 提供各种对话框、模态框、确认框等组件
 * 
 * @original: 对话框相关组件 (L8439-8472, L54379-54431, L55042-55071)
 */

import React from 'react';
import { Box, Text, SYMBOLS, COLORS, Fragment } from './base.js';
import { Select, SimpleSelect } from './select.js';
import { logger } from '../../utils/logger.js';

/**
 * 基础对话框组件
 * @original: 对话框基础结构 (L8439)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 对话框组件
 */
export function Dialog({
  children,
  title,
  width = 70,
  height,
  borderColor = COLORS.secondaryBorder,
  borderStyle = 'round',
  padding = 1,
  gap = 1,
  onCancel,
  showCancelHint = true,
  ...props
}) {
  // 处理取消操作
  React.useEffect(() => {
    if (!onCancel) return;

    const handleKeyPress = (key, input) => {
      if (key === 'escape' || (input.ctrl && key === 'c')) {
        onCancel();
      }
    };

    // 这里应该绑定到实际的键盘事件处理器
    
    return () => {
      // 清理事件监听器
    };
  }, [onCancel]);

  return React.createElement(Box, {
    flexDirection: 'column',
    borderStyle,
    borderColor,
    padding,
    width,
    height,
    gap,
    ...props
  },
    // 标题
    title && React.createElement(Text, {
      bold: true,
      color: COLORS.primary
    }, title),
    
    // 内容
    React.createElement(Box, {
      flexDirection: 'column',
      flexGrow: 1,
      gap
    }, children),
    
    // 取消提示
    showCancelHint && onCancel && React.createElement(Text, {
      color: COLORS.secondaryText,
      alignSelf: 'center'
    }, 'Press Esc to cancel')
  );
}

/**
 * 错误对话框组件
 * @original: 配置错误对话框 (L8439-8472)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 错误对话框组件
 */
export function ErrorDialog({
  title = 'Error',
  message,
  details,
  onRetry,
  onCancel,
  onExit,
  ...props
}) {
  const options = [];
  
  if (onRetry) {
    options.push({
      label: 'Retry',
      value: 'retry'
    });
  }
  
  if (onExit) {
    options.push({
      label: 'Exit and fix manually',
      value: 'exit'
    });
  }
  
  if (onCancel) {
    options.push({
      label: 'Cancel',
      value: 'cancel'
    });
  }

  const handleSelect = React.useCallback((value) => {
    switch (value) {
      case 'retry':
        onRetry?.();
        break;
      case 'exit':
        onExit?.();
        break;
      case 'cancel':
        onCancel?.();
        break;
    }
  }, [onRetry, onExit, onCancel]);

  return React.createElement(Dialog, {
    title,
    borderColor: COLORS.error,
    onCancel,
    ...props
  },
    React.createElement(Text, {
      bold: true,
      color: COLORS.error
    }, title),
    
    React.createElement(Box, {
      flexDirection: 'column',
      gap: 1
    },
      React.createElement(Text, null, message),
      details && React.createElement(Text, {
        color: COLORS.secondaryText
      }, details)
    ),
    
    options.length > 0 && React.createElement(Box, {
      flexDirection: 'column'
    },
      React.createElement(Text, {
        bold: true
      }, 'Choose an option:'),
      React.createElement(Select, {
        options,
        onSubmit: handleSelect,
        onCancel
      })
    )
  );
}

/**
 * 确认对话框组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 确认对话框组件
 */
export function ConfirmDialog({
  title = 'Confirm',
  message,
  confirmLabel = 'Yes',
  cancelLabel = 'No',
  onConfirm,
  onCancel,
  variant = 'default', // 'default' | 'warning' | 'danger'
  ...props
}) {
  const borderColor = {
    default: COLORS.secondaryBorder,
    warning: COLORS.warning,
    danger: COLORS.error
  }[variant];

  const titleColor = {
    default: COLORS.primary,
    warning: COLORS.warning,
    danger: COLORS.error
  }[variant];

  const options = [
    { label: confirmLabel, value: 'confirm' },
    { label: cancelLabel, value: 'cancel' }
  ];

  const handleSelect = React.useCallback((value) => {
    if (value === 'confirm') {
      onConfirm?.();
    } else {
      onCancel?.();
    }
  }, [onConfirm, onCancel]);

  return React.createElement(Dialog, {
    title,
    borderColor,
    onCancel,
    ...props
  },
    React.createElement(Text, {
      bold: true,
      color: titleColor
    }, title),
    
    React.createElement(Text, null, message),
    
    React.createElement(Select, {
      options,
      onSubmit: handleSelect,
      onCancel,
      color: titleColor
    })
  );
}

/**
 * 警告对话框组件
 * @original: 警告对话框逻辑 (L55042-55071)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 警告对话框组件
 */
export function WarningDialog({
  title = 'Warning',
  message,
  details,
  onAccept,
  onDecline,
  acceptLabel = 'Accept',
  declineLabel = 'Decline',
  showWarningIcon = true,
  ...props
}) {
  const options = [
    { label: declineLabel, value: 'decline' },
    { label: acceptLabel, value: 'accept' }
  ];

  const handleSelect = React.useCallback((value) => {
    if (value === 'accept') {
      onAccept?.();
    } else {
      onDecline?.();
    }
  }, [onAccept, onDecline]);

  return React.createElement(Dialog, {
    title,
    borderColor: COLORS.warning,
    onCancel: onDecline,
    ...props
  },
    React.createElement(Box, {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 1
    },
      showWarningIcon && React.createElement(Text, {
        color: COLORS.warning
      }, SYMBOLS.warning),
      React.createElement(Text, {
        bold: true,
        color: COLORS.warning
      }, title)
    ),
    
    React.createElement(Box, {
      flexDirection: 'column',
      gap: 1
    },
      React.createElement(Text, null, message),
      details && React.createElement(Text, {
        color: COLORS.secondaryText
      }, details)
    ),
    
    React.createElement(Select, {
      options,
      onSubmit: handleSelect,
      onCancel: onDecline,
      color: COLORS.warning
    })
  );
}

/**
 * 信息对话框组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 信息对话框组件
 */
export function InfoDialog({
  title = 'Information',
  message,
  details,
  onClose,
  closeLabel = 'OK',
  showInfoIcon = true,
  ...props
}) {
  const options = [
    { label: closeLabel, value: 'close' }
  ];

  const handleSelect = React.useCallback(() => {
    onClose?.();
  }, [onClose]);

  return React.createElement(Dialog, {
    title,
    borderColor: COLORS.info,
    onCancel: onClose,
    ...props
  },
    React.createElement(Box, {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 1
    },
      showInfoIcon && React.createElement(Text, {
        color: COLORS.info
      }, SYMBOLS.info),
      React.createElement(Text, {
        bold: true,
        color: COLORS.info
      }, title)
    ),
    
    React.createElement(Box, {
      flexDirection: 'column',
      gap: 1
    },
      React.createElement(Text, null, message),
      details && React.createElement(Text, {
        color: COLORS.secondaryText
      }, details)
    ),
    
    React.createElement(Select, {
      options,
      onSubmit: handleSelect,
      onCancel: onClose,
      color: COLORS.info
    })
  );
}

/**
 * 加载对话框组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 加载对话框组件
 */
export function LoadingDialog({
  title = 'Loading',
  message = 'Please wait...',
  progress,
  onCancel,
  showProgress = false,
  ...props
}) {
  const [dots, setDots] = React.useState('');

  // 动画效果
  React.useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev.length >= 3) return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, []);

  return React.createElement(Dialog, {
    title,
    borderColor: COLORS.info,
    onCancel,
    showCancelHint: Boolean(onCancel),
    ...props
  },
    React.createElement(Box, {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 1
    },
      React.createElement(Text, {
        color: COLORS.info
      }, SYMBOLS.info),
      React.createElement(Text, {
        bold: true,
        color: COLORS.info
      }, title)
    ),
    
    React.createElement(Text, null, message + dots),
    
    showProgress && typeof progress === 'number' && React.createElement(Box, {
      flexDirection: 'column',
      gap: 1
    },
      React.createElement(Text, {
        color: COLORS.secondaryText
      }, `Progress: ${Math.round(progress)}%`),
      React.createElement(ProgressBar, {
        progress,
        width: 40
      })
    )
  );
}

/**
 * 进度条组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 进度条组件
 */
function ProgressBar({
  progress = 0,
  width = 20,
  fillChar = '█',
  emptyChar = '░',
  color = COLORS.success,
  ...props
}) {
  const filledWidth = Math.round((progress / 100) * width);
  const emptyWidth = width - filledWidth;

  return React.createElement(Box, {
    flexDirection: 'row',
    ...props
  },
    React.createElement(Text, {
      color
    }, fillChar.repeat(filledWidth)),
    React.createElement(Text, {
      color: COLORS.secondaryText
    }, emptyChar.repeat(emptyWidth))
  );
}
