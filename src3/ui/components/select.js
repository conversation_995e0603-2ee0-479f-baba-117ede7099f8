/**
 * 选择器UI组件
 * 
 * 提供单选、多选等选择器组件
 * 
 * @original: Select和MultiSelect组件 (L7737-7753, L7495-7511, L7912-7928)
 */

import React from 'react';
import { Box, Text, SYMBOLS, COLORS } from './base.js';
import { logger } from '../../utils/logger.js';

/**
 * 选择器选项组件
 * @original: BR2组件 (L7737)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 选项组件
 */
function SelectOption({
  children,
  isFocused = false,
  isSelected = false,
  color = COLORS.primary,
  ...props
}) {
  return React.createElement(Box, {
    ...props
  },
    // 焦点指示器
    isFocused && React.createElement(Text, {
      color
    }, SYMBOLS.pointer),
    
    // 标签
    React.createElement(Text, {
      color: isFocused ? color : undefined,
      bold: isSelected
    }, children),
    
    // 选中指示器
    isSelected && React.createElement(Text, {
      color: COLORS.success
    }, ` ${SYMBOLS.tick}`)
  );
}

/**
 * 多选选项组件
 * @original: sM2组件 (L7495)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 多选选项组件
 */
function MultiSelectOption({
  children,
  isFocused = false,
  isSelected = false,
  color = COLORS.primary,
  ...props
}) {
  return React.createElement(Box, {
    ...props
  },
    // 焦点指示器
    isFocused && React.createElement(Text, {
      color
    }, SYMBOLS.pointer),
    
    // 标签
    React.createElement(Text, {
      color: isFocused ? color : undefined,
      bold: isSelected
    }, children),
    
    // 选中指示器
    isSelected && React.createElement(Text, {
      color: COLORS.success
    }, ` ${SYMBOLS.tick}`)
  );
}

/**
 * 选择器容器组件
 * @original: Select组件逻辑 (L7912)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 选择器组件
 */
export function Select({
  options = [],
  value,
  onChange,
  onSubmit,
  onCancel,
  placeholder = 'Select an option...',
  searchable = false,
  searchQuery = '',
  onSearchChange,
  visibleOptionCount = 10,
  color = COLORS.primary,
  disabled = false,
  ...props
}) {
  const [focusedIndex, setFocusedIndex] = React.useState(0);
  const [searchValue, setSearchValue] = React.useState(searchQuery);

  // 过滤选项
  const filteredOptions = React.useMemo(() => {
    if (!searchable || !searchValue) {
      return options;
    }
    
    return options.filter(option => 
      option.label.toLowerCase().includes(searchValue.toLowerCase())
    );
  }, [options, searchValue, searchable]);

  // 可见选项
  const visibleOptions = React.useMemo(() => {
    return filteredOptions.slice(0, visibleOptionCount);
  }, [filteredOptions, visibleOptionCount]);

  // 处理键盘事件
  React.useEffect(() => {
    if (disabled) return;

    const handleKeyPress = (key, input) => {
      if (input.ctrl && key === 'c') {
        onCancel?.();
        return;
      }

      if (key === 'escape') {
        onCancel?.();
        return;
      }

      if (key === 'return') {
        const selectedOption = visibleOptions[focusedIndex];
        if (selectedOption) {
          onChange?.(selectedOption.value);
          onSubmit?.(selectedOption.value);
        }
        return;
      }

      if (key === 'up' || key === 'upArrow') {
        setFocusedIndex(prev => 
          prev > 0 ? prev - 1 : visibleOptions.length - 1
        );
        return;
      }

      if (key === 'down' || key === 'downArrow') {
        setFocusedIndex(prev => 
          prev < visibleOptions.length - 1 ? prev + 1 : 0
        );
        return;
      }

      // 搜索功能
      if (searchable && key.length === 1) {
        const newSearchValue = searchValue + key;
        setSearchValue(newSearchValue);
        onSearchChange?.(newSearchValue);
        setFocusedIndex(0);
      }

      if (searchable && key === 'backspace') {
        const newSearchValue = searchValue.slice(0, -1);
        setSearchValue(newSearchValue);
        onSearchChange?.(newSearchValue);
        setFocusedIndex(0);
      }
    };

    // 这里应该绑定到实际的键盘事件处理器
    // 在实际的Ink应用中，这会通过useInput hook处理
    
    return () => {
      // 清理事件监听器
    };
  }, [disabled, focusedIndex, visibleOptions, searchValue, searchable, onChange, onSubmit, onCancel, onSearchChange]);

  if (visibleOptions.length === 0) {
    return React.createElement(Box, props,
      React.createElement(Text, {
        color: COLORS.secondaryText
      }, searchable && searchValue ? `No results for "${searchValue}"` : 'No options available')
    );
  }

  return React.createElement(Box, {
    flexDirection: 'column',
    ...props
  },
    // 搜索提示
    searchable && searchValue && React.createElement(Text, {
      color: COLORS.secondaryText
    }, `Search: ${searchValue}`),
    
    // 选项列表
    visibleOptions.map((option, index) => {
      let label = option.label;
      
      // 高亮搜索匹配的文本
      if (searchable && searchValue && option.label.includes(searchValue)) {
        const matchIndex = option.label.indexOf(searchValue);
        label = React.createElement(React.Fragment, null,
          option.label.slice(0, matchIndex),
          React.createElement(Text, {
            backgroundColor: COLORS.yellow,
            color: COLORS.black
          }, searchValue),
          option.label.slice(matchIndex + searchValue.length)
        );
      }

      return React.createElement(SelectOption, {
        key: option.value,
        isFocused: !disabled && focusedIndex === index,
        isSelected: value === option.value,
        color
      }, label);
    })
  );
}

/**
 * 多选器组件
 * @original: MultiSelect组件逻辑 (L7711)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 多选器组件
 */
export function MultiSelect({
  options = [],
  value = [],
  onChange,
  onSubmit,
  onCancel,
  placeholder = 'Select options...',
  searchable = false,
  searchQuery = '',
  onSearchChange,
  visibleOptionCount = 10,
  color = COLORS.primary,
  disabled = false,
  ...props
}) {
  const [focusedIndex, setFocusedIndex] = React.useState(0);
  const [searchValue, setSearchValue] = React.useState(searchQuery);

  // 过滤选项
  const filteredOptions = React.useMemo(() => {
    if (!searchable || !searchValue) {
      return options;
    }
    
    return options.filter(option => 
      option.label.toLowerCase().includes(searchValue.toLowerCase())
    );
  }, [options, searchValue, searchable]);

  // 可见选项
  const visibleOptions = React.useMemo(() => {
    return filteredOptions.slice(0, visibleOptionCount);
  }, [filteredOptions, visibleOptionCount]);

  // 处理选项切换
  const toggleOption = React.useCallback((optionValue) => {
    const newValue = value.includes(optionValue)
      ? value.filter(v => v !== optionValue)
      : [...value, optionValue];
    
    onChange?.(newValue);
  }, [value, onChange]);

  // 处理键盘事件
  React.useEffect(() => {
    if (disabled) return;

    const handleKeyPress = (key, input) => {
      if (input.ctrl && key === 'c') {
        onCancel?.();
        return;
      }

      if (key === 'escape') {
        onCancel?.();
        return;
      }

      if (key === 'return') {
        onSubmit?.(value);
        return;
      }

      if (key === 'space') {
        const selectedOption = visibleOptions[focusedIndex];
        if (selectedOption) {
          toggleOption(selectedOption.value);
        }
        return;
      }

      if (key === 'up' || key === 'upArrow') {
        setFocusedIndex(prev => 
          prev > 0 ? prev - 1 : visibleOptions.length - 1
        );
        return;
      }

      if (key === 'down' || key === 'downArrow') {
        setFocusedIndex(prev => 
          prev < visibleOptions.length - 1 ? prev + 1 : 0
        );
        return;
      }

      // 搜索功能
      if (searchable && key.length === 1) {
        const newSearchValue = searchValue + key;
        setSearchValue(newSearchValue);
        onSearchChange?.(newSearchValue);
        setFocusedIndex(0);
      }

      if (searchable && key === 'backspace') {
        const newSearchValue = searchValue.slice(0, -1);
        setSearchValue(newSearchValue);
        onSearchChange?.(newSearchValue);
        setFocusedIndex(0);
      }
    };

    // 这里应该绑定到实际的键盘事件处理器
    
    return () => {
      // 清理事件监听器
    };
  }, [disabled, focusedIndex, visibleOptions, searchValue, searchable, value, toggleOption, onSubmit, onCancel, onSearchChange]);

  if (visibleOptions.length === 0) {
    return React.createElement(Box, props,
      React.createElement(Text, {
        color: COLORS.secondaryText
      }, searchable && searchValue ? `No results for "${searchValue}"` : 'No options available')
    );
  }

  return React.createElement(Box, {
    flexDirection: 'column',
    ...props
  },
    // 搜索提示
    searchable && searchValue && React.createElement(Text, {
      color: COLORS.secondaryText
    }, `Search: ${searchValue}`),
    
    // 已选择项目提示
    value.length > 0 && React.createElement(Text, {
      color: COLORS.success
    }, `Selected: ${value.length} item${value.length !== 1 ? 's' : ''}`),
    
    // 选项列表
    visibleOptions.map((option, index) => {
      let label = option.label;
      
      // 高亮搜索匹配的文本
      if (searchable && searchValue && option.label.includes(searchValue)) {
        const matchIndex = option.label.indexOf(searchValue);
        label = React.createElement(React.Fragment, null,
          option.label.slice(0, matchIndex),
          React.createElement(Text, {
            backgroundColor: COLORS.yellow,
            color: COLORS.black
          }, searchValue),
          option.label.slice(matchIndex + searchValue.length)
        );
      }

      return React.createElement(MultiSelectOption, {
        key: option.value,
        isFocused: !disabled && focusedIndex === index,
        isSelected: value.includes(option.value),
        color
      }, label);
    })
  );
}

/**
 * 简单选择器组件（用于是/否选择）
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 简单选择器组件
 */
export function SimpleSelect({
  options = [
    { label: 'Yes', value: true },
    { label: 'No', value: false }
  ],
  defaultValue,
  onChange,
  onSubmit,
  onCancel,
  color = COLORS.primary,
  ...props
}) {
  const [selectedValue, setSelectedValue] = React.useState(defaultValue);

  const handleChange = React.useCallback((value) => {
    setSelectedValue(value);
    onChange?.(value);
  }, [onChange]);

  const handleSubmit = React.useCallback((value) => {
    onSubmit?.(value);
  }, [onSubmit]);

  return React.createElement(Select, {
    options,
    value: selectedValue,
    onChange: handleChange,
    onSubmit: handleSubmit,
    onCancel,
    color,
    ...props
  });
}

/**
 * 确认选择器组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 确认选择器组件
 */
export function ConfirmSelect({
  message = 'Are you sure?',
  confirmLabel = 'Yes',
  cancelLabel = 'No',
  onConfirm,
  onCancel,
  color = COLORS.warning,
  ...props
}) {
  const options = [
    { label: confirmLabel, value: 'confirm' },
    { label: cancelLabel, value: 'cancel' }
  ];

  const handleSubmit = React.useCallback((value) => {
    if (value === 'confirm') {
      onConfirm?.();
    } else {
      onCancel?.();
    }
  }, [onConfirm, onCancel]);

  return React.createElement(Box, {
    flexDirection: 'column',
    gap: 1,
    ...props
  },
    React.createElement(Text, null, message),
    React.createElement(Select, {
      options,
      onSubmit: handleSubmit,
      onCancel,
      color
    })
  );
}
