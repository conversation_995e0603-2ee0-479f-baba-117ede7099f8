/**
 * Claude Code - 主入口文件
 * 
 * 这是Claude Code CLI工具的主入口点，负责初始化应用程序并启动CLI界面。
 * 
 * @original: 原始文件中的ER8()函数和相关初始化代码
 */

import { createCLI } from './cli/index.js';
import { initializeGlobalState } from './core/session.js';
import { setupEnvironment } from './utils/environment.js';
import { logger } from './utils/logger.js';
import { globalPerformanceMonitor } from './utils/performance.js';
import { globalSessionManager } from './core/session.js';
import { globalAuthManager } from './services/auth.js';
import { globalDatabaseManager } from './services/database.js';

/**
 * 初始化核心服务
 * @returns {Promise<void>}
 */
async function initializeCoreServices() {
  try {
    logger.debug('初始化核心服务...');

    // 启动性能监控
    globalPerformanceMonitor.startMonitoring({
      interval: 30000, // 30秒
      memorySnapshots: true,
      logMetrics: false
    });

    // 初始化认证管理器
    await globalAuthManager.checkAuthStatus();

    // 初始化数据库
    const sessionFile = process.env.CLAUDE_SESSION_FILE ||
      require('path').join(require('os').homedir(), '.claude', 'sessions', 'default.jsonl');
    await globalDatabaseManager.initialize(sessionFile);

    // 加载会话
    await globalSessionManager.loadAllSessions();

    // 启动自动保存
    globalSessionManager.startAutoSave();

    logger.info('核心服务初始化完成');

  } catch (error) {
    logger.error('核心服务初始化失败', { error: error.message });
    throw error;
  }
}

/**
 * 主应用程序入口点
 * 初始化全局状态、设置环境并启动CLI
 *
 * @original: KR8() 和相关初始化函数
 */
async function main() {
  try {
    logger.info('Claude Code 启动中...');

    // 设置环境和全局状态
    setupEnvironment();
    initializeGlobalState();

    // 初始化核心服务
    await initializeCoreServices();

    // 创建并启动CLI应用
    const cli = createCLI();
    await cli.parseAsync(process.argv);

  } catch (error) {
    logger.error('应用程序启动失败:', error);

    // 记录性能数据
    const performanceSummary = globalPerformanceMonitor.getSummary();
    logger.debug('启动失败时的性能数据', performanceSummary);

    process.exit(1);
  }
}

/**
 * 优雅关闭处理
 */
function setupGracefulShutdown() {
  const shutdown = async (signal) => {
    logger.info(`收到 ${signal} 信号，开始优雅关闭...`);

    try {
      // 停止性能监控
      globalPerformanceMonitor.stopMonitoring();

      // 保存会话数据
      await globalSessionManager.saveAllSessions();

      // 停止自动保存
      globalSessionManager.stopAutoSave();

      // 保存数据库
      await globalDatabaseManager.saveToFile();

      logger.info('应用程序已优雅关闭');
      process.exit(0);

    } catch (error) {
      logger.error('优雅关闭失败', { error: error.message });
      process.exit(1);
    }
  };

  process.on('SIGINT', () => shutdown('SIGINT'));
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGQUIT', () => shutdown('SIGQUIT'));

  // 处理未捕获的异常
  process.on('uncaughtException', (error) => {
    logger.error('未捕获的异常', { error: error.message, stack: error.stack });
    shutdown('uncaughtException');
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('未处理的Promise拒绝', { reason, promise });
    shutdown('unhandledRejection');
  });
}

/**
 * 导出主要功能供外部使用
 * @original: export语句在文件末尾
 */
export {
  main as default,
  createCLI,
  initializeGlobalState,
  initializeCoreServices,
  setupEnvironment
};

// 设置优雅关闭
setupGracefulShutdown();

// 如果直接运行此文件，则启动主程序
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('未捕获的错误:', error);
    process.exit(1);
  });
}
