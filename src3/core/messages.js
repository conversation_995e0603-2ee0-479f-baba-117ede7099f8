/**
 * 消息处理系统
 * 
 * 管理Claude Code的消息流、工具使用、流式响应等
 * 
 * @original: 消息处理相关代码 (L12013-12323, L55592-55670, L56536-56593)
 */

import { logger } from '../utils/logger.js';
import { generateUUID } from '../utils/crypto.js';

/**
 * 消息类型枚举
 */
export const MESSAGE_TYPES = {
  USER: 'user',
  ASSISTANT: 'assistant',
  SYSTEM: 'system',
  TOOL_USE: 'tool_use',
  TOOL_RESULT: 'tool_result',
  CONTROL_REQUEST: 'control_request',
  CONTROL_RESPONSE: 'control_response',
  STREAM_REQUEST_START: 'stream_request_start',
  ATTACHMENT: 'attachment'
};

/**
 * 工具使用状态枚举
 */
export const TOOL_USE_STATUS = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

/**
 * 消息处理器类
 */
export class MessageProcessor {
  constructor(options = {}) {
    this.maxMessageLength = options.maxMessageLength || 100000;
    this.enableStreaming = options.enableStreaming !== false;
    this.toolUseTimeout = options.toolUseTimeout || 300000; // 5分钟
  }

  /**
   * 处理用户消息
   * @original: 用户消息处理逻辑 (L55592-55625)
   * @param {string} content - 消息内容
   * @param {Object} options - 处理选项
   * @returns {Object} 处理后的消息
   */
  processUserMessage(content, options = {}) {
    const message = {
      type: MESSAGE_TYPES.USER,
      role: 'user',
      content: this.sanitizeContent(content),
      timestamp: new Date().toISOString(),
      uuid: generateUUID(),
      requestId: options.requestId,
      parent_tool_use_id: options.parentToolUseId || null,
      session_id: options.sessionId
    };

    logger.debug('处理用户消息', { 
      contentLength: content.length,
      hasParentToolUse: !!options.parentToolUseId 
    });

    return message;
  }

  /**
   * 处理助手消息
   * @original: 助手消息处理逻辑 (L55594-55608)
   * @param {Object} assistantMessage - 助手消息
   * @param {Object} options - 处理选项
   * @returns {Object} 处理后的消息
   */
  processAssistantMessage(assistantMessage, options = {}) {
    const message = {
      type: MESSAGE_TYPES.ASSISTANT,
      role: 'assistant',
      content: assistantMessage.content,
      timestamp: new Date().toISOString(),
      uuid: generateUUID(),
      parent_tool_use_id: options.parentToolUseId || null,
      session_id: options.sessionId,
      usage: assistantMessage.usage,
      model: assistantMessage.model
    };

    // 处理工具使用
    if (this.containsToolUse(assistantMessage)) {
      message.tool_uses = this.extractToolUses(assistantMessage);
    }

    logger.debug('处理助手消息', { 
      hasToolUses: !!message.tool_uses,
      toolUseCount: message.tool_uses?.length || 0
    });

    return message;
  }

  /**
   * 检查消息是否包含工具使用
   * @original: O8B()函数 (L12013)
   * @param {Object} message - 消息对象
   * @returns {boolean} 是否包含工具使用
   */
  containsToolUse(message) {
    if (!Array.isArray(message.content)) return false;
    
    return message.content.some(block => 
      block.type === 'tool_use' || 
      block.type === 'server_tool_use' || 
      block.type === 'mcp_tool_use'
    );
  }

  /**
   * 提取工具使用
   * @param {Object} message - 消息对象
   * @returns {Array} 工具使用数组
   */
  extractToolUses(message) {
    if (!Array.isArray(message.content)) return [];
    
    return message.content
      .filter(block => 
        block.type === 'tool_use' || 
        block.type === 'server_tool_use' || 
        block.type === 'mcp_tool_use'
      )
      .map(block => ({
        id: block.id,
        name: block.name,
        input: block.input,
        type: block.type,
        status: TOOL_USE_STATUS.PENDING
      }));
  }

  /**
   * 处理工具结果
   * @param {string} toolUseId - 工具使用ID
   * @param {Object} result - 工具结果
   * @param {Object} options - 处理选项
   * @returns {Object} 工具结果消息
   */
  processToolResult(toolUseId, result, options = {}) {
    const message = {
      type: MESSAGE_TYPES.TOOL_RESULT,
      tool_use_id: toolUseId,
      content: this.formatToolResult(result),
      timestamp: new Date().toISOString(),
      session_id: options.sessionId,
      is_error: result.type === 'error'
    };

    logger.debug('处理工具结果', { 
      toolUseId, 
      resultType: result.type,
      isError: message.is_error 
    });

    return message;
  }

  /**
   * 格式化工具结果
   * @param {Object} result - 工具结果
   * @returns {string|Array} 格式化的结果
   */
  formatToolResult(result) {
    if (result.type === 'error') {
      return `Error: ${result.message}`;
    }

    if (result.content) {
      return result.content;
    }

    if (result.message) {
      return result.message;
    }

    return JSON.stringify(result);
  }

  /**
   * 清理消息内容
   * @param {string} content - 原始内容
   * @returns {string} 清理后的内容
   */
  sanitizeContent(content) {
    if (!content || typeof content !== 'string') {
      return '';
    }

    // 截断过长的内容
    if (content.length > this.maxMessageLength) {
      return content.substring(0, this.maxMessageLength) + '\n... (content truncated)';
    }

    return content.trim();
  }

  /**
   * 创建控制请求
   * @original: 控制请求创建逻辑 (L55293-55299)
   * @param {string} subtype - 子类型
   * @param {Object} requestData - 请求数据
   * @returns {Object} 控制请求
   */
  createControlRequest(subtype, requestData) {
    const requestId = generateUUID();
    
    const request = {
      type: MESSAGE_TYPES.CONTROL_REQUEST,
      request_id: requestId,
      request: {
        subtype,
        ...requestData
      }
    };

    logger.debug('创建控制请求', { subtype, requestId });
    
    return request;
  }

  /**
   * 创建控制响应
   * @original: 控制响应创建逻辑 (L55554-55557)
   * @param {string} requestId - 请求ID
   * @param {string} subtype - 子类型
   * @param {Object} responseData - 响应数据
   * @returns {Object} 控制响应
   */
  createControlResponse(requestId, subtype, responseData = {}) {
    const response = {
      type: MESSAGE_TYPES.CONTROL_RESPONSE,
      response: {
        subtype,
        request_id: requestId,
        ...responseData
      }
    };

    logger.debug('创建控制响应', { requestId, subtype });
    
    return response;
  }

  /**
   * 验证消息格式
   * @param {Object} message - 消息对象
   * @returns {boolean} 是否有效
   */
  validateMessage(message) {
    if (!message || typeof message !== 'object') {
      return false;
    }

    if (!message.type || !Object.values(MESSAGE_TYPES).includes(message.type)) {
      return false;
    }

    // 根据消息类型进行特定验证
    switch (message.type) {
      case MESSAGE_TYPES.USER:
      case MESSAGE_TYPES.ASSISTANT:
        return !!(message.role && message.content);
      
      case MESSAGE_TYPES.TOOL_USE:
        return !!(message.id && message.name);
      
      case MESSAGE_TYPES.TOOL_RESULT:
        return !!message.tool_use_id;
      
      case MESSAGE_TYPES.CONTROL_REQUEST:
        return !!(message.request_id && message.request);
      
      case MESSAGE_TYPES.CONTROL_RESPONSE:
        return !!(message.response && message.response.request_id);
      
      default:
        return true;
    }
  }

  /**
   * 规范化消息历史
   * @param {Array} messages - 消息数组
   * @returns {Array} 规范化的消息数组
   */
  normalizeMessageHistory(messages) {
    return messages
      .filter(message => this.validateMessage(message))
      .map(message => this.normalizeMessage(message))
      .filter(message => message !== null);
  }

  /**
   * 规范化单个消息
   * @param {Object} message - 消息对象
   * @returns {Object|null} 规范化的消息
   */
  normalizeMessage(message) {
    // 跳过控制消息
    if (message.type === MESSAGE_TYPES.CONTROL_REQUEST || 
        message.type === MESSAGE_TYPES.CONTROL_RESPONSE) {
      return null;
    }

    // 确保必要字段存在
    const normalized = {
      ...message,
      timestamp: message.timestamp || new Date().toISOString(),
      uuid: message.uuid || generateUUID()
    };

    return normalized;
  }
}

/**
 * 流式消息处理器类
 */
export class StreamingMessageProcessor {
  constructor(options = {}) {
    this.bufferSize = options.bufferSize || 8192;
    this.flushInterval = options.flushInterval || 100;
    this.buffer = '';
    this.pendingFlush = null;
  }

  /**
   * 处理流式数据块
   * @param {string} chunk - 数据块
   * @param {Function} onMessage - 消息处理函数
   */
  processChunk(chunk, onMessage) {
    this.buffer += chunk;
    
    // 尝试解析完整的消息
    const messages = this.extractMessages(this.buffer);
    
    for (const message of messages) {
      try {
        const parsed = JSON.parse(message);
        onMessage(parsed);
      } catch (error) {
        logger.warn('解析流式消息失败', { message, error: error.message });
      }
    }

    // 调度刷新
    this.scheduleFlush(onMessage);
  }

  /**
   * 提取完整消息
   * @param {string} buffer - 缓冲区内容
   * @returns {Array} 完整消息数组
   */
  extractMessages(buffer) {
    const messages = [];
    const lines = buffer.split('\n');
    
    // 保留最后一行（可能不完整）
    this.buffer = lines.pop() || '';
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed) {
        messages.push(trimmed);
      }
    }
    
    return messages;
  }

  /**
   * 调度刷新
   * @param {Function} onMessage - 消息处理函数
   */
  scheduleFlush(onMessage) {
    if (this.pendingFlush) {
      clearTimeout(this.pendingFlush);
    }

    this.pendingFlush = setTimeout(() => {
      this.flush(onMessage);
    }, this.flushInterval);
  }

  /**
   * 刷新缓冲区
   * @param {Function} onMessage - 消息处理函数
   */
  flush(onMessage) {
    if (this.buffer.trim()) {
      try {
        const parsed = JSON.parse(this.buffer);
        onMessage(parsed);
        this.buffer = '';
      } catch (error) {
        // 保留缓冲区内容，可能还不完整
      }
    }
    
    this.pendingFlush = null;
  }

  /**
   * 重置处理器
   */
  reset() {
    this.buffer = '';
    if (this.pendingFlush) {
      clearTimeout(this.pendingFlush);
      this.pendingFlush = null;
    }
  }
}

/**
 * 工具使用管理器类
 */
export class ToolUseManager {
  constructor() {
    this.activeToolUses = new Map();
    this.completedToolUses = new Map();
    this.toolUseQueue = [];
  }

  /**
   * 添加工具使用
   * @param {Object} toolUse - 工具使用对象
   */
  addToolUse(toolUse) {
    const id = toolUse.id || generateUUID();
    
    const toolUseWithId = {
      ...toolUse,
      id,
      status: TOOL_USE_STATUS.PENDING,
      createdAt: new Date().toISOString()
    };

    this.activeToolUses.set(id, toolUseWithId);
    this.toolUseQueue.push(toolUseWithId);

    logger.debug('添加工具使用', { id, name: toolUse.name });
    
    return id;
  }

  /**
   * 更新工具使用状态
   * @param {string} id - 工具使用ID
   * @param {string} status - 新状态
   * @param {Object} data - 附加数据
   */
  updateToolUseStatus(id, status, data = {}) {
    const toolUse = this.activeToolUses.get(id);
    
    if (!toolUse) {
      logger.warn('工具使用不存在', { id });
      return;
    }

    const updatedToolUse = {
      ...toolUse,
      status,
      updatedAt: new Date().toISOString(),
      ...data
    };

    this.activeToolUses.set(id, updatedToolUse);

    // 如果完成或失败，移动到完成列表
    if (status === TOOL_USE_STATUS.COMPLETED || 
        status === TOOL_USE_STATUS.FAILED || 
        status === TOOL_USE_STATUS.CANCELLED) {
      this.completedToolUses.set(id, updatedToolUse);
      this.activeToolUses.delete(id);
      
      // 从队列中移除
      this.toolUseQueue = this.toolUseQueue.filter(item => item.id !== id);
    }

    logger.debug('更新工具使用状态', { id, status });
  }

  /**
   * 获取工具使用
   * @param {string} id - 工具使用ID
   * @returns {Object|null} 工具使用对象
   */
  getToolUse(id) {
    return this.activeToolUses.get(id) || this.completedToolUses.get(id) || null;
  }

  /**
   * 获取活跃的工具使用
   * @returns {Array} 活跃的工具使用数组
   */
  getActiveToolUses() {
    return Array.from(this.activeToolUses.values());
  }

  /**
   * 获取工具使用队列
   * @returns {Array} 工具使用队列
   */
  getToolUseQueue() {
    return [...this.toolUseQueue];
  }

  /**
   * 清除完成的工具使用
   * @param {number} maxAge - 最大保留时间（毫秒）
   */
  clearCompletedToolUses(maxAge = 3600000) { // 1小时
    const now = Date.now();
    const toRemove = [];

    for (const [id, toolUse] of this.completedToolUses) {
      const completedAt = new Date(toolUse.updatedAt).getTime();
      if (now - completedAt > maxAge) {
        toRemove.push(id);
      }
    }

    for (const id of toRemove) {
      this.completedToolUses.delete(id);
    }

    if (toRemove.length > 0) {
      logger.debug('清除过期的工具使用记录', { count: toRemove.length });
    }
  }
}

/**
 * 消息验证器类
 */
export class MessageValidator {
  /**
   * 验证消息结构
   * @param {Object} message - 消息对象
   * @returns {Object} 验证结果
   */
  validateMessageStructure(message) {
    const errors = [];

    if (!message) {
      errors.push('Message is null or undefined');
      return { valid: false, errors };
    }

    if (!message.type) {
      errors.push('Message type is required');
    } else if (!Object.values(MESSAGE_TYPES).includes(message.type)) {
      errors.push(`Invalid message type: ${message.type}`);
    }

    // 类型特定验证
    switch (message.type) {
      case MESSAGE_TYPES.USER:
      case MESSAGE_TYPES.ASSISTANT:
        if (!message.role) {
          errors.push('Message role is required for user/assistant messages');
        }
        if (!message.content) {
          errors.push('Message content is required');
        }
        break;

      case MESSAGE_TYPES.TOOL_USE:
        if (!message.id) {
          errors.push('Tool use ID is required');
        }
        if (!message.name) {
          errors.push('Tool name is required');
        }
        break;

      case MESSAGE_TYPES.TOOL_RESULT:
        if (!message.tool_use_id) {
          errors.push('Tool use ID is required for tool result');
        }
        break;
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证消息序列
   * @param {Array} messages - 消息数组
   * @returns {Object} 验证结果
   */
  validateMessageSequence(messages) {
    const errors = [];
    const toolUseIds = new Set();
    const toolResultIds = new Set();

    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      const validation = this.validateMessageStructure(message);
      
      if (!validation.valid) {
        errors.push(`Message ${i}: ${validation.errors.join(', ')}`);
        continue;
      }

      // 检查工具使用和结果的配对
      if (message.type === MESSAGE_TYPES.TOOL_USE) {
        toolUseIds.add(message.id);
      } else if (message.type === MESSAGE_TYPES.TOOL_RESULT) {
        toolResultIds.add(message.tool_use_id);
        
        if (!toolUseIds.has(message.tool_use_id)) {
          errors.push(`Message ${i}: Tool result without corresponding tool use: ${message.tool_use_id}`);
        }
      }
    }

    // 检查未完成的工具使用
    const uncompletedToolUses = Array.from(toolUseIds).filter(id => !toolResultIds.has(id));
    if (uncompletedToolUses.length > 0) {
      errors.push(`Uncompleted tool uses: ${uncompletedToolUses.join(', ')}`);
    }

    return {
      valid: errors.length === 0,
      errors,
      stats: {
        totalMessages: messages.length,
        toolUses: toolUseIds.size,
        toolResults: toolResultIds.size,
        uncompletedToolUses: uncompletedToolUses.length
      }
    };
  }
}

/**
 * 全局消息处理器实例
 */
export const globalMessageProcessor = new MessageProcessor();

/**
 * 全局工具使用管理器实例
 */
export const globalToolUseManager = new ToolUseManager();

/**
 * 全局消息验证器实例
 */
export const globalMessageValidator = new MessageValidator();

/**
 * 便捷函数：处理用户消息
 * @param {string} content - 消息内容
 * @param {Object} options - 处理选项
 * @returns {Object} 处理后的消息
 */
export function processUserMessage(content, options = {}) {
  return globalMessageProcessor.processUserMessage(content, options);
}

/**
 * 便捷函数：处理助手消息
 * @param {Object} assistantMessage - 助手消息
 * @param {Object} options - 处理选项
 * @returns {Object} 处理后的消息
 */
export function processAssistantMessage(assistantMessage, options = {}) {
  return globalMessageProcessor.processAssistantMessage(assistantMessage, options);
}

/**
 * 便捷函数：添加工具使用
 * @param {Object} toolUse - 工具使用对象
 * @returns {string} 工具使用ID
 */
export function addToolUse(toolUse) {
  return globalToolUseManager.addToolUse(toolUse);
}

/**
 * 便捷函数：更新工具使用状态
 * @param {string} id - 工具使用ID
 * @param {string} status - 新状态
 * @param {Object} data - 附加数据
 */
export function updateToolUseStatus(id, status, data = {}) {
  return globalToolUseManager.updateToolUseStatus(id, status, data);
}
