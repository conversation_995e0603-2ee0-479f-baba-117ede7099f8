/**
 * 工具管理系统
 * 
 * 管理Claude Code的各种工具，包括权限验证、工具执行、结果处理等
 * 
 * @original: 工具相关代码 (L2987-3004, L4499-4651, L53156-53270)
 */

import { logger } from '../utils/logger.js';
import { PERMISSION_MODES } from '../config/constants.js';

/**
 * 工具类型枚举
 */
export const TOOL_TYPES = {
  FILE_PATTERN: 'filePattern',
  BASH_PREFIX: 'bashPrefix',
  CUSTOM: 'custom',
  MCP: 'mcp',
  BUILTIN: 'builtin'
};

/**
 * 工具分类配置
 * @original: kr1对象 (L4499)
 */
export const TOOL_CATEGORIES = {
  filePatternTools: ['Read', 'Write', 'Edit', 'Glob', 'MultiEdit', 'NotebookRead', 'NotebookEdit'],
  bashPrefixTools: ['Bash'],
  customValidation: {
    WebSearch: (pattern) => {
      if (pattern.includes('*') || pattern.includes('?')) {
        return {
          valid: false,
          error: 'WebSearch does not support wildcard patterns',
          suggestion: 'Use specific search terms instead of wildcards'
        };
      }
      return { valid: true };
    }
  }
};

/**
 * 工具权限决策结果
 */
export class ToolPermissionResult {
  constructor(behavior, options = {}) {
    this.behavior = behavior; // 'allow' | 'deny' | 'ask'
    this.message = options.message;
    this.updatedInput = options.updatedInput;
    this.decisionReason = options.decisionReason;
    this.ruleSuggestions = options.ruleSuggestions;
  }

  static allow(updatedInput, decisionReason) {
    return new ToolPermissionResult('allow', { updatedInput, decisionReason });
  }

  static deny(message, decisionReason) {
    return new ToolPermissionResult('deny', { message, decisionReason });
  }

  static ask(message, ruleSuggestions) {
    return new ToolPermissionResult('ask', { message, ruleSuggestions });
  }
}

/**
 * 工具管理器类
 */
export class ToolManager {
  constructor() {
    this.tools = new Map();
    this.toolDecisions = new Map();
    this.permissionRules = new Map();
  }

  /**
   * 注册工具
   * @param {Object} tool - 工具对象
   */
  registerTool(tool) {
    if (!tool.name) {
      throw new Error('Tool must have a name');
    }

    this.tools.set(tool.name, tool);
    logger.debug('工具已注册', { toolName: tool.name });
  }

  /**
   * 获取工具
   * @param {string} name - 工具名称
   * @returns {Object|null} 工具对象
   */
  getTool(name) {
    return this.tools.get(name) || null;
  }

  /**
   * 获取所有工具
   * @returns {Object[]} 工具数组
   */
  getAllTools() {
    return Array.from(this.tools.values());
  }

  /**
   * 检查工具是否为文件模式工具
   * @original: ZYA()函数 (L4533)
   * @param {string} toolName - 工具名称
   * @returns {boolean} 是否为文件模式工具
   */
  isFilePatternTool(toolName) {
    return TOOL_CATEGORIES.filePatternTools.includes(toolName);
  }

  /**
   * 检查工具是否为Bash前缀工具
   * @original: GYA()函数 (L4536)
   * @param {string} toolName - 工具名称
   * @returns {boolean} 是否为Bash前缀工具
   */
  isBashPrefixTool(toolName) {
    return TOOL_CATEGORIES.bashPrefixTools.includes(toolName);
  }

  /**
   * 获取工具的自定义验证函数
   * @original: FYA()函数 (L4539)
   * @param {string} toolName - 工具名称
   * @returns {Function|null} 验证函数
   */
  getCustomValidation(toolName) {
    return TOOL_CATEGORIES.customValidation[toolName] || null;
  }

  /**
   * 验证工具规则
   * @original: 工具规则验证逻辑 (L4555-4651)
   * @param {string} ruleString - 规则字符串
   * @returns {Object} 验证结果
   */
  validateToolRule(ruleString) {
    if (!ruleString || ruleString.trim().length === 0) {
      return {
        valid: false,
        error: 'Rule cannot be empty'
      };
    }

    // 检查空括号
    if (ruleString.trim() === '()') {
      return {
        valid: false,
        error: 'Empty parentheses with no tool name',
        suggestion: 'Specify a tool name before the parentheses'
      };
    }

    const parsed = this.parseToolRule(ruleString);
    const mcpInfo = this.parseMcpToolName(parsed.toolName);

    // MCP工具验证
    if (mcpInfo) {
      if (parsed.ruleContent !== undefined) {
        return {
          valid: false,
          error: 'MCP rules do not support patterns',
          suggestion: `Use "${parsed.toolName}" without parentheses`,
          examples: [
            `mcp__${mcpInfo.serverName}`,
            mcpInfo.toolName ? `mcp__${mcpInfo.serverName}__${mcpInfo.toolName}` : undefined
          ].filter(Boolean)
        };
      }
      return { valid: true };
    }

    // 工具名称验证
    if (!parsed.toolName || parsed.toolName.length === 0) {
      return {
        valid: false,
        error: 'Tool name cannot be empty'
      };
    }

    if (parsed.toolName[0] !== parsed.toolName[0]?.toUpperCase()) {
      return {
        valid: false,
        error: 'Tool names must start with uppercase',
        suggestion: `Use "${String(parsed.toolName).charAt(0).toUpperCase() + String(parsed.toolName).slice(1)}"`
      };
    }

    // 自定义验证
    const customValidator = this.getCustomValidation(parsed.toolName);
    if (customValidator && parsed.ruleContent !== undefined) {
      const result = customValidator(parsed.ruleContent);
      if (!result.valid) return result;
    }

    // Bash前缀工具验证
    if (this.isBashPrefixTool(parsed.toolName) && parsed.ruleContent !== undefined) {
      return this.validateBashPrefixRule(parsed);
    }

    // 文件模式工具验证
    if (this.isFilePatternTool(parsed.toolName) && parsed.ruleContent !== undefined) {
      return this.validateFilePatternRule(parsed);
    }

    return { valid: true };
  }

  /**
   * 验证Bash前缀规则
   * @param {Object} parsed - 解析后的规则
   * @returns {Object} 验证结果
   */
  validateBashPrefixRule(parsed) {
    const content = parsed.ruleContent;

    if (content.includes(':*') && !content.endsWith(':*')) {
      return {
        valid: false,
        error: 'Bash prefix rules with ":*" must end with ":*"',
        suggestion: `Use "${content.split(':')[0]}:*" to allow all subcommands`,
        examples: [
          `${parsed.toolName}(git:*) - allows all git commands`,
          `${parsed.toolName}(npm:*) - allows all npm commands`
        ]
      };
    }

    return { valid: true };
  }

  /**
   * 验证文件模式规则
   * @param {Object} parsed - 解析后的规则
   * @returns {Object} 验证结果
   */
  validateFilePatternRule(parsed) {
    const content = parsed.ruleContent;

    if (content.includes(':*')) {
      return {
        valid: false,
        error: 'The ":*" syntax is only for Bash prefix rules',
        suggestion: 'Use glob patterns like "*" or "**" for file matching',
        examples: [
          `${parsed.toolName}(*.ts) - matches .ts files`,
          `${parsed.toolName}(src/**) - matches all files in src`,
          `${parsed.toolName}(**/*.test.ts) - matches test files`
        ]
      };
    }

    if (content.includes('*') && 
        !content.match(/^\*|\*$|\*\*|\/\*|\*\.|\*\)/) && 
        !content.includes('**')) {
      return {
        valid: false,
        error: 'Wildcard placement might be incorrect',
        suggestion: 'Wildcards are typically used at path boundaries',
        examples: [
          `${parsed.toolName}(*.js) - all .js files`,
          `${parsed.toolName}(src/*) - all files directly in src`,
          `${parsed.toolName}(src/**) - all files recursively in src`
        ]
      };
    }

    return { valid: true };
  }

  /**
   * 解析工具规则
   * @param {string} ruleString - 规则字符串
   * @returns {Object} 解析结果
   */
  parseToolRule(ruleString) {
    const match = ruleString.match(/^([^(]+)\(([^)]+)\)$/);
    
    if (!match) {
      return { toolName: ruleString };
    }

    const toolName = match[1];
    const ruleContent = match[2];

    if (!toolName || !ruleContent) {
      return { toolName: ruleString };
    }

    return {
      toolName,
      ruleContent
    };
  }

  /**
   * 解析MCP工具名称
   * @param {string} toolName - 工具名称
   * @returns {Object|null} MCP信息
   */
  parseMcpToolName(toolName) {
    if (!toolName.startsWith('mcp__')) {
      return null;
    }

    const parts = toolName.substring(5).split('__');
    
    return {
      serverName: parts[0],
      toolName: parts[1] || undefined
    };
  }

  /**
   * 格式化工具规则
   * @original: d5()函数 (L3277)
   * @param {Object} ruleValue - 规则值
   * @returns {string} 格式化的规则字符串
   */
  formatToolRule(ruleValue) {
    return ruleValue.ruleContent 
      ? `${ruleValue.toolName}(${ruleValue.ruleContent})` 
      : ruleValue.toolName;
  }

  /**
   * 检查工具权限
   * @original: Nw()函数 (L3337)
   * @param {Object} tool - 工具对象
   * @param {Object} input - 输入参数
   * @param {Object} context - 权限上下文
   * @returns {Promise<ToolPermissionResult>} 权限检查结果
   */
  async checkToolPermission(tool, input, context) {
    // 检查中止信号
    if (context.abortController?.signal.aborted) {
      throw new Error('Operation was aborted');
    }

    // 检查拒绝规则
    const denyResult = this.checkDenyRules(context.getToolPermissionContext(), tool);
    if (denyResult) {
      return ToolPermissionResult.deny(
        denyResult.message,
        denyResult.decisionReason
      );
    }

    // 检查绕过权限模式
    if (context.getToolPermissionContext().mode === PERMISSION_MODES.BYPASS) {
      return ToolPermissionResult.allow(input, {
        type: 'mode',
        mode: context.getToolPermissionContext().mode
      });
    }

    // 检查允许规则
    const allowRule = this.findAllowRule(context.getToolPermissionContext(), tool);
    if (allowRule) {
      return ToolPermissionResult.allow(input, {
        type: 'rule',
        rule: allowRule
      });
    }

    // 需要询问用户
    const message = this.generatePermissionMessage(context.getToolPermissionContext(), tool.name);
    return ToolPermissionResult.ask(message);
  }

  /**
   * 检查拒绝规则
   * @param {Object} permissionContext - 权限上下文
   * @param {Object} tool - 工具对象
   * @returns {Object|null} 拒绝结果
   */
  checkDenyRules(permissionContext, tool) {
    // 实现拒绝规则检查逻辑
    return null;
  }

  /**
   * 查找允许规则
   * @param {Object} permissionContext - 权限上下文
   * @param {Object} tool - 工具对象
   * @returns {Object|null} 允许规则
   */
  findAllowRule(permissionContext, tool) {
    // 实现允许规则查找逻辑
    return null;
  }

  /**
   * 生成权限消息
   * @param {Object} permissionContext - 权限上下文
   * @param {string} toolName - 工具名称
   * @returns {string} 权限消息
   */
  generatePermissionMessage(permissionContext, toolName) {
    return `Permission required to use ${toolName}`;
  }

  /**
   * 记录工具决策
   * @original: WI1()函数 (L53130)
   * @param {Object} context - 权限上下文
   * @param {string} toolName - 工具名称
   * @param {string} decision - 决策结果
   * @param {string} source - 决策来源
   */
  recordToolDecision(context, toolName, decision, source) {
    if (!context.toolDecisions) {
      context.toolDecisions = new Map();
    }

    context.toolDecisions.set(toolName, {
      source,
      decision,
      timestamp: Date.now()
    });

    logger.debug('工具决策已记录', { toolName, decision, source });
  }

  /**
   * 创建工具决策遥测数据
   * @original: JI1()函数 (L53147)
   * @param {Object} tool - 工具对象
   * @param {Object} input - 输入参数
   * @param {string} decision - 决策结果
   * @param {string} source - 决策来源
   * @returns {Object} 遥测数据
   */
  createToolDecisionTelemetry(tool, input, decision, source) {
    const language = this.detectLanguage(input);
    
    return {
      decision,
      source,
      tool_name: tool.name,
      ...(language && { language })
    };
  }

  /**
   * 检测编程语言
   * @param {Object} input - 输入参数
   * @returns {string|null} 检测到的语言
   */
  detectLanguage(input) {
    // 实现语言检测逻辑
    if (input.file_path) {
      const ext = input.file_path.split('.').pop()?.toLowerCase();
      const languageMap = {
        'js': 'javascript',
        'ts': 'typescript',
        'py': 'python',
        'java': 'java',
        'cpp': 'cpp',
        'c': 'c',
        'go': 'go',
        'rs': 'rust',
        'php': 'php',
        'rb': 'ruby',
        'swift': 'swift',
        'kt': 'kotlin'
      };
      return languageMap[ext] || null;
    }
    return null;
  }

  /**
   * 执行工具
   * @param {Object} tool - 工具对象
   * @param {Object} input - 输入参数
   * @param {Object} context - 执行上下文
   * @returns {Promise<*>} 执行结果
   */
  async executeTool(tool, input, context) {
    logger.debug('执行工具', { toolName: tool.name, input });

    try {
      // 检查工具是否启用
      if (!tool.isEnabled()) {
        throw new Error(`Tool ${tool.name} is not enabled`);
      }

      // 验证输入
      if (tool.validateInput) {
        const validation = await tool.validateInput(input, context);
        if (validation && !validation.result) {
          throw new Error(`Tool ${tool.name} input is invalid: ${validation.message}`);
        }
      }

      // 执行工具
      const result = await tool.call(input, context);
      
      logger.debug('工具执行成功', { toolName: tool.name });
      return result;

    } catch (error) {
      logger.error('工具执行失败', { toolName: tool.name, error: error.message });
      throw error;
    }
  }

  /**
   * 批量执行工具
   * @param {Array} toolCalls - 工具调用数组
   * @param {Object} context - 执行上下文
   * @returns {Promise<Array>} 执行结果数组
   */
  async executeToolsBatch(toolCalls, context) {
    const results = [];
    
    for (const toolCall of toolCalls) {
      try {
        const tool = this.getTool(toolCall.name);
        if (!tool) {
          throw new Error(`Tool ${toolCall.name} not found`);
        }

        const result = await this.executeTool(tool, toolCall.input, context);
        results.push({
          toolCall,
          result,
          success: true
        });

      } catch (error) {
        results.push({
          toolCall,
          error: error.message,
          success: false
        });
      }
    }

    return results;
  }

  /**
   * 获取工具描述
   * @param {Object} tool - 工具对象
   * @param {Object} context - 上下文
   * @returns {Promise<string>} 工具描述
   */
  async getToolDescription(tool, context) {
    if (tool.description) {
      if (typeof tool.description === 'function') {
        return await tool.description({}, context);
      }
      return tool.description;
    }
    
    return `Tool: ${tool.name}`;
  }

  /**
   * 获取启用的工具列表
   * @param {Object} permissionContext - 权限上下文
   * @returns {Object[]} 启用的工具列表
   */
  getEnabledTools(permissionContext) {
    return this.getAllTools().filter(tool => {
      try {
        return tool.isEnabled && tool.isEnabled();
      } catch (error) {
        logger.warn('检查工具启用状态失败', { toolName: tool.name, error: error.message });
        return false;
      }
    });
  }

  /**
   * 过滤允许的工具
   * @original: B8Q()函数 (L2987)
   * @param {Object} settings - 设置对象
   * @param {Array} rules - 规则数组
   * @returns {string[]} 允许的工具列表
   */
  filterAllowedTools(settings, rules) {
    if (!settings.allowedTools || settings.allowedTools.length < 1) {
      return [];
    }

    const explicitlyAllowed = new Set();
    for (const rule of rules) {
      if (rule.ruleBehavior === 'allow' && rule.source === 'localSettings') {
        explicitlyAllowed.add(this.formatToolRule(rule.ruleValue));
      }
    }

    const filtered = new Set();
    for (const toolName of settings.allowedTools) {
      if (!explicitlyAllowed.has(toolName)) {
        filtered.add(toolName);
      }
    }

    return Array.from(filtered);
  }

  /**
   * 过滤忽略模式
   * @original: Q8Q()函数 (L2994)
   * @param {Object} settings - 设置对象
   * @param {Array} rules - 规则数组
   * @returns {Object[]} 忽略模式列表
   */
  filterIgnorePatterns(settings, rules) {
    if (!settings.ignorePatterns || settings.ignorePatterns.length < 1) {
      return [];
    }

    const explicitlyDenied = new Set();
    for (const rule of rules) {
      if (rule.ruleBehavior === 'deny' && 
          rule.source === 'localSettings' && 
          rule.ruleValue.toolName === 'Read' && 
          rule.ruleValue.ruleContent !== undefined) {
        explicitlyDenied.add(rule.ruleValue.ruleContent);
      }
    }

    const filtered = new Set();
    for (const pattern of settings.ignorePatterns) {
      if (!explicitlyDenied.has(pattern)) {
        filtered.add(pattern);
      }
    }

    return Array.from(filtered).map(pattern => ({
      toolName: 'Read',
      ruleContent: pattern
    }));
  }
}

/**
 * 全局工具管理器实例
 */
export const globalToolManager = new ToolManager();

/**
 * 便捷函数：注册工具
 * @param {Object} tool - 工具对象
 */
export function registerTool(tool) {
  return globalToolManager.registerTool(tool);
}

/**
 * 便捷函数：获取工具
 * @param {string} name - 工具名称
 * @returns {Object|null} 工具对象
 */
export function getTool(name) {
  return globalToolManager.getTool(name);
}

/**
 * 便捷函数：执行工具
 * @param {string} toolName - 工具名称
 * @param {Object} input - 输入参数
 * @param {Object} context - 执行上下文
 * @returns {Promise<*>} 执行结果
 */
export async function executeTool(toolName, input, context) {
  const tool = globalToolManager.getTool(toolName);
  if (!tool) {
    throw new Error(`Tool ${toolName} not found`);
  }
  
  return globalToolManager.executeTool(tool, input, context);
}

/**
 * 便捷函数：检查工具权限
 * @param {Object} tool - 工具对象
 * @param {Object} input - 输入参数
 * @param {Object} context - 权限上下文
 * @returns {Promise<ToolPermissionResult>} 权限检查结果
 */
export async function checkToolPermission(tool, input, context) {
  return globalToolManager.checkToolPermission(tool, input, context);
}
