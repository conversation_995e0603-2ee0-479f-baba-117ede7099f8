/**
 * 代理（Agent）管理系统
 * 
 * 管理AI代理的创建、配置、执行和生命周期
 * 
 * @original: Agent相关代码 (L45992-46194, L47597-47954)
 */

import { logger } from '../utils/logger.js';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { logTelemetryEvent } from '../services/telemetry.js';

/**
 * 代理位置枚举
 */
export const AGENT_LOCATIONS = {
  BUILT_IN: 'built-in',
  USER: 'user',
  PROJECT: 'project',
  GLOBAL: 'global'
};

/**
 * 代理状态枚举
 */
export const AGENT_STATUS = {
  IDLE: 'idle',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

/**
 * 代理类型枚举
 */
export const AGENT_TYPES = {
  GENERAL: 'general',
  CODE_REVIEW: 'code_review',
  DEBUGGING: 'debugging',
  REFACTORING: 'refactoring',
  TESTING: 'testing',
  DOCUMENTATION: 'documentation'
};

/**
 * 代理管理器类
 */
export class AgentManager {
  constructor() {
    this.agents = new Map();
    this.runningAgents = new Set();
    this.agentConfigs = new Map();
  }

  /**
   * 获取代理描述
   * @original: VuB()函数 (L45992-46123)
   * @param {Object} tools - 可用工具
   * @returns {Promise<string>} 代理描述
   */
  async getAgentDescription(tools) {
    const readTool = tools.find(tool => tool.name === 'read');
    const grepTool = tools.find(tool => tool.name === 'grep');

    return `启动一个新的代理来自主处理复杂的多步骤任务。

代理非常适合以下任务：
- 需要多个步骤的复杂任务
- 需要在多个文件中进行更改的重构
- 需要分析和理解大型代码库的任务
- 需要协调多个工具的复合操作

**何时使用代理工具：**
- 当任务需要多个步骤且每个步骤都依赖于前一个步骤的结果时
- 当您需要分析大型代码库并进行系统性更改时
- 当任务涉及复杂的决策制定过程时

**何时不使用代理工具：**
- 如果您想读取特定文件路径，请使用${readTool?.name || 'read'}或${grepTool?.name || 'grep'}工具，以更快地找到匹配项
- 如果您正在搜索特定的类定义（如"class Foo"），请使用${grepTool?.name || 'grep'}工具，以更快地找到匹配项
- 如果您在特定文件或2-3个文件集合中搜索代码，请使用${readTool?.name || 'read'}工具而不是代理工具，以更快地找到匹配项
- 与上述代理描述无关的其他任务

代理将自主工作以完成任务，并在完成时提供详细的报告。`;
  }

  /**
   * 创建新代理
   * @param {string} name - 代理名称
   * @param {string} description - 代理描述
   * @param {Object} config - 代理配置
   * @returns {Promise<string>} 代理ID
   */
  async createAgent(name, description, config = {}) {
    try {
      const agentId = this.generateAgentId();
      
      const agent = {
        id: agentId,
        name,
        description,
        config,
        status: AGENT_STATUS.IDLE,
        createdAt: new Date().toISOString(),
        location: config.location || AGENT_LOCATIONS.USER
      };

      this.agents.set(agentId, agent);
      this.agentConfigs.set(agentId, config);

      logger.info('代理创建成功', { agentId, name });
      
      logTelemetryEvent('agent_created', {
        agentId,
        agentType: config.type || AGENT_TYPES.GENERAL
      });

      return agentId;

    } catch (error) {
      logger.error('创建代理失败', { name, error: error.message });
      throw error;
    }
  }

  /**
   * 保存代理配置
   * @original: TuB()函数 (L47597-47612)
   * @param {string} location - 保存位置
   * @param {string} name - 代理名称
   * @param {string} description - 代理描述
   * @param {string} systemPrompt - 系统提示
   * @param {Array} tools - 工具列表
   * @param {boolean} enableMemory - 是否启用内存
   * @param {Object} options - 其他选项
   * @returns {Promise<void>}
   */
  async saveAgentConfig(location, name, description, systemPrompt, tools, enableMemory = true, options = {}) {
    if (location === AGENT_LOCATIONS.BUILT_IN) {
      throw new Error('无法保存内置代理');
    }

    this.validateAgentName(name);

    try {
      const config = {
        name,
        description,
        systemPrompt,
        tools: tools || [],
        enableMemory,
        ...options,
        updatedAt: new Date().toISOString()
      };

      const configPath = this.getAgentConfigPath(location, name);
      const configDir = dirname(configPath);

      // 确保目录存在
      if (!existsSync(configDir)) {
        const fs = require('fs');
        fs.mkdirSync(configDir, { recursive: true });
      }

      writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8');
      
      logger.info('代理配置保存成功', { location, name, configPath });

    } catch (error) {
      logger.error('保存代理配置失败', { location, name, error: error.message });
      throw error;
    }
  }

  /**
   * 更新代理配置
   * @original: PuB()函数 (L47613-47622)
   * @param {Object} agent - 代理对象
   * @param {string} name - 新名称
   * @param {string} description - 新描述
   * @param {string} systemPrompt - 新系统提示
   * @param {Array} tools - 新工具列表
   * @param {boolean} enableMemory - 是否启用内存
   * @returns {Promise<void>}
   */
  async updateAgentConfig(agent, name, description, systemPrompt, tools, enableMemory) {
    if (agent.location === AGENT_LOCATIONS.BUILT_IN) {
      throw new Error('无法更新内置代理');
    }

    try {
      const fs = require('fs');
      const configPath = this.getAgentConfigPath(agent.location, agent.name);

      // 如果名称改变，需要重命名文件
      if (name !== agent.name) {
        const newConfigPath = this.getAgentConfigPath(agent.location, name);
        fs.renameSync(configPath, newConfigPath);
      }

      // 更新配置
      await this.saveAgentConfig(
        agent.location,
        name,
        description,
        systemPrompt,
        tools,
        enableMemory
      );

      logger.info('代理配置更新成功', { 
        oldName: agent.name, 
        newName: name 
      });

    } catch (error) {
      logger.error('更新代理配置失败', { agent: agent.name, error: error.message });
      throw error;
    }
  }

  /**
   * 删除代理
   * @original: SuB()函数 (L47623-47632)
   * @param {Object} agent - 代理对象
   * @returns {Promise<void>}
   */
  async deleteAgent(agent) {
    if (agent.location === AGENT_LOCATIONS.BUILT_IN) {
      throw new Error('无法删除内置代理');
    }

    try {
      const fs = require('fs');
      const configPath = this.getAgentConfigPath(agent.location, agent.name);

      if (existsSync(configPath)) {
        fs.unlinkSync(configPath);
      }

      this.agents.delete(agent.id);
      this.agentConfigs.delete(agent.id);

      logger.info('代理删除成功', { agentName: agent.name });

    } catch (error) {
      logger.error('删除代理失败', { agent: agent.name, error: error.message });
      throw error;
    }
  }

  /**
   * 加载代理配置
   * @param {string} location - 代理位置
   * @param {string} name - 代理名称
   * @returns {Promise<Object|null>} 代理配置
   */
  async loadAgentConfig(location, name) {
    try {
      const configPath = this.getAgentConfigPath(location, name);
      
      if (!existsSync(configPath)) {
        return null;
      }

      const configContent = readFileSync(configPath, 'utf8');
      const config = JSON.parse(configContent);

      return {
        ...config,
        location,
        configPath
      };

    } catch (error) {
      logger.error('加载代理配置失败', { location, name, error: error.message });
      return null;
    }
  }

  /**
   * 获取代理配置路径
   * @param {string} location - 代理位置
   * @param {string} name - 代理名称
   * @returns {string} 配置文件路径
   */
  getAgentConfigPath(location, name) {
    const { homedir } = require('os');
    const baseDir = homedir();

    switch (location) {
      case AGENT_LOCATIONS.USER:
        return join(baseDir, '.claude', 'agents', `${name}.json`);
      
      case AGENT_LOCATIONS.PROJECT:
        return join(process.cwd(), '.claude', 'agents', `${name}.json`);
      
      case AGENT_LOCATIONS.GLOBAL:
        return join(baseDir, '.config', 'claude', 'agents', `${name}.json`);
      
      default:
        throw new Error(`不支持的代理位置: ${location}`);
    }
  }

  /**
   * 验证代理名称
   * @original: iq8()函数相关逻辑
   * @param {string} name - 代理名称
   * @throws {Error} 如果名称无效
   */
  validateAgentName(name) {
    if (!name || typeof name !== 'string') {
      throw new Error('代理名称必须是非空字符串');
    }

    if (name.length < 1 || name.length > 50) {
      throw new Error('代理名称长度必须在1-50个字符之间');
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(name)) {
      throw new Error('代理名称只能包含字母、数字、下划线和连字符');
    }
  }

  /**
   * 生成代理ID
   * @returns {string} 唯一的代理ID
   */
  generateAgentId() {
    return `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取所有代理
   * @returns {Array} 代理列表
   */
  getAllAgents() {
    return Array.from(this.agents.values());
  }

  /**
   * 根据ID获取代理
   * @param {string} agentId - 代理ID
   * @returns {Object|null} 代理对象
   */
  getAgentById(agentId) {
    return this.agents.get(agentId) || null;
  }

  /**
   * 根据名称获取代理
   * @param {string} name - 代理名称
   * @returns {Object|null} 代理对象
   */
  getAgentByName(name) {
    for (const agent of this.agents.values()) {
      if (agent.name === name) {
        return agent;
      }
    }
    return null;
  }

  /**
   * 启动代理
   * @param {string} agentId - 代理ID
   * @param {Object} context - 执行上下文
   * @returns {Promise<void>}
   */
  async startAgent(agentId, context = {}) {
    const agent = this.getAgentById(agentId);
    
    if (!agent) {
      throw new Error(`代理不存在: ${agentId}`);
    }

    if (this.runningAgents.has(agentId)) {
      throw new Error(`代理已在运行: ${agentId}`);
    }

    try {
      agent.status = AGENT_STATUS.RUNNING;
      agent.startedAt = new Date().toISOString();
      
      this.runningAgents.add(agentId);

      logger.info('代理启动', { agentId, agentName: agent.name });
      
      logTelemetryEvent('agent_started', {
        agentId,
        agentType: agent.config?.type || AGENT_TYPES.GENERAL
      });

    } catch (error) {
      agent.status = AGENT_STATUS.FAILED;
      this.runningAgents.delete(agentId);
      
      logger.error('代理启动失败', { agentId, error: error.message });
      throw error;
    }
  }

  /**
   * 停止代理
   * @param {string} agentId - 代理ID
   * @param {string} reason - 停止原因
   * @returns {Promise<void>}
   */
  async stopAgent(agentId, reason = 'manual') {
    const agent = this.getAgentById(agentId);
    
    if (!agent) {
      throw new Error(`代理不存在: ${agentId}`);
    }

    try {
      agent.status = reason === 'manual' ? AGENT_STATUS.CANCELLED : AGENT_STATUS.COMPLETED;
      agent.stoppedAt = new Date().toISOString();
      agent.stopReason = reason;
      
      this.runningAgents.delete(agentId);

      logger.info('代理停止', { agentId, agentName: agent.name, reason });
      
      logTelemetryEvent('agent_stopped', {
        agentId,
        reason,
        duration: agent.startedAt ? Date.now() - new Date(agent.startedAt).getTime() : 0
      });

    } catch (error) {
      logger.error('代理停止失败', { agentId, error: error.message });
      throw error;
    }
  }

  /**
   * 获取运行中的代理
   * @returns {Array} 运行中的代理列表
   */
  getRunningAgents() {
    return Array.from(this.runningAgents)
      .map(agentId => this.getAgentById(agentId))
      .filter(agent => agent !== null);
  }

  /**
   * 清理已完成的代理
   * @param {number} maxAge - 最大保留时间（毫秒）
   * @returns {number} 清理的代理数量
   */
  cleanupCompletedAgents(maxAge = 24 * 60 * 60 * 1000) { // 24小时
    let cleanedCount = 0;
    const now = Date.now();

    for (const [agentId, agent] of this.agents) {
      if (agent.status === AGENT_STATUS.COMPLETED || agent.status === AGENT_STATUS.FAILED) {
        const completedAt = new Date(agent.stoppedAt || agent.createdAt).getTime();
        
        if (now - completedAt > maxAge) {
          this.agents.delete(agentId);
          this.agentConfigs.delete(agentId);
          cleanedCount++;
        }
      }
    }

    if (cleanedCount > 0) {
      logger.info('清理已完成的代理', { cleanedCount });
    }

    return cleanedCount;
  }
}

/**
 * 代理执行器类
 */
export class AgentExecutor {
  constructor(agentManager) {
    this.agentManager = agentManager || new AgentManager();
    this.executionQueue = [];
    this.maxConcurrentAgents = 3;
  }

  /**
   * 执行代理任务
   * @original: 代理执行逻辑 (L46124-46194)
   * @param {string} agentId - 代理ID
   * @param {string} prompt - 任务提示
   * @param {Object} context - 执行上下文
   * @returns {Promise<Object>} 执行结果
   */
  async executeAgent(agentId, prompt, context = {}) {
    const agent = this.agentManager.getAgentById(agentId);
    
    if (!agent) {
      throw new Error(`代理不存在: ${agentId}`);
    }

    try {
      await this.agentManager.startAgent(agentId, context);

      // 构建执行上下文
      const executionContext = {
        agentId,
        prompt,
        systemPrompt: agent.config?.systemPrompt || '',
        tools: agent.config?.tools || [],
        enableMemory: agent.config?.enableMemory !== false,
        ...context
      };

      // 执行代理逻辑
      const result = await this.performAgentExecution(executionContext);

      await this.agentManager.stopAgent(agentId, 'completed');

      return {
        success: true,
        agentId,
        result,
        executionTime: Date.now() - new Date(agent.startedAt).getTime()
      };

    } catch (error) {
      await this.agentManager.stopAgent(agentId, 'failed');
      
      logger.error('代理执行失败', { agentId, error: error.message });
      
      return {
        success: false,
        agentId,
        error: error.message
      };
    }
  }

  /**
   * 执行代理逻辑
   * @param {Object} context - 执行上下文
   * @returns {Promise<Object>} 执行结果
   */
  async performAgentExecution(context) {
    // 这里实现具体的代理执行逻辑
    // 包括调用AI模型、使用工具、处理响应等
    
    logger.info('开始执行代理任务', { 
      agentId: context.agentId,
      prompt: context.prompt.substring(0, 100) + '...'
    });

    // 模拟代理执行过程
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      message: '代理任务执行完成',
      steps: [],
      output: '任务已成功完成'
    };
  }

  /**
   * 获取执行队列状态
   * @returns {Object} 队列状态
   */
  getQueueStatus() {
    return {
      queueLength: this.executionQueue.length,
      runningAgents: this.agentManager.getRunningAgents().length,
      maxConcurrent: this.maxConcurrentAgents
    };
  }
}

/**
 * 代理模板管理器类
 */
export class AgentTemplateManager {
  constructor() {
    this.templates = new Map();
    this.loadBuiltInTemplates();
  }

  /**
   * 加载内置模板
   */
  loadBuiltInTemplates() {
    const builtInTemplates = [
      {
        name: 'code-reviewer',
        type: AGENT_TYPES.CODE_REVIEW,
        description: '代码审查代理，专门用于代码质量检查和改进建议',
        systemPrompt: '你是一个专业的代码审查员，专注于代码质量、最佳实践和潜在问题的识别。',
        tools: ['read', 'grep', 'write'],
        enableMemory: true
      },
      {
        name: 'debugger',
        type: AGENT_TYPES.DEBUGGING,
        description: '调试代理，专门用于查找和修复代码中的错误',
        systemPrompt: '你是一个专业的调试专家，专注于识别、分析和修复代码中的错误。',
        tools: ['read', 'grep', 'bash', 'write'],
        enableMemory: true
      },
      {
        name: 'refactorer',
        type: AGENT_TYPES.REFACTORING,
        description: '重构代理，专门用于代码重构和架构改进',
        systemPrompt: '你是一个专业的代码重构专家，专注于改进代码结构、可读性和可维护性。',
        tools: ['read', 'grep', 'write'],
        enableMemory: true
      }
    ];

    for (const template of builtInTemplates) {
      this.templates.set(template.name, {
        ...template,
        location: AGENT_LOCATIONS.BUILT_IN
      });
    }
  }

  /**
   * 获取模板
   * @param {string} name - 模板名称
   * @returns {Object|null} 模板对象
   */
  getTemplate(name) {
    return this.templates.get(name) || null;
  }

  /**
   * 获取所有模板
   * @returns {Array} 模板列表
   */
  getAllTemplates() {
    return Array.from(this.templates.values());
  }

  /**
   * 根据类型获取模板
   * @param {string} type - 代理类型
   * @returns {Array} 匹配的模板列表
   */
  getTemplatesByType(type) {
    return this.getAllTemplates().filter(template => template.type === type);
  }
}

/**
 * 全局代理管理器实例
 */
export const globalAgentManager = new AgentManager();

/**
 * 全局代理执行器实例
 */
export const globalAgentExecutor = new AgentExecutor(globalAgentManager);

/**
 * 全局代理模板管理器实例
 */
export const globalAgentTemplateManager = new AgentTemplateManager();

/**
 * 便捷函数：创建代理
 * @param {string} name - 代理名称
 * @param {string} description - 代理描述
 * @param {Object} config - 代理配置
 * @returns {Promise<string>} 代理ID
 */
export async function createAgent(name, description, config = {}) {
  return globalAgentManager.createAgent(name, description, config);
}

/**
 * 便捷函数：执行代理任务
 * @param {string} agentId - 代理ID
 * @param {string} prompt - 任务提示
 * @param {Object} context - 执行上下文
 * @returns {Promise<Object>} 执行结果
 */
export async function executeAgent(agentId, prompt, context = {}) {
  return globalAgentExecutor.executeAgent(agentId, prompt, context);
}

/**
 * 便捷函数：获取代理描述
 * @param {Object} tools - 可用工具
 * @returns {Promise<string>} 代理描述
 */
export async function getAgentDescription(tools) {
  return globalAgentManager.getAgentDescription(tools);
}
